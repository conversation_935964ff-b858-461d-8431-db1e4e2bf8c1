﻿using Elegal.Interface.Api.Common.Control.Web;
using Elegal.Interface.Api.Common.FuncService;
using Elegal.Interface.Api.Common.Model.ResultModel;
using Microsoft.AspNetCore.Mvc;

namespace Elegal.Flow.Api.Controllers
{
    /// <summary>
    /// 所屬地對應表拓展服務
    /// </summary>
    [Route("[controller]")]
    [ApiController]
    public class PsSubLocVwAExtendController : BaseController
    {
        /// <summary>
        ///选择數據源
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("GetDataSource")]
        public async Task<ApiResultModelByObject> GetDataSource()
        {
            ApiResultModelByObject apiResult = new ApiResultModelByObject();

            apiResult.listData = PsSubLocVwAExtendService.GetDataSource();
            apiResult.rtnSuccess = true;

            return await Task.FromResult(apiResult);
        }
    }
}
