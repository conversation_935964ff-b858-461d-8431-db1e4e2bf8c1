using Elegal.Flow.Api.Services.ApplicationPrint;
using Elegal.Flow.Common.Services.LiteratureApply;
using Elegal.Interface.Api.Common.Control.Filter;
using Elegal.Interface.Api.Common.Control.Web;
using Elegal.Interface.Api.Common.Model.DBModel.literatureApply;
using Elegal.Interface.Api.Common.Model.ParaModel.FlowApi.FlowStep;
using Elegal.Interface.Api.Common.Model.ResultModel;
using Elegal.Orm;
using Microsoft.AspNetCore.Mvc;
using System.Text;

namespace Elegal.Flow.Api.Controllers.ApplyCase
{
    /// <summary>
    /// 資料建檔
    /// </summary>
    [Route("[controller]")]
    [ApiController]
    public class LiteratureApplyController : BaseController
    {
        #region 資料建檔資訊 -> 暫存單

        #region 當前操作人員是否存在暫存單
        /// <summary>
        /// 當前操作人員是否存在暫存單
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("GetTempLiteratureApplication")]
        public async Task<ApiResultModelByObject> GetTempLiteratureApplication()
        {
            ApiResultModelByObject apiResult = new();

            apiResult.listData = LiteratureApplyService.GetTempLiteratureApplication();
            apiResult.rtnSuccess = true;

            return await Task.FromResult(apiResult);
        }
        #endregion

        #region 重置暫存單(事務處理)
        /// <summary>
        /// 重置暫存單
        /// </summary>
        [HttpPut]
        [Route("ResetTempLiteratureApplication")]
        public async Task<ApiResultModelByObject> ResetTempLiteratureApplication()
        {
            ApiResultModelByObject apiResult = new();

            apiResult.rtnSuccess = LiteratureApplyService.ResetTempLiteratureApplicationToTransaction();
            //记录日志
            InitLogRecord(logRecord =>
            {
            });
            return await Task.FromResult(apiResult);
        }
        #endregion

        #region 將暫存單改為已核准(事務處理)
        /// <summary>
        /// 將暫存單改為已核准
        /// </summary>
        /// <param name="para"></param>
        /// <returns></returns>
        [HttpPut]
        [Route("SetLiteratureApplyInProgress")]
        public async Task<ApiResultModelByObject> SetLiteratureApplyInProgress([FromBody] FormProgressParaModel? para = null)
        {
            ApiResultModelByObject apiResult = new();
            var result = LiteratureApplyService.SetLiteratureApplyInProgressToTransaction(para);
            apiResult.listData = result;
            apiResult.rtnSuccess = true;
            //记录日志
            InitLogRecord(logRecord =>
            {
                StringBuilder stringBuilder = new();
                if (para != null)
                {
                    stringBuilder.AppendLine($"申請單號: {result.apply_number}");
                }
                logRecord.Detail = stringBuilder.ToString();
            });
            return await Task.FromResult(apiResult);
        }
        #endregion

        #endregion

        #region 資料建檔資訊功能修改/新增(事務處理)
        /// <summary>
        /// 資料建檔資訊功能修改/新增
        /// </summary>
        /// <param name="hlam"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("HandleLiteratureApplication")]
        public async Task<ApiResultModelByObject> HandleLiteratureApplication([FromBody] HandleLiteratureApplicationModel hlam)
        {
            ApiResultModelByObject apiResult = new ApiResultModelByObject();
            string applyNumber = LiteratureApplyService.HandleLiteratureApplicationToTransaction(hlam);
            apiResult.listData = applyNumber;
            apiResult.rtnSuccess = !string.IsNullOrEmpty(applyNumber);
            //記錄日誌
            InitLogRecord(applyNumber, logRecord =>
            {
                StringBuilder stringBuilder = new();
                var istemp = DbAccess.Database.ExecuteScalar($"select emplid from ps_sub_ee_lgl_vw_a where emplid ='{applyNumber}'")?.ToString() ?? "";
                if (istemp != null && istemp != "")
                    stringBuilder.AppendLine($"申請單號: -");
                else
                    if (!string.IsNullOrEmpty(applyNumber)) stringBuilder.AppendLine($"申請單號: {applyNumber}");
                logRecord.Detail = stringBuilder.ToString();
            });
            return await Task.FromResult(apiResult);
        }
        #endregion

        #region 資料建檔資訊 -> 查詢+列印

        #region 獲取資料建檔申請單資訊(全部)
        /// <summary>
        /// 獲取資料建檔申請單資訊(全部)
        /// </summary>
        /// <param name="applyNumber"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetLiteratureApplication")]
        public async Task<ApiResultModelByObject> GetLiteratureApplication([FromQuery] string applyNumber)
        {
            ApiResultModelByObject apiResult = new ApiResultModelByObject();

            apiResult.listData = LiteratureApplyService.GetLiteratureApplication(applyNumber);
            apiResult.rtnSuccess = true;

            return await Task.FromResult(apiResult);
        }
        #endregion

        #region 獲取資料建檔申請單資訊(列印)
        /// <summary>
        /// 獲取資料建檔申請單資訊(列印)
        /// </summary>
        /// <param name="applyNumber"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetLiteratureApplicationByPrint")]
        public async Task<ApiResultModelByObject> GetLiteratureApplicationByPrint([FromQuery] string applyNumber)
        {
            ApiResultModelByObject apiResult = new ApiResultModelByObject();

            apiResult.listData = LiteratureApplyPrintService.GetLiteratureApplicationByPrint(applyNumber);
            apiResult.rtnSuccess = true;

            return await Task.FromResult(apiResult);
        }
        #endregion

        #endregion
    }
}