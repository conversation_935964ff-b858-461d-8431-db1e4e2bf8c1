﻿using Elegal.Interface.Api.Common.Control.Filter;
using Elegal.Interface.Api.Common.Model;
using Elegal.Interface.Api.Common.Model.ResultModel.FlowApi;
using Elegal.Interface.Api.Common.Repository;
using Microsoft.IdentityModel.Tokens;
using System.Text;

namespace Elegal.Flow.Api.Repository
{
    /// <summary>
    /// 公共頁簽和公告管理
    /// </summary>
    public class UndertakeWindowRepository : BaseRepository
    {
        /// <summary>
        /// 實例化
        /// </summary>
        public UndertakeWindowRepository() : base() { }

        /// <summary>
        /// 查詢所有公共
        /// </summary>
        /// <returns></returns>
        public List<UndertakeWindow> QueryUndertakeWindow(UndertakeWindow undertake, string logging_locale, string timeZone = "Taipei Standard Time")
        {
            StringBuilder sqlBuilder = new();
            sqlBuilder.Append(@$"SELECT fnid,item,fc.empid,fc.contact_agent,
                                item_name,--項目
                                ee.deptid,--部門代號
                                og.descr,--部門名稱
                                ee.name AS contact_cuser,--承辦窗口聯絡人中文名
                                ee.name_a AS contact_euser,--承辦窗口聯絡人英文名
                                ee.emplid AS contact_user,
                                (CASE ee.phone WHEN '' THEN '' ELSE ee.area_code + N'+' + phone END) AS tel,--電話(區碼+分機)
                                agentee.name AS agent_cuser,--代理人中文名
                                agentee.name_a AS agent_euser,--代理人英文名
                                agentee.emplid AS agent_user,
                                contact_notes,--備註
                                operate_time,--更新時間
                                opee.name AS operate_cuser,--更新人中文名
                                opee.name_a AS operate_euser,--更新人英文名
                                is_show_home
                                FROM (
                                SELECT fnid,item,empid,(SELECT fun_name FROM sys_parameters WHERE para_code = 'contactManage' AND lang_type = @logging_locale AND func_code = item) AS item_name,contact_agent,contact_notes,ISNULL(modify_user,create_user) AS operate_user,ISNULL(modify_time,create_time) AS operate_time,is_show_home FROM fnp_contact
                                ) AS fc
                                LEFT JOIN (SELECT emplid,deptid,name_a,name,RTRIM(LTRIM(ISNULL(phone_a, ''))) AS phone,CASE RTRIM(LTRIM(ISNULL(prefix_dial_code_a, ''))) WHEN '' THEN '0000' ELSE RTRIM(LTRIM(ISNULL(prefix_dial_code_a, ''))) END AS area_code FROM ps_sub_ee_lgl_vw_a) AS ee ON ee.emplid = fc.empid
                                LEFT JOIN (SELECT deptid,descr FROM ps_sub_og_lgl_vw_a) AS og ON og.deptid = ee.deptid
                                LEFT JOIN (SELECT emplid,name,name_a FROM ps_sub_ee_lgl_vw_a) AS agentee ON agentee.emplid = fc.contact_agent
                                LEFT JOIN (SELECT emplid,name,name_a FROM ps_sub_ee_lgl_vw_a) AS opee ON opee.emplid = fc.operate_user
                                WHERE 1 = 1");
            if (!string.IsNullOrEmpty(undertake.empid))
                sqlBuilder.Append($@"AND (ee.name LIKE CONCAT(N'%', @empid, N'%') OR ee.name_a LIKE CONCAT(N'%', @empid, N'%') OR fc.empid LIKE CONCAT(N'%', @empid, N'%'))");
            if (undertake.is_show_home != null)
            {
                sqlBuilder.Append($@" AND fc.is_show_home = @is_show_home");
            }
            sqlBuilder.Append(@" ORDER BY item ASC,ee.deptid ASC , stuff(fc.empid,1,patindex('%[A-z]%',substring(fc.empid,1,1))-1,'') asc, len(fc.empid) desc,fc.empid desc");
            return this.NpgsqlSearchByList<UndertakeWindow>(sqlBuilder.ToString(), new { contact_cuser = undertake.contact_cuser, contact_euser = undertake.contact_euser, empid = undertake.empid, logging_locale = logging_locale, is_show_home = undertake.is_show_home },
                "time_zone", timeZone);
        }

        /// <summary>
        /// 根據fnid查詢承辦人窗口數據
        /// </summary>
        /// <returns></returns>
        public UndertakeWindow QueryUndertakeWindowByFnid(int fnid, string logging_locale, string timeZone = "Taipei Standard Time")
        {
            StringBuilder sqlBuilder = new();
            sqlBuilder.Append(@$"SELECT fnid,item,fc.empid,
                                item_name,--項目
                                ee.deptid,--部門代號
                                og.descr,--部門名稱
                                ee.name AS contact_cuser,--承辦窗口聯絡人中文名
                                ee.name_a AS contact_euser,--承辦窗口聯絡人英文名
                                ee.emplid AS contact_user,
                                ee.email,
                                (CASE ee.phone WHEN '' THEN '' ELSE ee.area_code + N'+' + phone END) AS tel,--電話(區碼+分機)
                                fc.contact_agent,
                                agentee.name AS agent_cuser,--代理人中文名
                                agentee.name_a AS agent_euser,--代理人英文名
                                agentee.emplid AS agent_user,
                                contact_notes,--備註
                                operate_time,--更新時間
                                opee.name AS operate_cuser,--更新人中文名
                                opee.name_a AS operate_euser,--更新人英文名
                                is_show_home
                                FROM (
                                SELECT fnid,item,empid,(SELECT fun_name FROM sys_parameters WHERE para_code = 'contactManage' AND lang_type = 'zh-tw' AND func_code = item) AS item_name,contact_agent,contact_notes,ISNULL(modify_user,create_user) AS operate_user,ISNULL(modify_time,create_time) AS operate_time,is_show_home FROM fnp_contact
                                ) AS fc
                                LEFT JOIN (SELECT emplid,deptid,name_a,name,email_address_a email,RTRIM(LTRIM(ISNULL(phone_a, ''))) AS phone,CASE RTRIM(LTRIM(ISNULL(prefix_dial_code_a, ''))) WHEN '' THEN '0000' ELSE RTRIM(LTRIM(ISNULL(prefix_dial_code_a, ''))) END AS area_code FROM ps_sub_ee_lgl_vw_a) AS ee ON ee.emplid = fc.empid
                                LEFT JOIN (SELECT deptid,descr FROM ps_sub_og_lgl_vw_a) AS og ON og.deptid = ee.deptid
                                LEFT JOIN (SELECT emplid,name,name_a FROM ps_sub_ee_lgl_vw_a) AS agentee ON agentee.emplid = fc.contact_agent
                                LEFT JOIN (SELECT emplid,name,name_a FROM ps_sub_ee_lgl_vw_a) AS opee ON opee.emplid = fc.operate_user
                                WHERE 1 = 1");
            if (fnid > 0)
                sqlBuilder.Append($@"AND fnid = @fnid");
            sqlBuilder.Append(" ORDER BY item ASC,ee.deptid ASC , stuff(fc.empid,1,patindex('%[A-z]%',substring(fc.empid,1,1))-1,'') asc, len(fc.empid) desc,fc.empid desc");
            return this.NpgsqlSearchBySingle<UndertakeWindow>(sqlBuilder.ToString(), new { fnid = fnid, logging_locale = logging_locale },
                "time_zone", timeZone);
        }

        /// <summary>
        /// 修改承辦人窗口數據
        /// </summary>
        /// <param name="undertake"></param>
        /// <returns></returns>
        public int UpdateUndertakeWindow(UndertakeWindow undertake)
        {
            string sql = @$"UPDATE fnp_contact SET item = @item, empid = @empid, contact_agent = @contact_agent, contact_notes = @contact_notes, is_show_home = @is_show_home, modify_user = @operate_euser, modify_time = getutcdate() WHERE fnid = @fnid;";
            return this.ExecuteCommand(sql, new { empid = undertake.empid, contact_agent = undertake.contact_agent, contact_notes = undertake.contact_notes, operate_euser = undertake.operate_euser, fnid = undertake.fnid, item = undertake.item, is_show_home = undertake.is_show_home });
        }

        /// <summary>
        /// 新增承辦人窗口數據
        /// </summary>
        /// <param name="undertake"></param>
        /// <returns></returns>
        public int InsertUndertakeWindow(UndertakeWindow undertake)
        {
            string sql = @$"INSERT INTO fnp_contact (item, empid, create_user, contact_agent, contact_notes, is_show_home)
                            VALUES(@item, @empid, @operate_cuser,  @contact_agent, @contact_notes, @is_show_home);";
            return this.ExecuteCommand(sql, new { empid = undertake.empid, contact_agent = undertake.contact_agent, contact_notes = undertake.contact_notes, operate_cuser = undertake.operate_cuser, item = undertake.item, is_show_home = undertake.is_show_home });
        }

        /// <summary>
        /// 重複性校驗承辦人窗口數據
        /// </summary>
        /// <param name="undertake"></param>
        /// <returns></returns>
        public bool DuplicateVerificationUndertakeWindow(UndertakeWindow undertake)
        {
            StringBuilder sb = new StringBuilder();
            sb.Append(@$"SELECT COUNT(empid) FROM fnp_contact WHERE item = @item AND empid = @empid");
            if (undertake.fnid > 0)
                sb.Append($" AND fnid != @fnid");
            return Convert.ToInt32(this.NpgsqlSearchBySingleValue(sb.ToString(), new { item = undertake.item, empid = undertake.empid, fnid = undertake.fnid })) == 0;
        }

        /// <summary>
        /// 刪除承辦人窗口數據
        /// </summary>
        /// <param name="fnid"></param>
        /// <returns></returns>
        public int DeleteUndertakeWindow(int fnid)
        {
            string sql = @$"DELETE FROM fnp_contact WHERE fnid = {fnid}";
            return this.ExecuteCommand(sql, null);
        }

        /// <summary>
        /// 查詢項目下拉數據
        /// </summary>
        /// <returns></returns>
        public List<DropDownListModel> QueryContact()
        {
            string sql = @$"SELECT func_code AS paraKey,fun_name AS paraValue FROM sys_parameters WHERE para_code = 'contactManage' AND lang_type = '{MvcContext.UserInfo.logging_locale}' ORDER BY sort_order ASC";
            return this.NpgsqlSearchByList<DropDownListModel>(sql, null);
        }
    }
}
