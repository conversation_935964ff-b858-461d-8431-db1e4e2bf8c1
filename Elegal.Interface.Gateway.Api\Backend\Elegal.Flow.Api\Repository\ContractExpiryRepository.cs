﻿using Elegal.Interface.Api.Common.Control.Filter;
using Elegal.Interface.Api.Common.FuncHelper;
using Elegal.Interface.Api.Common.Model.ResultModel.FlowApi;
using Elegal.Interface.Api.Common.Repository;
using Elegal.Orm;
using System.Text;

namespace Elegal.Flow.Api.Repository
{
    /// <summary>
    /// 合約效期提醒
    /// </summary>
    public class ContractExpiryRepository : BaseRepository
    {
        #region 申請單簽核關卡匯總
        /// <summary>
        /// 申請單簽核關卡匯總
        /// </summary>
        private static readonly List<string> signOffStep = AppSettingHelper.GetValue("signOffStep").Split(",").ToList();
        #endregion

        #region 合約效期提醒查詢
        /// <summary>
        /// 合約效期提醒查詢
        /// </summary>
        public IEnumerable<ContractExpiryModel> ListData(string applyNumber)
        {
            StringBuilder sqlBuilder = new();
            sqlBuilder.Append(@$"SELECT
                    --需要根據申請單號+操作人查詢是否有過點擊
                    (SELECT count(fec.apply_number) FROM dbo.fnp_exp_click AS fec WHERE fec.apply_number = fa.apply_number AND emplid = N'{MvcContext.UserInfo.current_emp}') AS readStatus,
                    fa.apply_number as applyNumber,
                    fa.apply_form,
                    fa.apply_type,
                    fa.form_type,
                    apply_time as applyTime,
                    fe.entity,
                    fa.entity_id as entityId,
                    fe.entity_namec as entityNamec,
                    fe.entity_namee as entityNamee,
                    fa.other_party,
                    fa.contract_name as contract_name,
                    fau.confirm_exp_date,
                    COALESCE(fau.confirm_exp_date,fau.exp_date) as finishedDate,
                    fa.delay_reminder as expReminderDays,
                    fa.pic_deptid as picDeptid,
                    fa.incumbent_emplid as incumbentEmplid,
                    fa.confiden_level as confiden_level,
                    iee.name as incCuser,
                    iee.name_a as incEuser,
                    fss.step_id as stepId,
                    (SELECT fun_name FROM (SELECT func_code,fun_name FROM dbo.sys_parameters AS sp WHERE lang_type = N'{MvcContext.UserInfo.logging_locale}'
                    AND EXISTS(SELECT 1 FROM (SELECT value AS para_code FROM STRING_SPLIT(@signOffStep, ',')) AS fpc WHERE fpc.para_code = sp.para_code)) AS sp WHERE sp.func_code = CONVERT(NVARCHAR,fss.step_id)) AS currentLevelsName,
                    fss.current_signer as currentSigner,
                    fss.is_invitee as isInvitee,
                    fa.application_state as applicationState,
                    (SELECT fun_name FROM dbo.sys_parameters WHERE lang_type = N'{MvcContext.UserInfo.logging_locale}' AND para_code = N'applicationState' AND func_code = CONVERT(NVARCHAR,fa.application_state)) AS applicationStateName
                    FROM (
                    SELECT apply_form,apply_type,form_type,apply_number,confiden_level,apply_time,entity_id,other_party,delay_reminder,COALESCE(incumbent_emplid,pic_emplid,fill_emplid) AS incumbent_emplid,pic_deptid,application_state,contract_name FROM dbo.v_getallapplication AS fa 
                    WHERE EXISTS(SELECT 1 FROM (SELECT value AS apply_number FROM STRING_SPLIT(@applyNumber, ',')) AS asn WHERE asn.apply_number = fa.apply_number)
                    ) AS fa
                    LEFT JOIN (SELECT ae.apply_number,ae.exp_date,oar.confirm_exp_date FROM (
                         --合約申請用戶填寫的到日期
                         SELECT apply_number,exp_date FROM dbo.form_application_undertake
                         UNION
                         --資料建檔用戶填寫的到日期
                         SELECT apply_number,exp_date FROM dbo.literature_application
                         UNION
                         --關企建檔用戶填寫的到期日
                         SELECT apply_number,exp_date FROM dbo.enterprise_application
                         ) AS ae
                         --排除正本歸檔狀態為已作廢的數據
                         LEFT JOIN (SELECT apply_number,confirm_exp_date FROM dbo.original_archive_record WHERE origin_archive_type <> N'05') AS oar ON oar.apply_number = ae.apply_number
                         ) AS fau ON fau.apply_number = fa.apply_number
                    LEFT JOIN ({new FnpEntityRepository().GetAllEntity()}
                    ) AS fe ON fe.entity_id = fa.entity_id
                    LEFT JOIN (SELECT emplid,name,name_a FROM dbo.ps_sub_ee_lgl_vw_a) AS iee ON iee.emplid = fa.incumbent_emplid
                    LEFT JOIN (SELECT DISTINCT apply_number,step_id,
                    (SELECT DISTINCT signerName COLLATE Chinese_Taiwan_Stroke_CI_AS FROM dbo.F_GetApplicationSignerName(apply_number) WHERE inviteSigner = is_invitee) AS current_signer,is_invitee FROM dbo.flow_step_signer) AS fss ON fss.apply_number = fa.apply_number
                    order by fa.apply_time desc,fa.apply_number desc");
            return DbAccess.Database.SqlQuery<ContractExpiryModel>(sqlBuilder.ToString(), new
            {
                applyNumber = applyNumber,
                logging_locale = MvcContext.UserInfo.logging_locale,
                emplid = MvcContext.UserInfo.current_emp,
                signOffStep = string.Join(',', signOffStep.Select(s => s))
            });
        }
        #endregion
    }
}
