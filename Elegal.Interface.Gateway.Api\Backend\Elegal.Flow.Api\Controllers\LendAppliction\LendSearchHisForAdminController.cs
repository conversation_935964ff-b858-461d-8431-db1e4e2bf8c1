﻿using Elegal.Flow.Api.Services.LendAppliction;
using Elegal.Interface.Api.Common.Control.Filter;
using Elegal.Interface.Api.Common.Control.Web;
using Elegal.Interface.Api.Common.FuncHelper;
using Elegal.Interface.Api.Common.Model.ParaModel.FlowApi.LendAppliction;
using Elegal.Interface.Api.Common.Model.ResultModel;
using Microsoft.AspNetCore.Mvc;
using System.Text;

namespace Elegal.Flow.Api.Controllers.LendAppliction;

/// <summary>
/// 资料查询，历史借出（管理员角色）【主表，子表，导出，检视】
/// </summary>
[Route("[controller]")]
[ApiController]
public class LendSearchHisForAdminController : BaseController
{
    /// <summary>
    /// 资料查询，借出查询
    /// </summary>
    /// <param name="para"></param>
    /// <returns></returns>
    [HttpPost]
    [Route("QueryLendHisForAdmin")]
    public async Task<ApiResultModelByObject> QueryLendHisForAdmin(LendHisForAdminPara para)
    {
        ApiResultModelByObject apiResult = new ApiResultModelByObject();
        var (data, count) = LendSearchHisForAdminService.QueryLendHisForAdmin(para, MvcContext.UserInfo);
        apiResult.listData = data;
        apiResult.totalCount = count;
        apiResult.rtnSuccess = true;
        #region 記錄日誌
        InitLogRecord(para, log =>
        {
            StringBuilder logstrb = new StringBuilder();
            if (!string.IsNullOrWhiteSpace(para.ContractNumber))
            {
                logstrb.AppendLine(string.Format(ActionFilter.GetMultilingualValue("LendSearch_contractNumber", true), para.ContractNumber));
            }
            if (!string.IsNullOrWhiteSpace(para.ContractName))
            {
                logstrb.AppendLine(string.Format(ActionFilter.GetMultilingualValue("LendSearch_contractName", true), para.ContractName));
            }
            if (para.LendFillEmplid.Count > 0)
            {
                string names = string.Join(",", para.LendFillEmplid.Select(e => e.Emplid));
                logstrb.AppendLine(string.Format(ActionFilter.GetMultilingualValue("LendSearch_lendFillEmplid", true), names));
            }
            if (para.LendHanderEmplid.Count > 0)
            {
                string names = string.Join(",", para.LendHanderEmplid.Select(e => e.Emplid));
                logstrb.AppendLine(string.Format(ActionFilter.GetMultilingualValue("LendSearch_lendHanderEmplid", true), names));
            }

            if (!string.IsNullOrWhiteSpace(para.LendNumber))
            {
                logstrb.AppendLine(string.Format(ActionFilter.GetMultilingualValue("LendSearch_lendNumber", true), para.LendNumber));
            }
            if (para.LendStatus.Count > 0)
            {
                logstrb.AppendLine(string.Format(ActionFilter.GetMultilingualValue("LendSearch_lendStatus", true), string.Join(",", para.LendStatus.Select(e => e.Value))));
            }
            if (para.PickupTime.Start.HasValue || para.PickupTime.End.HasValue)
            {
                string d1 = string.Empty;
                string d2 = string.Empty;
                if (para.PickupTime.Start.HasValue)
                {
                    d1 = TimeZoneInfoConvertHelper.ConvertDateByTimeZoneByLocal(para.PickupTime.Start.Value, MvcContext.UserInfo.time_zone).ToString("yyyy/MM/dd");
                }
                if (para.PickupTime.End.HasValue)
                {
                    d2 = TimeZoneInfoConvertHelper.ConvertDateByTimeZoneByLocal(para.PickupTime.End.Value, MvcContext.UserInfo.time_zone).ToString("yyyy/MM/dd");
                }

                logstrb.AppendLine(string.Format(ActionFilter.GetMultilingualValue("LendSearch_lendTakeTime", true), d1, d2));
            }


            if (para.ApplicationTime.Start.HasValue || para.ApplicationTime.End.HasValue)
            {
                string d1 = string.Empty;
                string d2 = string.Empty;
                if (para.ApplicationTime.Start.HasValue)
                {
                    d1 = TimeZoneInfoConvertHelper.ConvertDateByTimeZoneByLocal(para.ApplicationTime.Start.Value, MvcContext.UserInfo.time_zone).ToString("yyyy/MM/dd");
                }
                if (para.ApplicationTime.End.HasValue)
                {
                    d2 = TimeZoneInfoConvertHelper.ConvertDateByTimeZoneByLocal(para.ApplicationTime.End.Value, MvcContext.UserInfo.time_zone).ToString("yyyy/MM/dd");
                }

                logstrb.AppendLine(string.Format(ActionFilter.GetMultilingualValue("LendSearch_ApplicationTime", true), d1, d2));
            }


            if (para.ActualReturnTime.Start.HasValue || para.ActualReturnTime.End.HasValue)
            {
                string d1 = string.Empty;
                string d2 = string.Empty;
                if (para.ActualReturnTime.Start.HasValue)
                {
                    d1 = TimeZoneInfoConvertHelper.ConvertDateByTimeZoneByLocal(para.ActualReturnTime.Start.Value, MvcContext.UserInfo.time_zone).ToString("yyyy/MM/dd");
                }
                if (para.ActualReturnTime.End.HasValue)
                {
                    d2 = TimeZoneInfoConvertHelper.ConvertDateByTimeZoneByLocal(para.ActualReturnTime.End.Value, MvcContext.UserInfo.time_zone).ToString("yyyy/MM/dd");
                }

                logstrb.AppendLine(string.Format(ActionFilter.GetMultilingualValue("LendSearch_returnTime", true), d1, d2));
            }

            log.Detail = logstrb.ToString();
        });
        #endregion
        return await Task.FromResult<ApiResultModelByObject>(apiResult);
    }

    /// <summary>
    /// 资料查询，历史借出導出
    /// </summary>
    /// <param name="para"></param>
    /// <returns></returns>
    [HttpPost]
    [Route("QueryLendHisForAdminExprt")]
    public FileContentResult QueryLendHisForAdminExprt([FromBody] LendHisForAdminExportPara para)
    {
        ApiResultModelByObject apiResult = new ApiResultModelByObject();
        var excelBytes = LendSearchHisForAdminService.QueryLendHisForAdminExprt(para, MvcContext.UserInfo);
        #region 記錄日誌
        InitLogRecord(para, log =>
        {
            StringBuilder logstrb = new StringBuilder();
            if (!string.IsNullOrWhiteSpace(para.ContractNumber))
            {
                logstrb.AppendLine(string.Format(ActionFilter.GetMultilingualValue("LendSearch_contractNumber", true), para.ContractNumber));
            }
            if (!string.IsNullOrWhiteSpace(para.ContractName))
            {
                logstrb.AppendLine(string.Format(ActionFilter.GetMultilingualValue("LendSearch_contractName", true), para.ContractName));
            }
            if (para.LendFillEmplid.Count > 0)
            {
                string names = string.Join(",", para.LendFillEmplid.Select(e => e.Emplid));
                logstrb.AppendLine(string.Format(ActionFilter.GetMultilingualValue("LendSearch_lendFillEmplid", true), names));
            }
            if (para.LendHanderEmplid.Count > 0)
            {
                string names = string.Join(",", para.LendHanderEmplid.Select(e => e.Emplid));
                logstrb.AppendLine(string.Format(ActionFilter.GetMultilingualValue("LendSearch_lendHanderEmplid", true), names));
            }

            if (!string.IsNullOrWhiteSpace(para.LendNumber))
            {
                logstrb.AppendLine(string.Format(ActionFilter.GetMultilingualValue("LendSearch_lendNumber", true), para.LendNumber));
            }
            if (para.LendStatus.Count > 0)
            {
                logstrb.AppendLine(string.Format(ActionFilter.GetMultilingualValue("LendSearch_lendStatus", true), string.Join(",", para.LendStatus.Select(e => e.Value))));
            }
            if (para.PickupTime.Start.HasValue || para.PickupTime.End.HasValue)
            {
                string d1 = string.Empty;
                string d2 = string.Empty;
                if (para.PickupTime.Start.HasValue)
                {
                    d1 = TimeZoneInfoConvertHelper.ConvertDateByTimeZoneByLocal(para.PickupTime.Start.Value, MvcContext.UserInfo.time_zone).ToString("yyyy/MM/dd");
                }
                if (para.PickupTime.End.HasValue)
                {
                    d2 = TimeZoneInfoConvertHelper.ConvertDateByTimeZoneByLocal(para.PickupTime.End.Value, MvcContext.UserInfo.time_zone).ToString("yyyy/MM/dd");
                }

                logstrb.AppendLine(string.Format(ActionFilter.GetMultilingualValue("LendSearch_lendTakeTime", true), d1, d2));
            }


            if (para.ApplicationTime.Start.HasValue || para.ApplicationTime.End.HasValue)
            {
                string d1 = string.Empty;
                string d2 = string.Empty;
                if (para.ApplicationTime.Start.HasValue)
                {
                    d1 = TimeZoneInfoConvertHelper.ConvertDateByTimeZoneByLocal(para.ApplicationTime.Start.Value, MvcContext.UserInfo.time_zone).ToString("yyyy/MM/dd");
                }
                if (para.ApplicationTime.End.HasValue)
                {
                    d2 = TimeZoneInfoConvertHelper.ConvertDateByTimeZoneByLocal(para.ApplicationTime.End.Value, MvcContext.UserInfo.time_zone).ToString("yyyy/MM/dd");
                }

                logstrb.AppendLine(string.Format(ActionFilter.GetMultilingualValue("LendSearch_ApplicationTime", true), d1, d2));
            }


            if (para.ActualReturnTime.Start.HasValue || para.ActualReturnTime.End.HasValue)
            {
                string d1 = string.Empty;
                string d2 = string.Empty;
                if (para.ActualReturnTime.Start.HasValue)
                {
                    d1 = TimeZoneInfoConvertHelper.ConvertDateByTimeZoneByLocal(para.ActualReturnTime.Start.Value, MvcContext.UserInfo.time_zone).ToString("yyyy/MM/dd");
                }
                if (para.ActualReturnTime.End.HasValue)
                {
                    d2 = TimeZoneInfoConvertHelper.ConvertDateByTimeZoneByLocal(para.ActualReturnTime.End.Value, MvcContext.UserInfo.time_zone).ToString("yyyy/MM/dd");
                }

                logstrb.AppendLine(string.Format(ActionFilter.GetMultilingualValue("LendSearch_returnTime", true), d1, d2));
            }

            log.Detail = logstrb.ToString();
        });
        #endregion
        return File(excelBytes, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "借出查詢_历史借出清單" + DateTime.UtcNow.ConvertDateByTimeZoneByUtc(MvcContext.UserInfo.time_zone).ToString("yyyyMMdd") + ".xlsx");
    }
}
