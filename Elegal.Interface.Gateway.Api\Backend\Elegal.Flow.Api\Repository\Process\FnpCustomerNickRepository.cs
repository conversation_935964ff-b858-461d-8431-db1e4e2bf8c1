﻿using Elegal.Interface.Api.Common.Repository;
using Elegal.Orm;
using System.Text;

namespace Elegal.Flow.Api.Repository.Process
{
    /// <summary>
    /// 客戶暱稱管理
    /// </summary>
    public class FnpCustomerNickRepository
    {
        #region 新增客戶暱稱
        /// <summary>
        /// 新增客戶暱稱
        /// </summary>
        /// <param name="nikeName">暱稱集合</param>
        /// <param name="emplid">操作者工號</param>
        /// <returns></returns>
        public bool InsertNikeName(List<string> nikeName, string emplid)
        {
            StringBuilder sb = new StringBuilder();
            sb.AppendLine(@"DELETE FROM fnp_customer_nick;");
            foreach (string nike in nikeName)
            {
                sb.AppendFormat(@"INSERT INTO fnp_customer_nick (id, hypocorism_name, create_user) VALUES(NEWID(), N'{0}', N'{1}');", nike, emplid).AppendLine();
            }
            sb.AppendLine(@"SELECT @@ROWCOUNT;");
            return DbAccess.Database.SqlExecute(sb.ToString()) > 0;
        } 
        #endregion
    }
}
