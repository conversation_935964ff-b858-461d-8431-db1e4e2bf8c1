﻿using Elegal.Flow.Api.Services.Individual;
using Elegal.Interface.Api.Common.Control.Filter;
using Elegal.Interface.Api.Common.Control.Web;
using Elegal.Interface.Api.Common.FuncHelper;
using Elegal.Interface.Api.Common.FuncService;
using Elegal.Interface.Api.Common.Model.Enum;
using Elegal.Interface.Api.Common.Model.ParaModel.FlowApi;
using Elegal.Interface.Api.Common.Model.ResultModel;
using Elegal.Interface.ApiData.Service.FuncService;
using Elegal.Interface.ApiData.Service.Model.DbModel;
using Elegal.Interface.ApiData.Service.Model.ViewModel;
using Microsoft.AspNetCore.Mvc;
using System.Text;

namespace Elegal.Flow.Api.Controllers.Individual
{
    /// <summary>
    /// 代理人設定
    /// </summary>
    [Route("[controller]")]
    [ApiController]
    public class SysAgentController : BaseController
    {
        private string time_zone = AppSettingHelper.GetValue("TimeZone");

        #region 新增代理人
        /// <summary>
        /// 新增代理人
        /// </summary>
        /// <param name="model">数据模型</param>
        /// <returns></returns>
        [HttpPut]
        [Route("Add")]
        public async Task<ApiResultModel> Add([FromBody] SysAgentAddModel model)
        {
            #region 校驗項
            #region 校驗代理部門必填
            if (model.Departments.Count <= 0)
            {
                return new()
                {
                    messageTitle = ActionFilter.GetMultilingualValue("custom:messageTitle:createFail"),
                    messageContent = new List<string> { ActionFilter.GetMultilingualValue("custom:messageContent:validateError") },
                    messageType = MessageTypeUtils.Warning.ToString()
                };
            }
            #endregion
            #region 校驗起始时间不能大于结束时间
            if (model.StartTime > model.EndTime)
            {
                return new()
                {
                    messageTitle = ActionFilter.GetMultilingualValue("custom:messageTitle:createFail"),
                    messageContent = new List<string> { ActionFilter.GetMultilingualValue("custom:messageContent:StartTimeLargerThanEndTime") },
                    messageType = MessageTypeUtils.Warning.ToString()
                };
            }
            #endregion
            #endregion


            return await Task.FromResult(SysAgentService.Add(model));
        }
        #endregion

        #region 刪除代理人
        /// <summary>
        /// 刪除代理人
        /// </summary>
        /// <param name="key">主键标识</param>
        /// <returns></returns>
        [HttpDelete]
        [Route("Delete")]
        public async Task<ApiResultModel> Delete(int key)
        {
            #region 校驗數據是否已被刪除
            if (SysAgentDataService.FindByKey(key) == null)
            {
                return new()
                {
                    messageTitle = ActionFilter.GetMultilingualValue("custom:messageTitle:dataNotexist"),
                    messageContent = new List<string> { ActionFilter.GetMultilingualValue("custom:messageContent:dataChurn") }
                };
            }
            #endregion
            return await Task.FromResult(SysAgentService.DeleteByKey(key));
        }
        #endregion

        /// <summary>
        /// 获取可代理管理部门
        /// </summary>
        /// <param name="empid">工号</param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetManagerDept")]
        public async Task<ApiResultModelByObject> GetManagerDept(string empid)
        {
            UserInfoService.UpdateUser(MvcContext.UserInfo, empid);
            return await Task.FromResult(new ApiResultModelByObject()
            {
                listData = SysAgentService.GetManagerDept(MvcContext.UserInfo),
                rtnSuccess = true
            });
        }

        /// <summary>
        /// 获取我的代理人设定列表数据
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("GetAgentList")]
        public async Task<ApiResultModelByObject> GetAgentList()
        {
            return await Task.FromResult(new ApiResultModelByObject()
            {
                listData = SysAgentService.GetAgentList(),
                rtnSuccess = true
            });
        }

        /// <summary>
        /// 获取担任他人代理列表数据
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("GetBeAgentList")]
        public async Task<ApiResultModelByObject> GetBeAgentList()
        {
            return await Task.FromResult(new ApiResultModelByObject()
            {
                listData = SysAgentService.GetBeAgentList(),
                rtnSuccess = true
            });
        }


        /// <summary>
        /// 依照工號獲取擔任他人代理列表数据
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("GetBeAgentListByEmplid")]
        public async Task<ApiResultModelByObject> GetBeAgentListByEmplid([FromQuery] string emplid)
        {
            return await Task.FromResult(new ApiResultModelByObject()
            {
                listData = SysAgentService.GetBeAgentListByEmplid(emplid),
                rtnSuccess = true
            });
        }
    }
}
