﻿using Elegal.Flow.Api.Services;
using Elegal.Interface.Api.Common.Control.Filter;
using Elegal.Interface.Api.Common.Control.Web;
using Elegal.Interface.Api.Common.FuncService;
using Elegal.Interface.Api.Common.Model;
using Elegal.Interface.Api.Common.Model.ParaModel.FlowApi;
using Elegal.Interface.Api.Common.Model.ResultModel;
using Elegal.Interface.Api.Common.Model.ResultModel.FlowApi;
using Elegal.Interface.Api.Common.Model.ViewModel;
using Elegal.Interface.Api.Common.Model.ViewModel.FlowApi;
using Elegal.Orm.Utils;
using Microsoft.AspNetCore.Mvc;
using System.Text;

namespace Elegal.Flow.Api.Controllers
{
    /// <summary>
    /// 主體管理服務
    /// </summary>
    [Route("[controller]")]
    [ApiController]
    public class EntityManageController : BaseController
    {

        #region 停用原因 entityStopReason
        /// <summary>
        /// 獲取停用原因數據
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("GetEntityStopReason")]
        public async Task<ApiResultModelByObject> GetEntityStopReason()
        {
            ApiResultModelByObject apiResult = new ApiResultModelByObject();
            string locale = MvcContext.UserInfo != null && MvcContext.UserInfo.logging_locale != null ? MvcContext.UserInfo.logging_locale : "ZH-TW";
            apiResult.listData = SysParametersService.GetDropDownList("entityStopReason", "", locale, 1);
            apiResult.rtnSuccess = true;
            return await Task.FromResult<ApiResultModelByObject>(apiResult);
        }
        #endregion

        #region 流程Step entityStopReason
        /// <summary>
        /// 獲取流程Step數據
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("GetEntityManageStep")]
        public async Task<ApiResultModelByObject> GetEntityManageStep()
        {
            ApiResultModelByObject apiResult = new ApiResultModelByObject();
            string locale = MvcContext.UserInfo != null && MvcContext.UserInfo.logging_locale != null ? MvcContext.UserInfo.logging_locale : "ZH-TW";
            apiResult.listData = SysParametersService.GetDropDownList("entityManageStep", "", locale, 1);
            apiResult.rtnSuccess = true;

            return await Task.FromResult<ApiResultModelByObject>(apiResult);
        }
        #endregion

        #region 獲取主體公司簡稱
        /// <summary>
        /// 獲取主體公司簡稱
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("GetPrincipalCompanyAbbreviation")]
        public async Task<ApiResultModelByObject> GetPrincipalCompanyAbbreviation()
        {
            ApiResultModelByObject apiResult = new ApiResultModelByObject();
            List<DropDownListModel> list = EntityManageService.GetPrincipalCompanyAbbreviation();
            apiResult.listData = list;
            apiResult.rtnSuccess = true;

            return await Task.FromResult<ApiResultModelByObject>(apiResult);
        }
        #endregion

        #region 獲取區域
        /// <summary>
        /// 獲取區域
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("GetArea")]
        public async Task<ApiResultModelByObject> GetArea()
        {
            ApiResultModelByObject apiResult = new ApiResultModelByObject();
            List<DropDownListModel> list = EntityManageService.GetArea();
            apiResult.listData = list;
            apiResult.rtnSuccess = true;

            return await Task.FromResult<ApiResultModelByObject>(apiResult);
        }
        #endregion

        #region 條件查詢
        /// <summary>
        /// 獲取區域
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [Route("QueryEntityManage")]
        public async Task<ApiResultModelByObject> QueryEntityManage([FromBody] EntityManageModel entityManageModel)
        {
            ApiResultModelByObject apiResult = new ApiResultModelByObject();
            UserInfoModel user = MvcContext.UserInfo;
            List<EntityManage> list = EntityManageService.QueryEntityManage(entityManageModel, user.logging_locale, user.time_zone);
            if (entityManageModel.entitys != null)
            {
                string entity = BulletinsService.GetEntityByEntityID(entityManageModel.entitys);
                InitLogRecord(entityManageModel, log =>
                {
                    StringBuilder stringBuilder = new();
                    if (!string.IsNullOrEmpty(entityManageModel.entitys))
                        stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("EntityManager_log:Entity", true)}：{entity}");
                    InitQueryLog(entityManageModel, stringBuilder);
                    log.Detail = stringBuilder.ToString();

                });
            }
            apiResult.listData = list;
            apiResult.rtnSuccess = true;

            return await Task.FromResult<ApiResultModelByObject>(apiResult);
        }

        /// <summary>
        /// 初始化查詢日誌
        /// </summary>
        /// <returns></returns>
        private static void InitQueryLog(EntityManageModel entityManageModel, StringBuilder stringBuilder)
        {
            if (entityManageModel.stoptype != null)
            {
                string stoptype = "";
                switch (entityManageModel.stoptype)
                {
                    case 0:
                        stoptype = ActionFilter.GetMultilingualValue("EntityManager_log:stoptypeCode0", true);
                        break;
                    case 1:
                        stoptype = ActionFilter.GetMultilingualValue("EntityManager_log:stoptypeCode1", true);
                        break;
                    case 2:
                        stoptype = ActionFilter.GetMultilingualValue("EntityManager_log:stoptypeCode2", true);
                        break;
                    case 3:
                        stoptype = ActionFilter.GetMultilingualValue("EntityManager_log:stoptypeCode3", true);
                        break;
                }
                stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("EntityManager_log:stoptype", true)}：{stoptype}");
            }
            if (null != entityManageModel.deliverydate)
                stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("EntityManager_log:Deliverydate", true)}：{GetLocalTimeToString(entityManageModel.deliverydate)}");
            if (!string.IsNullOrEmpty(entityManageModel.areaids))
            {
                string area = EntityManageService.GetAreaByAreaId(entityManageModel.areaids);
                stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("EntityManager_log:AreaId", true)}：{area}");
            }
            if (entityManageModel.status >= 0)
            {
                string status = "";
                switch (entityManageModel.status)
                {
                    case 0:
                        status = ActionFilter.GetMultilingualValue("EntityManager_log:enable", true);
                        break;
                    case 1:
                        status = ActionFilter.GetMultilingualValue("EntityManager_log:disable", true);
                        break;
                }
                stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("EntityManager_log:Status", true)}：{status}");
            }
            if (!string.IsNullOrEmpty(entityManageModel.company))
                stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("EntityManager_log:Company", true)}：{entityManageModel.company}");
        }
        #endregion

        #region 查詢流程當前關卡
        /// <summary>
        /// 查詢流程當前關卡
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [Route("QueryEntityCurrentStep")]
        public async Task<ApiResultModelByObject> QueryEntityCurrentStep(int entity_id)
        {
            ApiResultModelByObject apiResult = new ApiResultModelByObject();
            apiResult.listData = EntityManageService.QueryEntityCurrentStep(entity_id);
            apiResult.rtnSuccess = true;

            return await Task.FromResult<ApiResultModelByObject>(apiResult);
        }
        #endregion

        #region 查詢授權公司代碼
        /// <summary>
        /// 查詢授權公司代碼
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("QueryAuthCompany")]
        public async Task<ApiResultModelByObject> QueryAuthCompany()
        {
            ApiResultModelByObject apiResult = new ApiResultModelByObject();
            apiResult.listData = EntityManageService.QueryAuthCompany();
            apiResult.rtnSuccess = true;
            return await Task.FromResult<ApiResultModelByObject>(apiResult);
        }
        #endregion

        #region 建立主體
        /// <summary>
        /// 建立主體
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [Route("InsertEntityInfo")]
        public async Task<ApiResultModelByObject> InsertEntityInfo([FromBody] ModifyEntityInfo createEntityInfo)
        {
            ApiResultModelByObject apiResult = new ApiResultModelByObject();
            UserInfoModel user = MvcContext.UserInfo;
            createEntityInfo.CreateUser = user.current_emp;
            createEntityInfo.ModifyUser = user.current_emp;

            //驗證主體是否重複
            if (!EntityManageService.VerifyEntityRepeat(createEntityInfo)) {
                apiResult.messageTitle = ActionFilter.GetMultilingualValue("custom:messageTitle:dataExists");
                apiResult.messageContent = new List<string> { string.Format(ActionFilter.GetMultilingualValue("custom:messageContent:repeatMessage")), $"{createEntityInfo.Entity}{ActionFilter.GetMultilingualValue("custom:messageContent:EntityExits")}" };
                return await Task.FromResult<ApiResultModelByObject>(apiResult);
            }

            //驗證區域是否存在
            if (!EntityManageService.VerifyArea(createEntityInfo.AreaId)) {
                apiResult.messageTitle = ActionFilter.GetMultilingualValue("custom:messageTitle:dataNotexist");
                apiResult.messageContent = new List<string> { string.Format(ActionFilter.GetMultilingualValue("custom:messageContent:areaDataNotExists")) };
                return await Task.FromResult<ApiResultModelByObject>(apiResult);
            }

            //驗證主體和修改前主體簡稱不同
            if (createEntityInfo.BeforeRenameEntity.Equals(createEntityInfo.Entity)) {
                apiResult.messageTitle = ActionFilter.GetMultilingualValue("custom:messageTitle:createFail");
                apiResult.messageContent = new List<string> { string.Format(ActionFilter.GetMultilingualValue("custom:messageContent:BeforeEntityConsistency")) };
                return await Task.FromResult<ApiResultModelByObject>(apiResult);
            }

            //校验主体实际公司代码
            if (createEntityInfo.actual_company == null || EntityManageService.VerifyActualCompany(createEntityInfo.actual_company, createEntityInfo.EntityId))
            {
                int entity_id = EntityManageService.InsertEntityInfo(createEntityInfo);
                apiResult.listData = entity_id;
                initInsertLogNext(createEntityInfo, apiResult, entity_id);
            }
            else
            {
                apiResult.messageTitle = ActionFilter.GetMultilingualValue("custom:messageTitle:updateFail");
                apiResult.messageContent = new List<string> { string.Format(ActionFilter.GetMultilingualValue("custom:messageContent:actualCompanyHavaBeenBound"), EntityManageService.QueryActualCompany(createEntityInfo.actual_company)) };
            }

            return await Task.FromResult<ApiResultModelByObject>(apiResult);
        }

        /// <summary>
        /// 初始化插入主體日誌2
        /// </summary>
        /// <returns></returns>
        private void initInsertLogNext(ModifyEntityInfo createEntityInfo, ApiResultModelByObject apiResult, int entity_id)
        {
            if (entity_id > 0)
            {
                StringBuilder stringBuilder = new();
                if (!string.IsNullOrEmpty(createEntityInfo.Entity))
                    stringBuilder.AppendLine($"{AttributeUtil.GetParameterDescribe<UndertakeWindow>("contact_cuser")}：{createEntityInfo.Entity}");

                InitLogRecord(createEntityInfo, log =>
                {
                    StringBuilder stringBuilder = new();
                    InitInsertLog(createEntityInfo, stringBuilder);
                    if(!string.IsNullOrEmpty(createEntityInfo.BeforeRenameEntity))
                    stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("EntityManager_log:BeforeRenameEntity", true)}：{createEntityInfo.BeforeRenameEntity}");
                    string is_send_mail = createEntityInfo.is_send_mail == 0 ? "N" : "Y";
                    stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("EntityManager_log:isSendMail", true)}：{is_send_mail}");
                    string is_search_data = createEntityInfo.is_search_data == 0 ? "N" : "Y";
                    stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("EntityManager_log:isSearchData", true)}：{is_search_data}");
                    if (!string.IsNullOrEmpty(createEntityInfo.company))
                        stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("EntityManager_log:Company", true)}：{createEntityInfo.company}");
                    log.Detail = stringBuilder.ToString();
                });
                apiResult.rtnSuccess = true;
            }
        }

        /// <summary>
        /// 初始化插入主體日誌
        /// </summary>
        /// <returns></returns>
        private static void InitInsertLog(ModifyEntityInfo createEntityInfo, StringBuilder stringBuilder)
        {
            if (!string.IsNullOrEmpty(createEntityInfo.Entity))
                stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("EntityManager_log:Entity", true)}：{createEntityInfo.Entity}");
            if (!string.IsNullOrEmpty(createEntityInfo.EntityNamec))
                stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("EntityManager_log:EntityNamec", true)}：{createEntityInfo.EntityNamec}");
            if (!string.IsNullOrEmpty(createEntityInfo.EntityNamee))
                stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("EntityManager_log:EntityNamee", true)}：{createEntityInfo.EntityNamee}");
            string IsSignLegalperson = createEntityInfo.IsSignLegalperson == 0 ? "N" : "Y";
            stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("EntityManager_log:IsSignLegalperson", true)}：{IsSignLegalperson}");
            if (!string.IsNullOrEmpty(createEntityInfo.actual_company))
                stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("EntityManager_log:ActualCompany", true)}：{createEntityInfo.actual_company}");
            if (createEntityInfo.AreaId != null)
            {
                int areaId = (int)createEntityInfo.AreaId;
                string area = EntityManageService.GetAreaByAreaId(areaId.ToString());
                stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("EntityManager_log:AreaId", true)}：{area}");
            }
            stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("EntityManager_log:Status", true)}：{ActionFilter.GetMultilingualValue("EntityManager_log:enable", true)}");
            if (!string.IsNullOrEmpty(createEntityInfo.Comment))
                stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("EntityManager_log:Comment", true)}：{createEntityInfo.Comment}");
        }
        #endregion

        #region 根據主體id查詢對應信息
        /// <summary>
        /// 根據主體id查詢對應信息
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [Route("QueryEntityInfoById")]
        public async Task<ApiResultModelByObject> QueryEntityInfoById(int entity_id)
        {
            ApiResultModelByObject apiResult = new ApiResultModelByObject();
            UserInfoModel user = MvcContext.UserInfo;
            EntityManage entityManage = EntityManageService.QueryEntityInfoById(entity_id, user.logging_locale, user.time_zone);
            InitLogRecord(entity_id, log =>
            {
                StringBuilder stringBuilder = new();
                if (!string.IsNullOrEmpty(entityManage.entity))
                    stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("EntityManager_log:Entity", true)}：{entityManage.entity}");
                log.Detail = stringBuilder.ToString();
            });
            apiResult.listData = entityManage;
            apiResult.rtnSuccess = true;
            return await Task.FromResult<ApiResultModelByObject>(apiResult);
        }
        #endregion

        #region 變更主體數據
        /// <summary>
        /// 變更主體數據
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [Route("UpdateEntityInfo")]
        public async Task<ApiResultModelByObject> UpdateEntityInfo([FromBody] ModifyEntityInfo modifyEntityInfo)
        {
            ApiResultModelByObject apiResult = new ApiResultModelByObject();
            UserInfoModel user = MvcContext.UserInfo;
            modifyEntityInfo.CreateUser = user.current_emp;
            modifyEntityInfo.ModifyUser = user.current_emp;
            //啟用主體的時候清空主體的停用原因
            if (modifyEntityInfo.Status.Equals(0))
            {
                modifyEntityInfo.Stoptype = null;
                modifyEntityInfo.Deliverydate = null;
            }
            //停用主體的時候清空主體的停用原因
            if (!EntityManageService.VerifyArea(modifyEntityInfo.AreaId))
            {
                apiResult.messageTitle = ActionFilter.GetMultilingualValue("custom:messageTitle:dataNotexist");
                apiResult.messageContent = new List<string> { string.Format(ActionFilter.GetMultilingualValue("custom:messageContent:areaDataNotExists")) };
            }
            //驗證主體和修改前主體簡稱不同
            if (modifyEntityInfo.BeforeRenameEntity.Equals(modifyEntityInfo.Entity))
            {
                apiResult.messageTitle = ActionFilter.GetMultilingualValue("custom:messageTitle:createFail");
                apiResult.messageContent = new List<string> { string.Format(ActionFilter.GetMultilingualValue("custom:messageContent:BeforeEntityConsistency")) };
                return await Task.FromResult<ApiResultModelByObject>(apiResult);
            }
            int entity_id = Convert.ToInt32(modifyEntityInfo.EntityId);
            EntityManage entityManage = EntityManageService.QueryEntityInfoById(entity_id, user.logging_locale, user.time_zone);

            //主體發生修改場景判斷檢查合約管理員是否至少一人
            if (entityManage.status == 1 && modifyEntityInfo.Status == 0 && !EntityManageService.CheckContractManager(entity_id))
            {
                apiResult.messageTitle = ActionFilter.GetMultilingualValue("custom:messageTitle:enableFail");
                apiResult.messageContent = new List<string> { string.Format(ActionFilter.GetMultilingualValue("custom:messageContent:contractManagerMessage")) };
                return await Task.FromResult<ApiResultModelByObject>(apiResult);
            }
            apiResult.listData = EntityManageService.UpdateEntityInfo(modifyEntityInfo, entityManage.status);
            EntityManage entityManageNow = EntityManageService.QueryEntityInfoById(entity_id, user.logging_locale, user.time_zone);
            InitLogRecord(modifyEntityInfo, log =>
            {
                StringBuilder stringBuilder = new();
                InitUpdateLog(entityManageNow, stringBuilder);
                if (entityManage.status == 0 && modifyEntityInfo.Status != 0)
                {
                    //查詢主體停用時，實際公司代碼關聯的其他主體
                    string entityName = EntityManageService.QueryOtherEntityRelationCompany(modifyEntityInfo.EntityId, modifyEntityInfo.actual_company);
                    if (!string.IsNullOrEmpty(entityName)) {
                        List<string> strings = entityName.Split(',').ToList();
                        stringBuilder.AppendLine("-------------------------");
                        stringBuilder.AppendLine(string.Format(ActionFilter.GetMultilingualValue("custom:messageContent:entityIsUsedOtherEntity"), modifyEntityInfo.actual_company, strings.Count));
                        stringBuilder.AppendLine(entityName);
                    }
                }
                log.Detail = stringBuilder.ToString();

                StringBuilder stringBuilderFormer = new();
                InitUpdateLog(entityManage, stringBuilderFormer);
                log.DetailFormer = stringBuilderFormer.ToString();
            });
            apiResult.rtnSuccess = true;
            return await Task.FromResult<ApiResultModelByObject>(apiResult);
        }

        /// <summary>
        /// 初始化修改主體信息日誌
        /// </summary>
        /// <returns></returns>
        private static void InitUpdateLog(EntityManage entityManage, StringBuilder stringBuilder)
        {
            if (!string.IsNullOrEmpty(entityManage.entity_namec))
                stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("EntityManager_log:EntityNamec", true)}：{entityManage.entity_namec}");
            if (!string.IsNullOrEmpty(entityManage.entity_namee))
                stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("EntityManager_log:EntityNamee", true)}：{entityManage.entity_namee}");
            if (!string.IsNullOrEmpty(entityManage.BeforeRenameEntity))
                stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("EntityManager_log:BeforeRenameEntity", true)}：{entityManage.BeforeRenameEntity}");
            if (entityManage.deliverydate != null)
                stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("EntityManager_log:Deliverydate", true)}：{GetLocalTimeToString(entityManage.deliverydate)}");
            if (entityManage.status >= 0)
            {
                string status = "";
                switch (entityManage.status)
                {
                    case 0:
                        status = ActionFilter.GetMultilingualValue("EntityManager_log:enable", true);
                        break;
                    case 1:
                        status = ActionFilter.GetMultilingualValue("EntityManager_log:disable", true);
                        break;
                }
                stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("EntityManager_log:Status", true)}：{status}");
            }
            if (entityManage.area_id >= 0)
                stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("EntityManager_log:AreaId", true)}：{entityManage.area_name}");
            if (!string.IsNullOrEmpty(entityManage.company))
                stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("EntityManager_log:Company", true)}：{entityManage.company}");
            if (!string.IsNullOrEmpty(entityManage.actual_company))
                stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("EntityManager_log:ActualCompany", true)}：{entityManage.actual_company}");
            string IsSignLegalperson = entityManage.IsSignLegalperson == 0 ? "N" : "Y";
            stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("EntityManager_log:IsSignLegalperson", true)}：{IsSignLegalperson}");
            string is_send_mail = entityManage.is_send_mail == 0 ? "N" : "Y";
            stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("EntityManager_log:isSendMail", true)}：{is_send_mail}");
            string is_search_data = entityManage.is_search_data == 0 ? "N" : "Y";
            stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("EntityManager_log:isSearchData", true)}：{is_search_data}");
            if (!string.IsNullOrEmpty(entityManage.Comment))
                stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("EntityManager_log:Comment", true)}：{entityManage.Comment}");
        }
        #endregion

        #region 查詢實際公司代碼關聯的其他主體
        /// <summary>
        /// 查詢實際公司代碼關聯的其他主體
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [Route("QueryOtherEntityRelationCompany")]
        public async Task<ApiResultModelByObject> QueryOtherEntityRelationCompany([FromBody] ModifyEntityInfo modifyEntityInfo)
        {
            ApiResultModelByObject apiResult = new ApiResultModelByObject();
            List<string> list = EntityManageService.QueryOtherEntityRelation(apiResult,modifyEntityInfo.EntityId);
            apiResult.messageContent = list;
            apiResult.rtnSuccess = true;
            apiResult.messageType = "warning";
            return await Task.FromResult<ApiResultModelByObject>(apiResult);
        }
        #endregion

        #region 變更主體流程進度
        /// <summary>
        /// 變更主體流程進度
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [Route("ModifyPushStep")]
        public async Task<ApiResultModelByObject> ModifyPushStep([FromBody] ModifyEntityInfo modifyEntityInfo)
        {
            ApiResultModelByObject apiResult = new ApiResultModelByObject();
            UserInfoModel user = MvcContext.UserInfo;
            modifyEntityInfo.CreateUser = user.current_emp;
            modifyEntityInfo.ModifyUser = user.current_emp;
            apiResult.listData = EntityManageService.ModifyPushStep(modifyEntityInfo);
            string entity = BulletinsService.GetEntityByEntityID(modifyEntityInfo.EntityId);
            string entity_step = EntityManageService.GetStep(modifyEntityInfo.entity_stepid, user.logging_locale);
            string entity_next_step = EntityManageService.GetStep(modifyEntityInfo.entity_next_stepid, user.logging_locale);
            InitLogRecord(modifyEntityInfo, log =>
            {
                StringBuilder stringBuilder = new();
                if (!string.IsNullOrEmpty(modifyEntityInfo.EntityId))
                    stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("EntityManager_log:Entity", true)}：{entity}");
                if (modifyEntityInfo.entity_stepid != null)
                    stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("EntityManager_log:Entity_stepid", true)}：{entity_step}");
                if (modifyEntityInfo.entity_next_stepid != null)
                    stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("EntityManager_log:Entity_next_step", true)}：{entity_next_step}");
                if (modifyEntityInfo.ModifyUser != null)
                    stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("EntityManager_log:ModifyUser", true)}：{modifyEntityInfo.ModifyUser}");
                log.Detail = stringBuilder.ToString();
            });
            apiResult.rtnSuccess = true;
            return await Task.FromResult<ApiResultModelByObject>(apiResult);
        }
        #endregion

        #region 根據授權公司代碼顯示數據
        /// <summary>
        /// 根據授權公司代碼顯示數據
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [Route("QueryAuthCompanyByEntity")]
        public async Task<ApiResultModelByObject> QueryAuthCompanyByEntity(int entity_id)
        {
            ApiResultModelByObject apiResult = new ApiResultModelByObject();
            UserInfoModel user = MvcContext.UserInfo;
            apiResult.listData = EntityManageService.QueryAuthCompanyByEntity(entity_id, user.time_zone);
            string entity = BulletinsService.GetEntityByEntityID(entity_id.ToString());
            InitLogRecord(entity_id, log =>
            {
                log.Detail = $"{ActionFilter.GetMultilingualValue("EntityManager_log:Entity", true)}：{entity}";
            });
            apiResult.rtnSuccess = true;
            return await Task.FromResult<ApiResultModelByObject>(apiResult);
        }
        #endregion

        #region 更新授權公司數據
        /// <summary>
        /// 更新授權公司數據
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [Route("ModifyAuthCompanyAndEntity")]
        public async Task<ApiResultModelByObject> ModifyAuthCompanyAndEntity([FromBody] List<ModifyEntityInfo> modifyEntityInfos)
        {
            ApiResultModelByObject apiResult = new ApiResultModelByObject();
            UserInfoModel user = MvcContext.UserInfo;
            List<ModifyEntityInfo> RevisedModel = EntityManageService.QueryRevisedAuthCompanyAndEntity(modifyEntityInfos);
            string company = string.Join(",", RevisedModel.Select(x => x.company));
            string EntityId = "";
            if (modifyEntityInfos != null)
            {
                ModifyEntityInfo? modifyEntityInfo = modifyEntityInfos.FirstOrDefault();
                if (modifyEntityInfo != null)
                {
                    EntityId = modifyEntityInfo.EntityId;
                }
            }
            List<ModifyEntityInfo> FormerModel = EntityManageService.QueryFormerAuthCompanyAndEntity(EntityId, company);
            apiResult.listData = EntityManageService.ModifyAuthCompanyAndEntity(modifyEntityInfos, user.current_emp);
            apiResult.rtnSuccess = true;
            InitLogRecord(RevisedModel, log =>
            {
                StringBuilder stringBuilder = new StringBuilder();
                InitUpdateLog(RevisedModel, stringBuilder);
                log.Detail = stringBuilder.ToString();
                StringBuilder stringBuilderFormer = new StringBuilder();
                InitUpdateLog(FormerModel, stringBuilderFormer);
                log.DetailFormer = stringBuilderFormer.ToString();
            });
            return await Task.FromResult<ApiResultModelByObject>(apiResult);
        }

        /// <summary>
        /// 更新授權公司LOG
        /// </summary>
        /// <returns></returns>
        private static void InitUpdateLog(List<ModifyEntityInfo> RevisedModel, StringBuilder stringBuilder)
        {
            if (RevisedModel != null)
            {
                RevisedModel.OrderBy(m => m.company).ToList().ForEach(model =>
                {
                    stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("EntityManager_log:Entity", true)}:{BulletinsService.GetEntityByEntityID(model.EntityId)}");
                    if (!string.IsNullOrEmpty(model.company))
                        stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("EntityManager_log:Company", true)}:{model.company}");
                    if (model.level_start > 0)
                        stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("EntityManager_log:LevelStart", true)}:{model.level_start}");
                    if (model.level_end > 0)
                        stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("EntityManager_log:LevelEnd", true)}:{model.level_end}");
                    if (RevisedModel.Count > 1)
                        stringBuilder.AppendLine("-------------------------------------");
                });
            }
        }
        #endregion

        #region 根據主體ID查詢關卡詳情
        /// <summary>
        /// 根據主體ID查詢關卡詳情
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [Route("QueryApproveManagement")]
        public async Task<ApiResultModelByObject> QueryApproveManagement(int entity_id)
        {
            ApiResultModelByObject apiResult = new ApiResultModelByObject();
            UserInfoModel user = MvcContext.UserInfo;
            apiResult.listData = EntityManageService.QueryApproveManagement(entity_id, user.logging_locale, user.time_zone);
            string entity = BulletinsService.GetEntityByEntityID(entity_id.ToString());
            InitLogRecord(entity_id, log =>
            {
                log.Detail = $"{ActionFilter.GetMultilingualValue("EntityManager_log:Entity", true)}：{entity}";
            });
            apiResult.rtnSuccess = true;
            return await Task.FromResult<ApiResultModelByObject>(apiResult);
        }
        #endregion

        #region 顯示主體公告管理
        /// <summary>
        /// 顯示主體公告管理
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [Route("QueryEntityBulletins")]
        public async Task<ApiResultModelByObject> QueryEntityBulletins(int entity_id)
        {
            ApiResultModelByObject apiResult = new ApiResultModelByObject();
            UserInfoModel user = MvcContext.UserInfo;
            Bulletins bulletins = new Bulletins();
            bulletins.entity_id = new KeyValuePair<string, string>(entity_id.ToString(), "");
            string entity = BulletinsService.GetEntityByEntityID(entity_id.ToString());
            apiResult.listData = BulletinsService.QueryBulletins(bulletins, user.logging_locale, user.time_zone);
            InitLogRecord(entity_id, log =>
            {
                log.Detail = $"{ActionFilter.GetMultilingualValue("EntityManager_log:Entity", true)}：{entity}";
            });
            apiResult.rtnSuccess = true;
            return await Task.FromResult<ApiResultModelByObject>(apiResult);
        }
        #endregion

        #region 顯示主體角色與權限
        /// <summary>
        /// 顯示主體角色與權限
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [Route("QueryEntityRoleAndUser")]
        public async Task<ApiResultModelByObject> QueryEntityRoleAndUser(int entity_id)
        {
            ApiResultModelByObject apiResult = new ApiResultModelByObject();
            UserInfoModel user = MvcContext.UserInfo;
            apiResult.listData = EntityManageService.QueryEntityRoleAndUser(entity_id, user.time_zone);
            string entity = BulletinsService.GetEntityByEntityID(entity_id.ToString());
            InitLogRecord(entity_id, log =>
            {
                log.Detail = $"{ActionFilter.GetMultilingualValue("EntityManager_log:Entity", true)}：{entity}";
            });
            apiResult.rtnSuccess = true;
            return await Task.FromResult<ApiResultModelByObject>(apiResult);
        }
        #endregion

        #region 顯示角色使用者授權
        /// <summary>
        /// 顯示角色使用者授權
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [Route("QueryUserRoleAuthorization")]
        public async Task<ApiResultModelByObject> QueryUserRoleAuthorization(UserRoleAuthorizationModel model)
        {
            ApiResultModelByObject apiResult = new ApiResultModelByObject();
            UserInfoModel user = MvcContext.UserInfo;
            apiResult.listData = EntityManageService.QueryUserRoleAuthorization(model, user.time_zone);
            string entity_id = "";
            if (!String.IsNullOrEmpty(model.entity_id))
                entity_id = model.entity_id;
            string entity = BulletinsService.GetEntityByEntityID(entity_id);
            InitLogRecord(model, log =>
            {
                StringBuilder sb = new StringBuilder();
                sb.AppendLine($"{ActionFilter.GetMultilingualValue("EntityManager_log:Entity", true)}：{entity}");
                if (model.r_id != null && model.r_id > 0)
                    sb.AppendLine($"{ActionFilter.GetMultilingualValue("EntityManager_log:RoleName", true)}：{BulletinsService.GetRoleNameByRID(model.r_id)}");
                if (!string.IsNullOrEmpty(model.emplid))
                    sb.AppendLine($"{ActionFilter.GetMultilingualValue("EntityManager_log:Emplid", true)}：{model.emplid}");
                log.Detail = sb.ToString();
            });
            apiResult.rtnSuccess = true;
            return await Task.FromResult<ApiResultModelByObject>(apiResult);
        }
        #endregion

        #region 啟用主體
        /// <summary>
        /// 啟用主體
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [Route("EnableEneity")]
        public async Task<ApiResultModelByObject> EnableEneity(int entity_id)
        {
            ApiResultModelByObject apiResult = new ApiResultModelByObject();
            UserInfoModel user = MvcContext.UserInfo;
            if (!EntityManageService.CheckCkWhqLowAdmin(entity_id))
            {
                apiResult.messageTitle = ActionFilter.GetMultilingualValue("custom:messageTitle:enableFail");
                apiResult.messageContent = new List<string> { string.Format(ActionFilter.GetMultilingualValue("custom:messageContent:entityEnableMessage")) };
                return await Task.FromResult<ApiResultModelByObject>(apiResult);
            }
            if (!EntityManageService.CheckContractManager(entity_id))
            {
                apiResult.messageTitle = ActionFilter.GetMultilingualValue("custom:messageTitle:enableFail");
                apiResult.messageContent = new List<string> { string.Format(ActionFilter.GetMultilingualValue("custom:messageContent:contractManagerMessage")) };
                return await Task.FromResult<ApiResultModelByObject>(apiResult);
            }
            apiResult.listData = EntityManageService.EnableEneity(entity_id, user.current_emp);
            string entity = BulletinsService.GetEntityByEntityID(entity_id.ToString());
            InitLogRecord(entity_id, log =>
            {
                log.Detail = $"{ActionFilter.GetMultilingualValue("EntityManager_log:Entity", true)}：{entity}";
            });
            apiResult.rtnSuccess = true;
            return await Task.FromResult<ApiResultModelByObject>(apiResult);
        }
        #endregion

        #region 移除特殊主體設定
        /// <summary>
        /// 移除特殊主體設定
        /// </summary>
        /// <returns></returns>
        [HttpPut]
        [Route("DeleteFnpEntitySpecial")]
        public async Task<ApiResultModelByObject> DeleteFnpEntitySpecial(string entity_id)
        {
            ApiResultModelByObject apiResult = new ApiResultModelByObject();
            List<string> empList = FnpEntityUserService.DeleteFnpEntityUserByEntityId(entity_id);
            List<string> deptList = FnpEntityDeptService.DeleteFnpEntityDeptByEntityId(entity_id);

            if ((empList != null && empList.Count > 0) || (deptList != null && deptList.Count > 0))
            {
                apiResult.messageTitle = ActionFilter.GetMultilingualValue("commonWord:information");
            }
            if (empList != null && empList.Count > 0)
            {
                apiResult.messageContent.Add(ActionFilter.GetMultilingualValue("custom:messageContent:RemoveFnpEntityUser"));
                apiResult.messageContent.Add(string.Join(", ", empList));
                apiResult.messageContent.Add(string.Format(ActionFilter.GetMultilingualValue("commonWord:count"), empList.Count.ToString()));
            }
            if (deptList != null && deptList.Count > 0)
            {
                apiResult.messageContent.Add(ActionFilter.GetMultilingualValue("custom:messageContent:RemoveFnpEntityDept"));
                apiResult.messageContent.Add(string.Join(", ", deptList));
                apiResult.messageContent.Add(string.Format(ActionFilter.GetMultilingualValue("commonWord:count"), deptList.Count.ToString()));
            }

            apiResult.listData = true;
            apiResult.rtnSuccess = true;

            return await Task.FromResult<ApiResultModelByObject>(apiResult);
        }
        #endregion

        /// <summary>
        /// 获取本地时区时间
        /// </summary>
        /// <returns></returns>
        public static string GetLocalTimeToString(DateTime? time, string time_zone = "Taipei Standard Time")
        {
            if (time != null)
            {
                string dateTime = TimeZoneInfo.ConvertTimeFromUtc((DateTime)time, TimeZoneInfo.FindSystemTimeZoneById(time_zone)).ToString("yyyy/MM/dd");
                return dateTime;
            }
            return "";
        }
    }
}
