﻿using Elegal.Flow.Api.Repository.Application;
using Elegal.Flow.Api.Services.Application;
using Elegal.Flow.Common.Repository.FormApply;
using Elegal.Flow.Common.Services.FlowStep;
using Elegal.Flow.Common.Services.FormApply;
using Elegal.Interface.Api.Common.Control.Filter;
using Elegal.Interface.Api.Common.FuncHelper;
using Elegal.Interface.Api.Common.FuncService;
using Elegal.Interface.Api.Common.Model.ViewModel.FlowApi;
using Elegal.Interface.Api.Common.Model.ViewModel.FlowApi.FormApply;
using Newtonsoft.Json;

namespace Elegal.Flow.Api.Services.ApplicationPrint
{
    public class FormApplyPrintService
    {
        private static readonly FormApplyRepository _repository = new();
        private static ContractOriginalArchiveRepository _originalArchiveRepository = new ContractOriginalArchiveRepository();

        #region 取得合約申請單(列印)
        public static FormApplyPrintModel GetFormApply_Print(string apply_number)
        {
            if (string.IsNullOrEmpty(apply_number)) throw new Exception(ActionFilter.GetMultilingualValue("custom:messageTitle:dataNotexist"));

            FormApplicationPrintViewModel form = _repository.GetFormApplyPrintViewModel(apply_number);
            if (form == null) throw new Exception(ActionFilter.GetMultilingualValue("custom:messageTitle:dataNotexist"));

            List<string> list = ["formType", "acknowledgeType", "suggestStatus", "originalType", "sealStatus", "confidentStatus",
                "confidentialStatus", "amountStatus", "currency", "accountType", "payType", "taxType", "beType", "contractType",
                "fileCategory", "targetType", "egType", "dateOptions", "currency", "originaStatus", "archiveStatus",
                "finalSignLevel"];
            var sysParams = SysParametersService.GetParameters(list);

            var formOriginalListTask = Task.Run(() => _repository.GetFormOriginalList(apply_number));
            var formSupervisorListTask = Task.Run(() => _repository.GetFormSupervisorList(apply_number));
            var formPrInfoTask = Task.Run(() => _repository.GetFormPrInfo(apply_number));
            var formAcknowledgeTypeListTask = Task.Run(() => FormApplyAcknowledgeService.GetFormAcknowledgeType(apply_number));
            var flowStepHistoryViewModelsTask = Task.Run(() => FlowStepService.GetFlowHistoryView(apply_number));
            var ContractValidityModelTask = Task.Run(() => ContractOriginalArchiveService.GetBaseInformation(apply_number));
            var ArchiveRecordsTask = Task.Run(() => _originalArchiveRepository.ArchiveRecords(apply_number));
            var ContractHistoryTask = Task.Run(() => ContractOriginalArchiveService.GetContractHistoryData(apply_number));

            Task.WaitAll(formOriginalListTask, formSupervisorListTask, formPrInfoTask, formAcknowledgeTypeListTask, ContractValidityModelTask, ArchiveRecordsTask);

            form.formOriginalList = formOriginalListTask.Result;
            form.formSupervisorList = formSupervisorListTask.Result;
            form.formPrInfo = formPrInfoTask.Result;
            form.formAcknowledgeTypeList = formAcknowledgeTypeListTask.Result;
            if (!string.IsNullOrEmpty(form.other_party))
            {
                try { form.other_partys = JsonConvert.DeserializeObject<List<string>>(form.other_party) ?? []; }
                catch (Exception) { form.other_partys = form.other_party.Split("/").ToList(); }
            }

            ContractValidityModel contractValidityModel = ContractValidityModelTask.Result;

            if (!string.IsNullOrEmpty(form.other_party))
            {
                // 反序列化 JSON 字串為 List<string>
                form.other_party_list = JsonConvert.DeserializeObject<List<string>>(form.other_party);
                form.other_party = ConvertJsonToCommaSeparated(form.other_party);
            }

            if (!string.IsNullOrEmpty(form.form_type))
                form.form_type_str = sysParams.Where(w => w.ParaCode.Equals("formType") && w.FuncCode.Equals(form.form_type)).FirstOrDefault()?.FunName;

            if (!string.IsNullOrEmpty(form.acknowledge_type))
                form.acknowledge_type_str = sysParams.Where(w => w.ParaCode.Equals("acknowledgeType") && w.FuncCode.Equals(form.acknowledge_type)).FirstOrDefault()?.FunName;

            if (!string.IsNullOrEmpty(form.original_written_type))
                form.original_written_type_str = sysParams.Where(w => w.ParaCode.Equals("originalType") && w.FuncCode.Equals(form.original_written_type)).FirstOrDefault()?.FunName;

            if (!string.IsNullOrEmpty(form.seal_type))
                form.seal_type_str = sysParams.Where(w => w.ParaCode.Equals("sealStatus") && w.FuncCode.Equals(form.seal_type)).FirstOrDefault()?.FunName;

            if (!string.IsNullOrEmpty(form.confiden_level))
                form.confiden_level_str = sysParams.Where(w => w.ParaCode.Equals("confidentStatus") && w.FuncCode.Equals(form.confiden_level)).FirstOrDefault()?.FunName;

            if (!string.IsNullOrEmpty(form.confidential_identification))
                form.confidential_identification_str = sysParams.Where(w => w.ParaCode.Equals("confidentialStatus") && w.FuncCode.Equals(form.confidential_identification)).FirstOrDefault()?.FunName;

            if (!string.IsNullOrEmpty(form.is_having_money))
                form.is_having_money_str = sysParams.Where(w => w.ParaCode.Equals("amountStatus") && w.FuncCode.Equals(form.is_having_money)).FirstOrDefault()?.FunName;

            if (!string.IsNullOrEmpty(form.currency))
                form.currency_str = sysParams.Where(w => w.ParaCode.Equals("currency") && w.FuncCode.Equals(form.currency)).FirstOrDefault()?.FunName;

            if (!string.IsNullOrEmpty(form.account_type))
                form.account_type_str = sysParams.Where(w => w.ParaCode.Equals("accountType") && w.FuncCode.Equals(form.account_type)).FirstOrDefault()?.FunName;

            if (!string.IsNullOrEmpty(form.account_method))
                form.account_method_str = sysParams.Where(w => w.ParaCode.Equals("payType") && w.FuncCode.Equals(form.account_method)).FirstOrDefault()?.FunName;

            if (!string.IsNullOrEmpty(form.taxes_type))
                form.taxes_type_str = sysParams.Where(w => w.ParaCode.Equals("taxType") && w.FuncCode.Equals(form.taxes_type)).FirstOrDefault()?.FunName;

            if (!string.IsNullOrEmpty(form.benefit_evaluation))
                form.benefit_evaluation_str = sysParams.Where(w => w.ParaCode.Equals("beType") && w.FuncCode.Equals(form.benefit_evaluation)).FirstOrDefault()?.FunName;

            if (!string.IsNullOrEmpty(form.contract_type))
                form.contract_type_str = sysParams.Where(w => w.ParaCode.Equals("contractType") && w.FuncCode.Equals(form.contract_type)).FirstOrDefault()?.FunName;

            if (!string.IsNullOrEmpty(form.file_category))
                form.file_category_str = sysParams.Where(w => w.ParaCode.Equals("fileCategory") && w.FuncCode.Equals(form.file_category)).FirstOrDefault()?.FunName;

            if (!string.IsNullOrEmpty(form.contract_obj))
                form.contract_obj_str = sysParams.Where(w => w.ParaCode.Equals("targetType") && w.FuncCode.Equals(form.contract_obj)).FirstOrDefault()?.FunName;

            if (!string.IsNullOrEmpty(form.endorsement))
                form.endorsement_str = sysParams.Where(w => w.ParaCode.Equals("egType") && w.FuncCode.Equals(form.endorsement)).FirstOrDefault()?.FunName;

            if (!string.IsNullOrEmpty(form.sign_type))
                form.sign_type_str = sysParams.Where(w => w.ParaCode.Equals("dateOptions") && w.FuncCode.Equals(form.sign_type)).FirstOrDefault()?.FunName;

            if (!string.IsNullOrEmpty(form.eff_type))
                form.eff_type_str = sysParams.Where(w => w.ParaCode.Equals("dateOptions") && w.FuncCode.Equals(form.eff_type)).FirstOrDefault()?.FunName;

            if (!string.IsNullOrEmpty(form.exp_type))
                form.exp_type_str = sysParams.Where(w => w.ParaCode.Equals("dateOptions") && w.FuncCode.Equals(form.exp_type)).FirstOrDefault()?.FunName;

            if (!string.IsNullOrEmpty(form.tax_currency))
                form.tax_currency_str = sysParams.Where(w => w.ParaCode.Equals("currency") && w.FuncCode.Equals(form.tax_currency)).FirstOrDefault()?.FunName;

            if (!string.IsNullOrEmpty(form.final_sign_level))
                form.final_sign_level_str = sysParams.Where(w => w.ParaCode.Equals("finalSignLevel") && w.FuncCode.Equals(form.final_sign_level)).FirstOrDefault()?.FunName;

            List<PaperBasicDataPrintModel> paperbasicData = new List<PaperBasicDataPrintModel>();

            if (contractValidityModel != null)
            {
                if (!string.IsNullOrEmpty(contractValidityModel.ConfirmEffDateType))
                    contractValidityModel.ConfirmEffDateType = sysParams.Where(w => w.ParaCode.Equals("dateOptions") && w.FuncCode.Equals(contractValidityModel.ConfirmEffDateType)).FirstOrDefault()?.FunName;

                if (!string.IsNullOrEmpty(contractValidityModel.ConfirmSignDateType))
                    contractValidityModel.ConfirmSignDateType = sysParams.Where(w => w.ParaCode.Equals("dateOptions") && w.FuncCode.Equals(contractValidityModel.ConfirmSignDateType)).FirstOrDefault()?.FunName;

                if (!string.IsNullOrEmpty(contractValidityModel.ConfirmExpDateType))
                    contractValidityModel.ConfirmExpDateType = sysParams.Where(w => w.ParaCode.Equals("dateOptions") && w.FuncCode.Equals(contractValidityModel.ConfirmExpDateType)).FirstOrDefault()?.FunName;

                if (!string.IsNullOrEmpty(contractValidityModel.OriginArchiveType))
                    contractValidityModel.OriginArchiveType = sysParams.Where(w => w.ParaCode.Equals("archiveStatus") && w.FuncCode.Equals(contractValidityModel.OriginArchiveType)).FirstOrDefault()?.FunName;

                if (contractValidityModel.PaperBasicDatas != null && contractValidityModel.PaperBasicDatas.Count > 0)
                {
                    paperbasicData = contractValidityModel.PaperBasicDatas
                        .Select(p =>
                        {
                            var uploadFile = contractValidityModel.SysUploadFiles?.FirstOrDefault(f => f.Fileid == p.FileId);
                            return new PaperBasicDataPrintModel
                            {
                                BasicId = p.BasicId,
                                PaperApplicaId = p.PaperApplicaId,
                                PaperCode = p.PaperCode,
                                PaperName = p.PaperName,
                                PaperType = p.PaperType,
                                PaperConfidenLevel = p.PaperConfidenLevel,
                                ParerBorrowDays = p.ParerBorrowDays,
                                PaperPosition = p.PaperPosition,
                                PaperEntryStatus = p.PaperEntryStatus,
                                DestroyTime = p.DestroyTime,
                                LostTime = p.LostTime,
                                PaperReturnStatus = p.PaperReturnStatus,
                                IsExcel = p.IsExcel,
                                PaperRemarks = p.PaperRemarks,
                                CreateUser = p.CreateUser,
                                CreateTime = p.CreateTime,
                                ModifyUser = p.ModifyUser,
                                ModifyTime = p.ModifyTime,
                                DestroyReason = p.DestroyReason,
                                LostReason = p.LostReason,
                                FileId = p.FileId,
                                file_path = uploadFile?.FilePath ?? string.Empty,
                                file_name = uploadFile?.FileName ?? string.Empty
                            };
                        })
                        .ToList();
                }
            }
            form.show_school = FormApplyService.IsShowInterSchool(form.fill_deptid, form.entity_id);
            form.waterMark = CommonUtilHelper.GetConfidenLevelWatermarkText(form.confiden_level);
            FormApplyPrintModel res = new FormApplyPrintModel()
            {
                form = form,
                flowStepHistoryViewModels = flowStepHistoryViewModelsTask.Result,
                contractValidityModel = contractValidityModel,
                archiveRecordsList = ArchiveRecordsTask.Result,
                flowContractHistoryList = ContractHistoryTask.Result,
                paperBasicDatas = paperbasicData
            };

            return res;
        }
        #endregion

        public static string ConvertJsonToCommaSeparated(string jsonInput)
        {
            // 反序列化 JSON 字串為 List<string>
            List<string> items = JsonConvert.DeserializeObject<List<string>>(jsonInput);

            // 將 List<string> 轉換為用逗號分隔的字串
            return string.Join(", ", items);
        }
    }
}
