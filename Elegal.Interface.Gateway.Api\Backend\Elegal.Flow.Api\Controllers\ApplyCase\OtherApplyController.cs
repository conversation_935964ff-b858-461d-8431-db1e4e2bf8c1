﻿using Elegal.Flow.Common.Services.OtherApply;
using Elegal.Interface.Api.Common.Control.Filter;
using Elegal.Interface.Api.Common.Control.Web;
using Elegal.Interface.Api.Common.FuncHelper;
using Elegal.Interface.Api.Common.GetLogCommon;
using Elegal.Interface.Api.Common.Model.DBModel;
using Elegal.Interface.Api.Common.Model.DBModel.otherApply;
using Elegal.Interface.Api.Common.Model.ParaModel.FlowApi.ApplicationSearch;
using Elegal.Interface.Api.Common.Model.ParaModel.FlowApi.FlowStep;
using Elegal.Interface.Api.Common.Model.ParaModel.OtherApply;
using Elegal.Interface.Api.Common.Model.ResultModel;
using Elegal.Interface.ApiData.Service.Model.DbModel;
using Elegal.Interface.Api.Common.Model.ViewModel.FlowApi.OtherApply;
using Microsoft.AspNetCore.Mvc;
using System.Text;
using Elegal.Flow.Common.Services.FlowStep;
using Minio.DataModel;

namespace Elegal.Flow.Api.Controllers.ApplyCase
{
    /// <summary>
    /// 其他申請
    /// </summary>
    [Route("[controller]")]
    [ApiController]

    public class OtherApplyController : BaseController
    {
        #region 獲取暫存單
        /// <summary>
        /// 獲取暫存單
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("GetTempOtherApply")]
        public async Task<ApiResultModelByObject> GetTempOtherApply()
        {
            ApiResultModelByObject apiResult = new();

            apiResult.listData = OtherApplyService.GetTempOtherApply();
            apiResult.rtnSuccess = true;

            return await Task.FromResult(apiResult);
        }
        #endregion

        #region 取得調閱單號清單
        /// <summary>
        /// 取得調閱單號清單
        /// </summary>
        /// <param name="apply_number"></param>
        /// <param name="entity_id"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetOtherRetrieveNumberList")]
        public async Task<ApiResultModelByObject> GetOtherRetrieveNumberList([FromQuery] string? apply_number, string? entity_id)
        {
            ApiResultModelByObject apiResult = new();

            apiResult.listData = OtherApplyService.GetOtherRetrieveNumberList(apply_number, entity_id);
            apiResult.rtnSuccess = true;

            return await Task.FromResult(apiResult);
        }
        #endregion

        #region 新增合約申請單(主表及各類型子表)
        /// <summary>
        /// 新增合約申請單(主表及各類型子表)
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("InsertOtherApplication_Type")]
        public async Task<ApiResultModelByObject> InsertOtherApplication_Type([FromBody] InsertOtherApply data)
        {
            ApiResultModelByObject apiResult = new();
            InsertOtherApply oldData = new InsertOtherApply();
            (apiResult.listData,oldData) = OtherApplyService.InsertOtherApplication_TypeToTransaction(data);
            apiResult.rtnSuccess = true;
            //日誌
            InitLogRecord(data, LogRecord => {
                StringBuilder stringBuilder = new StringBuilder();
                StringBuilder oldstringBuilder = new();
                string newstr = string.Empty;
                string oldstr = string.Empty;
                (newstr, oldstr) = FlowStepService.GetApproveLogStrForOtherApply(data, oldData);
                if (!string.IsNullOrEmpty(newstr)) stringBuilder.AppendLine(newstr);
                if (!string.IsNullOrEmpty(oldstr)) oldstringBuilder.AppendLine(oldstr);
                LogRecord.Detail = stringBuilder.ToString();
                LogRecord.DetailFormer = oldstringBuilder.ToString();
            });
            return await Task.FromResult(apiResult);
        }
        #endregion

        #region 重置暫存單
        /// <summary>
        /// 重置暫存單
        /// </summary>
        /// <returns></returns>
        [HttpPut]
        [Route("ResetOtherTempForm")]
        public async Task<ApiResultModelByObject> ResetOtherTempForm()
        {
            ApiResultModelByObject apiResult = new();

            apiResult.listData = OtherApplyService.ResetTempOtherApplicationToTransaction();
            apiResult.rtnSuccess = true;
            //記錄日誌
            InitLogRecord();
            return await Task.FromResult(apiResult);
        }
        #endregion

        #region 將暫存單改為進行中
        /// <summary>
        /// 將暫存單改為進行中
        /// </summary>
        /// <param name="para"></param>
        /// <returns></returns>
        [HttpPut]
        [Route("SetOtherInProgress")]
        public async Task<ApiResultModelByObject> SetOtherInProgress([FromBody] FormProgressParaModel? para = null)
        {
            ApiResultModelByObject apiResult = new();
            var result = OtherApplyService.SetOtherInProgressToTransaction(para);
            apiResult.listData = result;
            apiResult.rtnSuccess = true;
            //記錄日誌
            InitLogRecord(para, log =>
            {
                StringBuilder stringBuilder = new StringBuilder();
                stringBuilder.GetLogString(result.apply_number, "PublicLogCodeFile_otherApplication_applyNumber");
                log.Detail = stringBuilder.ToString();
            });
            return await Task.FromResult(apiResult);
        }
        #endregion

        #region 更新行政資訊起迄日並結案
        /// <summary>
        /// 更新行政資訊起迄日並結案
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("UpdateAdminDate")]
        public async Task<ApiResultModelByObject> UpdateAdminDate([FromBody] UpdateAdminDateParaModel data)
        {
            ApiResultModelByObject apiResult = new();

            apiResult.listData = OtherApplyService.UpdateAdminDateToTransaction(data, out string errorMsg);
            apiResult.rtnSuccess = true;
            if (!string.IsNullOrEmpty(errorMsg)) apiResult.messageContent.Add(errorMsg);

            return await Task.FromResult(apiResult);
        }
        #endregion


        /// <summary>
        /// 新增其他申請單 申請單位資訊
        /// </summary>
        [HttpPost]
        [Route("InsertOtherApplication")]
        public async Task<ApiResultModelByObject> InsertOtherApplication([FromBody] other_application data)
        {
            ApiResultModelByObject apiResult = new();

            apiResult.listData = OtherApplyService.InsertOtherApplication(data);
            apiResult.rtnSuccess = true;

            return await Task.FromResult(apiResult);
        }

        /// <summary>
        /// 新增合約申請單 Type A
        /// </summary>
        [HttpPost]
        [Route("InsertOtherApplication_TypeA")]
        public async Task<ApiResultModelByObject> InsertOtherApplication_TypeA([FromBody] InsertOtherApply_A data)
        {
            ApiResultModelByObject apiResult = new();

            apiResult.listData = OtherApplyService.InsertOtherApplication_TypeA(data);
            apiResult.rtnSuccess = true;

            return await Task.FromResult(apiResult);
        }

        /// <summary>
        /// 新增合約申請單(主表及各類型子表)
        /// </summary>
        [HttpPost]
        [Route("InsertOtherApplicationLegal")]
        public async Task<ApiResultModelByObject> InsertOtherApplicationLegal([FromBody] InsertOtherApplyLegal data)
        {
            ApiResultModelByObject apiResult = new();

            apiResult.listData = OtherApplyService.InsertOtherApplicationLegal(data.apply_number, data.otherApplicationLegal);
            apiResult.rtnSuccess = true;
            //記錄日誌
            InitLogRecord(data, logRecord =>
            {
                StringBuilder stringBuilder = new();
                stringBuilder.GetLogString(data.apply_number, "PublicLogCodeFile_otherApplication_applyNumber")
                             .GetLogString(data.otherApplicationLegal.Select(x => x.legal_emplid).ToList(), "PublicLogCodeFile_otherApplication_legalEmplid")
                             ;
                logRecord.Detail = stringBuilder.ToString();
                //if (oldData != null)
                //{
                //    StringBuilder stringBuilderOld = new();
                //    stringBuilderOld.GetLogString(data.apply_number, "PublicLogCodeFile_otherApplication_applyNumber")
                //                    .GetLogString(oldData.Select(x => x.legal_emplid).ToList(), "PublicLogCodeFile_otherApplication_legalEmplid")
                //                    ;
                //    logRecord.DetailFormer = stringBuilderOld.ToString();
                //}
            });
            return await Task.FromResult(apiResult);
        }

        /// <summary>
        /// 新增其他申請單 行政資訊
        /// </summary>
        [HttpPost]
        [Route("InsertOtherApplicationAdmin")]
        public async Task<ApiResultModelByObject> InsertOtherApplicationAdmin([FromBody] other_application_admin data)
        {
            ApiResultModelByObject apiResult = new();

            apiResult.listData = OtherApplyService.InsertOtherApplicationAdmin(data);
            apiResult.rtnSuccess = true;
            //記錄日誌
            InitLogRecord(data, logRecord =>
            {
                List<p_role> proleList = OtherApplyService.GetOtherTypeRole(data.apply_number);
                StringBuilder stringBuilder = new();
                stringBuilder.GetLogString(data.apply_number, "PublicLogCodeFile_otherApplication_applyNumber")
                             .GetLogString(data.has_top_manager.Value == 1 ? $"{ActionFilter.GetMultilingualValue("PublicLogCodeFile_PublicStr_yes", true)}" : $"{ActionFilter.GetMultilingualValue("PublicLogCodeFile_PublicStr_no", true)}", "PublicLogCodeFile_otherApplication_hasTopManager")
                             .GetLogString(data.actual_date_start.ConvertDateByTimeZoneByUtc(MvcContext.UserInfo.time_zone)?.ToString("yyyy/MM/dd") ?? string.Empty, "PublicLogCodeFile_otherApplication_actualDateStart")
                             .GetLogString(data.actual_date_end.ConvertDateByTimeZoneByUtc(MvcContext.UserInfo.time_zone)?.ToString("yyyy/MM/dd") ?? string.Empty, "PublicLogCodeFile_otherApplication_actualDateEnd")
                             .GetLogString("PublicLogCodeFile_otherApplication_authRoleId", proleList.Where(x => x.r_id == data.auth_role_id).Select(x => x.r_name).ToList())
                             .GetLogString(data.admin_reason, "PublicLogCodeFile_otherApplication_adminReason")
                             ;
                logRecord.Detail = stringBuilder.ToString();
                //if (oldData != null)
                //{
                //    StringBuilder stringBuilderOld = new();
                //    stringBuilderOld.GetLogString(oldData.apply_number, "PublicLogCodeFile_otherApplication_applyNumber")
                //                    .GetLogString(oldData.has_top_manager.Value == 1 ? $"{ActionFilter.GetMultilingualValue("PublicLogCodeFile_PublicStr_yes", true)}" : $"{ActionFilter.GetMultilingualValue("PublicLogCodeFile_PublicStr_no", true)}", "PublicLogCodeFile_otherApplication_hasTopManager")
                //                    .GetLogString(oldData.actual_date_start.ConvertDateByTimeZoneByUtc(MvcContext.UserInfo.time_zone)?.ToString("yyyy/MM/dd") ?? string.Empty, "PublicLogCodeFile_otherApplication_actualDateStart")
                //                    .GetLogString(oldData.actual_date_end.ConvertDateByTimeZoneByUtc(MvcContext.UserInfo.time_zone)?.ToString("yyyy/MM/dd") ?? string.Empty, "PublicLogCodeFile_otherApplication_actualDateEnd")
                //                    .GetLogString("PublicLogCodeFile_otherApplication_authRoleId", proleList.Where(x => x.r_id == data.auth_role_id).Select(x => x.r_name).ToList())
                //                    .GetLogString(oldData.admin_reason, "PublicLogCodeFile_otherApplication_adminReason")
                //                    ;
                //    logRecord.DetailFormer = stringBuilderOld.ToString();
                //}
            });
            return await Task.FromResult(apiResult);
        }

        /// <summary>
        /// 取得其他申請單
        /// </summary>
        [HttpGet]
        [Route("GetOtherApplication")]
        public async Task<ApiResultModelByObject> GetOtherApplication([FromQuery] string apply_number, int isShowBCase)
        {
            ApiResultModelByObject apiResult = new();

            apiResult.listData = OtherApplyService.GetOtherApplication(apply_number, isShowBCase);
            apiResult.rtnSuccess = true;

            return await Task.FromResult(apiResult);
        }

        /// <summary>
        /// 取得其他申請單(列印)
        /// </summary>
        [HttpGet]
        [Route("GetOtherApplication_Print")]
        public async Task<ApiResultModelByObject> GetOtherApplication_Print([FromQuery] string apply_number)
        {
            ApiResultModelByObject apiResult = new();

            apiResult.listData = OtherApplyPrintService.GetOtherApplication_Print(apply_number);
            apiResult.rtnSuccess = true;
            //記錄日誌
            InitLogRecord(apply_number, logRecord =>
            {
                StringBuilder stringBuilder = new();
                stringBuilder.GetLogString(apply_number, "PublicLogCodeFile_otherApplication_applyNumber");
                //if (!string.IsNullOrEmpty(apply_number)) stringBuilder.AppendLine($"申請單號: {apply_number}");
                logRecord.Detail = stringBuilder.ToString();
            });
            return await Task.FromResult(apiResult);
        }

        /// <summary>
        /// 驗證調閱單號主體是否符合經辦人部門權限
        /// </summary>
        [HttpGet]
        [Route("CheckOtherRetrieveNumber")]
        public async Task<ApiResultModelByObject> CheckOtherRetrieveNumber([FromQuery] string pic_deptid, string entity_id)
        {
            ApiResultModelByObject apiResult = new();

            apiResult.listData = OtherApplyService.CheckOtherRetrieveNumber(pic_deptid, entity_id);
            apiResult.rtnSuccess = true;

            return await Task.FromResult(apiResult);
        }

        /// <summary>
        /// 驗證調閱單號主體是否符合經辦人部門權限
        /// </summary>
        [HttpGet]
        [Route("GetOtherExposeEmpList")]
        public async Task<ApiResultModelByObject> GetOtherExposeEmpList([FromQuery] string pic_deptid, string? emplid)
        {
            ApiResultModelByObject apiResult = new();

            apiResult.listData = OtherApplyService.GetOtherExposeEmpList(pic_deptid, emplid);
            apiResult.rtnSuccess = true;

            return await Task.FromResult(apiResult);
        }

        /// <summary>
        /// 驗證調閱單號主體是否符合經辦人部門權限
        /// </summary>
        [HttpGet]
        [Route("GetVoidAndSealOtherList")]
        public async Task<ApiResultModelByObject> GetVoidAndSealOtherList([FromQuery] string? apply_number)
        {
            ApiResultModelByObject apiResult = new();

            apiResult.listData = OtherApplyService.GetVoidAndSealOtherList(apply_number);
            apiResult.rtnSuccess = true;

            return await Task.FromResult(apiResult);
        }

        /// <summary>
        /// 依申請單類型取得角色
        /// </summary>
        [HttpGet]
        [Route("GetOtherTypeRole")]
        public async Task<ApiResultModelByObject> GetOtherTypeRole([FromQuery] string apply_number)
        {
            ApiResultModelByObject apiResult = new();

            apiResult.listData = OtherApplyService.GetOtherTypeRole(apply_number);
            apiResult.rtnSuccess = true;

            return await Task.FromResult(apiResult);
        }

        /// <summary>
        /// 依申請單類型取得角色
        /// </summary>
        [HttpGet]
        [Route("GetTypeBCaseList")]
        public async Task<ApiResultModelByObject> GetTypeBCaseList([FromQuery] string apply_number)
        {
            ApiResultModelByObject apiResult = new();

            var (obCount, obApplyNumber) = OtherApplyService.SetTypeBCaseList(apply_number);
            apiResult.listData = obApplyNumber;
            apiResult.rtnSuccess = true;

            return await Task.FromResult(apiResult);
        }

        /// <summary>
        /// 根據單號取得授權檢視頁
        /// </summary>
        [HttpGet]
        [Route("GetOtherAuthorizationView")]
        public async Task<ApiResultModelByObject> GetOtherAuthorizationView([FromQuery] string apply_number)
        {
            ApiResultModelByObject apiResult = new();

            apiResult.listData = OtherAuthorizationViewService.GetOtherAuthorizationView(apply_number);
            apiResult.rtnSuccess = true;

            return await Task.FromResult(apiResult);
        }

        /// <summary>
        /// 根據單號取得授權檢視頁(匯出)
        /// </summary>
        [HttpGet]
        [Route("GetOtherAuthorizationExport")]
        public async Task<ApiResultModelByObject> GetOtherAuthorizationExport([FromQuery] string apply_number)
        {
            ApiResultModelByObject apiResult = new();

            apiResult.listData = OtherAuthorizationViewService.GetOtherAuthorizationExport(apply_number);
            apiResult.rtnSuccess = true;

            return await Task.FromResult(apiResult);
        }

        /// <summary>
        /// 根據單號取得授權檢視頁(匯出) - 簽核中
        /// </summary>
        [HttpGet]
        [Route("GetOtherAuthorizationExport_InApproval")]
        public async Task<ApiResultModelByObject> GetOtherAuthorizationExport_InApproval([FromQuery] string apply_number)
        {
            ApiResultModelByObject apiResult = new();

            apiResult.listData = OtherAuthorizationViewService.GetOtherAuthorizationExport_InApproval(apply_number);
            apiResult.rtnSuccess = true;
            //記錄日誌
            InitLogRecord(LogRecord => { 
                StringBuilder stringBuilder = new();
                stringBuilder.GetLogString(apply_number, "PublicLogCodeFile_otherApplication_applyNumber");
                LogRecord.Detail = stringBuilder.ToString();
            });
            return await Task.FromResult(apiResult);
        }

        /// <summary>
        /// 檢查是否有TypeB的案件
        /// </summary>
        [HttpPost]
        [Route("CheckTypeBCaseList")]
        public async Task<ApiResultModelByObject> CheckTypeBCaseList([FromBody] ApplicationSearchParaModel detailed_other)
        {
            ApiResultModelByObject apiResult = new();

            apiResult.listData = OtherApplyService.CheckTypeBCaseList(detailed_other);
            apiResult.rtnSuccess = true;

            return await Task.FromResult(apiResult);
        }

        /// <summary>
        /// 檢查是否有TypeB的案件
        /// </summary>
        [HttpPost]
        [Route("ConvertOtherDetail")]
        public async Task<ApiResultModelByObject> ConvertOtherDetail([FromQuery] string apply_number)
        {
            ApiResultModelByObject apiResult = new();

            OtherApplyViewModel oavm = OtherApplyService.GetOtherApplication(apply_number);
            //apiResult.listData = OtherApplyService.ConvertOtherDetail(oavm.otherApplicationB.detailed_other);
            apiResult.listData = OtherApplyService.ConvertOtherExport(oavm.otherApplicationB.export_field);
            apiResult.rtnSuccess = true;

            return await Task.FromResult(apiResult);
        }
    }
}
