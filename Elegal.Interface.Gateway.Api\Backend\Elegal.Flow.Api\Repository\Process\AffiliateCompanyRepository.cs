﻿using Elegal.Interface.Api.Common.Model.ViewModel.FlowApi;
using Elegal.Interface.Api.Common.Repository;
using Elegal.Orm;
using System.Text;

namespace Elegal.Flow.Api.Repository
{
    /// <summary>
    /// 關企主體管理
    /// </summary>
    /// <returns></returns>
    public class AffiliateCompanyRepository : BaseRepository
    {
        /// <summary>
        /// 獲取最新一筆關企公司Code
        /// </summary>
        internal string GetAffiliateCompanyCode()
        {
            string sql = @"DECLARE @maxNumber INT
                            --獲取數據
                            SELECT @maxNumber = (sequence_value + 1) FROM dbo.sys_application_sequence WHERE sequence_key = 'affiliate_company';
                            --修改數據
                            --UPDATE dbo.sys_application_sequence SET sequence_value = @maxNumber WHERE sequence_key = 'affiliate_company';
                            --返回結果
                            SELECT CHAR(SUBSTRING(CONVERT(NVARCHAR,@maxNumber),0,3)) + SUBSTRING(CONVERT(NVARCHAR(6),@maxNumber),3,4) AS aff_company_code;";
            return DbAccess.Database.SqlQuery<string>(sql, null).FirstOrDefault();
        }

        /// <summary>
        /// 關公司列表查詢
        /// </summary>
        internal IEnumerable<AffiliateCompanyViewModel> GetDataList(AffiliateCompanySearchModel condition)
        {
            StringBuilder sqlBuilder = new StringBuilder();
            sqlBuilder.Append($@"SELECT
                                    ac.aff_company_code AS affCompanyCode,--關企主體代碼
                                    ac.aff_company_abb AS affCompanyAbb,--關企主體簡稱
                                    ac.aff_company_cname AS affCompanyCname,--關企主體中文名稱
                                    ac.aff_company_ename AS affCompanyEname,--關企主體英文名稱
                                    ac.aff_status AS affStatus,--關企公司狀態(1：啟用；0：禁用)
                                    ac.aff_reamrk AS affReamrk,--關企公司備註
                                    (select entity from fnp_entity where entity_id = ac.aff_group_entity) AS affGroupEntityName,--關聯我方公司主體
                                    ac.operate_time,--更新時間
                                    ac.operate_user,--更新者工號
                                    ee.name as operate_cuser,--更新者中文名稱
                                    ee.name_a AS operate_euser--更新者英文名稱
                                    FROM (
                                    SELECT
                                    aff_company_code,--關企主體代碼
                                    aff_company_abb,--關企主體簡稱
                                    aff_company_cname,--關企主體中文名稱
                                    aff_company_ename,--關企主體英文名稱
                                    aff_status,--關企公司狀態(1：啟用；0：禁用)
                                    aff_reamrk,--關企公司備註
                                    aff_group_entity,--關聯我方公司主體
                                    COALESCE(modify_time,create_time) AS operate_time,--更新時間
                                    COALESCE(modify_user,create_user) AS operate_user--更新人
                                    FROM dbo.affiliate_company
                                    WHERE 1 = 1");
            if (!string.IsNullOrEmpty(condition.Name))
                sqlBuilder.Append($@"--主體簡稱/中文/英文模糊查詢
                                    AND (aff_company_abb LIKE CONCAT(N'%', @name,N'%') OR aff_company_cname LIKE CONCAT(N'%', @name,N'%') OR aff_company_ename LIKE CONCAT(N'%', @name,N'%'))");
            sqlBuilder.Append($@") AS ac
                                    LEFT JOIN (SELECT emplid,name,name_a FROM dbo.ps_sub_ee_lgl_vw_a) AS ee ON ee.emplid = ac.operate_user order by ac.aff_status desc;");
            return DbAccess.Database.SqlQuery<AffiliateCompanyViewModel>(sqlBuilder.ToString(), new { condition.Name });
        }

        /// <summary>
        /// 查詢主體關聯申請單號
        /// </summary>
        internal IEnumerable<string> QueryApplyNumber(string affCompanyCode)
        {
            string sql = $@"SELECT apply_number FROM v_getallapplication WHERE entity_id = @affCompanyCode";
            return DbAccess.Database.SqlQuery<string>(sql, new { affCompanyCode = affCompanyCode });
        }
    }
}
