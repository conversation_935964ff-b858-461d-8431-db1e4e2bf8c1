﻿using Elegal.Flow.Api.Services.Paper;
using Elegal.Interface.Api.Common.Control.Web;
using Elegal.Interface.Api.Common.FuncHelper;
using Elegal.Interface.Api.Common.Model.ParaModel.FlowApi;
using Elegal.Interface.Api.Common.Model.ResultModel;
using Elegal.Interface.Api.Common.Model.ResultModel.FlowApi.LendAppliction;
using Elegal.Interface.Api.Common.Model.ViewModel.FlowApi;
using Elegal.Orm.Dtos;
using Microsoft.AspNetCore.Mvc;
using System.Text;

namespace Elegal.Flow.Api.Controllers.Paper
{
    /// <summary>
    /// 借出申請
    /// </summary>
    [Route("[controller]")]
    [ApiController]
    public class PaperLendingApplicationController : BaseController
    {
        /// <summary>
        /// 查詢借出申請資料
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [Route("Query")]
        public async Task<ApiResultModelByObject> QueryPaperLendingApplication([FromBody] qryPaperLendingApplication qry)
        {
            ApiResultModelByObject apiResult = new ApiResultModelByObject();

            PageResult<PaperLendingApplicationViewModel> result = PaperLendingApplicationService.QueryPaperLendingApplication(qry);
            apiResult.listData = result.Data;
            apiResult.totalCount = result.TotalRows;
            apiResult.rtnSuccess = true;
            InitLogRecord(qry, logRecord =>
            {
                StringBuilder stringBuilder = new();
                if (!string.IsNullOrEmpty(qry.paper_code)) stringBuilder.AppendLine($"紙本/合約編號:  {qry.paper_code}");
                if (!string.IsNullOrEmpty(qry.paper_name)) stringBuilder.AppendLine($"紙本/合約名稱:  {qry.paper_name}");
                if (!string.IsNullOrEmpty(qry.lend_number)) stringBuilder.AppendLine($"借出單號: {qry.lend_number}");
                if (!string.IsNullOrEmpty(qry.retrieve_number)) stringBuilder.AppendLine($"調閱單號: {qry.retrieve_number}");
                if (qry.lend_fill_emplid != null && qry.lend_fill_emplid.Count > 0) stringBuilder.AppendLine($"填單人工號: {string.Join(",", qry.lend_fill_emplid)}");
                if (!string.IsNullOrEmpty(qry.lend_handler_emplid)) stringBuilder.AppendLine($"經辦人工號: {qry.lend_handler_emplid}");
                if (qry.lend_status != null && qry.lend_status.Count > 0) stringBuilder.AppendLine($"申請單狀態: {PaperBasicDataService.GetTypeString("lib_lendStatus", string.Join(",", qry.lend_status))}");
                if (qry.create_time_start != null) stringBuilder.AppendLine($"申請日期開始:  {TimeZoneInfoConvertHelper.ConvertDateByTimeZoneByUtc(qry.create_time_start.Value).ToString("yyyy/MM/dd")}");
                if (qry.create_time_end != null) stringBuilder.AppendLine($"申請日期結束:  {TimeZoneInfoConvertHelper.ConvertDateByTimeZoneByUtc(qry.create_time_end.Value).ToString("yyyy/MM/dd")}");
                if (qry.pickup_time_start != null) stringBuilder.AppendLine($"取件日期開始:  {TimeZoneInfoConvertHelper.ConvertDateByTimeZoneByUtc(qry.pickup_time_start.Value).ToString("yyyy/MM/dd")}");
                if (qry.pickup_time_end != null) stringBuilder.AppendLine($"取件日期結束:  {TimeZoneInfoConvertHelper.ConvertDateByTimeZoneByUtc(qry.pickup_time_end.Value).ToString("yyyy/MM/dd")}");

                logRecord.Detail = stringBuilder.ToString();
            });

            return await Task.FromResult(apiResult);
        }

        /// <summary>
        /// 查詢借出申請明細
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("QueryDetails")]
        public async Task<ApiResultModelByObject> GetPaperLendingDetails([FromQuery] int lend_id)
        {
            ApiResultModelByObject apiResult = new ApiResultModelByObject();

            var oldData = PaperLendingApplicationService.GetPaperLendingApplicationByLendID(lend_id);

            List<PaperLendingApplicationDetailViewModel> result = PaperLendingApplicationService.GetPaperLendingDetails(lend_id);
            apiResult.listData = result;
            apiResult.totalCount = result.Count();
            apiResult.rtnSuccess = true;
            InitLogRecord<object>(new(), logRecord =>
            {
                StringBuilder stringBuilder = new();
                stringBuilder.AppendLine($"借出單號:  {oldData.lend_number}");

                logRecord.Detail = stringBuilder.ToString();
            });

            return await Task.FromResult(apiResult);
        }

        /// <summary>
        /// 調閱申請單列表
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("GetRetrieveList")]
        public async Task<ApiResultModelByObject> GetRetrieveList([FromQuery] string retrieve_number)
        {
            ApiResultModelByObject apiResult = new ApiResultModelByObject();

            List<string> result = PaperLendingApplicationService.GetOtherApplicationList(retrieve_number);
            apiResult.listData = result;
            apiResult.rtnSuccess = true;

            return await Task.FromResult(apiResult);
        }

        /// <summary>
        /// 調閱申請單資訊(新增-需求資訊)
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("GetRetrieveDetails")]
        public async Task<ApiResultModelByObject> GetRetrieveDetails([FromQuery] string retrieve_number)
        {
            ApiResultModelByObject apiResult = new ApiResultModelByObject();

            List<OtherApplicationDetailViewModel> result = PaperLendingApplicationService.GetOtherApplicationDetail(retrieve_number);
            apiResult.listData = result;
            apiResult.totalCount = result.Count();
            apiResult.rtnSuccess = true;
            InitLogRecord<object>(new(), logRecord =>
            {
                StringBuilder stringBuilder = new();
                stringBuilder.AppendLine($"單號:  {retrieve_number}");

                logRecord.Detail = stringBuilder.ToString();
            });

            return await Task.FromResult(apiResult);
        }

        /// <summary>
        /// 借出明細查詢(新增-借出明細)
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("GetLendingDetail")]
        public async Task<ApiResultModelByObject> GetLendingDetail([FromQuery] string? contract_number, string? paper_code, int? detail_limit)
        {
            ApiResultModelByObject apiResult = new ApiResultModelByObject();

            List<GetLendingDetailViewMode> result = PaperLendingApplicationService.GetLendingDetail(contract_number, paper_code, detail_limit);
            apiResult.listData = result;
            apiResult.totalCount = result.Count();
            apiResult.rtnSuccess = true;
            InitLogRecord<object>(new(), logRecord =>
            {
                StringBuilder stringBuilder = new();
                stringBuilder.AppendLine($"單號:  {paper_code}");

                logRecord.Detail = stringBuilder.ToString();
            });

            return await Task.FromResult(apiResult);
        }

        /// <summary>
        /// 借出明細批次查詢(新增-借出明細)
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("BatchGetLendingDetail")]
        public async Task<ApiResultModelByObject> BatchGetLendingDetail([FromQuery] string? contract_number, string? paper_code)
        {
            ApiResultModelByObject apiResult = new ApiResultModelByObject();

            List<GetLendingDetailViewMode> result = PaperLendingApplicationService.BatchGetLendingDetail(contract_number, paper_code);
            apiResult.listData = result;
            apiResult.totalCount = result.Count();
            apiResult.rtnSuccess = true;
            InitLogRecord<object>(new(), logRecord =>
            {
                StringBuilder stringBuilder = new();
                stringBuilder.AppendLine($"單號:  {paper_code}");

                logRecord.Detail = stringBuilder.ToString();
            });

            return await Task.FromResult(apiResult);
        }

        /// <summary>
        /// 查詢合約與紙本列表
        /// </summary>
        /// <param name="contract_number"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetLendingContractNumber")]
        public async Task<ApiResultModelByObject> GetLendingContractNumber([FromQuery] string contract_number, int? detail_limit = null)
        {
            ApiResultModelByObject apiResult = new ApiResultModelByObject();

            List<GetLendContractViewModel> result = PaperLendingApplicationService.GetContractNumber(contract_number, detail_limit);
            apiResult.listData = result;
            apiResult.totalCount = result.Count();
            apiResult.rtnSuccess = true;
            InitLogRecord<object>(new(), logRecord =>
            {
                StringBuilder stringBuilder = new();
                stringBuilder.AppendLine($"單號:  {contract_number}");

                logRecord.Detail = stringBuilder.ToString();
            });

            return await Task.FromResult(apiResult);
        }

        /// <summary>
        /// 新增/修改時查詢詳細資料
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("GetPaperLendingApplication")]
        public async Task<ApiResultModelByObject> GetPaperLendingApplication([FromQuery] int lendId)
        {
            ApiResultModelByObject apiResult = new ApiResultModelByObject();
            var res = PaperLendingApplicationService.GetPaperLendingApplication(lendId);

            apiResult.listData = res;
            apiResult.rtnSuccess = true;
            InitLogRecord<object>(new(), logRecord =>
            {
                StringBuilder stringBuilder = new();
                stringBuilder.AppendLine($"借出單號:  {res.PersonnelInfo.LendNumber}");

                logRecord.Detail = stringBuilder.ToString();
            });

            return await Task.FromResult(apiResult);
        }

        /// <summary>
        /// 送出借出申請
        /// 如lend_id為空值為新增，反之為修改
        /// lend_status 為 01:暫存，02:送出
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [Route("SendLending")]
        public async Task<ApiResultModelByObject> SendLending([FromBody] insPaperLendingApplication data)
        {
            ApiResultModelByObject apiResult = new ApiResultModelByObject();

            LoanDetailsView oldData = new LoanDetailsView();
            List<PaperLendingApplicationDetailViewModel> oldDetail = new List<PaperLendingApplicationDetailViewModel>();
            if (data.lend_id != null && data.lend_id != 0)
            {
                oldData = PaperLendingApplicationService.GetPaperLendingApplication(data.lend_id.Value);
                oldDetail = PaperLendingApplicationService.GetPaperLendingDetails(data.lend_id.Value);
            }

            apiResult.listData = PaperLendingApplicationService.SendLending(data);
            apiResult.rtnSuccess = true;
            InitLogRecord(data, logRecord =>
            {
                StringBuilder stringBuilder = new();
                if (!string.IsNullOrEmpty(data.lend_number)) stringBuilder.AppendLine($"借出單號: {data.lend_number}");
                if (!string.IsNullOrEmpty(data.lend_fill_emplid)) stringBuilder.AppendLine($"填單人工號: {data.lend_fill_emplid}");
                if (!string.IsNullOrEmpty(data.lend_fill_deptid)) stringBuilder.AppendLine($"填單人部門代號: {data.lend_fill_deptid}");
                if (!string.IsNullOrEmpty(data.lend_handler_emplid)) stringBuilder.AppendLine($"經辦人工號: {data.lend_handler_emplid}");
                if (!string.IsNullOrEmpty(data.lend_handler_deptid)) stringBuilder.AppendLine($"經辦人部門代號: {data.lend_handler_deptid}");
                if (!string.IsNullOrEmpty(data.lend_status)) stringBuilder.AppendLine($"申請單狀態: {PaperBasicDataService.GetTypeString("lib_lendStatus", data.lend_status)}");
                if (!string.IsNullOrEmpty(data.void_reason)) stringBuilder.AppendLine($"作廢原因: {data.void_reason}");
                if (!string.IsNullOrEmpty(data.retrieve_number)) stringBuilder.AppendLine($"其他申請單號: {data.retrieve_number}");
                if (data.borrow_days.HasValue && data.borrow_days.Value != 0) stringBuilder.AppendLine($"借出天數: {data.borrow_days}");
                if (!string.IsNullOrEmpty(data.demand_reason)) stringBuilder.AppendLine($"申請原因: {data.demand_reason}");
                if (!string.IsNullOrEmpty(data.retrieve_reason)) stringBuilder.AppendLine($"其它說明/備註: {data.retrieve_reason}");
                if (data.details != null && data.details.Count > 0) stringBuilder.AppendLine($"紙本編號: {string.Join(",", data.details.Select(x => x.paper_code).ToList())}");

                logRecord.Detail = stringBuilder.ToString();

                if (data.lend_id != null && data.lend_id != 0 && oldData != null && oldData.PersonnelInfo != null)
                {
                    stringBuilder = new StringBuilder();

                    if (!string.IsNullOrEmpty(oldData.PersonnelInfo.LendNumber)) stringBuilder.AppendLine($"借出單號: {oldData.PersonnelInfo.LendNumber}");
                    if (!string.IsNullOrEmpty(oldData.PersonnelInfo.LendFillEmplid)) stringBuilder.AppendLine($"填單人工號: {oldData.PersonnelInfo.LendFillEmplid}");
                    if (!string.IsNullOrEmpty(oldData.PersonnelInfo.LendFillDeptid)) stringBuilder.AppendLine($"填單人部門代號: {oldData.PersonnelInfo.LendFillDeptid}");
                    if (!string.IsNullOrEmpty(oldData.PersonnelInfo.LendHandlerEmplid)) stringBuilder.AppendLine($"經辦人工號: {oldData.PersonnelInfo.LendHandlerEmplid}");
                    if (!string.IsNullOrEmpty(oldData.PersonnelInfo.LendHandlerDeptid)) stringBuilder.AppendLine($"經辦人部門代號: {oldData.PersonnelInfo.LendHandlerDeptid}");
                    if (!string.IsNullOrEmpty(oldData.PersonnelInfo.LendStatus)) stringBuilder.AppendLine($"申請單狀態: {PaperBasicDataService.GetTypeString("lib_lendStatus", oldData.PersonnelInfo.LendStatus)}");
                    if (!string.IsNullOrEmpty(oldData.PersonnelInfo.VoidReason)) stringBuilder.AppendLine($"作廢原因: {oldData.PersonnelInfo.VoidReason}");
                    if (!string.IsNullOrEmpty(oldData.DemandInfo.RetrieveNumber)) stringBuilder.AppendLine($"調閱單號: {oldData.DemandInfo.RetrieveNumber}");
                    if (oldData.DemandInfo.BorrowDays.HasValue && oldData.DemandInfo.BorrowDays.Value != 0) stringBuilder.AppendLine($"借出天數: {oldData.DemandInfo.BorrowDays}");
                    if (!string.IsNullOrEmpty(oldData.DemandInfo.DemandReason)) stringBuilder.AppendLine($"申請原因: {oldData.DemandInfo.DemandReason}");
                    if (!string.IsNullOrEmpty(oldData.DemandInfo.RetrieveReason)) stringBuilder.AppendLine($"其它說明/備註: {oldData.DemandInfo.RetrieveReason}");
                    if (oldDetail != null && oldDetail.Count > 0) stringBuilder.AppendLine($"紙本編號: {string.Join(",", oldDetail.Select(x => x.paper_code).ToList())}");

                    logRecord.DetailFormer = stringBuilder.ToString();
                }
            });

            return await Task.FromResult(apiResult);
        }

        /// <summary>
        /// 查詢填單人資訊
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("GetLendFillEmpInfo")]
        public async Task<ApiResultModelByObject> GetLendFillEmpInfo([FromQuery] int lend_id)
        {
            ApiResultModelByObject apiResult = new ApiResultModelByObject();
            var res = PaperLendingApplicationService.GetLendFillEmpInfo(lend_id);
            apiResult.listData = res;
            apiResult.rtnSuccess = true;
            InitLogRecord<object>(new(), logRecord =>
            {
                StringBuilder stringBuilder = new();
                stringBuilder.AppendLine($"借出單號:  {res.lend_number}");

                logRecord.Detail = stringBuilder.ToString();
            });

            return await Task.FromResult(apiResult);
        }

        /// <summary>
        /// 查詢借出明細
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("GetLendingPaperInfo")]
        public async Task<ApiResultModelByObject> GetLendingPaperInfo([FromQuery] int lend_id)
        {
            ApiResultModelByObject apiResult = new ApiResultModelByObject();

            var oldData = PaperLendingApplicationService.GetPaperLendingApplicationByLendID(lend_id);

            List<LendingPaperInfo> res = PaperLendingApplicationService.GetLendingPaperInfo(lend_id);
            apiResult.listData = res;
            apiResult.totalCount = res.Count();
            apiResult.rtnSuccess = true;
            InitLogRecord<object>(new(), logRecord =>
            {
                StringBuilder stringBuilder = new();
                stringBuilder.AppendLine($"借出單號:  {oldData.lend_number}");

                logRecord.Detail = stringBuilder.ToString();
            });

            return await Task.FromResult(apiResult);
        }

        /// <summary>
        /// 查詢掃卡人員
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("GetUserByBadgeId")]
        public async Task<ApiResultModelByObject> GetUserByBadgeId([FromQuery] int lend_id, string? retrieve_number, string? badge_id, string? empid)
        {
            ApiResultModelByObject apiResult = new ApiResultModelByObject();

            UserEntityModel_Badge res = PaperLendingApplicationService.GetUserByBadgeId_new(lend_id, retrieve_number, badge_id);
            if (res == null)
            {
                apiResult.messageTitle = "非有權取件者";
                apiResult.messageContent.Add("此人員非有權取件者");
                apiResult.rtnSuccess = false;
            }
            else
            {
                apiResult.listData = res;
                apiResult.rtnSuccess = true;
                InitLogRecord<object>(new(), logRecord =>
                {
                    StringBuilder stringBuilder = new();
                    if (res.logging_type == 0) stringBuilder.AppendLine($"工號:  {badge_id}");
                    if (res.logging_type == 1) stringBuilder.AppendLine($"卡號:  {badge_id}");

                    logRecord.Detail = stringBuilder.ToString();
                });
            }

            return await Task.FromResult(apiResult);
        }

        /// <summary>
        /// 取件
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [Route("PickLending")]
        public async Task<ApiResultModelByObject> PickLending([FromBody] pickLendingPara data)
        {
            ApiResultModelByObject apiResult = new ApiResultModelByObject();

            var oldData = PaperLendingApplicationService.GetPaperLendingApplicationByLendID(data.lend_id);
            var oldLoan = PaperLendingApplicationService.GetPaperLendingApplication(data.lend_id);
            var pickdetail = PaperLendingApplicationService.GetPickPaperCode(data.lend_id, data.detail_id_List);

            apiResult.listData = PaperLendingApplicationService.PickLending(data);
            apiResult.rtnSuccess = true;
            InitLogRecord(data, logRecord =>
            {
                StringBuilder stringBuilder = new();
                stringBuilder.AppendLine($"借出單號:  {oldData.lend_number}");
                if (!string.IsNullOrEmpty(oldLoan.DemandInfo.RetrieveNumber)) stringBuilder.AppendLine($"其他申請單號:  {oldLoan.DemandInfo.RetrieveNumber}");
                if (!string.IsNullOrEmpty(data.pickup_status)) stringBuilder.AppendLine($"取件方式:  {PaperBasicDataService.GetTypeString("lib_pickupStatus", data.pickup_status)}");
                if (data.pickup_time != null) stringBuilder.AppendLine($"取件日期:  {TimeZoneInfoConvertHelper.ConvertDateByTimeZoneByUtc(data.pickup_time.Value).ToString("yyyy/MM/dd")}");
                if (data.should_return_time != null) stringBuilder.AppendLine($"應歸還日期:  {TimeZoneInfoConvertHelper.ConvertDateByTimeZoneByUtc(data.should_return_time.Value).ToString("yyyy/MM/dd HH:mm:ss")}");
                if (data.actual_pickup_time != null) stringBuilder.AppendLine($"實際取件時間:  {TimeZoneInfoConvertHelper.ConvertDateByTimeZoneByUtc(data.actual_pickup_time.Value).ToString("yyyy/MM/dd HH:mm:ss")}");
                if (!string.IsNullOrEmpty(data.consignment_number)) stringBuilder.AppendLine($"托運單號:  {data.consignment_number}");
                if (!string.IsNullOrEmpty(data.pickup_emplid)) stringBuilder.AppendLine($"取件者:  {data.pickup_emplid}");
                if (pickdetail != null && pickdetail.Count > 0) stringBuilder.AppendLine($"紙本編號:  {string.Join(",", pickdetail)}");
                if (!string.IsNullOrEmpty(data.selfpickup_emplid)) stringBuilder.AppendLine($"自取者工號:  {data.selfpickup_emplid}");
                if (!string.IsNullOrEmpty(data.selfpickup_badgeid)) stringBuilder.AppendLine($"自取者卡號:  {data.selfpickup_badgeid}");

                logRecord.Detail = stringBuilder.ToString();
            });

            return await Task.FromResult(apiResult);
        }


        /// <summary>
        /// 刪除
        /// </summary>
        /// <returns></returns>
        [HttpDelete]
        [Route("DeleteLending")]
        public async Task<ApiResultModelByObject> DeleteLending([FromQuery] int lend_id)
        {
            ApiResultModelByObject apiResult = new ApiResultModelByObject();

            var oldData = PaperLendingApplicationService.GetPaperLendingApplication(lend_id);
            var oldDetail = PaperLendingApplicationService.GetPaperLendingDetails(lend_id);
            apiResult.listData = PaperLendingApplicationService.DeleteLending(lend_id);
            apiResult.rtnSuccess = true;
            InitLogRecord<object>(new(), logRecord =>
            {
                StringBuilder stringBuilder = new();
                if (oldData != null && !string.IsNullOrEmpty(oldData.DemandInfo.RetrieveNumber)) stringBuilder.AppendLine($"其他申請單號:  {oldData.DemandInfo.RetrieveNumber}");
                if (oldDetail != null && oldDetail.Count > 0) stringBuilder.AppendLine($"紙本編號:  {string.Join(",", oldDetail.Select(x => x.paper_code).ToList())}");
                logRecord.Detail = stringBuilder.ToString();
            });

            return await Task.FromResult(apiResult);
        }

        /// <summary>
        /// 作廢
        /// </summary>
        /// <returns></returns>
        [HttpPut]
        [Route("VoidLending")]
        public async Task<ApiResultModelByObject> VoidLending([FromQuery] int lend_id, string void_reason)
        {
            ApiResultModelByObject apiResult = new ApiResultModelByObject();
            var oldData = PaperLendingApplicationService.GetPaperLendingApplication(lend_id);
            var oldDetail = PaperLendingApplicationService.GetPaperLendingDetails(lend_id);

            apiResult.listData = PaperLendingApplicationService.VoidLending(lend_id, void_reason);
            apiResult.rtnSuccess = true;
            InitLogRecord<object>(new(), logRecord =>
            {
                StringBuilder stringBuilder = new();
                if (oldData != null && !string.IsNullOrEmpty(oldData.PersonnelInfo.LendNumber)) stringBuilder.AppendLine($"借出單號:  {oldData.PersonnelInfo.LendNumber}");
                if (oldData != null && !string.IsNullOrEmpty(oldData.DemandInfo.RetrieveNumber)) stringBuilder.AppendLine($"其他申請單號:  {oldData.DemandInfo.RetrieveNumber}");
                if (!string.IsNullOrEmpty(void_reason)) stringBuilder.AppendLine($"作廢原因:  {void_reason}");
                if (oldDetail != null && oldDetail.Count > 0) stringBuilder.AppendLine($"紙本編號:  {string.Join(",", oldDetail.Select(x => x.paper_code).ToList())}");

                logRecord.Detail = stringBuilder.ToString();
            });

            return await Task.FromResult(apiResult);
        }

        /// <summary>
        /// 查詢是否有暫存單
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("GetTempApplication")]
        public async Task<ApiResultModelByObject> GetTempApplication([FromQuery] string? retrieve_number)
        {
            ApiResultModelByObject apiResult = new ApiResultModelByObject();

            apiResult.listData = PaperLendingApplicationService.GetTempLoanDetailsView(retrieve_number);
            apiResult.rtnSuccess = true;

            return await Task.FromResult(apiResult);
        }

        /// <summary>
        /// 查詢有新的紙本
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("GetNewOtherApplicationPaperList")]
        public async Task<ApiResultModelByObject> GetNewOtherApplicationPaperList([FromQuery] int lend_id)
        {
            ApiResultModelByObject apiResult = new ApiResultModelByObject();

            apiResult.listData = PaperLendingApplicationService.GetNewOtherApplicationPaperList(lend_id);
            apiResult.rtnSuccess = true;

            return await Task.FromResult(apiResult);
        }
    }
}
