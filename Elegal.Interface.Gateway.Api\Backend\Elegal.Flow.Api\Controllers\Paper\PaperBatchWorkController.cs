﻿using Elegal.Flow.Api.Services.Paper;
using Elegal.Interface.Api.Common.Control.Filter;
using Elegal.Interface.Api.Common.Control.Web;
using Elegal.Interface.Api.Common.Model.ParaModel.FlowApi;
using Elegal.Interface.Api.Common.Model.ResultModel;
using Elegal.Interface.Api.Common.Model.ResultModel.FlowApi;
using Elegal.Interface.Api.Common.Model.ViewModel.FlowApi;
using Elegal.Orm.Dtos;
using Microsoft.AspNetCore.Mvc;
using System.Text;

namespace Elegal.Flow.Api.Controllers.Paper
{
    /// <summary>
    /// 紙本批次作業
    /// </summary>
    [Route("[controller]")]
    [ApiController]
    public class PaperBatchWorkController : BaseController
    {
        #region 查詢存放位置清單
        /// <summary>
        /// 查詢存放位置清單
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("GetBatchPositionList")]
        public async Task<ApiResultModelByObject> GetBatchPositionList()
        {
            ApiResultModelByObject apiResult = new();

            apiResult.listData = PaperBatchWorkService.GetBatchPositionList();
            apiResult.rtnSuccess = true;

            return await Task.FromResult(apiResult);
        }
        #endregion

        #region 查詢紙本基本資料
        /// <summary>
        /// 查詢紙本基本資料
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [Route("Query")]
        public async Task<ApiResultModelByObject> QueryPaperBasicData([FromBody] qryPaperBatchData qry)
        {
            ApiResultModelByObject apiResult = new ApiResultModelByObject();

            PageResult<PaperBatchDataViewModel> result = PaperBatchWorkService.QueryPaperBatchData(qry);
            apiResult.listData = result.Data;
            apiResult.totalCount = result.TotalRows;
            apiResult.rtnSuccess = true;
            InitLogRecord(qry, logRecord =>
            {
                StringBuilder stringBuilder = new();
                if (!string.IsNullOrEmpty(qry.paper_code))
                    stringBuilder.AppendLine(string.Format(ActionFilter.GetMultilingualValue("PaperBatchWork_contractNumber", true), qry.paper_code));
                if (!string.IsNullOrEmpty(qry.paper_name))
                    stringBuilder.AppendLine(string.Format(ActionFilter.GetMultilingualValue("PaperBatchWork_contractName", true), qry.paper_name));
                if (!string.IsNullOrEmpty(qry.batch_entry_status))
                    stringBuilder.AppendLine(string.Format(ActionFilter.GetMultilingualValue("PaperBatchWork_paperEntryStatus", true), PaperBasicDataService.GetTypeString("lib_paperEntryStatus", qry.batch_entry_status)));
                if (qry.batch_position != null && qry.batch_position.Count > 0)
                    stringBuilder.AppendLine(string.Format(ActionFilter.GetMultilingualValue("PaperBatchWork_batchPosition", true), string.Join(",", qry.batch_position)));
                if (qry.start_time != null)
                    stringBuilder.AppendLine(string.Format(ActionFilter.GetMultilingualValue("PaperBatchWork_startTime", true), qry.start_time.Value.AddHours(8).ToString("yyyy/MM/dd")));
                if (qry.end_time != null)
                    stringBuilder.AppendLine(string.Format(ActionFilter.GetMultilingualValue("PaperBatchWork_endTime", true), qry.end_time.Value.ToString("yyyy/MM/dd")));

                logRecord.Detail = stringBuilder.ToString();
            });

            return await Task.FromResult(apiResult);
        }
        #endregion

        #region 根據掃碼/輸入查詢對應的合約編號與紙本名稱
        /// <summary>
        /// 根據掃碼/輸入查詢對應的合約編號與紙本名稱
        /// </summary>
        [HttpGet]
        [Route("GetPaperBatchDataList")]
        public async Task<ApiResultModelByObject> GetPaperBatchDataList([FromQuery] string paper_code)
        {
            ApiResultModelByObject apiResult = new ApiResultModelByObject();

            List<PaperBatchDataViewModel> result = PaperBatchWorkService.GetPaperBatchDataList(paper_code);
            apiResult.listData = result;
            apiResult.totalCount = result.Count();
            apiResult.rtnSuccess = true;
            InitLogRecord<object>(new(), logRecord =>
            {
                StringBuilder stringBuilder = new();
                stringBuilder.AppendLine(string.Format(ActionFilter.GetMultilingualValue("PaperBatchWork_paperCode", true), paper_code));
                logRecord.Detail = stringBuilder.ToString();
            });

            return await Task.FromResult(apiResult);
        }
        #endregion

        #region 新增批次資料
        /// <summary>
        /// 新增批次資料
        /// </summary>
        [HttpPost]
        [Route("InsertPaperBatchData")]
        public async Task<ApiResultModelByObject> InsertPaperBatchData([FromBody] insertPaperBatchData batchData)
        {
            ApiResultModelByObject apiResult = new ApiResultModelByObject();

            PaperBasicWorkResult res = PaperBatchWorkService.InsertPaperBatchData(batchData);

            if (res.success_list != null && res.success_list.Count > 0)
            {
                apiResult.messageTitle = ActionFilter.GetMultilingualValue("custom:messageTitle:createSuccess");
                apiResult.messageContent.Add($"{ActionFilter.GetMultilingualValue("custom:messageContent:Inbound_Status")} {PaperBasicDataService.GetTypeString("lib_paperEntryStatus", batchData.batch_entry_status)}");
                apiResult.messageContent.Add($"{ActionFilter.GetMultilingualValue("custom:messageContent:Storage_Location")} {batchData.batch_position}");
                apiResult.messageContent.Add($"{ActionFilter.GetMultilingualValue("commonWord:add")} {string.Format(ActionFilter.GetMultilingualValue("commonWord:count"), res.success_list.Count.ToString())}：<br />{string.Join(", ", res.success_list)}");
            }
            else
            {
                apiResult.messageTitle = ActionFilter.GetMultilingualValue("custom:messageTitle:createFail");
            }
            if (res.fail_list != null && res.fail_list.Count > 0)
            {
                apiResult.messageContent.Add($"{ActionFilter.GetMultilingualValue("custom:messageContent:batchPaperWork_LostOrDestroyed")} {string.Format(ActionFilter.GetMultilingualValue("commonWord:count"), res.fail_list.Count.ToString())}：<br />{string.Join(", ", res.fail_list)}");
            }
            apiResult.listData = true;
            apiResult.rtnSuccess = true;
            InitLogRecord(batchData, logRecord =>
            {
                StringBuilder stringBuilder = new();
                stringBuilder.AppendLine(string.Format(ActionFilter.GetMultilingualValue("PaperBatchWork_batchPosition", true), batchData.batch_position));
                stringBuilder.AppendLine(string.Format(ActionFilter.GetMultilingualValue("PaperBatchWork_paperEntryStatus", true), PaperBasicDataService.GetTypeString("lib_paperEntryStatus", batchData.batch_entry_status)));
                stringBuilder.AppendLine(string.Format(ActionFilter.GetMultilingualValue("PaperBatchWork_paperRemark", true), batchData.batch_remarks));
                if (batchData.batch_entry_status == "06")
                {
                    stringBuilder.AppendLine(string.Format(ActionFilter.GetMultilingualValue("PaperBatchWork_desDate", true), batchData.batch_destory_time.Value.ToString("yyyy/MM/dd")));
                    stringBuilder.AppendLine(string.Format(ActionFilter.GetMultilingualValue("PaperBatchWork_desReason", true), batchData.batch_destory_reason));
                }
                if (batchData.batch_entry_status == "07")
                {
                    stringBuilder.AppendLine(string.Format(ActionFilter.GetMultilingualValue("PaperBatchWork_losDate", true), batchData.batch_lost_time.Value.ToString("yyyy/MM/dd")));
                    stringBuilder.AppendLine(string.Format(ActionFilter.GetMultilingualValue("PaperBatchWork_losReason", true), batchData.batch_lost_reason));
                }
                if (res.success_list != null && res.success_list.Count > 0)
                    stringBuilder.AppendLine(string.Format(ActionFilter.GetMultilingualValue("PaperBatchWork_addPaper", true), string.Join(",", res.success_list)));
                if (res.fail_list != null && res.fail_list.Count > 0)
                    stringBuilder.AppendLine(string.Format(ActionFilter.GetMultilingualValue("PaperBatchWork_losPaper", true), string.Join(",", res.fail_list)));

                logRecord.Detail = stringBuilder.ToString();
            });

            return await Task.FromResult(apiResult);
        } 
        #endregion
    }
}
