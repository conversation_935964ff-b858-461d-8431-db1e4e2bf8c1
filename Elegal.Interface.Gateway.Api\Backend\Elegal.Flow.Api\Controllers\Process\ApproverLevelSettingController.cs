﻿using Elegal.Flow.Api.Services.Process;
using Elegal.Interface.Api.Common.Control.Filter;
using Elegal.Interface.Api.Common.Control.Web;
using Elegal.Interface.Api.Common.Model.Enum;
using Elegal.Interface.Api.Common.Model.ParaModel.FlowApi;
using Elegal.Interface.Api.Common.Model.ResultModel;
using Elegal.Interface.ApiData.Service.FuncService;
using Elegal.Interface.ApiData.Service.Model.Condition;
using Elegal.Interface.ApiData.Service.Model.DbModel;
using Elegal.Interface.ApiData.Service.Model.ViewModel;
using Elegal.Orm.Utils;
using Microsoft.AspNetCore.Mvc;
using System.Text;
#nullable disable
namespace Elegal.Flow.Api.Controllers.Process
{
    /// <summary>
    /// 签核层级设定
    /// </summary>
    [Route("[controller]")]
    [ApiController]
    public class ApproverLevelSettingController : BaseController
    {
        /// <summary>
        /// 查询列表数据
        /// </summary>
        /// <param name="condition">查询参数</param>
        /// <returns></returns>
        [HttpPost]
        [Route("GetDataList")]
        public async Task<ApiResultModelByObject> GetDataList([FromBody] ApproverLevelSettingSearchModel condition)
        {
            ApiResultModelByObject apiResult = new()
            {
                listData = ApproverLevelSettingService.GetDataList(condition),
                rtnSuccess = true
            };
            InitLogRecord(logRecord =>
            {
                StringBuilder stringBuilder = new();
                if (condition.Companys.Count != 0)
                    stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("ApproverLevelSetting_companys", true)}：{string.Join(",", condition.Companys)}");
                if (condition.FilterList.Count != 0)
                {
                    stringBuilder.AppendLine(CommonUtil.GetDivisionLine(ActionFilter.GetMultilingualValue("ApproverLevelSetting_filter", true)));
                    stringBuilder.AppendLine(string.Join("\r\n", condition.FilterList.Select(s => $"{ActionFilter.GetMultilingualValue($"ApproverLevelSetting_export:{Nomenclature.ToCamel(s.Key)}", true)}：{s.Value}")));
                }
                logRecord.Detail = stringBuilder.ToString();
            });
            return await Task.FromResult(apiResult);
        }

        /// <summary>
        /// 汇出日志记录
        /// </summary>
        /// <param name="condition">查询参数</param>
        /// <returns></returns>
        [HttpPost]
        [Route("ExportLog")]
        public async Task<ApiResultModelByObject> ExportLog([FromBody] ApproverLevelSettingSearchModel condition)
        {
            ActionFilter.InitLogRecord(logRecord =>
            {
                StringBuilder stringBuilder = new();
                if (condition.Companys.Count != 0)
                    stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("ApproverLevelSetting_companys", true)}：{string.Join(",", condition.Companys)}");
                if (condition.FilterList.Count != 0)
                {
                    stringBuilder.AppendLine(CommonUtil.GetDivisionLine(ActionFilter.GetMultilingualValue("ApproverLevelSetting_filter", true)));
                    stringBuilder.AppendLine(string.Join("\r\n", condition.FilterList.Select(s => $"{ActionFilter.GetMultilingualValue($"ApproverLevelSetting_export:{Nomenclature.ToCamel(s.Key)}", true)}：{s.Value}")));
                }
                logRecord.Detail = stringBuilder.ToString();
            });
            ApiResultModelByObject apiResult = new() { rtnSuccess = true };
            return await Task.FromResult(apiResult);
        }

        /// <summary>
        /// 编辑数据
        /// </summary>
        /// <param name="condition"></param>
        /// <returns></returns>
        [HttpPut]
        [Route("EditData")]
        public async Task<ApiResultModelByObject> EditData(FlowSignLevelViewModel condition)
        {
            condition.ModifyTime = DateTime.UtcNow;
            condition.ModifyUser = MvcContext.UserInfo.current_emp;
            return await Task.FromResult(ApproverLevelSettingService.EditData(condition));
        }

        /// <summary>
        /// 新增数据
        /// </summary>
        /// <param name="condition"></param>
        /// <returns></returns>
        [HttpPut]
        [Route("AddData")]
        public async Task<ApiResultModelByObject> AddData(FlowSignLevelViewModel condition)
        {
            return await Task.FromResult(ApproverLevelSettingService.AddData(condition));
        }
    }
}
