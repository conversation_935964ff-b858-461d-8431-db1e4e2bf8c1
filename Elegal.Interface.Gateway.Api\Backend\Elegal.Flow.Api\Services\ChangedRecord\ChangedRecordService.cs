﻿using Elegal.Interface.Api.Common.Model.ParaModel.FlowApi.ChangedRecord;
using Elegal.Interface.Api.Common.Model.ResultModel.FlowApi.ChangedRecord;
using Elegal.Orm.Dtos;
using Elegal.Orm;
using System.Text;
using Elegal.Interface.Api.Common.Model.ParaModel.FlowApi;
using Elegal.Flow.Api.Repository.LendAppliction;
using Elegal.Flow.Api.Repository.ChangedRecord;
using Elegal.Interface.Api.Common.Control.Filter;
using Elegal.Interface.Api.Common.FuncHelper;
using Elegal.Interface.Api.Common.Model.ParaModel.FlowApi.LendAppliction;
using Elegal.Interface.Api.Common.Model.ResultModel.FlowApi.LendAppliction;
using Elegal.Interface.Api.Common.Model.ViewModel;
using NPOI.SS.UserModel;
using NPOI.XSSF.UserModel;
using Elegal.Interface.Api.Common.FuncService;

namespace Elegal.Flow.Api.Services.ChangedRecord;

/// <summary>
/// 異動記錄
/// </summary>
public static class ChangedRecordService
{

    private static ChangedRecordRepository _Repository = new ChangedRecordRepository();
    /// <summary>
    /// 異動案件查詢
    /// </summary>
    /// <param name="serachPara"></param>
    /// <returns></returns>
    public static ChangePageResult<QryChangeRecordResultModel> QueryPageViewResult(QryChangeRecordPara serachPara)
    {
        ChangePageResult<ChangeRecordResultDBmodel> dbresult = _Repository.GetPageDBResult(serachPara, MvcContext.UserInfo.logging_locale); //原始數據庫結果

        List<QryChangeRecordResultModel> list = new List<QryChangeRecordResultModel>();//保存結果

        var user = MvcContext.UserInfo;

        var groupList = dbresult.Data.GroupBy(e => new { e.batch_id, e.work_key, e.change_type, e.change_next_type, e.empid, e.is_modify_annex, });
        foreach (var g in groupList)
        {
            //一個批次的異動
            var thatlist = g.ToList(); //當前批次內的異動單據 (正常情況下這裡至少有一條記錄)
            //為當前批次賦值
            QryChangeRecordResultModel item = new QryChangeRecordResultModel()
            {
                batch_id = g.Key.batch_id,
                work_key = g.Key.work_key,
                change_type = g.Key.change_type, //正常數據 batch_id 相同的話，這個字段一定一樣
                change_next_type = g.Key.change_next_type, //正常數據 batch_id 相同的話，這個字段一定一樣
                empid = g.Key.empid, //正常數據 batch_id 相同的話，這個字段一定一樣
                name = thatlist.FirstOrDefault()?.name ?? string.Empty, //正常數據 batch_id 相同的話，這個字段一定一樣
                is_modify_annex = g.Key.is_modify_annex, //正常數據 batch_id 相同的話，這個字段一定一樣
                create_time = thatlist.FirstOrDefault()?.create_time ?? DateTime.Now, //正常數據 batch_id 相同的話，這個字段一定一樣
                change_type_name = thatlist.FirstOrDefault()?.change_type_name ?? string.Empty, //正常數據 batch_id 相同的話，這個字段一定一樣
                change_next_type_name = thatlist.FirstOrDefault()?.change_next_type_name ?? string.Empty, //正常數據 batch_id 相同的話，這個字段一定一樣
            };

            //時區問題
            item.create_time = item.create_time.ConvertDateByTimeZoneByUtc(user.time_zone);

            item.ChangeInfos = new List<ChangeInfo>();

            foreach (var thatitem in thatlist) //循環批次內異動單據
            {

                string content_before = thatitem.content_before ?? string.Empty;
                string content_after = thatitem.content_after ?? string.Empty;

                #region 處理時間
                //處理時間， 如果 change_code_type 為 4表示存儲的是UTC時間要根據用戶時區轉換成對應時間的字符串
                if (thatitem.change_code_type == "4")
                {
                    List<string> beforeTimeList = thatitem.content_before?.Split(',')?.Select(e =>
                    {
                        if (string.IsNullOrWhiteSpace(e)) return string.Empty;
                        try
                        {
                            return Convert.ToDateTime(e) //（C# 会自动将UTC时间字符串 转换成local时间） 默认得到的是Local时间
                            .ToUniversalTime()  //转换为utc
                            .ConvertDateByTimeZoneByUtc(user.time_zone) //在转换为用户时区
                            .ToString("yyyy/MM/dd HH:mm:ss");
                        }
                        catch (Exception)
                        {
                            return string.Empty;

                        }
                    })
                    .Where(e => !string.IsNullOrWhiteSpace(e)).ToList() ?? new List<string>();
                    content_before = string.Join(",", beforeTimeList);

                    List<string> afterTimeList = thatitem.content_after?.Split(',')?.Select(e =>
                    {
                        if (string.IsNullOrWhiteSpace(e)) return string.Empty;
                        try
                        {
                            return Convert.ToDateTime(e).ToUniversalTime().ConvertDateByTimeZoneByUtc(user.time_zone).ToString("yyyy/MM/dd HH:mm:ss");
                        }
                        catch (Exception)
                        {
                            return string.Empty;

                        }
                    })
                    .Where(e => !string.IsNullOrWhiteSpace(e)).ToList() ?? new List<string>();
                    content_after = string.Join(",", afterTimeList);
                }
                #endregion

                if (string.IsNullOrWhiteSpace(thatitem.children_code))
                { //如果沒有異動子欄位
                    item.ChangeInfos.Add(new ChangeInfo() //直接添加到當前批次的異動信息
                    {
                        change_code = thatitem.change_code,
                        change_code_ename = thatitem.change_code_ename,
                        change_code_cname = thatitem.change_code_cname,
                        content_before = content_before,
                        content_after = content_after,
                        HasChildrenCode = false
                    });
                }
                else //如果此條數據有 有異動子欄位
                {
                    //在當前批次內的異動信息裡找到自己的父異動欄位
                    ChangeInfo? pci = item.ChangeInfos.FirstOrDefault(e => e.change_code == thatitem.change_code && e.HasChildrenCode);
                    if (pci == null)
                    { //如果沒找到，說明我是唯一的，或者是第一個 數據本 change_code 的  children_code 直接創建一個
                        pci = new ChangeInfo()
                        {
                            change_code = thatitem.change_code,
                            change_code_ename = thatitem.change_code_ename,
                            change_code_cname = thatitem.change_code_cname,
                            HasChildrenCode = true,
                            ChildrenChangeInfos = new List<ChangeInfo>()
                        };
                        item.ChangeInfos.Add(pci); //把剛創建的異動信息 直接添加到當前批次的異動信息
                    }
                    pci.ChildrenChangeInfos.Add(new ChangeInfo() //把自己添加到父 change_code 裡
                    {
                        change_code = thatitem.children_code,
                        change_code_ename = thatitem.children_code_ename,
                        change_code_cname = thatitem.children_code_cname,
                        content_before = content_before,
                        content_after = content_after,
                        HasChildrenCode = false
                    });
                }
            };

            list.Add(item); //將當前批次數據添加到結果集
        }


        //轉換後的結果
        ChangePageResult<QryChangeRecordResultModel> res = new ChangePageResult<QryChangeRecordResultModel>()
        {
            TotalRows = dbresult.TotalRows,
            Data = list,
            ExportExcelCount = dbresult.ExportExcelCount
        };
        return res;

    }

    /// <summary>
    /// 異動案件查詢，導出
    /// </summary>
    /// <param name="para"></param>
    /// <param name="user"></param>
    /// <returns></returns>
    public static byte[] ChangeRecordsExport(QryChangeRecordExportPara para, UserInfoModel user)
    {
        List<ChangeRecordResultDBmodel> data = _Repository.GetDBResultForExport(para, user.logging_locale);

        #region 處理時間
        data.ForEach(thatitem =>
        {
            //處理時間， 如果 change_code_type 為 4表示存儲的是UTC時間要根據用戶時區轉換成對應時間的字符串
            if (thatitem.change_code_type == "4")
            {
                List<string> beforeTimeList = thatitem.content_before?.Split(',')?.Select(e =>
                {
                    if (string.IsNullOrWhiteSpace(e)) return string.Empty;
                    try
                    {
                        return Convert.ToDateTime(e).ToUniversalTime().ConvertDateByTimeZoneByUtc(user.time_zone).ToString("yyyy/MM/dd HH:mm:ss");
                    }
                    catch (Exception)
                    {
                        return string.Empty;

                    }
                })
                .Where(e => !string.IsNullOrWhiteSpace(e)).ToList() ?? new List<string>();
                thatitem.content_before = string.Join(",", beforeTimeList);

                List<string> afterTimeList = thatitem.content_after?.Split(',')?.Select(e =>
                {
                    if (string.IsNullOrWhiteSpace(e)) return string.Empty;
                    try
                    {
                        return Convert.ToDateTime(e).ToUniversalTime().ConvertDateByTimeZoneByUtc(user.time_zone).ToString("yyyy/MM/dd HH:mm:ss");
                    }
                    catch (Exception)
                    {
                        return string.Empty;

                    }
                })
                .Where(e => !string.IsNullOrWhiteSpace(e)).ToList() ?? new List<string>();
                thatitem.content_after = string.Join(",", afterTimeList);
            }
        });
        #endregion

        //创建工作表
        IWorkbook workbook = new XSSFWorkbook();
        ISheet sheet = workbook.CreateSheet("Sheet1");

        //定义一个字体
        IFont font = workbook.CreateFont().configFont(f =>
        {
            f.FontHeightInPoints = 12;
            f.FontName = "Calibri";
        });
        //定义一个样式
        ICellStyle defaultstyle = sheet.Workbook.CreateCellStyle().SetDefaultBorder().AddFont(font).SetwrapTextOn().VerticalCenter();
        ICellStyle styleCenter = sheet.Workbook.CreateCellStyle().SetDefaultBorder().SetwrapTextOn().AddFont(font).VerticalCenter();

        //設置表頭
        sheet.SetHeader(new List<string>()
        {
            ActionFilter.GetMultilingualValue("ChangedRecord_Export:workKey", true), //申請單號
            ActionFilter.GetMultilingualValue("ChangedRecord_Export:typeName", true), //申請類型
            ActionFilter.GetMultilingualValue("ChangedRecord_Export:createTime", true),//異動時間
            ActionFilter.GetMultilingualValue("ChangedRecord_Export:emplName", true),//異動員工
            ActionFilter.GetMultilingualValue("ChangedRecord_Export:isModifyAnnex", true),//是否修改附件
            ActionFilter.GetMultilingualValue("ChangedRecord_Export:changeCode", true),//異動欄位
            ActionFilter.GetMultilingualValue("ChangedRecord_Export:contentBefore", true), //異動前內容
            ActionFilter.GetMultilingualValue("ChangedRecord_Export:contentAfter", true)//異動後內容
        });

        //設置列寬
        sheet.SetColumnListWidth(new List<int>() {4000,7000,6000,7000,3000,6000,10000,10000 });


        sheet.WriteData(data, (item, row) =>
        {
            item.work_key = item.work_key?.Replace("\u001f", "") ?? string.Empty;

            string type_name = item.change_type_name;
            if (!string.IsNullOrWhiteSpace(item.change_next_type_name))
            {
                type_name = item.change_type_name + "-" + item.change_next_type_name;
            }

            row.CreateElegalDefaultCell(0, defaultstyle).SetCellValue(item.work_key);
            row.CreateElegalDefaultCell(1, defaultstyle).SetCellValue(type_name);
            row.CreateElegalDefaultCell(2, defaultstyle).SetCellValue(item.create_time.ConvertDateByTimeZoneByUtc(user.time_zone).ToString("yyyy/MM/dd HH:mm"));
            row.CreateElegalDefaultCell(3, defaultstyle).SetCellValue(item.name);
            row.CreateElegalDefaultCell(4, defaultstyle).SetCellValue(item.is_modify_annex ? "Y" : "N");
            string changeCodeName = user.logging_locale == "ZH-TW" ? item.change_code_cname : item.change_code_ename;
            if (!string.IsNullOrWhiteSpace(item.children_code))
            {
                string childrenCode = user.logging_locale == "ZH-TW" ? item.children_code_cname : item.children_code_ename;
                changeCodeName += $@":{childrenCode}";
            }
            row.CreateElegalDefaultCell(5, defaultstyle).SetCellValue(changeCodeName);
            row.CreateElegalDefaultCell(6, defaultstyle).SetCellValue(item.content_before);
            row.CreateElegalDefaultCell(7, defaultstyle).SetCellValue(item.content_after);


        });


        return workbook.ToBytes();
        /*
        ExcelPackage.LicenseContext = LicenseContext.NonCommercial; // 設置LicenseContext屬性
        using (var package = new ExcelPackage())
        {
            var worksheet = package.Workbook.Worksheets.Add("Sheet1");
            IWorkbook workbook = new XSSFWorkbook();
            ISheet sheet = workbook.CreateSheet("Sheet1");
            worksheet.Cells.Style.Font.SetFromFont("Calibri", 12);
            // 添加Header
            var headerRange = worksheet.Cells["A1:H1"];
            headerRange.Style.Font.Bold = true; // 粗體
            //headerRange.Style.Border.BorderAround(ExcelBorderStyle.Medium); // 邊框
            headerRange.Style.Border.Top.Style = ExcelBorderStyle.Medium;
            headerRange.Style.Border.Bottom.Style = ExcelBorderStyle.Medium;
            headerRange.Style.Border.Left.Style = ExcelBorderStyle.Medium;
            headerRange.Style.Border.Right.Style = ExcelBorderStyle.Medium;
            headerRange.Style.Font.Size = 12;
            headerRange.Style.VerticalAlignment = ExcelVerticalAlignment.Center;
            // 添加Header
            worksheet.Cells["A1"].Value = ActionFilter.GetMultilingualValue("ChangedRecord_Export:workKey", true);
            worksheet.Cells["B1"].Value = ActionFilter.GetMultilingualValue("ChangedRecord_Export:createTime", true);
            worksheet.Cells["C1"].Value = ActionFilter.GetMultilingualValue("ChangedRecord_Export:emplid", true);
            worksheet.Cells["D1"].Value = ActionFilter.GetMultilingualValue("ChangedRecord_Export:emplName", true);
            worksheet.Cells["E1"].Value = ActionFilter.GetMultilingualValue("ChangedRecord_Export:isModifyAnnex", true);
            worksheet.Cells["F1"].Value = ActionFilter.GetMultilingualValue("ChangedRecord_Export:changeCode", true);
            worksheet.Cells["G1"].Value = ActionFilter.GetMultilingualValue("ChangedRecord_Export:contentBefore", true);
            worksheet.Cells["H1"].Value = ActionFilter.GetMultilingualValue("ChangedRecord_Export:contentAfter", true);



            int row = 2;
            foreach (ChangeRecordResultDBmodel item in data)
            {
                item.work_key = item.work_key?.Replace("\u001f", "") ?? string.Empty;
                var dataRange = worksheet.Cells[$"A{row}:H{row}"];
                // 邊框
                dataRange.Style.Border.Top.Style = ExcelBorderStyle.Medium;
                dataRange.Style.Border.Bottom.Style = ExcelBorderStyle.Medium;
                dataRange.Style.Border.Left.Style = ExcelBorderStyle.Medium;
                dataRange.Style.Border.Right.Style = ExcelBorderStyle.Medium;
                dataRange.Style.Font.Size = 12;
                dataRange.Style.VerticalAlignment = ExcelVerticalAlignment.Center;

                worksheet.Cells[$"A{row}"].Value = item.work_key;
                worksheet.Cells[$"B{row}"].Value = item.create_time.ConvertDateByTimeZoneByUtc(user.time_zone).ToString("yyyy/MM/dd");
                worksheet.Cells[$"C{row}"].Value = item.empid;
                worksheet.Cells[$"D{row}"].Value = item.name;
                worksheet.Cells[$"E{row}"].Value = item.is_modify_annex ? "Y" : "N";

                string changeCodeName = user.logging_locale == "ZH-TW" ? item.change_code_cname : item.change_code_ename;
                if (!string.IsNullOrWhiteSpace(item.children_code))
                {
                    string childrenCode = user.logging_locale == "ZH-TW" ? item.children_code_cname : item.children_code_ename;
                    changeCodeName += $@":{childrenCode}";
                }
                worksheet.Cells[$"F{row}"].Value = changeCodeName;
                worksheet.Cells[$"G{row}"].Value = item.content_before;
                worksheet.Cells[$"H{row}"].Value = item.content_after;

                row++;

            }
            // 自动调整列宽
            worksheet.Cells.AutoFitColumns(10, 40);
            worksheet.Cells.Style.WrapText = true;
            worksheet.Cells.Style.VerticalAlignment = ExcelVerticalAlignment.Center;
            return package.GetAsByteArray();
        **/



    }
}
