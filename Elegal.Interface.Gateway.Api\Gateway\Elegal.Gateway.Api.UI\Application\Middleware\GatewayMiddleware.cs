using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System;
using System.Net;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace Elegal.Gateway.Api.UI
{
    internal class GatewayMiddleware
    {
        private readonly RequestDelegate _next;
        readonly ILogger<ApiGatewayLog> _logger;

        public GatewayMiddleware(RequestDelegate next, ILogger<ApiGatewayLog> logger)
        {
            _next = next;
            _logger = logger;
        }

        private async Task HandleExceptionAsync(HttpContext context, Exception ex)
        {
            var result = JsonConvert.SerializeObject(new { error = ex.InnerException?.Message ?? ex.Message });
            context.Response.ContentType = "application/json";
            context.Response.StatusCode = (int)HttpStatusCode.InternalServerError;
            await context.Response.WriteAsync(result);
        }

        public async Task Invoke(HttpContext context, IApiOrchestrator orchestrator, ILogger<ApiGatewayLog> logger)
        {
            try
            {
                var path = context.Request.Path.Value;
                var segmentsMatch = Regex.Match(path, GatewayConstants.GATEWAY_PATH_REGEX, RegexOptions.IgnoreCase | RegexOptions.Compiled);

                if (segmentsMatch.Success)
                {
                    // 快速路徑驗證，減少不必要的處理
                    var api = segmentsMatch.Groups["api"].Captures[0].Value;
                    var key = segmentsMatch.Groups["key"].Captures[0].Value;
                    var method = context.Request.Method.ToUpper();

                    // 預先檢查路由是否存在，避免重複查詢
                    if (path.ToLower().Contains("api/gateway/hub"))
                    {
                        var hubInfo = orchestrator.GetHub(api);
                        var hubRouteInfo = hubInfo?.Mediator.GetRoute(key);
                        if (hubRouteInfo?.Verb.ToString() != method)
                        {
                            context.Response.StatusCode = (int)HttpStatusCode.MethodNotAllowed;
                            return;
                        }
                    }
                    else
                    {
                        var apiInfo = orchestrator.GetApi(api);
                        var routeInfo = apiInfo?.Mediator.GetRoute(key);
                        if (routeInfo?.Verb.ToString() != method)
                        {
                            context.Response.StatusCode = (int)HttpStatusCode.MethodNotAllowed;
                            return;
                        }
                    }
                }

                await _next(context);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Api Gateway error");
                context.Response.StatusCode = StatusCodes.Status404NotFound;
            }
        }
    }
}
