﻿using Elegal.Flow.Api.Repository;
using Elegal.Flow.Api.Repository.Individual;
using Elegal.Flow.Common;
using Elegal.Flow.Common.Repository;
using Elegal.Flow.Common.Services.FlowStep;
using Elegal.Interface.Api.Common.Control.Filter;
using Elegal.Interface.Api.Common.FuncHelper;
using Elegal.Interface.Api.Common.Model.Enum;
using Elegal.Interface.Api.Common.Model.ParaModel.FlowApi;
using Elegal.Interface.Api.Common.Model.ResultModel;
using Elegal.Interface.Api.Common.Model.ViewModel;
using Elegal.Interface.Api.Common.Model.ViewModel.FlowApi;
using Elegal.Interface.ApiData.Service.FuncService;
using Elegal.Interface.ApiData.Service.Model.Condition;
using Elegal.Interface.ApiData.Service.Model.DbModel;
using Elegal.Interface.ApiData.Service.Model.QueryCondition;
using Elegal.Orm;
using Elegal.Orm.Dtos;
using Elegal.Orm.Utils;
using Newtonsoft.Json;
using NPOI.SS.Formula.Functions;
using System.Security.AccessControl;
using System.Text;
using CommonUtil = Elegal.Orm.Utils.CommonUtil;

namespace Elegal.Flow.Api.Services.Individual
{
    /// <summary>
    /// 代理人設定
    /// </summary>
    public static class SysAgentService
    {
        private const string time_zone = "Taipei Standard Time";

        private static readonly SysAgentRepository _repository = new();

        private static UserInfoModel curr_user = MvcContext.UserInfo;

        private static McpRecordRepository mcpRecordRepository = new McpRecordRepository();

        #region 新增代理人
        internal static ApiResultModel Add(SysAgentAddModel model)
        {
            //當前操作用戶信息
            //記錄重複的數據
            StringBuilder repeatData = new();
            //記錄新增成功的數據
            StringBuilder addData = new();
            //新增成功的數據量
            int addCount = 0;
            foreach (var department in model.Departments)
            {
                //檢查時間段內是否該部門已被代理
                model.AuthDeptid = department.Key;
                if (_repository.IsAgent(model)) repeatData.Append($"<p class='rem_3'>{department.Value}</p>");
                else
                {
                    #region 新增代理人數據(sys_agent表)
                    SysAgent SysAgent = new SysAgent()
                    {
                        State = "1",
                        Type = "A",
                        CreateTime = DateTime.UtcNow,
                        CreateUser = MvcContext.UserInfo.current_emp,
                        StartTime = model.StartTime,
                        EndTime = model.EndTime,
                        AuthDeptid = department.Key,
                        AgentEmpid = model.Empid.Key,
                        AuthEmpid = model.AuthEmp.Key
                    };
                    SysAgentDataService.Create(SysAgent);
                    addCount++;//記錄新增數
                    #endregion

                    #region 新增簽核代理人設定(與sys_agent相同)數據
                    FlowStepProxy flowStepProxy = CommonUtil.Map<SysAgent, FlowStepProxy>(SysAgent);
                    flowStepProxy.AgentId = SysAgentDataService.Find(CommonUtil.Map<SysAgent, SysAgentCondition>(SysAgent))?.EId;
                    FlowStepProxyDataService.Create(flowStepProxy);
                    #endregion

                    //記錄新增成功的描述語
                    addData.Append($"<p class='rem_3'>{department.Value}</p>");

                    #region 代理人增派邏輯
                    if (model.StartTime != null && DateTime.UtcNow > model.StartTime)
                        FlowStepService.SendAgentMcp(model.AuthEmp.Key, department.Key, model.Empid.Key);
                    #endregion
                }
            }
            #region 如果無新增數據，返回並給到前端提示
            if (addCount == 0)
            {
                return new()
                {
                    messageTitle = ActionFilter.GetMultilingualValue("custom:messageTitle:createFail"),
                    messageContent = new() { ActionFilter.GetMultilingualValue("custom:messageContent:AgentsExist") },
                    messageType = MessageTypeUtils.Warning.ToString()
                };
            }
            #endregion

            #region 有新增數據，則提示新增或過濾的數據
            ApiResultModel apiResult = new();
            apiResult.messageTitle = ActionFilter.GetMultilingualValue("custom:messageTitle:createSuccess");
            apiResult.messageContent.Add(ActionFilter.GetMultilingualValue("custom:messageContent:createSuccess"));
            apiResult.messageContent.Add($"{ActionFilter.GetMultilingualValue("SysAgent_agent", true)}：{model.Empid.Key}({model.Empid.Value})");
            if (addData.Length > 0)
                apiResult.messageContent.Add($"{ActionFilter.GetMultilingualValue("commonWord:addedAgentDepartment")}：<br />{addData}");
            if (repeatData.Length > 0)
                apiResult.messageContent.Add($"<div class='red_text'>{ActionFilter.GetMultilingualValue("commonWord:existingAgentDepartment")}：<br />{repeatData}</div>");
            apiResult.rtnSuccess = true;
            #endregion

            ActionFilter.InitLogRecord(model, logRecord =>
            {
                StringBuilder stringBuilder = new();
                stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("SysAgent_authorized", true)}：{model.AuthEmp.Key}({model.AuthEmp.Value})");
                stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("SysAgent_agent", true)}：{model.Empid.Key}({model.Empid.Value})");
                stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("SysAgent_dates", true)}：{model.StartTime.GetValueOrDefault().ConvertDateByTimeZoneByUtc(time_zone).Date.ToString("yyyy/MM/dd HH:mm:ss")} ~ {model.EndTime.GetValueOrDefault().ConvertDateByTimeZoneByUtc(time_zone).Date.AddDays(1).AddSeconds(-1).ToString("yyyy/MM/dd HH:mm:ss")}");
                stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("SysAgent_departments", true)}：{string.Join(',', model.Departments.Select(s => s.Value))}");
                logRecord.Detail = stringBuilder.ToString();
            });

            #region 发送邮件
            SysAgentMailModel mailModel = new SysAgentMailModel()
            {
                AuthDeptid = model.AuthDeptid,
                AuthEmp = model.AuthEmp.Key,
                Empid = model.Empid.Key,
                StartTime = model.StartTime,
                EndTime = model.EndTime,
                Departments = model.Departments
            };
            UserEmailContent userEmailContent = ProxyAddSendMailContent(mailModel, "03", "AS");
            SysEmail sysEmail = new()
            {
                ESubject = userEmailContent.MailSubject,
                ESendtime = DateTime.UtcNow,
                EContent = userEmailContent.MailContent,
                EReceiver = SetMailReceivers(userEmailContent, mailModel, "R"),
                ECc = SetMailReceivers(userEmailContent, mailModel, "C"),
                ESendnum = 0,
                EIssend = (int)YesOrNoUtils.No,
                EType = userEmailContent.MailType
            };
            #region 有收件人才發送郵件
            if (!string.IsNullOrEmpty(sysEmail.EReceiver) || !string.IsNullOrEmpty(sysEmail.ECc)) SysEmailDataService.Create(sysEmail);
            #endregion
            #endregion
            return apiResult;
        }
        #endregion

        #region 刪除代理人
        /// <summary>
        /// 刪除代理人
        /// </summary>
        /// <param name="key">主键标识</param>
        /// <returns></returns>
        internal static ApiResultModel DeleteByKey(int key)
        {
            #region 人員信息(代理人、授權人)
            //代理人設定數據
            SysAgent sysAgent = SysAgentDataService.FindByKey(key);
            //代理人
            var agenter = PsSubEeLglVwADataService.FindByKey(sysAgent.AgentEmpid);
            //授權人
            var auther = PsSubEeLglVwADataService.FindByKey(sysAgent.AuthEmpid);
            #endregion

            #region 刪除代理人、簽核代理人設定數據
            SysAgentDataService.DeleteByKey(key);
            FlowStepProxy flowStepProxy = FlowStepProxyDataService.Find(new FlowStepProxyCondition() { AgentId = key });
            if (flowStepProxy != null) FlowStepProxyDataService.DeleteByKey(flowStepProxy.Rowid);
            #endregion

            #region 失效代理人抽單邏輯
            List<Interface.Api.Common.Model.DBModel.mcp.mcp_record> mcp_record = mcpRecordRepository.GetMcpRecordByEmplidID(sysAgent.AgentEmpid, sysAgent.AuthEmpid);
            if (mcp_record != null)
                foreach (var item in mcp_record)
                {
                    SendHelper.CancelForm(item);
                }
            #endregion

            #region 发送邮件(刪除有效期内代理人時，才發送郵件提醒)
            if (sysAgent.EndTime.HasValue && DateTime.UtcNow.ConvertDateByTimeZoneByUtc(time_zone) < sysAgent.EndTime.Value.ConvertDateByTimeZoneByUtc(time_zone).Date.AddDays(1))
            {
                SysAgentMailModel model = new SysAgentMailModel()
                {
                    AuthDeptid = sysAgent.AgentEmpid,
                    AuthEmp = sysAgent.AuthEmpid,
                    StartTime = sysAgent.StartTime,
                    EndTime = sysAgent.EndTime,
                    Departments = [new KeyValuePair<string, string>(sysAgent.AuthDeptid, sysAgent.AuthDeptid)],
                    Empid = sysAgent.AgentEmpid,
                };
                UserEmailContent userEmailContent = ProxyAddSendMailContent(model, "03", "AC");
                SysEmail sysEmail = new()
                {
                    ESubject = userEmailContent.MailSubject,
                    ESendtime = DateTime.UtcNow,
                    EContent = userEmailContent.MailContent,
                    EReceiver = SetMailReceivers(userEmailContent, model, "R"),
                    ECc = SetMailReceivers(userEmailContent, model, "C"),
                    ESendnum = 0,
                    EIssend = (int)YesOrNoUtils.No,
                    EType = userEmailContent.MailType
                };
                if (!sysAgent.CreateUser.Equals(sysAgent.AuthEmpid)) sysEmail.ECc = PsSubEeLglVwADataService.FindByKey(sysAgent.CreateUser).EmailAddressA;
                #region 有收件人才發送郵件
                if (!string.IsNullOrEmpty(sysEmail.EReceiver) || !string.IsNullOrEmpty(sysEmail.ECc)) SysEmailDataService.Create(sysEmail);
                #endregion
            }
            #endregion

            #region 記錄日志
            ActionFilter.InitLogRecord(logRecord =>
            {
                StringBuilder detail = new();
                detail.AppendLine($"{ActionFilter.GetMultilingualValue("SysAgent_authName", true)}：{auther.NameA}({auther.Name})");
                detail.AppendLine($"{ActionFilter.GetMultilingualValue("SysAgent_agentName", true)}：{agenter.NameA}({agenter.Name})");
                detail.AppendLine($"{ActionFilter.GetMultilingualValue("SysAgent_dates", true)}：{sysAgent.StartTime.GetValueOrDefault().ConvertDateByTimeZoneByUtc(time_zone).Date.ToString("yyyy/MM/dd HH:mm:ss")} ~ {sysAgent.EndTime.GetValueOrDefault().ConvertDateByTimeZoneByUtc(time_zone).Date.AddDays(1).AddSeconds(-1).ToString("yyyy/MM/dd HH:mm:ss")}");
                detail.AppendLine($"{ActionFilter.GetMultilingualValue("SysAgent_departments", true)}：{sysAgent.AuthDeptid}");
                logRecord.Detail = detail.ToString();
            });
            #endregion
            return new() { rtnSuccess = true };
        }
        #endregion

        /// <summary>
        /// 获取可代理管理部门
        /// </summary>
        /// <param name="user"></param>
        /// <returns></returns>
        internal static List<KeyValuePair<string, string>> GetManagerDept(UserInfoModel user)
        {
            List<KeyValuePair<string, string>> result = new();
            PsSubEeLglVwA psSubEeLglVwA = PsSubEeLglVwADataService.FindByKey(user.current_emp);
            //绑定可代理的关企部门
            if (user.current_emp.IndexOf("@") != -1)
            {
                result = AffiliateDeptDataService.Query(new AffiliateDeptQueryCondition()
                {
                    AffDeptid = psSubEeLglVwA?.Deptid,
                    AffDeptStatus = ((int)DisabledStateUtils.UDisabled).ToString(),
                }).Select(s => new KeyValuePair<string, string>(s.AffDeptid, $"{s.AffDeptcode}({(user.logging_locale.ToLower().Equals("en-us") ? s.AffDeptEname : s.AffDeptCname)})")).ToList();
            }
            else
            {
                PsCjTruckVwARepository psCjTruckVwARepository = new();
                //副职部门
                result = psCjTruckVwARepository.GetPsCjTruckVwAList(user.current_emp).Select(s =>
                {
                    string descr = user.logging_locale.ToLower().Equals("en-us") ? s.DescrA : s.Descr;
                    if (string.IsNullOrEmpty(descr))
                    {
                        return new KeyValuePair<string, string>(s.Deptid, $"{s.Deptid}");
                    }
                    return new KeyValuePair<string, string>(s.Deptid, $"{s.Deptid}({descr})");
                }).ToList();
                PsSubOgLglVwA psSubOgLglVwA = PsSubOgLglVwADataService.FindByKey(psSubEeLglVwA.Deptid);

                result.Insert(0, new KeyValuePair<string, string>(psSubEeLglVwA.Deptid, $"{psSubEeLglVwA.Deptid}({(user.logging_locale.ToLower().Equals("en-us") ? psSubOgLglVwA?.DescrA : psSubOgLglVwA?.Descr)})"));
            }
            return result.DistinctBy(b => b.Key).ToList();
        }
        /// <summary>
        /// 获取我的代理人设定列表数据
        /// </summary>
        /// <returns></returns>
        internal static IEnumerable<SysAgentModel> GetAgentList()
        {
            var list = _repository.GetAgentList();
            foreach (var item in list)
            {
                item.StartTime = item.StartTime.ConvertDateByTimeZoneByUtc(time_zone).Date;
                item.EndTime = item.EndTime.ConvertDateByTimeZoneByUtc(time_zone).Date.AddDays(1).AddSeconds(-1);
                item.Overdue = DateTime.UtcNow > item.EndTime;
            }
            return list;
        }
        /// <summary>
        /// 获取担任他人代理列表数据
        /// </summary>
        /// <returns></returns>
        internal static IEnumerable<SysAgentModel> GetBeAgentList()
        {
            DateTime dt = DateTime.UtcNow.ConvertDateByTimeZoneByUtc(time_zone);
            var list = _repository.GetBeAgentList();
            foreach (var item in list)
            {
                item.StartTime = item.StartTime.ConvertDateByTimeZoneByUtc(time_zone).Date;
                item.EndTime = item.EndTime.ConvertDateByTimeZoneByUtc(time_zone).Date.AddDays(1).AddSeconds(-1);
            }
            return list.Where(w => dt <= w.EndTime);
        }

        /// <summary>
        /// 依照工號获取担任他人代理列表数据
        /// </summary>
        /// <returns></returns>
        internal static IEnumerable<SysAgentModel> GetBeAgentListByEmplid(string emplid)
        {
            DateTime dt = DateTime.UtcNow.ConvertDateByTimeZoneByUtc(time_zone);
            var list = _repository.GetBeAgentListByEmplid(emplid);
            foreach (var item in list)
            {
                item.StartTime = item.StartTime.ConvertDateByTimeZoneByUtc(time_zone).Date;
                item.EndTime = item.EndTime.ConvertDateByTimeZoneByUtc(time_zone).Date.AddDays(1).AddSeconds(-1);
            }
            return list.Where(w => dt >= w.StartTime && dt <= w.EndTime);
        }

        #region 讀取郵件模板(開放欄位值替換)
        /// <summary>
        /// 讀取郵件模板(開放欄位值替換)
        /// </summary>
        /// <param name="model"></param>
        /// <param name="funcModule">功能模組</param>
        /// <param name="mailType">郵件類型</param>
        /// <returns></returns>
        internal static UserEmailContent ProxyAddSendMailContent(SysAgentMailModel model, string funcModule, string mailType)
        {
            //郵件模板
            UserEmailContent userEmailContent = UserEmailContentDataService.Find(new UserEmailContentCondition() { FuncModule = funcModule, MailType = mailType });
            //開放欄位數據
            var data = _repository.GetOpenFiledValue(model);
            //開放欄位
            var userEmailDictionarys = UserEmailDictionaryDataService.Query(new UserEmailDictionaryQueryCondition() { FieldType = funcModule })
                .Where(w => w.MailType.Split(";").Contains(mailType));
            SetMailContent(userEmailContent, data, userEmailDictionarys);
            return userEmailContent;
        }
        #endregion

        #region 替換郵件内容開放欄位值(包含標題)
        /// <summary>
        /// 替換郵件内容開放欄位值(包含標題)
        /// </summary>
        /// <param name="userEmailContent"></param>
        /// <param name="data"></param>
        /// <param name="userEmailDictionarys"></param>
        internal static void SetMailContent(UserEmailContent userEmailContent, object? data, IEnumerable<UserEmailDictionary> userEmailDictionarys)
        {
            if (data != null)
            {
                var itemDict = (IDictionary<string, object>)data;
                foreach (var field in userEmailDictionarys)
                {
                    #region 中文替換
                    string filed_code = field.FieldCode + "_zh";
                    var value = itemDict.ContainsKey(filed_code) ? itemDict[filed_code]?.ToString() ?? string.Empty : string.Empty;
                    userEmailContent.MailContent = userEmailContent.MailContent.Replace($"{{{filed_code}}}", value);
                    #endregion
                    #region 標題替換
                    userEmailContent.MailSubject = userEmailContent.MailSubject.Replace($"{{{filed_code}}}", value);
                    #endregion
                    #region 英文替換
                    filed_code = field.FieldCode + "_en";
                    value = itemDict.ContainsKey(filed_code) ? itemDict[filed_code]?.ToString() ?? string.Empty : string.Empty;
                    userEmailContent.MailContent = userEmailContent.MailContent.Replace($"{{{filed_code}}}", value);
                    #endregion

                }
            }
        }
        #endregion

        #region 設定郵件收件人郵箱
        /// <summary>
        /// 設定郵件收件人郵箱
        /// </summary>
        /// <param name="userEmailContent"></param>
        /// <param name="model"></param>
        /// <param name="type">類型(R：收件；C：CC)</param>
        /// <returns></returns>
        internal static string SetMailReceivers(UserEmailContent userEmailContent, SysAgentMailModel model, string type)
        {
            List<string> Receivers = [];
            string mailType = string.Empty;
            if (type.Equals("R")) mailType = userEmailContent.MailReType;
            if (type.Equals("C")) mailType = userEmailContent.MailCcType;
            var mailReTypes = JsonConvert.DeserializeObject<List<KeyValuePair<string, List<string>>>>(mailType) ?? [];
            //代理人
            if (mailReTypes.Any(a => a.Key.Equals("18"))) Receivers.Add(PsSubEeLglVwADataService.FindByKey(model.Empid)?.EmailAddressA ?? "");
            //授權人
            if (mailReTypes.Any(a => a.Key.Equals("19"))) Receivers.Add(PsSubEeLglVwADataService.FindByKey(model.AuthEmp)?.EmailAddressA ?? "");
            //操作人(滿足條件：操作人為第三者)
            if (mailReTypes.Any(a => a.Key.Equals("31")) && !curr_user.current_emp.Equals(model.Empid) && !curr_user.current_emp.Equals(model.AuthEmp))
                Receivers.Add(PsSubEeLglVwADataService.FindByKey(curr_user.current_emp)?.EmailAddressA ?? "");
            return string.Join(";", Receivers);
        }
        #endregion
    }
}
