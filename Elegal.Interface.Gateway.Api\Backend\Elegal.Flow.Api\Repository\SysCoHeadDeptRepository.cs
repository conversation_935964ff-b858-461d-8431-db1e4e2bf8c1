﻿using Elegal.Interface.Api.Common.FuncHelper;
using Elegal.Interface.Api.Common.Model.ParaModel.FlowApi;
using Elegal.Interface.Api.Common.Model.ViewModel.FlowApi;
using Elegal.Interface.Api.Common.Repository;
using Elegal.Orm;
using Elegal.Orm.Dtos;
using System.Text;

namespace Elegal.Flow.Api.Repository
{
    /// <summary>
    /// Co-Head部門資料存取層
    /// 處理與Co-Head部門相關的資料庫操作，包含查詢、更新等功能
    /// </summary>
    public class SysCoHeadDeptRepository : BaseRepository
    {
        private readonly string Prefix = DbAccess.Database.GetParameterPrefix();
        private readonly string Schema = DbAccess.Database.GetDbSchema();

        /// <summary>
        /// 查詢部門層級清單
        /// </summary>
        /// <returns>所有部門層級編號，依層級遞增排序</returns>
        public IEnumerable<string> GetTreeLevelList()
        {
            string sql = $@"SELECT
	                            distinct tree_level_num
                            FROM
	                            {Schema}.ps_sub_og_lgl_vw_a 
                            ORDER BY
                                tree_level_num ASC";
            return DbAccess.Database.SqlQuery<string>(sql);
        }

        /// <summary>
        /// 查詢Co-Head部門資料，支援多種查詢條件和分頁
        /// </summary>
        /// <param name="qry">查詢條件模型</param>
        /// <returns>分頁的查詢結果</returns>
        /// <remarks>
        /// 查詢邏輯說明：
        /// 1. 基礎查詢：從ps_sub_og_lgl_vw_a獲取部門基本資訊
        /// 2. 關聯查詢：
        ///    - 主管資訊：與ps_sub_ee_lgl_vw_a關聯
        ///    - Co-Head資訊：與ps_sub_coh_pm_vw_a關聯
        ///    - 跨部門資訊：與department_cross_g關聯
        /// 3. 特殊處理：
        ///    - 託管部門狀態判斷
        ///    - 部門狀態過濾（已刪除/未刪除）
        ///    - 支援模糊查詢
        /// </remarks>
        public PageResult<SysCoheadDeptViewModel> QueryCoHeadDept(qrySysCoHeadDeptModel qry)
        {
            PageResult<SysCoheadDeptViewModel> result = new PageResult<SysCoheadDeptViewModel>();
            string startLevel = AppSettingHelper.Configuration["CrossDept:startLevel"];
            string endLevel = AppSettingHelper.Configuration["CrossDept:endLevel"];

            StringBuilder countSql = new StringBuilder();
            countSql.Append(@"SELECT
                                    COUNT(1)
                           FROM (
                                    SELECT deptid,descr,manager_id,tree_level_num,uporg_code_a FROM ps_sub_og_lgl_vw_a AS og2
                                    WHERE 1 = 1");
            StringBuilder sql = new StringBuilder();
            sql.Append(@"SELECT
                                    deptid,--部門代號
                                    descr,--部門名稱
                                    manager_id,--主管工號
                                    (SELECT name_a FROM ps_sub_ee_lgl_vw_a WHERE emplid = manager_id) AS manager_name,--主管姓名
                                    (SELECT STUFF((SELECT N', ' + (SELECT name_a FROM ps_sub_ee_lgl_vw_a WHERE emplid = manager_id) FROM ps_sub_coh_pm_vw_a WHERE deptid = og.deptid FOR XML PATH('')),1,1,'')) AS cohead_name,--co-head姓名
                                    (SELECT STRING_AGG(ee.name_a, ', ')  FROM (SELECT deptid,manager_id FROM ps_sub_og_lgl_vw_a WHERE tree_level_num BETWEEN  @endLevel AND @startLevel) AS og1
                                        INNER JOIN (SELECT deptid,manager_id FROM department_cross_g WHERE relationship IN('X','V') AND deptid = og.deptid AND batch_id = (select max (batch_id) from department_cross_g)) AS r ON r.deptid = og1.deptid
                                        INNER JOIN (SELECT emplid,name_a FROM ps_sub_ee_lgl_vw_a WHERE ISNULL(termination,'') = '') AS ee ON ee.emplid = r.manager_id
                                    ) AS cross_name,--跨部門姓名
                                    tree_level_num,--部門層級
                                    uporg_code_a,--上層部門
                                    (SELECT sign_type FROM sys_cohead_dept WHERE deptid = og.deptid) AS cohead_sign,--co-head簽核方式
                                    (ISNULL((SELECT is_hosted FROM sys_hosted_dept WHERE deptid = og.deptid),0)) AS is_hosted,--託管部門
                                    @startLevel AS startLevel,
                                    @endLevel AS endLevel,
                                    create_date
                           FROM (
                                    SELECT deptid,descr,manager_id,tree_level_num,uporg_code_a,create_date FROM ps_sub_og_lgl_vw_a AS og2
                                    WHERE 1 = 1");

            StringBuilder condition = new StringBuilder();
            //部門代號起始字串查詢
            //edit by spring_jiang 20240108 因 ps_sub_og_lgl_vw_a_original 不在使用，需將其換為 ps_sub_og_lgl_vw_a_dsp
            if (!string.IsNullOrEmpty(qry.deptid))
            {
                //condition.Append(@$" AND EXISTS(SELECT 1 FROM ps_sub_og_lgl_vw_a_dsp AS oriog WHERE oriog.deptid = og2.deptid)");
                //改为前置匹配
                condition.Append($@" AND deptid LIKE CONCAT(@deptid, N'%')");
            }
            //部門狀態-未刪除部門
            if (!string.IsNullOrEmpty(qry.dept_type) && qry.dept_type == "0")
            {
                condition.Append(@$" AND EXISTS(SELECT 1 FROM ps_sub_og_lgl_vw_a_dsp AS oriog WHERE oriog.deptid = og2.deptid)");
            }
            //部門狀態-已刪除部門
            if (!string.IsNullOrEmpty(qry.dept_type) && qry.dept_type == "1")
            {
                condition.Append(@$" AND NOT EXISTS(SELECT 1 FROM ps_sub_og_lgl_vw_a_dsp AS oriog WHERE oriog.deptid = og2.deptid)");
            }
            //主管(主管工號 / 主管英文名)
            if (!string.IsNullOrEmpty(qry.manager))
                condition.Append(@$" AND EXISTS(SELECT 1 FROM ps_sub_ee_lgl_vw_a AS ee WHERE ((emplid = @manager OR emplid LIKE CONCAT(N'%', @manager, N'%')) OR (name_a = @manager OR name_a LIKE CONCAT(N'%', @manager, N'%') OR name = @manager OR name LIKE CONCAT(N'%', @manager, N'%'))) AND ee.emplid = og2.manager_id)");
            //co-head英文姓名
            if (!string.IsNullOrEmpty(qry.co_head))
                sql.Append(@$" AND EXISTS(SELECT 1 FROM (SELECT co.deptid FROM ps_sub_coh_pm_vw_a AS co INNER JOIN ps_sub_ee_lgl_vw_a AS ee ON ee.emplid = co.manager_id WHERE ee.name = @co_head OR ee.name LIKE CONCAT(N'%', @co_head, N'%') OR ee.name_a = @co_head OR ee.name_a LIKE CONCAT(N'%', @co_head, N'%')) AS coee WHERE coee.deptid = og2.deptid)");
            //部門層級
            if (!string.IsNullOrEmpty(qry.tree_level_num))
            {
                string[] trees = qry.tree_level_num.Split(',');
                condition.Append(@$" AND tree_level_num in ({string.Join(",", trees.Select(s => $"'{s}'"))})");
            }
            //託管部門
            if (!string.IsNullOrEmpty(qry.is_hosted))
            {
                string[] hosteds = qry.is_hosted.Split(',');

                // Check if the array contains both 0 and 1
                if (hosteds.Contains("0") && hosteds.Contains("1"))
                {
                    // If both 0 and 1 are present, include rows for is_hosted = 0, is_hosted = 1,
                    // and rows not in sys_hosted_dept
                    condition.Append(@" AND (EXISTS(SELECT 1 FROM sys_hosted_dept AS shd WHERE is_hosted in ('0','1') AND shd.deptid = og2.deptid) OR NOT EXISTS(SELECT 1 FROM sys_hosted_dept AS shd WHERE shd.deptid = og2.deptid))");
                }
                else if (hosteds.Contains("0"))
                {
                    // If only 0 is present, include rows for is_hosted = 0 and rows not in sys_hosted_dept
                    condition.Append(@" AND (EXISTS(SELECT 1 FROM sys_hosted_dept AS shd WHERE is_hosted in ('0') AND shd.deptid = og2.deptid) OR NOT EXISTS(SELECT 1 FROM sys_hosted_dept AS shd WHERE shd.deptid = og2.deptid))");
                }
                else if (hosteds.Contains("1"))
                {
                    // If only 1 is present, include rows for is_hosted = 1
                    condition.Append(@" AND EXISTS(SELECT 1 FROM sys_hosted_dept AS shd WHERE is_hosted in ('1') AND shd.deptid = og2.deptid)");
                }
            }
            //co-head簽核方式
            if (!string.IsNullOrEmpty(qry.sign_type))
            {
                string[] signs = qry.sign_type.Split(',');
                condition.Append(@$" AND EXISTS(SELECT 1 FROM sys_cohead_dept AS scd WHERE sign_type in ({string.Join(",", signs.Select(s => $"'{s}'"))} ) AND scd.deptid = og2.deptid)");
            }
            //部門創建日期開始
            if (qry.createDateStart != null)
            {
                condition.Append(@$" AND create_date >= SWITCHOFFSET(@createDateStart,'+08:00')");
            }
            //部門創建日期結束
            if (qry.createDateEnd != null)
            {
                condition.Append(@$" AND create_date <= SWITCHOFFSET(@createDateEnd,'+08:00')");
            }

            condition.Append($@") AS og");

            countSql.Append(condition.ToString());
            int totalCount = DbAccess.Database.SqlQuery<int>(countSql.ToString(), new
            {
                deptid = qry.deptid,
                manager = qry.manager,
                co_head = qry.co_head,
                startLevel = startLevel,
                endLevel = endLevel,
                createDateEnd = qry.createDateEnd,
                createDateStart = qry.createDateStart
            }).FirstOrDefault();

            sql.Append(condition.ToString());
            if (qry.page == null || (qry.page.PageSize == 0 && qry.page.PageIndex == 0))
                qry.page = new PageParam() { PageIndex = 1, PageSize = 25 };

            if (qry.order == null || string.IsNullOrEmpty(qry.order.Field))
                qry.order = new OrderByParam() { Field = "deptid", Order = OrderBy.ASC };

            int offset = (qry.page.PageIndex - 1) * qry.page.PageSize;
            string orderStr = qry.order.Field + " " + qry.order.Order.ToString();
            sql.Append(@$" ORDER BY {orderStr} OFFSET {offset} ROWS FETCH NEXT {qry.page.PageSize} ROWS ONLY");
            result.Data = this.NpgsqlSearchByList<SysCoheadDeptViewModel>(sql.ToString(),
            new
            {
                deptid = qry.deptid,
                manager = qry.manager,
                co_head = qry.co_head,
                startLevel = startLevel,
                endLevel = endLevel,
                createDateEnd = qry.createDateEnd,
                createDateStart = qry.createDateStart
            });
            result.TotalRows = totalCount;
            return result;
        }

        public IEnumerable<SysCoheadDeptViewModel> QueryUpperDept(string startDept)
        {
            string startLevel = AppSettingHelper.Configuration["CrossDept:startLevel"];
            string endLevel = AppSettingHelper.Configuration["CrossDept:endLevel"];
            string sql = $@"EXEC SP_GetUpperDept @startDept, @startLevel, @endLevel";
            IEnumerable<SysCoheadDeptViewModel> res = DbAccess.Database.SqlQuery<SysCoheadDeptViewModel>(sql, new { startDept = startDept, startLevel = startLevel, endLevel = endLevel });
            return res;
        }

        /// <summary>
        /// 更新Co-Head部門的簽核設定
        /// </summary>
        /// <param name="deptid">部門代號</param>
        /// <param name="signType">簽核類型</param>
        /// <param name="modifiedUser">修改人工號</param>
        /// <returns>更新是否成功</returns>
        /// <remarks>
        /// 處理邏輯：
        /// 1. 檢查是否存在記錄
        /// 2. 存在則更新，不存在則新增
        /// 3. 記錄修改時間和修改人
        /// </remarks>
        public bool UpdateCoHeadDept(string deptid, int signType, string modifiedUser)
        {
            bool res = false;

            // 檢查 sys_cohead_dept 是否存在對應部門的數據
            string checkExistQuery = $"SELECT 1 FROM sys_cohead_dept WHERE deptid = @deptid";
            bool is_exist = DbAccess.Database.SqlQuery<int>(checkExistQuery, new { deptid = deptid }).Any();

            if (is_exist)
            {
                // 存在數據，進行修改
                string updateQuery = $"UPDATE sys_cohead_dept SET sign_type = @signType, modify_user =@modifiedUser, modify_time = getutcdate() WHERE deptid = @deptid";
                res = DbAccess.Database.SqlExecute(updateQuery, new { signType = signType, modifiedUser = modifiedUser, deptid = deptid }) > 0;
            }
            else
            {
                // 不存在數據，進行新增
                string insertQuery = $"INSERT INTO sys_cohead_dept (deptid, sign_type, create_user, create_time) VALUES(@deptid, @signType, @modifiedUser, getutcdate())";
                res = DbAccess.Database.SqlExecute(insertQuery, new { deptid = deptid, signType = signType, modifiedUser = modifiedUser }) > 0;
            }

            return res;

            #region 舊邏輯
            //// 根據 sign_type 不同選擇不同的查詢
            //string selectDeptQuery = "";

            //switch (signType)
            //{
            //    case -1:
            //        // 刪除數據
            //        string deleteCoheadDeptQuery = $"UPDATE sys_organization_signer SET cohead_deptid = null, modify_user =@modifiedUser, modify_time = getutcdate() WHERE deptid = @deptid";
            //        res = DbAccess.Database.SqlExecute(deleteCoheadDeptQuery, new { modifiedUser = modifiedUser, deptid = deptid }) > 0;
            //        return res;
            //    case 0: //正主管
            //        //selectDeptQuery = $"SELECT uporg_code_a FROM ps_sub_og_lgl_vw_a WHERE deptid = @deptid";
            //        //break;
            //        return res;

            //    case 1: //副主管
            //        //selectDeptQuery = $"SELECT deptid FROM ps_sub_coh_pm_vw_a WHERE deptid IN (SELECT deptid FROM ps_cj_truck_vw_a WHERE emplid IN (SELECT emplid FROM ps_sub_ee_lgl_vw_a WHERE deptid = @deptid))";
            //        //break;
            //        return res;

            //    case 2: //正主管+副主管
            //        //selectDeptQuery = $"SELECT deptid FROM ps_sub_coh_pm_vw_a WHERE deptid IN (" +
            //        //                  $"(SELECT deptid FROM ps_cj_truck_vw_a WHERE emplid IN (SELECT emplid FROM ps_sub_ee_lgl_vw_a WHERE deptid = @deptid)))" +
            //        //                  $" UNION " +
            //        //                  $"SELECT uporg_code_a FROM ps_sub_og_lgl_vw_a WHERE deptid = @deptid";
            //        selectDeptQuery = $"SELECT deptid FROM ps_sub_coh_pm_vw_a WHERE deptid IN (" +
            //                          $"(SELECT deptid FROM ps_cj_truck_vw_a WHERE emplid IN (SELECT emplid FROM ps_sub_ee_lgl_vw_a WHERE deptid = @deptid)))";
            //        break;

            //    default:
            //        return res;
            //}

            //// 執行查詢並取得部門代號
            //string coheadDeptid = string.Join(",", DbAccess.Database.SqlQuery<string>(selectDeptQuery, new { deptid = deptid }).ToList());

            //// 更新對應的數據
            //string updateCoheadDeptQuery = $"UPDATE sys_organization_signer SET cohead_deptid = @coheadDeptid, modify_user =@modifiedUser, modify_time = getutcdate() WHERE deptid = @deptid";
            //res = DbAccess.Database.SqlExecute(updateCoheadDeptQuery, new { coheadDeptid = coheadDeptid, modifiedUser = modifiedUser, deptid = deptid }) > 0;
            #endregion
        }

        /// <summary>
        /// 更新部門的託管狀態
        /// </summary>
        /// <param name="deptid">部門代號</param>
        /// <param name="isHosted">託管狀態(0:非託管, 1:託管)</param>
        /// <param name="modifiedUser">修改人工號</param>
        /// <returns>更新是否成功</returns>
        /// <remarks>
        /// 處理邏輯：
        /// 1. 檢查是否存在記錄
        /// 2. 存在則更新，不存在則新增
        /// 3. 記錄修改時間和修改人
        /// </remarks>
        public bool UpdateHostedDept(string deptid, int isHosted, string modifiedUser)
        {
            bool res = false;

            // 檢查 sys_cohead_dept 是否存在對應部門的數據
            string checkExistQuery = $"SELECT 1 FROM sys_hosted_dept WHERE deptid = @deptid";
            bool is_exist = DbAccess.Database.SqlQuery<int>(checkExistQuery, new { deptid = deptid }).Any();

            if (is_exist)
            {
                // 存在數據，進行修改
                string updateQuery = $"UPDATE sys_hosted_dept SET is_hosted = @isHosted, modify_user =@modifiedUser, modify_time = getutcdate() WHERE deptid = @deptid";
                res = DbAccess.Database.SqlExecute(updateQuery, new { isHosted = isHosted, modifiedUser = modifiedUser, deptid = deptid }) > 0;
            }
            else
            {
                // 不存在數據，進行新增
                string insertQuery = $"INSERT INTO sys_hosted_dept (deptid, create_user, create_time, is_hosted) VALUES(@deptid, @create_user, getutcdate(), @isHosted)";
                res = DbAccess.Database.SqlExecute(insertQuery, new { deptid = deptid, isHosted = isHosted, create_user = modifiedUser }) > 0;
            }

            return res;
        }
    }
}
