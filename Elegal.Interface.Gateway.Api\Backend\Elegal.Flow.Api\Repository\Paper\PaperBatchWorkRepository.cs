﻿using Elegal.Interface.Api.Common.Control.Filter;
using Elegal.Interface.Api.Common.FuncHelper;
using Elegal.Interface.Api.Common.Model.DBModel;
using Elegal.Interface.Api.Common.Model.ParaModel.FlowApi;
using Elegal.Interface.Api.Common.Model.ViewModel.FlowApi;
using Elegal.Interface.Api.Common.Repository;
using Elegal.Orm;
using Elegal.Orm.Dtos;
using System.Text;

namespace Elegal.Flow.Api.Repository.Paper
{
    /// <summary>
    /// 紙本批次作業
    /// </summary>
    public class PaperBatchWorkRepository : BaseRepository
    {
        private readonly string Schema = DbAccess.Database.GetDbSchema();

        #region 查詢存放位置清單
        /// <summary>
        /// 查詢存放位置清單
        /// </summary>
        /// <returns></returns>
        public List<string> GetBatchPositionList()
        {
            string sql = $@"SELECT 
	                            DISTINCT batch_position 
                            FROM
	                            {Schema}.paper_batch_work
                            ORDER BY
                                batch_position ASC";
            return NpgsqlSearchByList<string>(sql);
        }
        #endregion

        #region 查詢紙本批次資料
        /// <summary>
        /// 查詢紙本批次資料
        /// </summary>
        /// <param name="qry"></param>
        /// <returns></returns>
        public PageResult<PaperBatchDataViewModel> QueryPaperBatchData(qryPaperBatchData qry)
        {
            PageResult<PaperBatchDataViewModel> result = new PageResult<PaperBatchDataViewModel>();
            StringBuilder countSql = new StringBuilder();
            countSql.Append(@"SELECT
                                    COUNT(1)
                              FROM (
                                      SELECT paper_basic_id, pbw.paper_code,vg.contract_number,pbw.paper_name,batch_entry_status,batch_position,pbw.create_time,pbw.create_user 
                                        FROM paper_batch_work pbw 
                                        LEFT JOIN paper_basic_data pbd on pbd.basic_id = pbw.paper_basic_id
							            LEFT JOIN paper_application_data pad on pbd.paper_applica_id = pad.application_id
							            LEFT JOIN V_GetApplicationSearch vg on pad.apply_number = vg.apply_number
                                      WHERE 1 = 1");
            StringBuilder sql = new StringBuilder();
            sql.Append(@"SELECT
                            paper_basic_id as basic_id,--基本資料ID
                            paper_code,--紙本編號
                            contract_number,--合約編號
                            paper_name,--紙本名稱
                            batch_entry_status,--入庫狀態
	                        (SELECT fun_name FROM sys_parameters WHERE lang_type = @lang_type AND para_code = N'lib_paperEntryStatus' AND func_code = batch_entry_status) AS batch_entry_status_name,--入庫狀態
                            batch_position,--存放位置
                            create_time AS operate_time,--更新時間
                            ee.name AS operate_cuser,--更新人中文名
                            ee.name_a AS operate_euser--更新人英文名
                        FROM (
                            SELECT paper_basic_id, pbw.paper_code,vg.contract_number,pbw.paper_name,batch_entry_status,batch_position,pbw.create_time,pbw.create_user 
                            FROM paper_batch_work pbw 
                            LEFT JOIN paper_basic_data pbd on pbd.basic_id = pbw.paper_basic_id
							LEFT JOIN paper_application_data pad on pbd.paper_applica_id = pad.application_id
							LEFT JOIN V_GetApplicationSearch vg on pad.apply_number = vg.apply_number
                            WHERE 1 = 1");

            StringBuilder condition = new StringBuilder();

            //紙本/合約編號
            if (!string.IsNullOrEmpty(qry.paper_code))
            {
                condition.Append(@$" AND ( pbw.paper_code LIKE CONCAT(N'%', @paper_code, N'%') OR vg.contract_number LIKE CONCAT(N'%', @paper_code, N'%'))");
            }
            //紙本/合約名稱
            if (!string.IsNullOrEmpty(qry.paper_name))
            {
                condition.Append(@$" AND ( pbw.paper_name LIKE CONCAT(N'%', @paper_name, N'%') OR vg.contract_name LIKE CONCAT(N'%', @paper_name, N'%'))");
            }
            //存放位置
            if (qry.batch_position != null && qry.batch_position.Count > 0)
            {
                string[] batch_position = qry.batch_position.ToArray();
                condition.Append(@$" AND pbw.batch_position IN ({string.Join(",", batch_position.Select(s => $"N'{s}'"))})");
            }
            //入庫狀態
            if (!string.IsNullOrEmpty(qry.batch_entry_status))
            {
                string[] types = qry.batch_entry_status.Split(',');
                condition.Append(@$" AND pbw.batch_entry_status IN ({string.Join(",", types.Select(s => $"'{s}'"))})");
            }
            //開始時間
            if (qry.start_time != null && qry.start_time != DateTime.MinValue)
            {
                condition.Append(@$" AND pbw.create_time >= @start_time");
            }
            //結束時間
            if (qry.end_time != null && qry.end_time != DateTime.MinValue)
            {
                condition.Append(@$" AND pbw.create_time <= @end_time");
            }

            condition.Append($@") AS batchData");
            condition.Append($@" LEFT JOIN (SELECT emplid,name,name_a FROM ps_sub_ee_lgl_vw_a) AS ee ON ee.emplid = batchData.create_user");
            countSql.Append(condition.ToString());
            int totalCount = DbAccess.Database.SqlQuery<int>(countSql.ToString(), new
            {
                qry.paper_code,
                qry.paper_name,
                qry.batch_position,
                qry.start_time,
                qry.end_time,
                lang_type = MvcContext.UserInfo.logging_locale
            }).FirstOrDefault();

            sql.Append(condition.ToString());
            if (qry.page == null || qry.page.PageSize == 0 && qry.page.PageIndex == 0)
                qry.page = new PageParam() { PageIndex = 1, PageSize = 25 };

            if (qry.order == null || string.IsNullOrEmpty(qry.order.Field))
                qry.order = new OrderByParam() { Field = "operate_time", Order = OrderBy.DESC };

            int offset = (qry.page.PageIndex - 1) * qry.page.PageSize;
            string orderStr = qry.order.Field + " " + qry.order.Order.ToString();
            sql.Append(@$" ORDER BY {orderStr} OFFSET {offset} ROWS FETCH NEXT {qry.page.PageSize} ROWS ONLY");
            result.Data = NpgsqlSearchByList<PaperBatchDataViewModel>(sql.ToString(),
            new
            {
                qry.paper_code,
                qry.paper_name,
                qry.batch_position,
                qry.start_time,
                qry.end_time,
                lang_type = MvcContext.UserInfo.logging_locale
            });
            result.TotalRows = totalCount;
            foreach (var item in result.Data)
            {
                if (!string.IsNullOrEmpty(item.contract_number)) item.contract_number = item.contract_number.Replace("\u001f", "");
                item.local_operate_time = item.operate_time.ConvertDateByTimeZoneByUtc(MvcContext.UserInfo.time_zone);
            }
            return result;
        }
        #endregion

        #region 根據掃碼/輸入查詢對應的合約編號與紙本名稱
        /// <summary>
        /// 根據掃碼/輸入查詢對應的合約編號與紙本名稱
        /// </summary>
        /// <param name="paper_code"></param>s
        public List<PaperBatchDataViewModel> GetPaperBatchDataList(string paper_code)
        {
            string sql = $@"SELECT TOP(200) 
                                basic_id,--基本資料ID 
                                paper_code,--紙本編號 
                                paper_name,--紙本名稱 
                                (SELECT vg.contract_number FROM paper_application_data pad INNER JOIN V_GetApplicationSearch vg on pad.apply_number = vg.apply_number WHERE application_id = paper_applica_id) AS contract_number,--合約編號
                                paper_entry_status as batch_entry_status, --入庫狀態
                                (SELECT fun_name FROM sys_parameters WHERE lang_type = N'{MvcContext.UserInfo.logging_locale}' AND para_code = N'lib_paperEntryStatus' AND func_code = paper_entry_status)  as batch_entry_status_name
                            FROM paper_basic_data WHERE paper_code Like CONCAT(N'%', @paper_code, N'%')";
            var res = NpgsqlSearchByList<PaperBatchDataViewModel>(sql, new { paper_code });
            foreach (var item in res)
            {
                if (!string.IsNullOrEmpty(item.contract_number)) item.contract_number = item.contract_number.Replace("\u001f", "");
            }
            return res;
        }
        #endregion

        #region 紙本批次作業新增時的查詢驗證
        /// <summary>
        /// 紙本批次作業新增時的查詢驗證
        /// </summary>
        /// <param name="basic_id_list"></param>
        /// <returns></returns>
        public List<PaperBasicApplicationViewModel> GetPaperBasicData(List<int> basic_id_list)
        {
            string sql = $@"SELECT basic_id,paper_code,paper_name,paper_position,paper_entry_status,destroy_time,lost_time,vg.contract_number,vg.contract_name, paper_remarks 
                            FROM
                                (
                                    SELECT basic_id,paper_applica_id,paper_code,paper_name,paper_position,paper_entry_status,destroy_time,lost_time, paper_remarks FROM paper_basic_data WHERE 1 = 1
                                    AND basic_id IN ({string.Join(",", basic_id_list.Select(s => $"'{s}'"))}) 
                                ) AS baseData
                            INNER JOIN paper_application_data AS pads ON baseData.paper_applica_id = pads.application_id
	                        LEFT JOIN V_GetApplicationSearch vg on vg.apply_number = pads.apply_number";
            List<PaperBasicApplicationViewModel> res = NpgsqlSearchByList<PaperBasicApplicationViewModel>(sql, new { basic_id_list });
            foreach (var item in res)
            {
                if (!string.IsNullOrEmpty(item.contract_number)) item.contract_number = item.contract_number.Replace("\u001f", "");
                if (!string.IsNullOrEmpty(item.contract_name)) item.contract_name = item.contract_name.Replace("\u001f", "");
            }
            return res;
        }
        #endregion

        #region 紙本批次作業(事務寫法)

        #region 新增紙本批次作業
        /// <summary>
        /// 新增紙本批次作業
        /// </summary>
        /// <param name="batch_list"></param>
        /// <param name="context"></param>
        /// <returns></returns>
        public bool InsertPaperBatchWorkToTransaction(List<paper_batch_work> batch_list, IDbContext context)
        {
            string sql = $@"INSERT INTO paper_batch_work
                                (batch_number, paper_basic_id, paper_code, paper_name, contract_number, contract_name, batch_position, paper_position, batch_entry_status, paper_entry_status, batch_destory_time, destroy_time, batch_lost_time, lost_time, batch_remarks, create_user, create_time, batch_destroy_reason, batch_lost_reason)
                            VALUES(@batch_number, @paper_basic_id, @paper_code, @paper_name, @contract_number, @contract_name, @batch_position, @paper_position, @batch_entry_status, @paper_entry_status, @batch_destory_time, @destroy_time, @batch_lost_time, @lost_time, @batch_remarks, @create_user, @create_time, @batch_destory_reason, @batch_lost_reason)";
            return ExecuteCommandToTransaction(sql, context, batch_list) > 0;
        }
        #endregion

        #region 批次修改紙本基本資料
        /// <summary>
        /// 批次修改紙本基本資料
        /// </summary>
        /// <param name="data_list"></param>
        /// <param name="context"></param>
        /// <returns></returns>
        public bool BatchUpdatePaperBasicDataToTransaction(List<paper_basic_data> data_list, IDbContext context)
        {
            string sql = $@"UPDATE paper_basic_data SET 
                                paper_position = @paper_position, 
                                paper_entry_status = @paper_entry_status, 
                                destroy_time = @destroy_time, 
                                lost_time = @lost_time, 
                                destroy_reason = @destroy_reason,
                                lost_reason = @lost_reason,
                                paper_remarks = @paper_remarks,
                                modify_user = @modify_user, 
                                modify_time = @modify_time 
                            WHERE basic_id = @basic_id";
            return ExecuteCommandToTransaction(sql, context, data_list) > 0;
        }
        #endregion

        #region 新增歷程數據
        /// <summary>
        /// 新增歷程數據
        /// </summary>
        /// <param name="data"></param>
        /// <param name="context"></param>
        /// <returns></returns>
        public bool BatchInsertPaperHistoryDataToTransaction(List<paper_history_data> data, IDbContext context)
        {
            string sql = $@"INSERT INTO paper_history_data (
                                paper_basic_id,
                                paper_entry_status,
                                course_emplid,
                                paper_remarks,
                                borrow_applynumber,
                                create_user,
                                create_time ) 
                            VALUES ( 
                                @paper_basic_id,
                                @paper_entry_status,
                                @course_emplid,
                                @paper_remarks,
                                @borrow_applynumber,
                                @create_user,
                                @create_time )";
            return ExecuteCommandToTransaction(sql, context, data) > 0;
        }
        #endregion 

        #endregion
    }
}
