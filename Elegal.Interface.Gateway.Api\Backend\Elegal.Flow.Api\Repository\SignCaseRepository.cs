﻿using Elegal.Interface.Api.Common.FuncHelper;
using Elegal.Interface.Api.Common.Model.ViewModel.PermissionApi.Home;
using Elegal.Interface.Api.Common.Repository;

namespace Elegal.Flow.Api.Repository
{
    /// <summary>
    /// 簽核中案件查詢
    /// </summary>
    public class SignerCaseRepository : BaseRepository
    {
        #region 申請單簽核關卡匯總

        /// <summary>
        /// 申請單簽核關卡匯總
        /// </summary>
        private static readonly List<string> signOffStep = AppSettingHelper.GetValue("signOffStep").Split(",").ToList();
        #endregion

        #region 我的案件查詢

        #region 我的案件查詢 -> 申請單案件
        /// <summary>
        /// 我的案件查詢 -> 申請單案件
        /// </summary>
        /// <param name="emplID"></param>
        /// <param name="langType"></param>
        /// <returns></returns>
        public List<HomeCaseViewModel> GetMyCase(string emplID, string langType = "ZH-TW")
        {
            string getMyCaseSql = @"
                         SELECT
                         appData.apply_number,--申請單號
                         appData.apply_time,--申請日期
                         appData.entity_id,--主體id
                         appData.apply_form,--案件類型層級拼接
                         appData.apply_type,--申請單類型(一級)
                         appData.form_type,--申請單類型(二級)
                         (SELECT entity FROM dbo.fnp_entity AS fe WHERE fe.entity_id = appData.entity_id) AS entity,--主體简称
                         (SELECT CONCAT(N'(',entity,N')',entity_namec,N'/',entity_namee) AS entity_name FROM dbo.fnp_entity AS fe WHERE fe.entity_id = appData.entity_id) AS entity_name,--我方主體
                         appData.other_party,--他方
                         CASE WHEN appData.apply_type = N'O'
                         THEN (SELECT fun_name FROM dbo.sys_parameters WHERE para_code = N'formType_O' AND lang_type = @langType AND func_code = CONVERT(NVARCHAR,appData.form_type))
                         ELSE appData.contract_name END AS contract_name,--合約名稱
                         appData.pic_emplid,--經辦人
                         (CASE WHEN appData.form_type = N'AR' THEN appData.pic_emplid ELSE ee.name END) AS pic_cname,--經辦人中文名稱
                         (CASE WHEN appData.form_type = N'AR' THEN appData.pic_emplid ELSE ee.name_a END) AS pic_ename,--經辦人英文名稱
                         fss.step_id,--關卡id
                         (SELECT fun_name FROM (SELECT func_code,fun_name FROM dbo.sys_parameters AS sp WHERE lang_type = @langType
                         AND EXISTS(SELECT 1 FROM (SELECT value AS para_code FROM STRING_SPLIT(@signOffStep, ',')) AS fpc WHERE fpc.para_code = sp.para_code)) AS sp WHERE sp.func_code = CONVERT(NVARCHAR,fss.step_id)) AS step_name,--關卡名稱
                         (SELECT signerName FROM dbo.F_GetApplicationSignerName(appData.apply_number) WHERE inviteSigner = fss.is_invitee AND termination_flag = 0) AS signer_name,--目前待審人員(在職)
                         (SELECT signerName FROM dbo.F_GetApplicationSignerName(appData.apply_number) WHERE inviteSigner = fss.is_invitee AND termination_flag = 1) AS signer_name_over,--目前待審人員(離職)
                         appData.application_state,--案件狀態
                         (SELECT fun_name FROM dbo.sys_parameters AS sa WHERE para_code = N'applicationState' AND lang_type = @langType AND sa.func_code = CONVERT(NVARCHAR,appData.application_state)) AS application_state_name,--案件狀態
                         fss.is_acknowledge,--是否被acknowledge
                         fss.is_invitee,--是否被Invite
                         appData.confiden_level,--機密等級
                         (SELECT fun_name FROM dbo.sys_parameters WHERE lang_type = @langType AND para_code = N'confidentStatus' AND func_code = CONVERT(NVARCHAR,appData.confiden_level)) AS confiden_level_name--機密等級名稱
                         FROM (
                         --自己為經辦人/填單人/現任聯絡人的案件
                         SELECT apply_form,apply_type,form_type,apply_number,apply_time,entity_id,other_party,contract_name,COALESCE(incumbent_emplid,pic_emplid) AS pic_emplid,application_state,confiden_level FROM v_getallapplication WHERE application_state = N'I' AND (COALESCE(incumbent_emplid,pic_emplid) = @emplID OR COALESCE(incumbent_emplid,fill_emplid) = @emplID)
                         ) AS appData
                         LEFT JOIN (SELECT apply_number,MAX(step_id) AS step_id,MAX(CAST(is_invitee AS INT)) AS is_invitee,MAX(CAST(is_acknowledge AS INT)) AS is_acknowledge FROM dbo.flow_step_signer GROUP BY apply_number) AS fss ON fss.apply_number = appData.apply_number
                         LEFT JOIN (SELECT emplid,name_a,name FROM dbo.ps_sub_ee_lgl_vw_a) AS ee ON ee.emplid = appData.pic_emplid
                         ORDER BY appData.apply_time DESC,appData.apply_number DESC;";

            return this.NpgsqlSearchByList<HomeCaseViewModel>(getMyCaseSql, new
            {
                emplID = emplID,
                langType = langType,
                signOffStep = string.Join(',', signOffStep.Select(s => s))
            });
        }
        #endregion

        #region 我的案件查詢 -> 轉單案件
        /// <summary>
        /// 我的案件查詢 -> 轉單案件
        /// </summary>
        /// <param name="emplID"></param>
        /// <param name="langType"></param>
        /// <returns></returns>
        public List<HomeCaseViewModel> GetTransferPicMyCase(string emplID, string langType = "ZH-TW")
        {
            string getTransferPicMyCaseSql = @"
                         SELECT
                         tData.transfer_pic_number AS apply_number,--申請單號
                         tData.apply_time,--申請時間
                         N'T' AS apply_type,--申請單類型
                         N'經辦人轉單' AS contract_name,--合約名稱(Spec上默認顯示)
                         tData.apply_emplid AS pic_emplid ,--經辦人
                         aee.name AS pic_cname,--經辦人中文名稱
                         aee.name_a AS pic_ename,--經辦人英文名稱,
                         fss.step_id,--關卡id
                         (SELECT fun_name FROM (SELECT func_code,fun_name FROM dbo.sys_parameters AS sp WHERE lang_type = @langType
                         AND EXISTS(SELECT 1 FROM (SELECT value AS para_code FROM STRING_SPLIT(@signOffStep, ',')) AS fpc WHERE fpc.para_code = sp.para_code)) AS sp WHERE sp.func_code = CONVERT(NVARCHAR,fss.step_id)) AS step_name,--目前關卡
                         (SELECT signerName FROM dbo.F_GetApplicationSignerName(tData.transfer_pic_number)) AS signer_name,--目前待審人員
                         tData.trans_application_status AS application_state,--案件狀態
                         (SELECT fun_name FROM dbo.sys_parameters WHERE para_code = N'transApplyStatus' AND lang_type = @langType
                         AND func_code = CONVERT(NVARCHAR,tData.trans_application_status)
                         ) AS application_state_name--案件狀態中文名稱
                         FROM (
                         --自己作為填單人案件
                         SELECT transfer_pic_number,apply_time,trans_application_status,is_new,apply_emplid
                         FROM dbo.transfer_pic_main WHERE 1 = 1 AND trans_application_status <> N'04' AND trans_application_status <> N'05'
                         AND fill_emplid = @emplID
                         ) AS tData
                         LEFT JOIN (SELECT apply_number,MAX(step_id) AS step_id,MAX(CAST(is_invitee AS INT)) AS is_invitee,MAX(CAST(is_acknowledge AS INT)) AS is_acknowledge FROM dbo.flow_step_signer GROUP BY apply_number) AS fss ON fss.apply_number = tData.transfer_pic_number
                         --經辦人/原聯絡人(轉出者)
                         LEFT JOIN (SELECT emplid,name,name_a FROM dbo.ps_sub_ee_lgl_vw_a) AS aee ON aee.emplid = tData.apply_emplid
                         ORDER BY tData.apply_time DESC,tData.transfer_pic_number DESC;";
            return this.NpgsqlSearchByList<HomeCaseViewModel>(getTransferPicMyCaseSql, new
            {
                emplID = emplID,
                langType = langType,
                signOffStep = string.Join(',', signOffStep.Select(s => s))
            });
        }
        #endregion

        #endregion

        #region 會審人員查詢

        #region 會審人員查詢 -> 申請單案件
        /// <summary>
        /// 會審人員查詢 -> 申請單案件
        /// </summary>
        /// <param name="emplID"></param>
        /// <param name="langType"></param>
        /// <returns></returns>
        public List<HomeCaseViewModel> GetSignerCase(string emplID, string langType = "ZH-TW")
        {
            //eidt by SpringJiang 20250620 -> 添加 flow_step_history_from_wilegal 查詢範圍
            string getSignerCaseSql = @"
                         SELECT
                         appData.apply_number,--申請單號
                         appData.apply_time,--申請日期
                         appData.entity_id,--主體id
                         appData.apply_form,--案件類型層級拼接
                         appData.apply_type,--申請單類型(一級)
                         appData.form_type,--申請單類型(二級)
                         (SELECT entity FROM dbo.fnp_entity AS fe WHERE fe.entity_id = appData.entity_id) AS entity,--主體简称
                         (SELECT CONCAT(N'(',entity,N')',entity_namec,N'/',entity_namee) AS entity_name FROM dbo.fnp_entity AS fe WHERE fe.entity_id = appData.entity_id) AS entity_name,--我方主體
                         appData.other_party,--他方
                         CASE WHEN appData.apply_type = N'O'
                         THEN (SELECT fun_name FROM dbo.sys_parameters WHERE para_code = N'formType_O' AND lang_type = @langType AND func_code = CONVERT(NVARCHAR,appData.form_type))
                         ELSE appData.contract_name END AS contract_name,--合約名稱
                         appData.pic_emplid,--經辦人
                         (CASE WHEN appData.form_type = N'AR' THEN appData.pic_emplid ELSE ee.name END) AS pic_cname,--經辦人中文名稱
                         (CASE WHEN appData.form_type = N'AR' THEN appData.pic_emplid ELSE ee.name_a END) AS pic_ename,--經辦人英文名稱
                         fss.step_id,--關卡id
                         (SELECT fun_name FROM (SELECT func_code,fun_name FROM dbo.sys_parameters AS sp WHERE lang_type = @langType
                         AND EXISTS(SELECT 1 FROM (SELECT value AS para_code FROM STRING_SPLIT(@signOffStep, ',')) AS fpc WHERE fpc.para_code = sp.para_code)) AS sp WHERE sp.func_code = CONVERT(NVARCHAR,fss.step_id)) AS step_name,--關卡名稱
                         (SELECT signerName FROM dbo.F_GetApplicationSignerName(appData.apply_number) WHERE inviteSigner = fss.is_invitee AND termination_flag = 0) AS signer_name,--目前待審人員(在職)
                         (SELECT signerName FROM dbo.F_GetApplicationSignerName(appData.apply_number) WHERE inviteSigner = fss.is_invitee AND termination_flag = 1) AS signer_name_over,--目前待審人員(離職)
                         appData.application_state,--案件狀態
                         (SELECT fun_name FROM dbo.sys_parameters AS sa WHERE para_code = N'applicationState' AND lang_type = @langType AND sa.func_code = CONVERT(NVARCHAR,appData.application_state)) AS application_state_name,--案件狀態
                         fss.is_acknowledge,--是否被acknowledge
                         fss.is_invitee,--是否被Invite
                         appData.confiden_level,--機密等級
                         (SELECT fun_name FROM dbo.sys_parameters WHERE lang_type = @langType AND para_code = N'confidentStatus' AND func_code = CONVERT(NVARCHAR,appData.confiden_level)) AS confiden_level_name--機密等級名稱
                         FROM (SELECT apply_form,apply_type,form_type,apply_number,apply_time,entity_id,other_party,contract_name,COALESCE(incumbent_emplid,pic_emplid) AS pic_emplid,application_state,confiden_level FROM v_getallapplication AS gaa WHERE application_state = N'I' AND EXISTS (SELECT 1 FROM (
                         SELECT apply_number,actual_signer_emplid,should_signer_emplid,step_action FROM dbo.flow_step_history_from_wilegal
                         UNION ALL
                         SELECT apply_number,actual_signer_emplid,should_signer_emplid,step_action FROM dbo.flow_step_history
                         ) AS fsh WHERE (fsh.actual_signer_emplid = @emplID OR fsh.should_signer_emplid = @emplID) AND step_action <> 0 AND fsh.apply_number = gaa.apply_number)) AS appData
                         LEFT JOIN (SELECT apply_number,MAX(step_id) AS step_id,MAX(CAST(is_invitee AS INT)) AS is_invitee,MAX(CAST(is_acknowledge AS INT)) AS is_acknowledge FROM dbo.flow_step_signer GROUP BY apply_number) AS fss ON fss.apply_number = appData.apply_number
                         LEFT JOIN (SELECT emplid,name_a,name FROM dbo.ps_sub_ee_lgl_vw_a) AS ee ON ee.emplid = appData.pic_emplid
                         ORDER BY appData.apply_time DESC,appData.apply_number DESC;";

            return this.NpgsqlSearchByList<HomeCaseViewModel>(getSignerCaseSql, new
            {
                emplID = emplID,
                langType = langType,
                signOffStep = string.Join(',', signOffStep.Select(s => s))
            });
        }
        #endregion

        #region 會審人員查詢 -> 轉單案件
        /// <summary>
        /// 會審人員查詢 -> 轉單案件
        /// </summary>
        /// <param name="emplID"></param>
        /// <param name="langType"></param>
        /// <returns></returns>
        public List<HomeCaseViewModel> GetTransferPicSignerCase(string emplID, string langType = "ZH-TW")
        {
            //eidt by SpringJiang 20250619 -> 添加 flow_step_history_from_wilegal 查詢範圍
            string getTransferPicSignerCaseSql = @"
                         SELECT
                         tData.transfer_pic_number AS apply_number,--申請單號
                         tData.apply_time,--申請時間
                         N'T' AS apply_type,--申請單類型
                         N'經辦人轉單' AS contract_name,--合約名稱(Spec上默認顯示)
                         tData.apply_emplid AS pic_emplid ,--經辦人
                         aee.name AS pic_cname,--經辦人中文名稱
                         aee.name_a AS pic_ename,--經辦人英文名稱,
                         fss.step_id,--關卡id
                         (SELECT fun_name FROM (SELECT func_code,fun_name FROM dbo.sys_parameters AS sp WHERE lang_type = @langType
                         AND EXISTS(SELECT 1 FROM (SELECT value AS para_code FROM STRING_SPLIT(@signOffStep, ',')) AS fpc WHERE fpc.para_code = sp.para_code)) AS sp WHERE sp.func_code = CONVERT(NVARCHAR,fss.step_id)) AS step_name,--目前關卡
                         (SELECT signerName FROM dbo.F_GetApplicationSignerName(tData.transfer_pic_number)) AS signer_name,--目前待審人員
                         tData.trans_application_status AS application_state,--案件狀態
                         (SELECT fun_name FROM dbo.sys_parameters WHERE para_code = N'transApplyStatus' AND lang_type = @langType
                         AND func_code = CONVERT(NVARCHAR,tData.trans_application_status)
                         ) AS application_state_name--案件狀態中文名稱
                         FROM (
                         --自己作為簽核人案件
                         SELECT transfer_pic_number,apply_time,trans_application_status,is_new,apply_emplid
                         FROM dbo.transfer_pic_main AS tpm WHERE 1 = 1 AND trans_application_status <> N'04' AND trans_application_status <> N'05'
                         AND EXISTS (SELECT 1 FROM (
                         SELECT apply_number,actual_signer_emplid,should_signer_emplid,step_action FROM dbo.flow_step_history_from_wilegal
                         UNION ALL
                         SELECT apply_number,actual_signer_emplid,should_signer_emplid,step_action FROM dbo.flow_step_history
                         ) AS fsh WHERE (fsh.actual_signer_emplid = @emplID OR fsh.should_signer_emplid = @emplID) AND step_action <> 0 AND fsh.apply_number = tpm.transfer_pic_number)
                         ) AS tData
                         LEFT JOIN (SELECT apply_number,MAX(step_id) AS step_id,MAX(CAST(is_invitee AS INT)) AS is_invitee,MAX(CAST(is_acknowledge AS INT)) AS is_acknowledge FROM dbo.flow_step_signer GROUP BY apply_number) AS fss ON fss.apply_number = tData.transfer_pic_number
                         --經辦人/原聯絡人(轉出者)
                         LEFT JOIN (SELECT emplid,name,name_a FROM dbo.ps_sub_ee_lgl_vw_a) AS aee ON aee.emplid = tData.apply_emplid
                         ORDER BY tData.apply_time DESC,tData.transfer_pic_number DESC;";

            return this.NpgsqlSearchByList<HomeCaseViewModel>(getTransferPicSignerCaseSql, new
            {
                emplID = emplID,
                langType = langType,
                signOffStep = string.Join(',', signOffStep.Select(s => s))
            });
        }
        #endregion

        #endregion
    }
}
