﻿using Elegal.Flow.Api.Services;
using Elegal.Interface.Api.Common.Control.Filter;
using Elegal.Interface.Api.Common.Control.Web;
using Elegal.Interface.Api.Common.Model.ParaModel.FlowApi;
using Elegal.Interface.Api.Common.Model.ResultModel;
using Elegal.Interface.Api.Common.Model.ViewModel.FlowApi;
using Elegal.Orm.Dtos;
using MathNet.Numerics.LinearAlgebra.Factorization;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Text;

namespace Elegal.Flow.Api.Controllers
{
    /// <summary>
    /// Co-Head簽核管理
    /// </summary>
    [Route("[controller]")]
    [ApiController]
    public class SysCoHeadDeptController : BaseController
    {
        /// <summary>
        /// 查詢部門層級清單
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("GetTreeLevelList")]
        public async Task<ApiResultModelByObject> GetTreeLevelList()
        {
            ApiResultModelByObject apiResult = new ApiResultModelByObject();

            apiResult.listData = SysCoHeadDeptService.GetTreeLevelList();
            apiResult.rtnSuccess = true;
            InitLogRecord<object>(new(), logRecord =>
            {
                StringBuilder stringBuilder = new();
                stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("SysCohead_GetTreeLevelList", true)}");
                logRecord.Detail = stringBuilder.ToString();
            });

            return await Task.FromResult(apiResult);
        }

        /// <summary>
        /// 查詢co_head清單
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [Route("Query")]
        public async Task<ApiResultModelByObject> QueryCoHeadDept([FromBody] qrySysCoHeadDeptModel qry)
        {
            ApiResultModelByObject apiResult = new ApiResultModelByObject();

            PageResult<SysCoheadDeptViewModel> result = SysCoHeadDeptService.QueryCoHeadDept(qry);
            apiResult.listData = result.Data;
            apiResult.totalCount = result.TotalRows;
            apiResult.rtnSuccess = true;
            InitLogRecord(qry, logRecord =>
            {
                StringBuilder stringBuilder = new();
                stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("SysCohead_QueryCoHeadDept", true)}");
                if (!string.IsNullOrEmpty(qry.deptid)) stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("SysCohead_Dept", true)}:  {qry.deptid}");
                if (!string.IsNullOrEmpty(qry.dept_type))
                {
                    string dept_type = qry.dept_type.Replace("0", $"{ActionFilter.GetMultilingualValue("SysCohead_NotDelDept", true)}").Replace("1", $"{ActionFilter.GetMultilingualValue("SysCohead_DelDept", true)}").Replace("2", $"{ActionFilter.GetMultilingualValue("SysCohead_AllDept", true)}");
                    stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("SysCohead_DeptStatus", true)}:  {dept_type}");
                }
                if (!string.IsNullOrEmpty(qry.manager)) stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("SysCohead_Manager", true)}:  {qry.manager}");
                if (!string.IsNullOrEmpty(qry.co_head)) stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("SysCohead_CoHead", true)}:  {qry.co_head}");
                if (!string.IsNullOrEmpty(qry.tree_level_num)) stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("SysCohead_DeptLevel", true)}:  {qry.tree_level_num}");
                if (!string.IsNullOrEmpty(qry.is_hosted))
                {
                    string is_hosted = qry.is_hosted.Replace("0", $"{ActionFilter.GetMultilingualValue("SysCohead_NonCustodialDept", true)}").Replace("1", $"{ActionFilter.GetMultilingualValue("SysCohead_CustodialDept", true)}");
                    stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("SysCohead_CustodialDept", true)}:  {is_hosted}");
                }

                if (!string.IsNullOrEmpty(qry.sign_type))
                {
                    string sign_type = qry.sign_type
                    .Replace("0", $"{ActionFilter.GetMultilingualValue("SysCohead_PrincipalManager", true)}")
                    .Replace("1", $"{ActionFilter.GetMultilingualValue("SysCohead_DeputyManager", true)}")
                    .Replace("2", $"{ActionFilter.GetMultilingualValue("SysCohead_PrincipalAndDeputyManager", true)}");
                    stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("SysCohead_CoHeadSign", true)}:  {sign_type}");
                }

                if (qry.createDateStart != null || qry.createDateEnd != null)
                {
                    var createDateStart = qry.createDateStart != null ? TimeZoneInfo.ConvertTimeFromUtc(qry.createDateStart.Value, TimeZoneInfo.FindSystemTimeZoneById(MvcContext.UserInfo.time_zone)).ToString("yyyy/MM/dd"):"";
                    var createDateEnd = qry.createDateEnd != null ? TimeZoneInfo.ConvertTimeFromUtc(qry.createDateEnd.Value, TimeZoneInfo.FindSystemTimeZoneById(MvcContext.UserInfo.time_zone)).ToString("yyyy/MM/dd") : "";
                    stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("SysCohead_DeptCreateDate", true)}：{createDateStart} ~ {createDateEnd}");
                }

                logRecord.Detail = stringBuilder.ToString();
            });

            return await Task.FromResult(apiResult);
        }

        /// <summary>
        /// 查詢上層部門
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("GetUpperDept")]
        public async Task<ApiResultModelByObject> GetUpperDept([FromQuery] string startDeptId)
        {
            ApiResultModelByObject apiResult = new ApiResultModelByObject();

            apiResult.listData = SysCoHeadDeptService.QueryUpperDept(startDeptId);
            apiResult.rtnSuccess = true;
            InitLogRecord<object>(new(), logRecord =>
            {
                StringBuilder stringBuilder = new();
                stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("SysCohead_GetUpperDept", true)}: {startDeptId}");
                logRecord.Detail = stringBuilder.ToString();
            });

            return await Task.FromResult(apiResult);
        }

        /// <summary>
        /// 更新CoHead簽核設定/託管部門
        /// </summary>
        /// <returns></returns>
        [HttpPut]
        [Route("UpdateCoHeadDept")]
        public async Task<ApiResultModelByObject> UpdateCoHeadDept([FromBody] updateSysCoHeadDeptModel req)
        {
            ApiResultModelByObject apiResult = new ApiResultModelByObject();

            SysCoheadDeptViewModel oldData = SysCoHeadDeptService.GetCoHeadDeptByDeptid(req.deptid);

            bool res = SysCoHeadDeptService.UpdateCoHeadDept(req, MvcContext.UserInfo.current_emp);

            apiResult.listData = res;
            apiResult.rtnSuccess = true;
            InitLogRecord(req, logRecord =>
            {
                StringBuilder stringBuilder = new();
                stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("SysCohead_UpdateCoHeadDept", true)}");
                stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("SysCohead_Dept", true)}: {req.deptid}");

                string is_hosted = req.is_hosted == 0 ? $"{ActionFilter.GetMultilingualValue("SysCohead_NonCustodialDept", true)}" : $"{ActionFilter.GetMultilingualValue("SysCohead_CustodialDept", true)}";
                stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("SysCohead_CustodialDept", true)}:  {is_hosted}");

                switch (req.sign_type)
                {
                    case -1:
                        stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("SysCohead_NoSetting", true)}");
                        break;
                    case 0:
                        stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("SysCohead_SignPrincipalManager", true)}");
                        break;
                    case 1:
                        stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("SysCohead_SignDeputyManager", true)}");
                        break;
                    case 2:
                        stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("SysCohead_SignPrincipalAndDeputyManager", true)}");
                        break;
                    default:
                        stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("SysCohead_NoSetting", true)}");
                        break;
                }

                logRecord.Detail = stringBuilder.ToString();

                if (oldData != null)
                {
                    stringBuilder = new();
                    stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("SysCohead_Dept", true)}: {oldData.deptid}");

                    string is_hosted_old = oldData.is_hosted == 0 ? $"{ActionFilter.GetMultilingualValue("SysCohead_NonCustodialDept", true)}" : $"{ActionFilter.GetMultilingualValue("SysCohead_CustodialDept", true)}";
                    stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("SysCohead_CustodialDept", true)}:  {is_hosted_old}");

                    switch (oldData.cohead_sign)
                    {
                        case -1:
                            stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("SysCohead_NoSetting", true)}");
                            break;
                        case 0:
                            stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("SysCohead_SignPrincipalManager", true)}");
                            break;
                        case 1:
                            stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("SysCohead_SignDeputyManager", true)}");
                            break;
                        case 2:
                            stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("SysCohead_SignPrincipalAndDeputyManager", true)}");
                            break;
                        default:
                            stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("SysCohead_NoSetting", true)}");
                            break;
                    }

                    logRecord.DetailFormer = stringBuilder.ToString();
                }
            });

            return await Task.FromResult(apiResult);
        }
    }
}
