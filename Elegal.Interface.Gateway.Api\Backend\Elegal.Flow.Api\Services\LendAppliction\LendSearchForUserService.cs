﻿using Elegal.Flow.Api.Repository.LendAppliction;
using Elegal.Flow.Api.Repository.Paper;
using Elegal.Flow.Api.Services.Paper;
using Elegal.Interface.Api.Common.Control.Filter;
using Elegal.Interface.Api.Common.FuncHelper;
using Elegal.Interface.Api.Common.FuncService;
using Elegal.Interface.Api.Common.Model.DBModel;
using Elegal.Interface.Api.Common.Model.ParaModel.FlowApi.LendAppliction;
using Elegal.Interface.Api.Common.Model.ResultModel.FlowApi;
using Elegal.Interface.Api.Common.Model.ResultModel.FlowApi.LendAppliction;
using Elegal.Interface.Api.Common.Model.ViewModel;
using NPOI.SS.UserModel;
using NPOI.XSSF.UserModel;

namespace Elegal.Flow.Api.Services.LendAppliction;

public static class LendSearchForUserService
{
    private static LendSearchForUserRepository _LendSearchRepository = new LendSearchForUserRepository();
    private static readonly PaperLendingApplicationRepository _repository = new PaperLendingApplicationRepository();

    /// <summary>
    /// 借出查詢 我的借出，當前借出
    /// </summary>
    /// <param name="para"></param>
    /// <param name="timeZone"></param>
    /// <returns>（當前頁碼數據，總數據條數）</returns>
    public static List<LendUserModel> QueryLendForUser(LendForUserPara para, UserInfoModel user)
    {
        if (para.LendStatus == null || para.LendStatus.Count == 0)
        {
            para.LendStatus = new List<string>() { "02", "03" };
        }
        para.LendTakeTime.SetUserTimeZone(user.time_zone);//設置用戶時區
        return _LendSearchRepository.QueryLendForUser(para, user.current_emp, user.logging_locale, e =>
                             e.CreateTime = e.CreateTime.ConvertDateByTimeZoneByUtc(user.time_zone)
                         );
    }

    /// <summary>
    /// 查询纸本借阅详情
    /// </summary>
    /// <param name="paperLendId">主表ID</param>
    /// <param name="timeZone">用户时区</param>
    /// <returns></returns>
    public static List<PaperLendingDetail> QueryPaperLendListForUser(int paperLendId, string timeZone)
    {
        return _LendSearchRepository.QueryPaperLendingDetailList(paperLendId, row =>
        {

            row.IsOverdueAndNotReturn = (row.OverdueDay.HasValue && row.OverdueDay.Value > 0)
                                     || (!row.ActualReturnTime.HasValue && row.ShouldReturnTime.HasValue && row.ShouldReturnTime.Value < DateTime.UtcNow.Date)
                                       ;
            row.ContractNumber = row.ContractNumber?.Replace("\u001f", "");
            row.AuthDateEnd = row.AuthDateEnd.ConvertDateByTimeZoneByUtc(timeZone);
            row.ShouldReturnTime = row.ShouldReturnTime.ConvertDateByTimeZoneByUtc(timeZone);

            //未取件的没有归还状态
            if (row.IsPickupLend != 1) row.IsReturn = null;

        });
    }


    /// <summary>
    /// 我的借出，当前借出，导出
    /// </summary>
    /// <param name="para"></param>
    /// <param name="user"></param>
    /// <returns></returns>
    public static byte[] QueryLendForUserExport(LendForUserExportPara para, UserInfoModel user)
    {
        para.LendTakeTime.SetUserTimeZone(user.time_zone);//設置用戶時區
        List<OnLendExportModel> data = _LendSearchRepository.QueryLendForUserExport(para, user.current_emp, user.logging_locale);
        //创建工作表
        IWorkbook workbook = new XSSFWorkbook();
        ISheet sheet = workbook.CreateSheet("Sheet1");

        //定义一个字体
        IFont font = workbook.CreateFont().configFont(f =>
        {
            f.FontHeightInPoints = 12;
            f.FontName = "Calibri";
        });
        //定义一个字体
        IFont fontRed = workbook.CreateFont().configFont(f =>
        {
            f.FontHeightInPoints = 12;
            f.FontName = "Calibri";
            f.Color = IndexedColors.Red.Index;
        });

        //定义一个样式
        ICellStyle redStyle = workbook.CreateCellStyle().SetDefaultBorder().AddFont(fontRed);
        ICellStyle defaultstyle = sheet.Workbook.CreateCellStyle().SetDefaultBorder().AddFont(font);
        ICellStyle styleCenter = sheet.Workbook.CreateCellStyle().SetDefaultBorder().HorizontallyCenter().AddFont(font);

        //设置表头
        List<string> heards = new List<string>()
        {
                ActionFilter.GetMultilingualValue("export:lendId"),
                ActionFilter.GetMultilingualValue("export:accessNumber"),
                ActionFilter.GetMultilingualValue("export:applyDate"),
                ActionFilter.GetMultilingualValue("export:applyStatus"),
                ActionFilter.GetMultilingualValue("export:contractNum"),
                ActionFilter.GetMultilingualValue("export:paperId"),
                ActionFilter.GetMultilingualValue("export:paperName"),
                ActionFilter.GetMultilingualValue("export:shouldReturnDate"),
                ActionFilter.GetMultilingualValue("export:pickUp"),
                ActionFilter.GetMultilingualValue("export:returnStatus")

        };
        sheet.SetHeader(heards);
        //设置列宽
        sheet.SetColumnListWidth(new List<int>() { 4000, 4000, 4000, 4000, 4000, 4000, 7000, 4000, 4000, 4000 });

        //写入数据
        sheet.WriteData(data, (item, row) =>
        {
            item.ContractNumber = item.ContractNumber?.Replace("\u001f", "") ?? string.Empty;
            row.CreateElegalDefaultCell(0, defaultstyle).SetCellValue(item.LendNumber);
            row.CreateElegalDefaultCell(1, defaultstyle).SetCellValue(item.RetrieveNumber);
            row.CreateElegalDefaultCell(2, defaultstyle).SetCellValue(item.CreateTime.ConvertDateByTimeZoneByUtc(user.time_zone).ToString("yyyy/MM/dd"));
            row.CreateElegalDefaultCell(3, defaultstyle).SetCellValue(item.LendStatusName);
            row.CreateElegalDefaultCell(4, defaultstyle).SetCellValue(item.ContractNumber);
            row.CreateElegalDefaultCell(5, defaultstyle).SetCellValue(item.PaperCode);
            row.CreateElegalDefaultCell(6, defaultstyle).SetCellValue(item.PaperName);
            row.CreateElegalDefaultCell(7, defaultstyle).SetCellValue(item.ShouldReturnTime.ConvertDateByTimeZoneByUtc(user.time_zone).ToString("yyyy/MM/dd"));
            row.CreateElegalDefaultCell(8, defaultstyle).SetCellValue(item.IsPickupLend == 1 ? ActionFilter.GetMultilingualValue("export:hasPickUp") : ActionFilter.GetMultilingualValue("export:noPickUp"));
            row.CreateElegalDefaultCell(9, defaultstyle).SetCellValue(item.IsReturn == 1 ? ActionFilter.GetMultilingualValue("export:hasReturn") : ActionFilter.GetMultilingualValue("export:noReturn"));

            // 应归还日期为今天00:00之前，也就是昨天以前 (逾期未归还)
            if ((item.OverdueDay.HasValue && item.OverdueDay.Value > 0)
            || (!item.ActualReturnTime.HasValue && item.ShouldReturnTime.HasValue && item.ShouldReturnTime.Value < DateTime.UtcNow.Date))
            {
                row.Cells[7].CellStyle = redStyle;

            }
        });
        return workbook.ToBytes();
    }


    /// <summary>
    /// 借出检视 详情数据
    /// </summary>
    /// <param name="lendId">出借表主键</param>
    /// <returns></returns>
    public static LoanDetailsView GetLoanDetailsView(int lendId, UserInfoModel user)
    {
        paper_lending_application data = _repository.GetPaperLendingApplicationByLendID(lendId);
        if (data == null) throw new Exception(ActionFilter.GetMultilingualValue("custom:messageContent:dataChurn"));

        return new LoanDetailsView()
        {
            PersonnelInfo = _LendSearchRepository.QueryPersonnelInfo(lendId, user.logging_locale).Assign(o =>
            {
                o.ApplicationTime = o.ApplicationTime.ConvertDateByTimeZoneByUtc(user.time_zone);
            }),
            DemandInfo = new DemandInfoWebView().Assign(e =>
            {
                List<DemandInfo> list = _LendSearchRepository.QueryDemandInfo(lendId);
                if (list == null || list.Count == 0) return;
                var d1 = list[0];
                e.RetrieveNumber = d1.RetrieveNumber;
                e.BorrowDays = d1.BorrowDays;
                e.DemandReason = d1.DemandReason;
                e.RetrieveReason = d1.RetrieveReason;
                e.Discloseperson = d1.Discloseperson;
                e.Other = d1.Other + " " + d1.RetrieveReason;
                e.ActualReturnTime = list.Select(e => e.ActualReturnTime).FirstOrDefault(e => e.HasValue);
                e.Otherperson = d1.Otherperson;
                e.AuthDateEnd = d1.AuthDateEnd.ConvertDateByTimeZoneByUtc(user.time_zone);
                e.AuthDateStart = d1.AuthDateStart.ConvertDateByTimeZoneByUtc(user.time_zone);
                e.Applynumber = string.Join(",", list.Select(e => e.Applynumber).Distinct());
                string dcstr = ActionFilter.GetMultilingualValue("commonWord:DepartmentCode");
                string dacstr = ActionFilter.GetMultilingualValue("commonWord:DepartmentAndCompany");
                if (list.Any(x => !string.IsNullOrEmpty(x.Empid)))
                {
                    e.InHouseStaff = string.Join("<br />", list.Select(item => $"{item.EName}({item.Empid}),{dcstr}:{item.Depid},{dacstr}{item.Company}").Distinct());
                }
                else
                {
                    e.InHouseStaff = "";
                }
            }),
            PickUpInfo = _LendSearchRepository.QueryPickUpInfo(lendId, user.logging_locale).Assign(o =>
            {
                o.PickupTime = o.PickupTime.ConvertDateByTimeZoneByUtc(user.time_zone);
                o.ActualPickupTime = o.ActualPickupTime.ConvertDateByTimeZoneByUtc(user.time_zone);
            }),
            LendDetails = _LendSearchRepository.QueryPldVList(lendId, user.logging_locale, o =>
            {
                o.ContractNumber = o.ContractNumber?.Replace("\u001f", "");
                o.AuthDateEnd = o.AuthDateEnd.ConvertDateByTimeZoneByUtc(user.time_zone);
                o.ShouldReturnTime = o.ShouldReturnTime.ConvertDateByTimeZoneByUtc(user.time_zone);
            }),
            UnlendingDetails = _LendSearchRepository.QueryPaperUnlendingDetailList(lendId, user.logging_locale, o =>
            {
                o.ContractNumber = o.ContractNumber?.Replace("\u001f", "");
                o.AuthDateEnd = o.AuthDateEnd.ConvertDateByTimeZoneByUtc(user.time_zone);
                o.ShouldReturnTime = o.ShouldReturnTime.ConvertDateByTimeZoneByUtc(user.time_zone);
            })
        }.Assign(res =>
        { //2024/3/13依借出申请的检视接口规格添加同一字段
            if (!string.IsNullOrEmpty(res.DemandInfo.RetrieveNumber))
                res.retrieve_detail = PaperLendingApplicationService.GetOtherApplicationDetail(res.DemandInfo.RetrieveNumber).FirstOrDefault();
        });
    }
}
