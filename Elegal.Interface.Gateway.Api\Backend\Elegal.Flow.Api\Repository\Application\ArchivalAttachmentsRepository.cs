﻿using Elegal.Interface.Api.Common.Control.Filter;
using Elegal.Interface.Api.Common.Model.ParaModel.MinioApi;
using Elegal.Interface.Api.Common.Model.ResultModel.FlowApi;
using Elegal.Interface.Api.Common.Model.ViewModel.FlowApi.Application;
using Elegal.Interface.Api.Common.Model.ViewModel.Minio;
using Elegal.Interface.ApiData.Service.Model.Condition;
using Elegal.Interface.ApiData.Service.Model.ViewModel;
using Elegal.Orm;
#nullable disable
namespace Elegal.Flow.Api.Repository
{
    /// <summary>
    /// 档案附件
    /// </summary>
    public class ArchivalAttachmentsRepository
    {
        private readonly string userSql = @"select
	                                emplid,
	                                name,
	                                name_a,
	                                email_address_a
                                from
	                                ps_sub_ee_lgl_vw_a
                                union
                                select
	                                aff_empid emplid,
	                                aff_emp_cname name,
	                                aff_emp_ename name_a,
	                                aff_emp_email email_address_a
                                from
	                                affiliate_emp";
        /// <summary>
        /// 获取申请单基本信息
        /// </summary>
        /// <param name="applyNumber"></param>
        /// <param name="applyType"></param>
        /// <param name="formType"></param>
        /// <returns></returns>
        public ArchivalAttachmentsViewModel GetBaseInformation(string applyNumber, string applyType, string formType)
        {
            string sql = $@"select
	                            af.apply_number applyNum,
	                            case
		                            when af.apply_type = 'C' then 2
                                    when af.apply_type = 'A' AND af.form_type = N'R' then 4
                                    when af.apply_type = 'A' AND af.form_type = N'AR' then 6
		                            when af.apply_type = 'O' then 5
		                            else null
	                            end as applicationType,
	                            af.application_state as applicationState,
                                --申请人信息
	                            af.fill_emplid fillerEmplid,
                                fee.name fillerCname,
                                fee.name_a fillerEname,
                                fee.email_address_a fillerMail,
                                --经办人信息
	                            af.pic_emplid handlerEmplid,
                                pee.name handlerCname,
                                pee.name_a handlerEname,
                                pee.email_address_a handlerMail,
                                --现任联络人信息
	                            af.incumbent_emplid currentContactPerson,
                                iee.name currentContactPersonCname,
                                iee.name_a currentContactPersonEname,
                                --承辦法務人员信息
                                af.legal_affairs_emplid legalEmplid,
                                lee.name legalCname,
                                lee.name_a legalEname,
                                lee.email_address_a legalMail,
                                --机密等级
	                            af.confiden_level confidenLevel,
	                            sp1.fun_name confidenLevelName,
                                ac.contract_number contractNumber
                            from
	                            V_GetAllApplication af
                            left join (select fun_name,func_code,lang_type from sys_parameters where para_code = 'confidentStatus' and lang_type='{MvcContext.UserInfo.logging_locale}') sp1 on sp1.func_code=af.confiden_level
                            left join V_GetAllContractNumber ac on ac.apply_number = af.apply_number
                            left join ({userSql}) lee on lee.emplid = af.legal_affairs_emplid--承辦法務人員
                            left join ({userSql}) pee on pee.emplid = af.pic_emplid--經辦人
                            left join ({userSql}) fee on fee.emplid = af.fill_emplid--申请人
                            left join ({userSql}) iee on iee.emplid = af.incumbent_emplid--現任聯絡人
                            where af.apply_number = {DbAccess.Database.GetParameterPrefix()}applyNumber
                            and apply_type = {DbAccess.Database.GetParameterPrefix()}applyType";
            if (!string.IsNullOrEmpty(formType))
            {
                sql += $" and af.form_type = {DbAccess.Database.GetParameterPrefix()}formType";
            }
            return DbAccess.Database.SqlQuery<ArchivalAttachmentsViewModel>(sql, new { applyNumber, applyType, formType }).FirstOrDefault();
        }
        /// <summary>
        /// 获取文件列表(列印合約、參考資料、歸檔)
        /// </summary>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public IEnumerable<SysUploadFileModel> GetFileList(SysUploadFileViewModel condition)
        {
            string sql = @$"select
                                fileid,
	                            archive_purposes,
	                            file_name,
	                            file_size,
	                            file_explain,
                                has_watermake,
	                            isnull(modify_time,create_time) operate_time,
	                            ee.name operate_cuser,
	                            ee.name_a operate_euser
                            from
	                            sys_upload_file left join ps_sub_ee_lgl_vw_a ee on ee.emplid = isnull(modify_user,create_user)
                            where
	                            is_watermake = 0 and upload_type = {DbAccess.Database.GetParameterPrefix()}upload_type and archive_purposes in(1,2,3) 
	                            and upload_key = {DbAccess.Database.GetParameterPrefix()}upload_key
                            order by archive_purposes,isnull(modify_time,create_time)"; //0247根据更新时间排序
            return DbAccess.Database.SqlQuery<SysUploadFileModel>(sql, new { upload_key = condition.UploadKey, upload_type = condition.UploadType });
        }
        /// <summary>
        /// 获取文件列表(列印合約、參考資料、歸檔)
        /// </summary>
        /// <param name="upload_key">业务关联主键</param>
        /// <param name="upload_type">申请类型</param>
        /// <returns></returns>
        public IEnumerable<SysUploadFileModel> GetFileListByType(string upload_key, int upload_type)
        {
            string sql = @$"select
                                fileid,
                                file_status,
	                            archive_purposes,
	                            file_name,
	                            file_size,
	                            file_explain,
                                has_watermake,
	                            isnull(modify_time,create_time) operate_time,
	                            ee.name operate_cuser,
	                            ee.name_a operate_euser
                            from
	                            sys_upload_file left join ps_sub_ee_lgl_vw_a ee on ee.emplid = create_user
                            where
	                            is_watermake = 0 and upload_type = {DbAccess.Database.GetParameterPrefix()}upload_type and archive_purposes in(1,2,3) 
	                            and upload_key = {DbAccess.Database.GetParameterPrefix()}upload_key
                            order by archive_purposes,create_time";
            return DbAccess.Database.SqlQuery<SysUploadFileModel>(sql, new { upload_key, upload_type });
        }
        /// <summary>
        /// 获取档案历程数据
        /// </summary>
        /// <param name="condition">查询参数</param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public IEnumerable<ArchiveOperationProcessResult> GetArchiveOperationProcess(ArchiveOperationProcessCondition condition)
        {
            string sql = $@"select
	                            sa.action_name,
	                            aop.archive_purposes,
	                            aop.file_name ,
	                            aop.file_size ,
	                            aop.file_explain,
	                            aop.create_time operate_time,
	                            ee.name operate_cuser,
	                            ee.name_a operate_euser
                            from
	                            archive_operation_process aop
                            left join sys_action sa on sa.lang_type = '{MvcContext.UserInfo.logging_locale}' and sa.action_code = aop.action_code 
                            left join ps_sub_ee_lgl_vw_a ee on ee.emplid = aop.create_user
                            where
	                            apply_number = {DbAccess.Database.GetParameterPrefix()}applyNumber and upload_type = {DbAccess.Database.GetParameterPrefix()}uploadType
                            order by aop.create_time asc";//  0247歷程相關顯示順序統一改為正序(舊在上)3.檔案附件>檔案歷程
            return DbAccess.Database.SqlQuery<ArchiveOperationProcessResult>(sql, new { applyNumber = condition.ApplyNumber, uploadType = condition.UploadType });
        }
        /// <summary>
        /// 获取档案历程数据(区分申请类型)
        /// </summary>
        /// <param name="upload_key">业务关联主键</param>
        /// <param name="upload_type">申请类型</param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public IEnumerable<ArchiveOperationProcessResult> GetArchiveOperationProcessByType(string upload_key, int upload_type)
        {
            string sql = $@"select
	                            sa.action_name,
	                            aop.archive_purposes,
	                            aop.file_name ,
	                            aop.file_size ,
	                            aop.file_explain,
	                            aop.create_time operate_time,
	                            ee.name operate_cuser,
	                            ee.name_a operate_euser
                            from
	                            archive_operation_process aop
                            left join sys_action sa on sa.lang_type = '{MvcContext.UserInfo.logging_locale}' and sa.action_code = aop.action_code 
                            left join ps_sub_ee_lgl_vw_a ee on ee.emplid = aop.create_user
                            where
	                            apply_number = {DbAccess.Database.GetParameterPrefix()}applyNumber and upload_type = {DbAccess.Database.GetParameterPrefix()}uploadType
                            order by aop.create_time desc";
            return DbAccess.Database.SqlQuery<ArchiveOperationProcessResult>(sql, new { applyNumber = upload_key, uploadType = upload_type });
        }

        /// <summary>
        /// 用來加水印或者生成文件名 所需要的一些單據信息
        /// </summary>
        /// <param name="applyNumber"></param>
        /// <returns></returns>
        public List<FileModelApplyInfo> GetfileModelApplyInfo(string applyNumber)
        {
            if (string.IsNullOrWhiteSpace(applyNumber)) return null;
            //O单单据类型如果是C或者是D的话，要拿到原单据机密等级
            string sql = @"SELECT 
                            fa.apply_number  --單號
                            ,fa.application_state --案件狀態，用來判斷是否是暫存單
                            ,fa.confiden_level --機密等級
                            ,fa.contract_number, --合約編號
                            fa.UploadType-- 单据类型
                            FROM (
	                            --合約/文件申請
	                            SELECT
	                            f.apply_number,
	                            f.application_state,
	                            f.confiden_level,
	                            (select top 1 faa.contract_number from dbo.form_application_admin faa where faa.apply_number=f.apply_number) as contract_number,
	                            2 as UploadType --合约
	                            FROM dbo.form_application f
	                            UNION
	                            --資料建檔
	                            SELECT
	                            apply_number,
	                            application_state,
	                            confiden_level,
	                            contract_number,
	                            4 as UploadType --资料建档
	                            FROM dbo.literature_application
	                            UNION
	                            --關企建檔
	                            SELECT 
	                            apply_number,
	                            application_state,
	                            confiden_level,
	                            contract_number,
	                            6 as UploadType --關企建檔
	                            FROM dbo.enterprise_application
	                            UNION
	                            --其他申請
	                            SELECT
                                apply_number 
                                ,application_state
                                ,(case form_type 
	                                when 'C' then  --O单单据类型如果是C的话，要拿到原单据机密等级
				                                (select top 1  vgaa.confiden_level  from V_GetAllApplication vgaa 
				                                 join other_application_c oac  on vgaa.apply_number = oac.voided_apply_number 
				                                 where oac.apply_number =oa.apply_number )
	                                when 'D' then --O单单据类型如果是D的话，要拿到原单据机密等级
				                                (select top 1  vgaa.confiden_level  from V_GetAllApplication vgaa 
				                                 join other_application_d oad  on vgaa.apply_number = oad.seal_apply_number 
				                                 where oad.apply_number =oa.apply_number  )
	                                else '02' --默认为02
                                   end
                                 ) AS confiden_level --O单据没有机密等级，默认为01
                                ,null as contract_number
                                ,5 as UploadType --其他申请
                                FROM dbo.other_application   oa
                            ) AS fa
                            where fa.apply_number =@applyNumber ";
            return DbAccess.Database.SqlQuery<FileModelApplyInfo>(sql, new { applyNumber = applyNumber }).ToList();
        }
    }
}
