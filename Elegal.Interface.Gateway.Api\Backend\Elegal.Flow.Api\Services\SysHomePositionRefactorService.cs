﻿using Elegal.Flow.Api.Services.Individual;
using Elegal.Interface.Api.Common.Control.Filter;
using Elegal.Interface.Api.Common.Model.Enum;
using Elegal.Interface.Api.Common.Model.ResultModel.PermissionApi;
using Elegal.Interface.Api.Common.Model;
using Elegal.Interface.ApiData.Service.FuncService;
using Elegal.Interface.ApiData.Service.Model.DbModel;
using Elegal.Interface.ApiData.Service.Model.QueryCondition;
using Newtonsoft.Json;
using Elegal.Orm;
using Elegal.Interface.Api.Common.Model.SqlSugarModels;
using Elegal.Interface.Api.Common.FuncHelper;

namespace Elegal.Flow.Api.Services;

public class SysHomePositionRefactorService
{
    /// <summary>
    /// 获取定位信息
    /// </summary>
    /// <returns></returns>
    public async Task<SysHomePositionView> GetSysHomePositionView()
    {
        SysHomePositionView sysHomePositionView = new();

        sys_home_position sysHomePositionSqlSugar = (await SqlSugarHelper.Db.Queryable<sys_home_position>().FirstAsync(e => e.emplid == MvcContext.UserInfo.current_emp))
                                                 ?? (await SqlSugarHelper.Db.Queryable<sys_home_position>().FirstAsync(e => e.emplid == "sys.admin"));
        SysHomePosition? sysHomePosition = sysHomePositionSqlSugar == null ? null : new SysHomePosition()
        {
            CreateTime = sysHomePositionSqlSugar.create_time,
            CreateUser = sysHomePositionSqlSugar.create_user,
            Emplid = sysHomePositionSqlSugar.emplid,
            HomeLock = sysHomePositionSqlSugar.home_lock ? 1 : 0,
            HomePosition = sysHomePositionSqlSugar.home_position,
            ModifyTime = sysHomePositionSqlSugar.modify_time,
            ModifyUser = sysHomePositionSqlSugar.modify_user
        };
        List<KeyValueObj<string, bool>> BlockShowList = await GetBlockShowList();
        List<SysHomePositionModel> sysHomePositionModelList;
        if (sysHomePosition == null)
        {
            sysHomePositionView.HomeLock = true;
            sysHomePositionModelList = SysHomeBlockService.SysHomePositionModelList;
        }
        else
        {
            sysHomePositionView.HomeLock = sysHomePosition.HomeLock == 1;
            sysHomePositionModelList = JsonConvert.DeserializeObject<List<SysHomePositionModel>>(sysHomePosition.HomePosition) ?? [];
        }
        foreach (var sysHomePositionModel in sysHomePositionModelList)
        {
            var block = BlockShowList.FirstOrDefault(f => f.Key.Equals(sysHomePositionModel.code));
            sysHomePositionModel.title = block?.Title;
            sysHomePositionModel.show = block?.Value ?? true;
        }
        sysHomePositionView.SysHomePositionModelList = sysHomePositionModelList.Where(w => w.show).ToList();
        SysTimezone sysTimezone = SysTimezoneDataService.FindByKey(MvcContext.UserInfo.current_emp);
        sysHomePositionView.GuidePageCompleted = sysTimezone != null && sysTimezone.Guided.Equals((int)YesOrNoUtils.Yes);
        sysHomePositionView.HomeRemind = sysTimezone != null && sysTimezone.HomeRemind.Equals((int)YesOrNoUtils.Yes);

        List<SysUserGuide> list = await SqlSugarHelper.Db.Queryable<sys_user_guide>().Where(e => e.used).OrderBy(e => e.sort_order).Select(e => new SysUserGuide
        {
            CanFinish = e.can_finish == null ? null : (e.can_finish == true ? 1 : 0),
            CanHighlightClick = e.can_highlight_click == null ? null : (e.can_highlight_click == true ? 1 : 0),
            CanNext = e.can_next == null ? null : e.can_next == true ? 1 : 0,
            CanPrevious = e.can_previous == null ? null : e.can_previous == true ? 1 : 0,
            CanSkip = e.can_skip == null ? null : e.can_skip == true ? 1 : 0,
            Enable = e.enable ? 1 : 0,
            Height = e.height,
            Id = e.id,
            Mbottom = e.mbottom,
            MenuCode = e.menu_code,
            Mleft = e.mleft,
            Mright = e.mright,
            Mtop = e.mtop,
            SortOrder = e.sort_order,
            TipEtext = e.tip_etext,
            TipEtitle = e.tip_etitle,
            TipImg = e.tip_img,
            TipText = e.tip_text,
            TipTitle = e.tip_title,
            TipWidth = e.tip_width,
            Used = e.used ? 1 : 0,
            Width = e.width
        }).ToListAsync();
        sysHomePositionView.SysUserGuideList = [.. list.Select(s => {
                s.TipTitle = MvcContext.UserInfo.logging_locale.Equals("EN-US")?s.TipEtitle:s.TipTitle;
                s.TipText = MvcContext.UserInfo.logging_locale.Equals("EN-US")?s.TipEtext:s.TipText;
                return s;
                })];
        sysHomePositionView.GuidePageEnable = sysHomePositionView.SysUserGuideList.Any(a => a.Enable.Equals((int)YesOrNoUtils.Yes));
        return sysHomePositionView;
    }


    /// <summary>
    /// 首页区块显示状态
    /// </summary>
    /// <returns></returns>
    public async Task<List<KeyValueObj<string, bool>>> GetBlockShowList()
    {
        var user = MvcContext.UserInfo;

        //List<SysHomeBlockStatus> blockStatus = SysHomeBlockStatusDataService.Query(new SysHomeBlockStatusQueryCondition() { Emplid = user.current_emp });
        List<SysHomeBlockStatus> blockStatus =await SqlSugarHelper.Db.Queryable<sys_home_block_status>().Where(e => e.emplid == user.current_emp)
                                                .Select(e => new SysHomeBlockStatus()
                                                {
                                                    BlockId = e.block_id,
                                                    CreateTime = e.create_time,
                                                    CreateUser = e.create_user,
                                                    Emplid = e.emplid,
                                                    ModifyTime = e.modify_time,
                                                    ModifyUser = e.modify_user,
                                                    Rowid = e.rowid,
                                                    Status = e.status ? 1 : 0
                                                }).ToListAsync();


        return DbAccess.GetAll<SysHomeBlock>().Select(s =>
        {
            //区块不可关闭时，不读取个人配置
            if (s.Closeable == (int)YesOrNoUtils.No) return new KeyValueObj<string, bool>(s.Code, true) { Title = user.logging_locale.Equals("EN-US") ? s.ETitle : s.CTitle };
            bool status = true;
            //可关闭时，读取个人区块配置状态
            if (blockStatus.Any(a => a.BlockId.Equals(s.Rowid)))
            {
                var block = blockStatus.FirstOrDefault(f => f.BlockId.Equals(s.Rowid));
                status = block?.Status == (int)YesOrNoUtils.Yes;
            }
            return new KeyValueObj<string, bool>(s.Code, status) { Title = user.logging_locale.Equals("EN-US") ? s.ETitle : s.CTitle };
        }).ToList();
    }
}
