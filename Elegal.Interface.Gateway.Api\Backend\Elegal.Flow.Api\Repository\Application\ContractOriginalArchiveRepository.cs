﻿using Elegal.Interface.Api.Common.Control.Filter;
using Elegal.Interface.Api.Common.Model.ResultModel.FlowApi;
using Elegal.Interface.Api.Common.Model.ViewModel.FlowApi;
using Elegal.Interface.Api.Common.Repository;
using Elegal.Orm;
#nullable disable
namespace Elegal.Flow.Api.Repository.Application
{
    /// <summary>
    /// 归档资讯
    /// </summary>
    public class ContractOriginalArchiveRepository : BaseRepository
    {
        /// <summary>
        /// 合约效期资讯
        /// </summary>
        /// <param name="applyNumber"></param>
        /// <returns></returns>
        public ContractValidityModel GetContractValidityInformation(string applyNumber)
        {
            string sql = $@"select
	                            eff_date ConfirmEffDate,--生效日
                                other_eff_date OtherEffDate,--無生效日時，必填寫欄位
                                eff_type ConfirmEffDateType,--生效日类型
	                            sign_date ConfirmSignDate,--簽約日
                                other_sign_date OtherSignDate,--無簽約日時，必填寫欄位
                                sign_type ConfirmSignDateType,--簽約日类型
                                exp_date ConfirmExpDate,--到期日
                                other_exp_date OtherExpDate,--無到期日時，必填寫欄位
                                exp_type ConfirmExpDateType,--到期日类型
                                has_exp_extend HasExpExtend--到期日是否延展；0：否；1：是
                            from
	                            form_application_undertake
                            where
	                            apply_number = {DbAccess.Database.GetParameterPrefix()}applyNumber";
            return DbAccess.Database.SqlQuery<ContractValidityModel>(sql, new { applyNumber }).FirstOrDefault();
        }
        /// <summary>
        /// 合约效期资讯
        /// </summary>
        /// <param name="applyNumber"></param>
        /// <returns></returns>
        public ContractValidityModel GetContractValidityInformationRecord(string applyNumber)
        {
            string sql = $@"select
	                            confirm_eff_date ConfirmEffDate,--生效日
	                            confirm_sign_date ConfirmSignDate,--簽約日
                                confirm_exp_date ConfirmExpDate--到期日
                            from
	                            original_archive_record
                            where
	                            apply_number = {DbAccess.Database.GetParameterPrefix()}applyNumber";
            return DbAccess.Database.SqlQuery<ContractValidityModel>(sql, new { applyNumber }).FirstOrDefault();
        }

        /// <summary>
        /// 获取归档记录
        /// </summary>
        /// <param name="applyNumber"></param>
        /// <returns></returns>
        public IEnumerable<ArchiveRecordsResult> ArchiveRecords(string applyNumber)
        {
            string sql = $@"select
	                            data.origin_archive_type OriginArchiveType,
	                            sp.fun_name OriginArchiveTypeName,
                                data.archive_site ArchiveSite,
	                            ee.name_a + '/' + data.archive_deptid ArchiveUserDept,
	                            data.create_time ArchiveTime,
	                            data.paper_detailed PaperDetailed,
	                            data.origin_archive_remark OriginArchiveRemark
                            from original_archive_history data
                            left join sys_parameters sp on sp.func_code = data.origin_archive_type and sp.lang_type = {DbAccess.Database.GetParameterPrefix()}langType and sp.para_code = 'archiveStatus'
                            left join ps_sub_ee_lgl_vw_a ee on ee.emplid = data.archive_emplid
                            where data.apply_number = {DbAccess.Database.GetParameterPrefix()}applyNumber
                            order by data.create_time";
            return DbAccess.Database.SqlQuery<ArchiveRecordsResult>(sql, new { applyNumber, langType = MvcContext.UserInfo.logging_locale });
        }
        /// <summary>
        /// 获取其他申请绑定信息
        /// </summary>
        /// <returns></returns>
        public IEnumerable<OtherNotice> GetOtherNotice(string applyNumber)
        {
            string sql = $@"select 
	                            dt.other_apply_number,dt.apply_number,dt.form_type,
	                            '('+dt.form_type+')'+(select fun_name from sys_parameters where para_code = 'formType_O' and lang_type = '{MvcContext.UserInfo.logging_locale}' and func_code = dt.form_type) form_type_name
                            from (select
	                            oa.apply_number other_apply_number,oac.voided_apply_number apply_number,oa.form_type
	                            from other_application oa 
	                            join other_application_c oac on oa.apply_number = oac.apply_number
	                            where oa.application_state in('I','A','F')
	                            union all
	                            select
		                            oa.apply_number other_apply_number,oad.seal_apply_number apply_number,oa.form_type
	                            from other_application oa 
	                            join other_application_d oad on oa.apply_number = oad.apply_number
	                            where oa.application_state in('I','A','F')) dt
                            where dt.apply_number = {DbAccess.Database.GetParameterPrefix()}applyNumber
                            order by dt.form_type";
            return DbAccess.Database.SqlQuery<OtherNotice>(sql, new { applyNumber });
        }

        #region 獲取合約管理作業備註
        /// <summary>
        /// 獲取合約管理作業備註
        /// CR：312 -> 新增欄位【合約管理作業注意事項】
        /// </summary>
        /// <param name="applyNumber"></param>
        /// <returns></returns>
        public string GetContractTaskRemark(string applyNumber)
        {
            return (string)this.NpgsqlSearchBySingleValue(@"SELECT contract_task_remark FROM dbo.form_application_admin WHERE apply_number = @applyNumber;", new { applyNumber = applyNumber });
        } 
        #endregion
    }
}
