﻿using Elegal.Interface.Api.Common.Model.ResultModel;
using Elegal.Interface.Api.Common.Control.Web;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Http;
using Elegal.Interface.Api.Common.Model.ResultModel.ManufacturerApi;
using WebMcpUtils;
using Minio;
using Elegal.Flow.Common;
using Microsoft.AspNetCore.Http.HttpResults;
using Elegal.Interface.Api.Common.Model.DBModel.mcp;
using Elegal.Flow.Common.Repository;
using MathNet.Numerics;
using Elegal.Flow.Common.Services.FlowStep;
using Elegal.Interface.Api.Common.Model.DBModel.FlowStep;
using Elegal.Interface.Api.Common.Model.DBModel;
using Elegal.Flow.Common.Repository.FlowStep;

namespace WebApi.Controllers
{
    /// <summary>
    /// mcp 调用 legal ap 接口
    /// </summary>
    [Route("[controller]")]
    [ApiController]
    public class McpController : ControllerBase
    {
        private readonly MinioClient _client;

        /// <summary>
        /// GET 方法测试
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("GetMCPTest")]
        public string Get()
        {
            //Logging.SaveLog(ELogLayer.MCP, "Get success");
            return "web api test - ok";
        }

        /// <summary>
        /// 签核
        /// </summary>
        /// <param name="receiveXml"></param>
        /// <returns></returns>
        // [ApiLangAttribute]
        [HttpPost("DoSign")]
        [Consumes("text/plain")]
        [Produces("application/json")]
        public RtnDataDto DoSign([FromBody] string receiveXml)
        {
            try
            {
                //Logging.SaveLog(ELogLayer.MCP, "DoSign begin receiveXml:" + receiveXml);
                var lang = 1; //Request.Headers.GetValues("lang"); 
                              //Logging.SaveLog(ELogLayer.MCP, "lang：" + Convert.ToInt32(((string[])(lang))[0]));

                var result = ReceiveRequestHelper.ReceiveRequest(receiveXml, lang);

                //在此强制执行一下，msg=null时一定成功
                if (result.rtnMsg == null)
                {
                    result.rtnCode = 1;
                }
                
                //Logging.SaveLog(ELogLayer.MCP, result.rtnCode == 1 ? "DoSign success" : "DoSign fail");
                return new RtnDataDto() { rtnCode = result.rtnCode, rtnMsg = result.rtnMsg };
            }
            catch (Exception ex)
            {
                //Logging.SaveLog(ELogLayer.MCP, "DoSign fail catch:" + ex.Message);
                return new RtnDataDto() { rtnCode = 0, rtnMsg = ex.Message };
            }
        }

        /// <summary>
        /// 日誌本地測試接口
        /// </summary>
        /// <param name="receiveXml"></param>
        /// <returns></returns>
        [HttpPost("DoSignForLog")]
        [Consumes("text/plain")]
        [Produces("application/json")]
        public RtnDataDto DoSignForLog([FromBody] string receiveXml)
        {
            try
            {
                //Logging.SaveLog(ELogLayer.MCP, "DoSign begin receiveXml:" + receiveXml);
                var lang = 1; //Request.Headers.GetValues("lang"); 
                              //Logging.SaveLog(ELogLayer.MCP, "lang：" + Convert.ToInt32(((string[])(lang))[0]));

                var result = ReceiveRequestHelper.ReceiveRequestForLog(receiveXml, lang);

                //在此强制执行一下，msg=null时一定成功
                if (result.rtnMsg == null)
                {
                    result.rtnCode = 1;
                }

                //Logging.SaveLog(ELogLayer.MCP, result.rtnCode == 1 ? "DoSign success" : "DoSign fail");
                return new RtnDataDto() { rtnCode = result.rtnCode, rtnMsg = result.rtnMsg };
            }
            catch (Exception ex)
            {
                //Logging.SaveLog(ELogLayer.MCP, "DoSign fail catch:" + ex.Message);
                return new RtnDataDto() { rtnCode = 0, rtnMsg = ex.Message };
            }
        }

        /// <summary>
        /// 测试取消签核单
        /// </summary>
        /// <param name="messgeId"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("Cancel")]
        public McpResultModel Cancel([FromBody] string messgeId)
        {
            try
            {
                var result = CallMcpApiHelper.Cancel(messgeId);
                //Logging.SaveLog(ELogLayer.MCP, "Cancel success");
                return new McpResultModel() { rtnCode = result.rtnCode, rtnMsg = result.rtnMsg };
            }
            catch (Exception ex)
            {
                //Logging.SaveLog(ELogLayer.MCP, "Cancel fail catch:" + ex.Message);
                return new McpResultModel() { rtnCode = 0, rtnMsg = ex.Message };
            }
        }



        /// <summary>
        /// 测试获取MCP文件服务器上文件
        /// </summary>
        /// <param name="applyNumber">单号</param>
        /// <returns></returns>  
        [HttpPost]
        [Route("SearchMcpFiles")]
        public RtnMcpFileDto SearchMcpFiles([FromBody] string applyNumber)
        {
            try
            {
                var result = CallMcpApiHelper.GetMcpFiles(applyNumber);
                return result;
            }
            catch (Exception ex)
            {
                return new RtnMcpFileDto() { data = null, rtnCode = 0, rtnMsg = ex.Message };
            }
        }

        /// <summary>
        /// 测试获取MCP文件服务器上文件
        /// </summary>
        /// <param name="applyNumber">单号</param>
        /// <returns></returns>  
        [HttpPost]
        [Route("SendFormTest")]
        public RtnDataDto SendFormTest()
        {
            try
            {
                var result = SendHelper.SendMcpForApply("C",CommonUtil.EnumContractSendType.FormApply, "C202400120", "6804003", "MLH300", 215, 0, "");
                return result;
            }
            catch (Exception ex)
            {
                return new RtnDataDto() { data = null, rtnCode = 0, rtnMsg = ex.Message };
            }
        }

        /// <summary>
        /// 签核
        /// </summary>
        /// <param name="receiveXml"></param>
        /// <returns></returns>
        // [ApiLangAttribute]
        [HttpPost("TestDoSign")]
        [Consumes("text/plain")]
        [Produces("application/json")]
        public RtnDataDto TestDoSign([FromBody] string receiveXml)
        {
            try
            {
                //Logging.SaveLog(ELogLayer.MCP, "DoSign begin receiveXml:" + receiveXml);
                var lang = 1; //Request.Headers.GetValues("lang"); 
                              //Logging.SaveLog(ELogLayer.MCP, "lang：" + Convert.ToInt32(((string[])(lang))[0]));

                var result = ReceiveRequestHelper.TestReceiveRequest(receiveXml, lang);

                //在此强制执行一下，msg=null时一定成功
                if (result.rtnMsg == null)
                {
                    result.rtnCode = 1;
                }

                return new RtnDataDto() { rtnCode = result.rtnCode, rtnMsg = result.rtnMsg };
            }
            catch (Exception ex)
            {
                return new RtnDataDto() { rtnCode = 0, rtnMsg = ex.Message };
            }
        }

        /// <summary>
        /// 签核
        /// </summary>
        /// <param name="receiveXml"></param>
        /// <returns></returns>
        // [ApiLangAttribute]
        [HttpPost("TestDoAction")]
        [Consumes("text/plain")]
        [Produces("application/json")]
        public RtnDataDto TestDoAction([FromBody] string receiveXml)
        {
            try
            {
                //Logging.SaveLog(ELogLayer.MCP, "DoSign begin receiveXml:" + receiveXml);
                var lang = 1; //Request.Headers.GetValues("lang"); 
                              //Logging.SaveLog(ELogLayer.MCP, "lang：" + Convert.ToInt32(((string[])(lang))[0]));

                var result = ReceiveRequestHelper.TestDoAction(receiveXml);

                //在此强制执行一下，msg=null时一定成功
                if (result.rtnMsg == null)
                {
                    result.rtnCode = 1;
                }

                return new RtnDataDto() { rtnCode = result.rtnCode, rtnMsg = result.rtnMsg };
            }
            catch (Exception ex)
            {
                return new RtnDataDto() { rtnCode = 0, rtnMsg = ex.Message };
            }
        }

        /// <summary>
        /// 测试获取MCP文件服务器上文件
        /// </summary>
        [HttpPost]
        [Route("TestUploadFile")]
        public RtnDataDto TestUploadFile([FromQuery] string message_id)
        {
            McpRecordRepository _mcpRepository = new McpRecordRepository();
            mcp_record mcp = _mcpRepository.GetMcpRecordByMessgaeID(message_id);
            if (mcp != null)
            {
               SendHelper.SendFileToMcp(mcp.apply_number, message_id, mcp.emplid);
            }
            return new RtnDataDto() {  };
        }

        [HttpPost]
        [Route("TestSendMCP")]
        public RtnDataDto TestSendMCP([FromQuery] string message_id)
        {
            //SendHelper.SendMcpForApply("C", CommonUtil.EnumContractSendType.FormApply, "C202410028", "9406022", "WJA000", 102, 0, "");
            return new RtnDataDto() { };
        }

        [HttpPost]
        [Route("TestProcess")]
        public void TestProcess([FromQuery] string message_id)
        {
            FlowStepRepository _flowRepository = new FlowStepRepository();
            string apply = "C202400022";

            V_GetAllApplication form = _flowRepository.GetAllApplication(apply);

            int flow_step = Convert.ToInt16(form.form_type);
            List<ApplicationApproveProcess> contractMaTaskList = FlowStepProcessService.GetNextValidApplicationApproveProcess(form.apply_type, form.form_type, apply, Convert.ToInt16(form.form_type), _flowRepository.GetContractMaTaskStepid(flow_step));

        }
    }
}
