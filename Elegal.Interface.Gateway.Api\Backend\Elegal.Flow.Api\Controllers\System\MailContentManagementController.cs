﻿using Elegal.Flow.Api.Services.System;
using Elegal.Interface.Api.Common.Control.Web;
using Elegal.Interface.Api.Common.Model.ParaModel.FlowApi;
using Elegal.Interface.Api.Common.Model.ResultModel;
using Elegal.Interface.ApiData.Service.FuncService;
using Elegal.Interface.ApiData.Service.Model.QueryCondition;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;

namespace Elegal.Flow.Api.Controllers.System
{

    /// <summary>
    /// 郵件模板管理
    /// </summary>
    [Route("[controller]")]
    [ApiController]
    public class MailContentManagementController : BaseController
    {
        /// <summary>
        /// 郵件模板頁面查詢
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [Route("GetDataList")]
        public async Task<ApiResultModelByObject> GetDataList([FromBody] MailContentModel condition)
        {
            ApiResultModelByObject apiResult = new();
            List<MailSearchContentModel> mailSearchContentModels = MailContentManagementService.QueryMailContent(condition);
            mailSearchContentModels.ForEach(e =>
            {
                e.tableJsons = JsonConvert.DeserializeObject<List<TableJson>>(e.table_json ?? "") ?? [];
                e.zhContentJsons = JsonConvert.DeserializeObject<List<contentJson>>(e.zh_content_json ?? "") ?? [];
                e.enContentJsons = JsonConvert.DeserializeObject<List<contentJson>>(e.en_content_json ?? "") ?? [];
                e.recipientList = JsonConvert.DeserializeObject<List<KeyValuePair<string, List<string>>>>(e.mail_re_type ?? "") ?? [];
                e.ccList = JsonConvert.DeserializeObject<List<KeyValuePair<string, List<string>>>>(e.mail_cc_type ?? "") ?? [];
            });
            apiResult.listData = mailSearchContentModels;
            apiResult.rtnSuccess = true;
            return await Task.FromResult(apiResult);
        }

        /// <summary>
        /// 邮件模板开放栏位查询
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("GetOpenColumnDataList")]
        public async Task<ApiResultModelByObject> GetOpenColumnDataList(string fieldType)
        {
            ApiResultModelByObject apiResult = new();
            apiResult.listData = UserEmailDictionaryDataService.Query(new UserEmailDictionaryQueryCondition() { FieldType = fieldType });
            apiResult.rtnSuccess = true;
            return await Task.FromResult(apiResult);
        }

        /// <summary>
        /// 郵件模板修改
        /// </summary>
        /// <param name="condition">模板参数</param>
        /// <returns></returns>
        [HttpPost]
        [Route("UpdateMailContent")]
        public async Task<ApiResultModelByObject> UpdateMailContent([FromBody] MailContentModel condition)
        {
            ApiResultModelByObject apiResult = new ApiResultModelByObject()
            {
                listData = MailContentManagementService.UpdateMailContent(condition),
                rtnSuccess = true,
            };
            return await Task.FromResult(apiResult);
        }

        /// <summary>
        /// 获取开放栏位
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("GetOpenFieldList")]
        public async Task<ApiResultModelByObject> GetOpenFieldList()
        {
            ApiResultModelByObject apiResult = new()
            {
                listData = MailContentManagementService.GetOpenFieldList(),
                rtnSuccess = true
            };
            return await Task.FromResult(apiResult);
        }
    }
}
