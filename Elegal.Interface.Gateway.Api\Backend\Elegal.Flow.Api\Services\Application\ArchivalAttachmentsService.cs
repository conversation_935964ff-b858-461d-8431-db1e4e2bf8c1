﻿using Elegal.Flow.Api.Repository;
using Elegal.Flow.Common.Repository.FlowStep;
using Elegal.Flow.Common.Repository.OtherApply;
using Elegal.Interface.Api.Common.Control.Filter;
using Elegal.Interface.Api.Common.FuncHelper;
using Elegal.Interface.Api.Common.FuncService;
using Elegal.Interface.Api.Common.Model;
using Elegal.Interface.Api.Common.Model.DBModel;
using Elegal.Interface.Api.Common.Model.DBModel.otherApply;
using Elegal.Interface.Api.Common.Model.Enum;
using Elegal.Interface.Api.Common.Model.ParaModel.FlowApi;
using Elegal.Interface.Api.Common.Model.ResultModel;
using Elegal.Interface.Api.Common.Model.ResultModel.FlowApi;
using Elegal.Interface.Api.Common.Model.ViewModel.FlowApi.Application;
using Elegal.Interface.Api.Common.Model.ViewModel.Minio;
using Elegal.Interface.ApiData.Service.FuncService;
using Elegal.Interface.ApiData.Service.Model.Condition;
using Elegal.Interface.ApiData.Service.Model.DbModel;
using Elegal.Interface.ApiData.Service.Model.QueryCondition;
using Elegal.Interface.ApiData.Service.Model.ViewModel;
using Elegal.Orm;
using Elegal.Orm.Dtos;
using Elegal.Orm.Utils;
using Minio;
using Minio.DataModel.Args;
using System.Text;
#nullable disable
namespace Elegal.Flow.Api.Service
{
    /// <summary>
    /// 档案附件
    /// </summary>
    public static class ArchivalAttachmentsService
    {
        #region 申請單簽核關卡匯總
        /// <summary>
        /// 申請單簽核關卡匯總
        /// </summary>
        private static readonly List<string> signOffStep = AppSettingHelper.GetValue("signOffStep").Split(",").ToList();
        #endregion

        private static readonly ArchivalAttachmentsRepository _repository = new ArchivalAttachmentsRepository();
        private static readonly FlowStepRepository _flowRepository = new FlowStepRepository();
        private static readonly OtherApplyRepository _otherApplyRepository = new OtherApplyRepository();
        private static readonly ContractNumberRepository _contractNumberRepository = new ContractNumberRepository();

        #region 構造函數，注入MinioClient、MinIOConfig
        /// <summary>
        /// 注入MinioClient
        /// </summary>
        public static MinioClient _minioClient;
        /// <summary>
        /// 注入MinioClient、MinIOConfig
        /// </summary>
        public static MinIOConfig _minIOConfig;
        #endregion

        #region 获取申请单基本信息
        /// <summary>
        /// 获取申请单基本信息
        /// </summary>
        /// <param name="applyNumber">申请单号</param>
        /// <param name="applyType">申请单类型</param>
        /// <returns></returns>
        public static ArchivalAttachmentsViewModel GetBaseInformation(string applyNumber, string applyType)
        {
            var sysParameters = SysParametersDataService.Query(new SysParametersQueryCondition()
            {
                LangType = MvcContext.UserInfo.logging_locale,
                SearchItemGroup = new SearchItemGroup()
                {
                    Items = new List<SearchItem>() { new SearchItem() { Field = "para_code", Values = signOffStep, Compare = CompareOperator.ARRAYIN } }
                }
            });//参数
            var flowStepSigners = DbAccess.Query<FlowStepSigner>(new FlowStepSignerCondition { ApplyNumber = applyNumber });//当前签核人
            var flowStepSignerInvitees = DbAccess.Query<FlowStepSignerInvitee>(new FlowStepSignerInviteeCondition { ApplyNumber = applyNumber });//当前加签人
            //当关签核人员
            var users = new List<PsSubEeLglVwA>();
            if (flowStepSigners.Length != 0)
                users = DbAccess.Query<PsSubEeLglVwA>(new PsSubEeLglVwAQueryCondition()
                {
                    SearchItemGroup = new SearchItemGroup()
                    {
                        Items =
                        [
                            new()
                            {
                                Compare = CompareOperator.ARRAYIN,
                                Field = "emplid",
                                Values =  flowStepSigners.Select(s=>s.SignerEmplid)
                            }
                        ]
                    }
                }).ToList();
            //加签签核人员
            flowStepSignerInvitees = flowStepSignerInvitees.Where(w => w.SpInviteLevel.Equals(flowStepSignerInvitees.OrderBy(b => b.SpInviteLevel).FirstOrDefault().SpInviteLevel)).ToArray();
            var inviteeUsers = new List<PsSubEeLglVwA>();
            if (flowStepSignerInvitees.Length != 0)
                inviteeUsers = DbAccess.Query<PsSubEeLglVwA>(new PsSubEeLglVwAQueryCondition()
                {
                    SearchItemGroup = new SearchItemGroup()
                    {
                        Items =
                        [
                            new()
                            {
                                Compare = CompareOperator.ARRAYIN,
                                Field = "emplid",
                                Values =  flowStepSignerInvitees.Select(s=>s.InviteeEmplid)
                            }
                        ]
                    }
                }).ToList();
            ArchivalAttachmentsViewModel result = new();
            string newApplyType = PublicHelper.GetApplicationType(applyType);
            if (newApplyType.ToUpper() != "A") { applyType = string.Empty; }
            CommonUtil.TypeCopy(_repository.GetBaseInformation(applyNumber, newApplyType, applyType), result);
            //SIT：644 -> 使用新的狀態進行區分
            if (newApplyType.Equals("O"))
            {
                var legals = OtherApplicationLegalDataService.Query(new OtherApplicationLegalQueryCondition() { ApplyNumber = applyNumber });
                if (legals.Count != 0)
                    result.legalEname = string.Join(", ", PsSubEeLglVwADataService.Query(new PsSubEeLglVwAQueryCondition()
                    {
                        SearchItemGroup = new SearchItemGroup()
                        {
                            Items =
                            [
                                new()
                            {
                                Compare = CompareOperator.ARRAYIN,
                                Field = "emplid",
                                Values =  legals.Select(s=>s.LegalEmplid)
                            }
                            ]
                        }
                    }).Select(s => s.NameA));
            }
            if (flowStepSigners.Length != 0)
            {
                if (Convert.ToInt32(flowStepSigners.FirstOrDefault()?.StepId.ToString()) == _flowRepository.GetAcknowledgeStepid(Convert.ToInt32(flowStepSigners.FirstOrDefault()?.FlowId.ToString()))
                    && flowStepSigners.FirstOrDefault()?.AcknowledgeStep != null)
                {
                    result.currStepName = sysParameters.FirstOrDefault(f => f.FuncCode.Equals(flowStepSigners.FirstOrDefault()?.AcknowledgeStep.ToString()))?.FunName;
                }
                else
                {
                    result.currStepName = sysParameters.FirstOrDefault(f => f.FuncCode.Equals(flowStepSigners.FirstOrDefault()?.StepId.ToString()))?.FunName;
                }
            }
            if (users.Count != 0) result.shouldSigners = string.Join(", ", users.Select(u => u.NameA));
            if (flowStepSignerInvitees.FirstOrDefault()?.InviteeType == 1)
            {
                result.inviteeUser = string.Join(", ", inviteeUsers.Select(u => u.NameA));
                result.inviteeRemark = flowStepSignerInvitees.FirstOrDefault()?.InviteeRemark ?? "";//一般加簽原因
            }
            if (flowStepSignerInvitees.FirstOrDefault()?.InviteeType == 2) result.specialInviteeUser = string.Join(", ", inviteeUsers.Select(u => u.NameA));

            result.sysUploadFiles = GetFileListByType(applyNumber, result.applicationType);
            result.archiveOperations = GetArchiveOperationProcessByType(applyNumber, result.applicationType);
            return result;
        }
        #endregion

        #region 文件上传(检查文件总大小)
        /// <summary>
        /// 文件上传(检查文件总大小)
        /// </summary>
        /// <param name="formFile"></param>
        /// <param name="option"></param>
        /// <returns></returns>
        public static async Task<ApiResultModelByObject> UploadSingleFileCheckSize(IFormFileCollection formFile, SysUploadFileParaModel option)
        {
            //注釋：刪除檔案之後無法再次上傳 edit by springjiang 20241213
            //if (SysUploadFileDataService.Find(new SysUploadFileCondition() { UploadKey = model.UploadKey }) == null)
            //    return new ApiResultModelByObject()
            //    {
            //        rtnSuccess = false,
            //        messageContent = [ActionFilter.GetMultilingualValue("custom:messageContent:dataChurn")],
            //    };

            //記錄上傳成功、失敗的文件數據(内存超出、文件重名)
            try
            {
                List<string> successFiles = [], memoryExceededFiles = [], repeatFiles = [];
                foreach (var file in formFile)
                {
                    #region minio文件上傳
                    //20250306新增上傳文件監控，不影響正常流程，可不上PRD
                    UploadLogDataService.Create(new UploadLog()
                    {
                        UploadKey = option.UploadKey,
                        UploadType = option.UploadType.ToString(),
                        FileName = file.FileName,
                        UploadStep = "Upload MINIO Start",
                        CreateUser = MvcContext.UserInfo.current_emp
                    });

                    string minioPath = option.FilePath + "/" + DateTime.Now.ToString("yyyyMMddHHmmss") + file.FileName;
                    //20250307修改minio上傳方式優化
                    using (var stream = file.OpenReadStream())
                    {
                        var putObjectArgs = new PutObjectArgs()
                            .WithBucket(_minIOConfig.Bucket)
                            .WithObject(minioPath)
                            .WithStreamData(stream)
                            .WithObjectSize(file.Length)
                            .WithContentType("application/octet-stream");

                        await _minioClient.PutObjectAsync(putObjectArgs).ConfigureAwait(false);
                    }

                    //20250306新增上傳文件監控，不影響正常流程，可不上PRD
                    UploadLogDataService.Create(new UploadLog()
                    {
                        UploadKey = option.UploadKey,
                        UploadType = option.UploadType.ToString(),
                        FileName = file.FileName,
                        UploadStep = "Upload MINIO End",
                        CreateUser = MvcContext.UserInfo.current_emp
                    });

                    #endregion

                    #region DB存儲數據
                    //判斷文件縂大小是否超80M,否則停止上傳
                    var contractFiles = SysUploadFileDataService.Query(new SysUploadFileQueryCondition()
                    {
                        UploadType = option.UploadType,
                        UploadKey = option.UploadKey.ToUpper(),
                        IsWatermake = (int)YesOrNoUtils.No,
                        SearchItemGroup = new SearchItemGroup()
                        {
                            Items = [new SearchItem() { Compare = CompareOperator.ARRAYIN, Field = "archive_purposes", Values = ["1", "2", "3"], Logic = LogicOperator.And }]
                        }
                    });
                    if (file.Length + contractFiles.Sum(s => s.FileSize) <= 1024 * 1024 * 80)
                    {
                        SysUploadFile sysUploadFile = new()
                        {
                            UploadType = option.UploadType,
                            UploadKey = option.UploadKey.ToUpper(),
                            FileName = file.FileName,
                            FileType = Path.GetExtension(file.FileName).TrimStart('.'),
                            FilePath = minioPath,
                            FileSize = file.Length,
                            IsTemporary = option.IsTemporary,
                            CreateUser = MvcContext.UserInfo.current_emp,
                            ArchivePurposes = option.ArchivePurposes
                        };
                        //存儲之前判斷文件是否已存在
                        if (DbAccess.Exists<SysUploadFile>(new SysUploadFile() { UploadKey = option.UploadKey, UploadType = option.UploadType, FileName = file.FileName }))
                        {
                            repeatFiles.Add(file.FileName);
                            continue;
                        }
                        DbAccess.Create(sysUploadFile);
                        int? fileId = DbAccess.Find<SysUploadFile>(sysUploadFile).Fileid;
                        //档案历程
                        ArchiveOperationProcessDataService.Create(new ArchiveOperationProcess()
                        {
                            ActionCode = 3,
                            FileId = fileId,
                            FileName = file.FileName,
                            FileSize = file.Length,
                            ArchivePurposes = option.ArchivePurposes,
                            CreateUser = MvcContext.UserInfo.current_emp,
                            ApplyNumber = option.UploadKey.ToUpper(),
                            UploadType = option.UploadType,
                        });
                    }
                    else memoryExceededFiles.Add(file.FileName);

                    //20250306新增上傳文件監控，不影響正常流程，可不上PRD
                    UploadLogDataService.Create(new UploadLog()
                    {
                        UploadKey = option.UploadKey,
                        UploadType = option.UploadType.ToString(),
                        FileName = file.FileName,
                        UploadStep = "Write History Data End",
                        CreateUser = MvcContext.UserInfo.current_emp
                    });
                    #endregion
                }
                ApiResultModelByObject result = new ApiResultModelByObject()
                {
                    rtnSuccess = true,
                    messageTitle = ActionFilter.GetMultilingualValue("custom:messageTitle:uploadSuccess"),
                    messageContent = [ActionFilter.GetMultilingualValue("custom:messageContent:fileUpload")],
                    listData = successFiles.Count,
                };
                //文件全部上傳失敗
                if (memoryExceededFiles.Count + repeatFiles.Count == formFile.Count)
                    result = new ApiResultModelByObject()
                    {
                        messageTitle = ActionFilter.GetMultilingualValue("custom:messageTitle:fileUploadFail")
                    };
                if (memoryExceededFiles.Count > 0) result.messageContent.Add($"{ActionFilter.GetMultilingualValue($"ArchivalAttachments_sizeUpper", true)}：{string.Join("、", memoryExceededFiles)}");
                if (repeatFiles.Count > 0) result.messageContent.Add($"{ActionFilter.GetMultilingualValue($"ArchivalAttachments_fileExist", true)}：{string.Join("、", repeatFiles)}");
                ActionFilter.InitLogRecord(log =>
                {
                    StringBuilder stringBuilder = new StringBuilder();
                    stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue($"ArchivalAttachments_applyNumber", true)}：{option.UploadKey}");
                    stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue($"ArchivalAttachments_fileList", true)}：{string.Join("，", formFile.Select(s =>
                    {
                        if (s.Length / 1024 > 1000) return $"{s.FileName}({Math.Round((decimal)s.Length / 1024 / 1024, 2)}MB)";
                        else if (s.Length > 1024) return $"{s.FileName}({Math.Round((decimal)s.Length / 1024, 0)}KB)";
                        else return $"{s.FileName}({s.Length}B)";
                    }))}");
                    log.Detail = stringBuilder.ToString();
                });

                #region 當存在申請單號時，需要記錄異動案件記錄 異動案件記錄新增
                if (!string.IsNullOrEmpty(option.applyNumber) && option.menuCode > 0)
                {
                    V_GetAllApplication va = _flowRepository.GetAllApplication(option.applyNumber);
                    SysChangeRecordService.AddSysChangeRecords(new SysChangeRecordParaModel
                    {
                        work_key = option.applyNumber,
                        is_modify_annex = true,
                        menu_code = option.menuCode,
                        change_type = va?.apply_type,
                        change_next_type = va?.form_type
                    });
                }
                #endregion

                UploadLogDataService.Create(new UploadLog()
                {
                    UploadKey = option.UploadKey,
                    UploadType = option.UploadType.ToString(),
                    UploadStep = "End",
                    CreateUser = MvcContext.UserInfo.current_emp
                });
                return result;
            }
            catch (Exception e)
            {
                //20250306新增上傳文件監控，不影響正常流程，可不上PRD
                UploadLogDataService.Create(new UploadLog()
                {
                    UploadKey = option.UploadKey,
                    UploadType = option.UploadType.ToString(),
                    UploadStep = "ERROR",
                    LogDetail = e.ToString(),
                    CreateUser = MvcContext.UserInfo.current_emp
                });
                return new ApiResultModelByObject()
                {
                    messageTitle = ActionFilter.GetMultilingualValue("custom:messageTitle:fileUploadFail")
                };
            }

        }
        #endregion

        #region 批量删除文件(同时删除转档文件)
        /// <summary>
        /// 批量删除文件(同时删除转档文件)
        /// </summary>
        /// <param name="fileIds"></param>
        /// <param name="applyNumber">申請單號</param>
        /// <param name="menuCode">菜單欄</param>
        /// <returns></returns>
        public static async Task<ApiResultModelByObject> DeleteMultipleFile(List<int> fileIds, string applyNumber = "", string menuCode = "")
        {
            ApiResultModelByObject result = new ApiResultModelByObject() { rtnSuccess = true, messageType = MessageTypeUtils.Success.ToString(), messageTitle = "文件已删除" };
            //原档文件集合
            var sysUploadFiles = SysUploadFileDataService.Query(new SysUploadFileQueryCondition()
            {
                SearchItemGroup = new SearchItemGroup()
                {
                    Items = new List<SearchItem>()
                    {
                        new SearchItem() { Compare = CompareOperator.ARRAYIN,Values = fileIds.Select(s=>s.ToString()),Field = "fileid",Logic = LogicOperator.And}
                    }
                }
            });
            if (!sysUploadFiles.Select(s => (int)s.Fileid).Distinct().Order().SequenceEqual(fileIds.Distinct().Order()))
            {
                return new ApiResultModelByObject()
                {
                    messageTitle = ActionFilter.GetMultilingualValue("custom:messageTitle:dataNotexist"),
                    messageContent = new List<string> { ActionFilter.GetMultilingualValue("custom:messageContent:dataChurn") },
                    messageType = MessageTypeUtils.Warning.ToString()
                };
            }
            List<string> deleteFile = [], deletePdfFile = [], relationFiles = [], noFile = [];
            List<SysUploadFile> logFile = [];
            foreach (var sysUploadFile in sysUploadFiles)
            {
                //是否关联纸本
                if (DbAccess.Find<PaperBasicData>(new PaperBasicDataCondition() { FileId = sysUploadFile.Fileid }) != null)
                {
                    relationFiles.Add(sysUploadFile.FileName); continue;
                }
                logFile.Add(sysUploadFile);
                if (DbAccess.FindByKey<SysUploadFile, int?>(sysUploadFile.Fileid) != null)
                {
                    #region 删除原档
                    await _minioClient.RemoveObjectAsync(new RemoveObjectArgs().WithBucket(_minIOConfig.Bucket).WithObject(sysUploadFile.FilePath));
                    DbAccess.DeleteByKey<SysUploadFile, int?>(sysUploadFile.Fileid);
                    deleteFile.Add(sysUploadFile.FileName);
                    #endregion

                    #region 档案历程
                    DbAccess.Create(new ArchiveOperationProcess()
                    {
                        ActionCode = 4,
                        FileId = sysUploadFile.Fileid,
                        FileName = sysUploadFile.FileName,
                        FileSize = sysUploadFile.FileSize,
                        ArchivePurposes = sysUploadFile.ArchivePurposes,
                        CreateUser = MvcContext.UserInfo.current_emp,
                        ApplyNumber = sysUploadFile.UploadKey.ToUpper(),
                        UploadType = sysUploadFile.UploadType,
                    });
                    #endregion

                    #region 删除转档
                    var sysUploadFilePdf = DbAccess.Find<SysUploadFile>(new SysUploadFileCondition() { OriginalFileId = sysUploadFile.Fileid });
                    if (sysUploadFilePdf != null)
                    {
                        if (!sysUploadFile.FileType.ToLower().Equals("pdf")) deletePdfFile.Add(sysUploadFilePdf.FileName);
                        await _minioClient.RemoveObjectAsync(new RemoveObjectArgs().WithBucket(_minIOConfig.Bucket).WithObject(sysUploadFilePdf.FilePath));
                        DbAccess.DeleteByKey<SysUploadFile, int?>(sysUploadFilePdf.Fileid);
                    }
                    #endregion
                }
                else noFile.Add(sysUploadFile.FileName);
            }
            if (deleteFile.Any()) result.messageContent.Add($"{ActionFilter.GetMultilingualValue($"ArchivalAttachments_deleteFiles", true)}：[{string.Join("、", deleteFile)}]");
            if (deletePdfFile.Any()) result.messageContent.Add($"{ActionFilter.GetMultilingualValue($"ArchivalAttachments_deletePdfFiles", true)}：[{string.Join("、", deletePdfFile)}]");
            if (noFile.Any()) result.messageContent.Add($"{ActionFilter.GetMultilingualValue($"ArchivalAttachments_noFile", true)}：[{string.Join("、", noFile)}]");
            if (relationFiles.Any()) result.messageContent.Add($"{ActionFilter.GetMultilingualValue($"ArchivalAttachments_deleteMsg", true)}：[{string.Join("、", relationFiles)}]");
            if (fileIds.Count == relationFiles.Count)
            {
                result.rtnSuccess = false;
                result.messageTitle = ActionFilter.GetMultilingualValue("custom:messageTitle:fileDeleteFail");
                result.messageType = MessageTypeUtils.Warning.ToString();
            }
            ActionFilter.InitLogRecord(log =>
            {
                //判断单据是否是暂存单
                var applyNumber = logFile.FirstOrDefault()?.UploadKey;
                var istemp = DbAccess.Database.ExecuteScalar($"select emplid from ps_sub_ee_lgl_vw_a where emplid ='{applyNumber}'")?.ToString() ?? "";

                StringBuilder stringBuilder = new StringBuilder();
                if (istemp != null && istemp != "")
                    stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue($"ArchivalAttachments_applyNumber", true)}：-");
                else
                    stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue($"ArchivalAttachments_applyNumber", true)}：{logFile.FirstOrDefault()?.UploadKey}");
                stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue($"ArchivalAttachments_fileList", true)}：{string.Join("，", logFile.Select(s =>
                {
                    if (s.FileSize / 1024 > 1000) return $"{s.FileName}({Math.Round((decimal)s.FileSize / 1024 / 1024, 2)}MB)";
                    else if (s.FileSize > 1024) return $"{s.FileName}({Math.Round((decimal)s.FileSize / 1024, 0)}KB)";
                    else return $"{s.FileName}({s.FileSize}B)";
                }))}");
                log.Detail = stringBuilder.ToString();
            });

            #region 當存在申請單號時，需要記錄異動案件記錄 異動案件記錄新增
            if (!string.IsNullOrEmpty(applyNumber) && !string.IsNullOrEmpty(menuCode))
            {
                V_GetAllApplication va = _flowRepository.GetAllApplication(applyNumber);
                SysChangeRecordService.AddSysChangeRecords(new SysChangeRecordParaModel
                {
                    work_key = applyNumber,
                    is_modify_annex = true,
                    menu_code = Convert.ToInt32(menuCode),
                    change_type = va?.apply_type,
                    change_next_type = va?.form_type
                });
            }
            #endregion

            return result;
        }
        #endregion

        #region 文件列表信息獲取(SpringJiang已經重新整理邏輯)

        #region 獲取文件列表
        /// <summary>
        /// 獲取文件列表
        /// </summary>
        public static IEnumerable<SysUploadFileModel> GetFileList(SysUploadFileViewModel condition)
        {
            //如果為暫存單則根據工號查詢，反之則為本身
            condition.UploadKey = string.IsNullOrEmpty(condition.UploadKey) ? MvcContext.UserInfo.current_emp : condition.UploadKey;
            //獲取機密等級
            string confidenLevel = ArchivalAttachmentsService.GetfileModelApplyInfo(condition.UploadKey, condition.UploadType ?? 0).Item2;
            //返回結果集合
            return _repository.GetFileList(condition).Select(s =>
            {
                s.file_name = GetFileName(s.file_name, confidenLevel);
                return s;
            });
        }
        #endregion

        #region 獲取文件列表(區分申請類型)
        /// <summary>
        /// 獲取文件列表(區分申請類型)
        /// </summary>
        /// <param name="upload_key">业务关联主键</param>
        /// <param name="upload_type">申请类型</param>
        /// <returns></returns>
        public static IEnumerable<SysUploadFileModel> GetFileListByType(string upload_key, int upload_type)
        {
            //獲取機密等級
            string confidenLevel = GetConfidenLevel(new SysUploadFileViewModel
            {
                UploadType = upload_type,
                UploadKey = upload_key
            });

            return _repository.GetFileListByType(upload_key, upload_type).Select(s =>
            {
                s.file_name = GetFileName(s.file_name, confidenLevel);
                return s;
            });
        }
        #endregion

        #region 獲取檔案歷程數據
        /// <summary>
        /// 獲取檔案歷程數據
        /// </summary>
        /// <param name="condition">查询参数</param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public static IEnumerable<ArchiveOperationProcessResult> GetArchiveOperationProcess(ArchiveOperationProcessCondition condition)
        {
            //獲取機密等級
            string confidenLevel = GetConfidenLevel(new SysUploadFileViewModel
            {
                UploadType = condition.UploadType,
                UploadKey = condition.ApplyNumber
            });

            return _repository.GetArchiveOperationProcess(condition).Select(s =>
            {
                s.file_name = GetFileName(s.file_name, confidenLevel);
                return s;
            });
        }
        #endregion

        #region 獲取檔案歷程數據(區分申請類型)
        /// <summary>
        /// 獲取檔案歷程數據(區分申請類型)
        /// </summary>
        /// <param name="upload_key">业务关联主键</param>
        /// <param name="upload_type">申请类型</param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public static IEnumerable<ArchiveOperationProcessResult> GetArchiveOperationProcessByType(string upload_key, int upload_type)
        {
            //獲取機密等級
            string confidenLevel = GetConfidenLevel(new SysUploadFileViewModel
            {
                UploadType = upload_type,
                UploadKey = upload_key
            });

            return _repository.GetArchiveOperationProcessByType(upload_key, upload_type).Select(s =>
            {
                s.file_name = GetFileName(s.file_name, confidenLevel);
                return s;
            });
        }
        #endregion

        #region 根據附件信息獲取申請單的機密等級
        /// <summary>
        /// 根據附件信息獲取申請單的機密等級
        /// </summary>
        /// <param name="condition"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public static string GetConfidenLevel(SysUploadFileViewModel condition)
        {
            //默認機密等級為：機密 02 
            string confidenLevel = "02";
            //定義申請單號
            string applyNumber = string.Empty;
            //根據  condition.UploadKey 查詢數據
            switch (condition.UploadType)
            {
                case 2://合約申請
                case 4://資料建檔
                case 6://關企建檔
                    applyNumber = condition.UploadKey;
                    break;
                case 5://其他申請單
                    V_GetAllApplication otherForm = _flowRepository.GetAllApplication("O", condition.UploadKey);
                    if (otherForm != null)
                    {
                        //申請單的類型
                        switch (otherForm.form_type.ToLower())
                        {
                            case "c"://作廢
                                List<other_application_c> listC = _otherApplyRepository.GetOtherApplicationC(otherForm.apply_number);
                                //表示有且只有一筆數據
                                if (listC.Any() && listC.Count == 1)
                                {
                                    applyNumber = listC[0].voided_apply_number;
                                }
                                break;
                            case "d"://重新用印
                                List<other_application_d> listD = _otherApplyRepository.GetOtherApplicationD(otherForm.apply_number);
                                //表示有且只有一筆數據
                                if (listD.Any() && listD.Count == 1)
                                {
                                    applyNumber = listD[0].seal_apply_number;
                                }
                                break;
                        }
                    }
                    break;
            }
            //獲取到申請單號
            if (!string.IsNullOrEmpty(applyNumber))
            {
                V_GetAllApplication form = _flowRepository.GetAllApplication(applyNumber);
                //申請單不爲空且機密等級也不爲空
                if (form != null && !string.IsNullOrEmpty(form.confiden_level))
                {
                    confidenLevel = form.confiden_level;
                }
            }
            return confidenLevel;
        }
        #endregion

        #endregion

        #region 修改文件列表信息(SpringJiang已經重新整理邏輯)
        /// <summary>
        /// 修改文件列表信息
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public static ApiResultModelByObject Update(SysUploadFileParaModel model)
        {
            //當檔案用途不存在時，需要給出提示，不進行後續作業
            if (model.ArchivePurposes != null)
            {
                #region 修改原始檔案
                //根據原檔fileid獲取原檔文件
                SysUploadFile oldData = SysUploadFileDataService.FindByKey(model.Fileid);
                //文件不存在時，返回提示信息
                if (oldData == null)
                {
                    return new ApiResultModelByObject()
                    {
                        messageType = MessageTypeUtils.Warning.ToString(),
                        messageTitle = ActionFilter.GetMultilingualValue("custom:messageTitle:dataNotexist"),
                        messageContent = new List<string> { ActionFilter.GetMultilingualValue("custom:messageContent:dataChurn") }
                    };
                }
                else
                {
                    SysUploadFileDataService.Update(new SysUploadFile
                    {
                        Fileid = oldData.Fileid,
                        //備注
                        FileExplain = model.FileExplain,
                        //檔案用途
                        ArchivePurposes = model.ArchivePurposes,
                        ModifyUser = MvcContext.UserInfo.current_emp,
                        ModifyTime = DateTime.UtcNow
                    });
                }
                #endregion

                #region 修改轉檔文件
                //根據原檔fileid獲取轉檔文件(pdf)
                SysUploadFile oldPdfData = SysUploadFileDataService.Find(new SysUploadFileCondition() { OriginalFileId = oldData.Fileid });
                //儅文件已經轉檔，且存在轉檔數據時，才進行修改
                if (oldPdfData != null)
                {
                    SysUploadFileDataService.Update(new SysUploadFile
                    {
                        //轉檔文件使用 oldPdfData
                        Fileid = oldPdfData.Fileid,
                        //備注             
                        FileExplain = model.FileExplain,
                        //檔案用途
                        ArchivePurposes = model.ArchivePurposes,
                        ModifyUser = MvcContext.UserInfo.current_emp,
                        ModifyTime = DateTime.UtcNow
                    });
                }
                #endregion

                #region 記錄檔案歷程
                ArchiveOperationProcessDataService.Create(new ArchiveOperationProcess()
                {
                    ActionCode = 2,
                    FileId = oldData.Fileid,
                    FileName = oldData.FileName,
                    FileExplain = model.FileExplain,
                    FileSize = oldData.FileSize,
                    ArchivePurposes = model.ArchivePurposes,
                    CreateUser = MvcContext.UserInfo.current_emp,
                    ApplyNumber = oldData.UploadKey.ToUpper(),
                    UploadType = oldData.UploadType,
                });
                #endregion

                #region 記錄log日志
                ActionFilter.InitLogRecord(log =>
                {
                    string size = oldData.FileSize / 1024 > 1000 ?
                        $"{oldData.FileName}({Math.Round((decimal)oldData.FileSize / 1024 / 1024, 2)}MB)" :
                        $"{oldData.FileName}({Math.Round((decimal)oldData.FileSize / 1024, 0)}KB)";

                    //判断是否是暂存单
                    var applyNumber = oldData.UploadKey;
                    var istemp = DbAccess.Database.ExecuteScalar($"select emplid from ps_sub_ee_lgl_vw_a where emplid ='{applyNumber}'")?.ToString() ?? "";
                    StringBuilder stringBuilder = new StringBuilder();
                    if (istemp != null && istemp != "")
                        stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue($"ArchivalAttachments_applyNumber", true)}：-");
                    else
                        stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue($"ArchivalAttachments_applyNumber", true)}：{oldData.UploadKey}");
                    stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue($"ArchivalAttachments_fileName", true)}：{oldData.FileName}");
                    stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue($"ArchivalAttachments_fileSize", true)}：{size}");
                    stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue($"ArchivalAttachments_archivalAttachments", true)}：{ActionFilter.GetMultilingualValue($"ArchivalAttachments_archivalAttachments{oldData.ArchivePurposes}", true)}");
                    stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue($"ArchivalAttachments_remark", true)}：{oldData.FileExplain}");
                    log.DetailFormer = stringBuilder.ToString();

                    stringBuilder = new StringBuilder();
                    if (istemp != null && istemp != "")
                        stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue($"ArchivalAttachments_applyNumber", true)}：-");
                    else
                        stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue($"ArchivalAttachments_applyNumber", true)}：{oldData.UploadKey}");
                    stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue($"ArchivalAttachments_fileName", true)}：{oldData.FileName}");
                    stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue($"ArchivalAttachments_fileSize", true)}：{size}");
                    stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue($"ArchivalAttachments_archivalAttachments", true)}：{ActionFilter.GetMultilingualValue($"ArchivalAttachments_archivalAttachments{model.ArchivePurposes}", true)}");
                    stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue($"ArchivalAttachments_remark", true)}：{model.FileExplain}");
                    log.Detail = stringBuilder.ToString();

                });

                #region 當存在申請單號時，需要記錄異動案件記錄 異動案件記錄新增
                if (!string.IsNullOrEmpty(model.applyNumber) && model.menuCode > 0)
                {
                    V_GetAllApplication va = _flowRepository.GetAllApplication(model.applyNumber);
                    SysChangeRecordService.AddSysChangeRecords(new SysChangeRecordParaModel
                    {
                        work_key = model.applyNumber,
                        is_modify_annex = true,
                        menu_code = model.menuCode,
                        change_type = va?.apply_type,
                        change_next_type = va?.form_type
                    });
                }
                #endregion

                #endregion

                return new ApiResultModelByObject() { listData = true, rtnSuccess = true };
            }
            else
            {
                return new ApiResultModelByObject()
                {
                    messageType = MessageTypeUtils.Warning.ToString(),
                    messageTitle = ActionFilter.GetMultilingualValue("custom:messageTitle:updateFail"),
                    messageContent = new List<string> { ActionFilter.GetMultilingualValue("custom:messageContent:validateError") }
                };
            }
        }
        #endregion

        #region 根據機密等級重新定義文件名稱

        #region 原始文件名
        /// <summary>
        /// 原始文件名
        /// </summary>
        /// <param name="fileName"></param>
        /// <param name="confidenLevel"></param>
        /// <returns></returns>
        public static string GetFileName(string fileName, string confidenLevel)
        {
            if (string.IsNullOrEmpty(confidenLevel)) return fileName;
            return $"{Path.GetFileNameWithoutExtension(fileName)}_{CommonUtilHelper.GetConfidenLevelWatermarkText(confidenLevel)}{Path.GetExtension(fileName)}";
        }
        #endregion

        #region 轉檔文件名
        /// <summary>
        /// 轉檔文件名
        /// </summary>
        /// <param name="fileName"></param>
        /// <param name="confidenLevel"></param>
        /// <returns></returns>
        public static string GetPdfFileName(string fileName, string confidenLevel)
        {
            if (string.IsNullOrEmpty(confidenLevel)) return $"{fileName}_Watermark.pdf";
            return $"{Path.GetFileNameWithoutExtension(fileName)}_{CommonUtilHelper.GetConfidenLevelWatermarkText(confidenLevel)}{Path.GetExtension(fileName)}_Watermark.pdf";
        }
        #endregion

        #endregion

        #region 根據申請單號獲取合約編號
        /// <summary>
        /// 根據申請單號獲取合約編號
        /// </summary>
        /// <param name="applyNumber"></param>
        /// <returns></returns>
        public static string GetContractNumber(string applyNumber)
        {
            return _contractNumberRepository.GetContractNumberByApplyNumber(applyNumber);
        }
        #endregion

        #region 驗證單據是否為暫存單
        /// <summary>
        /// 驗證單據是否為暫存單
        /// </summary>
        /// <param name="fileType"></param>
        /// <param name="applyNumber"></param>
        /// <returns></returns>
        public static bool GetApplicationStatue(int? fileType, string applyNumber)
        {
            bool isTemp = false;
            #region 根據文件類型定義申請單類型，默認為合約申請
            string applictionType = "C";
            switch (fileType)
            {
                case 2://合約申請
                    applictionType = "C";
                    break;
                case 4://資料建檔
                    applictionType = "R";
                    break;
                case 6://關企建檔
                    applictionType = "AR";
                    break;
                case 5://其他申請單
                    applictionType = "O";
                    break;
            }
            #endregion

            //查詢數據
            V_GetAllApplication form = _flowRepository.GetAllApplication(applictionType, applyNumber);
            if (form != null)
            {
                isTemp = form.application_state.ToUpper() == "t".ToUpper();
            }
            return isTemp;
        }
        #endregion

        /// <summary>
        /// 根據單號，獲取是否暫存單，機密等級，合約編號
        /// </summary>
        /// <param name="applyNumber"></param>
        /// <param name="uploadType">由於暫存單單號都一樣，所以用 上傳類型來區分</param>
        /// <returns>(bool isTemp,string confidenLevel,string contractNumber)</returns>
        public static (bool, string, string) GetfileModelApplyInfo(string applyNumber, int uploadType)
        {
            if (string.IsNullOrWhiteSpace(applyNumber)) return (false, string.Empty, string.Empty);
            var list = _repository.GetfileModelApplyInfo(applyNumber);//獲取數據
            var form = list.FirstOrDefault(e => e.UploadType == uploadType); //根據 uploadType 獲取
            if (form == null) list.FirstOrDefault();//如果根據 /根據 uploadType 獲取 不到，則默認僅僅根據單號獲取
            if (form == null) return (false, string.Empty, string.Empty); //
            return (form.application_state?.ToUpper() == "t".ToUpper(),
                    form.confiden_level,
                    form.contract_number
                   );
        }
    }
}
