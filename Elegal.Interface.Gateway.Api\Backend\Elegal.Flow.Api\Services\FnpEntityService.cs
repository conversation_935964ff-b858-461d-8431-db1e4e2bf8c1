﻿using Elegal.Interface.Api.Common.Model.ParaModel.FlowApi;
using Elegal.Interface.Api.Common.Model.ViewModel.FlowApi;
using Elegal.Interface.Api.Common.Repository;
using Elegal.Interface.ApiData.Service.FuncService;
using Elegal.Interface.ApiData.Service.Model.DbModel;
using Elegal.Interface.ApiData.Service.Model.QueryCondition;
using Elegal.Orm;
using Elegal.Orm.Dtos;
using Elegal.Orm.Utils;
using iText.StyledXmlParser.Jsoup.Nodes;

namespace Elegal.Flow.Api.Services
{
    /// <summary>
    /// 主體
    /// </summary>
    public static class FnpEntityService
    {
        private static FnpEntityRepository _repository = new FnpEntityRepository();

        #region 主體信息查詢(條件查詢)
        /// <summary>
        /// 主體信息查詢(條件查詢)
        /// </summary>
        /// <param name="condition"></param>
        /// <returns></returns>
        public static List<FnpEntityModel> Query(FnpEntityQueryModel condition)
        {
            FnpEntityQueryCondition fnpEntityQueryCondition = new FnpEntityQueryCondition()
            {
                SearchItemGroup = new SearchItemGroup()
                {
                    Logic = LogicOperator.And,
                    Items = new List<SearchItem>()
                },
                OrderBys = new List<OrderByParam>()
                    {
                        new OrderByParam()
                        {
                            Field="Entity",
                            Order=OrderBy.ASC
                        }
                    }
            };
            AffiliateCompanyQueryCondition affiliateCompanyQueryCondition = new AffiliateCompanyQueryCondition()
            {
                SearchItemGroup = new SearchItemGroup()
                {
                    Logic = LogicOperator.And,
                    Items = new List<SearchItem>()
                }
            };
            //模糊查詢匹配主體簡稱&中英文名稱
            if (!string.IsNullOrEmpty(condition.Entity))
            {
                fnpEntityQueryCondition.SearchItemGroup.Items.AddRange([
                        new SearchItem()
                        {
                            Field = "Entity",
                            Value = condition.Entity,
                            Logic = LogicOperator.Or,
                            Compare = CompareOperator.LIKE
                        },
                        new SearchItem()
                        {
                            Field = "EntityNamec",
                            Value = condition.Entity,
                            Logic = LogicOperator.Or,
                            Compare = CompareOperator.LIKE
                        },
                        new SearchItem()
                        {
                            Field = "EntityNamee",
                            Value = condition.Entity,
                            Logic = LogicOperator.Or,
                            Compare = CompareOperator.LIKE
                        }
                    ]);
                affiliateCompanyQueryCondition.SearchItemGroup.Items.AddRange([
                        new SearchItem()
                        {
                            Field = "AffCompanyAbb",
                            Value = condition.Entity,
                            Logic = LogicOperator.Or,
                            Compare = CompareOperator.LIKE
                        },
                        new SearchItem()
                        {
                            Field = "AffCompanyCname",
                            Value = condition.Entity,
                            Logic = LogicOperator.Or,
                            Compare = CompareOperator.LIKE
                        },
                        new SearchItem()
                        {
                            Field = "AffCompanyEname",
                            Value = condition.Entity,
                            Logic = LogicOperator.Or,
                            Compare = CompareOperator.LIKE
                        }
                    ]);
            }
            if (condition.Status != null)
            {
                fnpEntityQueryCondition.SearchItemGroup.Items.Add(new SearchItem()
                {
                    Field = "Status",
                    Value = condition.Status.ToString(),
                    Logic = LogicOperator.And,
                    Compare = CompareOperator.EQ
                });
                affiliateCompanyQueryCondition.SearchItemGroup.Items.Add(new SearchItem()
                {
                    Field = "AffStatus",
                    Value = ((condition.Status + 3) % 2).ToString(),
                    Logic = LogicOperator.And,
                    Compare = CompareOperator.EQ
                });
            }
            var areaList = DbAccess.GetAll<SysArea>();
            var result = FnpEntityDataService.Query(fnpEntityQueryCondition)
                .Select(s =>
                {
                    FnpEntityModel fnpEntityModel = CommonUtil.Map<FnpEntity, FnpEntityModel>(s);
                    fnpEntityModel.AreaName = areaList.FirstOrDefault(f => f.AreaId == s.AreaId)?.AreaName;
                    return fnpEntityModel;
                }).ToList();
            var affiliateList = AffiliateCompanyDataService.Query(affiliateCompanyQueryCondition)
                .Select(s => new FnpEntityModel()
                {
                    Entity = s.AffCompanyAbb,
                    EntityId = s.AffCompanyCode,
                    EntityNamec = s.AffCompanyCname,
                    EntityNamee = s.AffCompanyEname,
                    Status = (Convert.ToInt32(s.AffStatus) + 1) % 2,
                    CreateUser = s.CreateUser,
                    CreateTime = s.CreateTime,
                    ModifyUser = s.ModifyUser,
                    ModifyTime = s.ModifyTime,
                    AreaName = ""
                }).ToList();
            //edit by SpringJiang 20250210 根據狀態獲取主體停用或啟用  Issue：190
            if (condition.Status.HasValue)
            {
                result = result.Where(s => s.Status == condition.Status).ToList();
                affiliateList = affiliateList.Where(s => s.Status == condition.Status).ToList();
            }
            if (condition.WithAffiliateCompany) result.AddRange(affiliateList);
            if (condition.OnlyAffiliateCompany) return affiliateList;
            return result;
        }
        #endregion

        #region 根據區域查詢主體數據
        /// <summary>
        /// 根據區域查詢主體數據
        /// </summary>
        /// <param name="areaIds">主体id</param>
        /// <returns></returns>
        public static IEnumerable<FnpEntity> QueryDataByArea(List<int?> areaIds)
        {
            string unionSql = @"select
	                                entity_id EntityId,entity Entity,entity_namec EntityNamec,entity_namee EntityNamee,status Status,area_id AreaId
                                from
	                                fnp_entity
                                union 
                                select
	                                aff_company_code entity_id,
	                                aff_company_abb entity,
	                                aff_company_cname entity_namec,
	                                aff_company_ename entity_namee,
	                                case when aff_status = '1' then 0 else 1 end as Status,
	                                null area_id
                                from
	                                affiliate_company";
            var result = DbAccess.Database.SqlQuery<FnpEntity>(unionSql);
            if (areaIds.Count != 0) return result.Where(w => areaIds.Contains(w.AreaId)).ToList();
            return result;
        }
        #endregion

        #region 合約管理人/總部法務行政關卡/兩者的代理人對應的主體信息查詢
        /// <summary>
        /// 合約管理人/總部法務行政關卡/兩者的代理人對應的主體信息查詢
        /// </summary>
        /// <returns></returns>
        public static IEnumerable<FnpEntityModel> QueryByOriginal()
        {
            return _repository.QueryByOriginal();
        }
        #endregion

        #region 根據主體id集合獲取主體信息
        /// <summary>
        /// 根據主體id集合獲取主體信息  CR：266
        /// </summary>
        /// <param name="entityIds"></param>
        /// <returns></returns>
        public static List<FnpEntityModel> GetFnpEntityDataByEntityID(List<string> entityIds)
        {
            return _repository.GetFnpEntityDataByEntityID(entityIds);
        }
        #endregion

        #region 根據主體獲取關聯主體數據
        /// <summary>
        /// 根據主體獲取關聯主體數據 CR：265
        /// </summary>
        /// <param name="entityIds"></param>
        /// <returns></returns>
        public static object GetFnpEntityDataByGroupEntityID(List<string> entityIds)
        {
            return _repository.GetFnpEntityDataByGroupEntityID(entityIds);
        } 
        #endregion
    }
}
