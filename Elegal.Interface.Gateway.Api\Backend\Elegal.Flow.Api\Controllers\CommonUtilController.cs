﻿using Elegal.Interface.Api.Common.Control.Web;
using Elegal.Interface.Api.Common.FuncHelper;
using Elegal.Interface.Api.Common.Model.ResultModel;
using Microsoft.AspNetCore.Mvc;

namespace Elegal.Flow.Api.Controllers
{
    /// <summary>
    /// 工具方法
    /// </summary>
    [Route("[controller]")]
    [ApiController]
    public class CommonUtilController : BaseController
    {
        /// <summary>
        /// 工号排序(8碼->7碼->英文開頭工號)
        /// </summary>
        /// <param name="list"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("SortEmp")]
        public async Task<ApiResultModelByObject> SortEmp([FromBody] List<KeyValuePair<int, string>> list)
        {
            ApiResultModelByObject apiResult = new();
            List<KeyValuePair<int, string>> keyValuePairs = new List<KeyValuePair<int, string>>();
            var list1 = list.Where(w => char.IsDigit(w.Value[0]));
            var list2 = list.Except(list1);
            keyValuePairs.AddRange(list1.OrderByDescending(o => o.Value.Length).ThenByDescending(o => o.Value));
            keyValuePairs.AddRange(list2.OrderBy(o => o.Value));
            apiResult.listData = keyValuePairs;
            apiResult.rtnSuccess = true;
            return await Task.FromResult(apiResult);
        }

        /// <summary>
        /// 獲取機密等級水印文字(讀取配置檔)
        /// </summary>
        /// <param name="confidenLevel"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetConfidenLevelWatermarkText")]
        public async Task<ApiResultModelByObject> GetConfidenLevelWatermarkText(string confidenLevel)
        {
            ApiResultModelByObject apiResult = new()
            {
                listData = CommonUtilHelper.GetConfidenLevelWatermarkText(confidenLevel),
                rtnSuccess = true
            };
            return await Task.FromResult(apiResult);
        }

        /// <summary>
        /// 獲取時區的偏移量
        /// </summary>
        /// <returns></returns>
        /// <returns></returns>
        [HttpGet]
        [Route("GetUtcOffset")]
        public async Task<ApiResultModelByObject> GetUtcOffset()
        {
            ApiResultModelByObject apiResult = new()
            {
                listData = TimeZoneInfoConvertHelper.GetBaseUtcOffset(AppSettingHelper.GetValue("TimeZone")),
                rtnSuccess = true
            };
            return await Task.FromResult(apiResult);
        }
    }
}
