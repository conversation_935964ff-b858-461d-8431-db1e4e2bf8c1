﻿using Elegal.Flow.Api.Services.Individual;
using Elegal.Flow.Api.Services.Report;
using Elegal.Interface.Api.Common.Control.Filter;
using Elegal.Interface.Api.Common.Control.Web;
using Elegal.Interface.Api.Common.Model.ResultModel;
using Elegal.Interface.Api.Common.Model.ViewModel.FlowApi;
using Elegal.Interface.ApiData.Service.FuncService;
using Elegal.Interface.ApiData.Service.Model.QueryCondition;
using Elegal.Orm.Dtos;
using Elegal.Orm.Utils;
using Microsoft.AspNetCore.Mvc;
using System.Linq;
using System.Text;

namespace Elegal.Flow.Api.Controllers.Individual
{
    /// <summary>
    /// 首页区块管理
    /// </summary>
    [Route("[controller]")]
    [ApiController]
    public class SysHomeBlockController : BaseController
    {
        #region 系统维护-首頁區塊管理
        /// <summary>
        /// 获取区块信息
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("GetList")]
        public async Task<ApiResultModelByObject> GetList()
        {
            ApiResultModelByObject apiResult = new ApiResultModelByObject()
            {
                listData = SysHomeBlockService.GetList(),
                rtnSuccess = true,
            };
            return await Task.FromResult(apiResult);
        }

        /// <summary>
        /// 保存区块信息
        /// </summary>
        /// <param name="list"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("Save")]
        public async Task<ApiResultModelByObject> Save(List<SysHomeBlockModel> list)
        {
            ApiResultModelByObject apiResult = new ApiResultModelByObject();
            if (list.Count == 0) { return await Task.FromResult(apiResult); }
            apiResult.listData = SysHomeBlockService.Save(list);
            apiResult.rtnSuccess = true;
            return await Task.FromResult(apiResult);
        }
        #endregion

        #region 个人设定-首页设定
        /// <summary>
        /// 获取个人区块信息
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("GetBlockInfo")]
        public async Task<ApiResultModelByObject> GetBlockInfo()
        {
            ApiResultModelByObject apiResult = new ApiResultModelByObject()
            {
                listData = SysHomeBlockService.GetBlockInfo(),
                rtnSuccess = true,
            };
            return await Task.FromResult(apiResult);
        }

        /// <summary>
        /// 保存个人区块显示信息
        /// </summary>
        /// <param name="list"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("SaveUserBlock")]
        public async Task<ApiResultModelByObject> SaveUserBlock(List<SysHomeBlockStatusModel> list)
        {
            ApiResultModelByObject apiResult = new ApiResultModelByObject();
            if (list.Count == 0) { return await Task.FromResult(apiResult); }
            var oldData = SysHomeBlockStatusDataService.Query(new SysHomeBlockStatusQueryCondition() { Emplid = MvcContext.UserInfo.current_emp });
            //新增
            if (oldData.Count == 0) return SysHomeBlockService.SaveAdd(list);
            //修改
            else return SysHomeBlockService.SaveEdit(list);
        }
        #endregion

        /// <summary>
        /// 完成引导页
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [Route("GuidePageCompleted")]
        public async Task<ApiResultModelByObject> GuidePageCompleted(string? id)
        {
            ApiResultModelByObject apiResult = new ApiResultModelByObject()
            {
                listData = SysHomeBlockService.GuidePageCompleted(),
                rtnSuccess = true,
            };
            InitLogRecord(log =>
            {
                if (!string.IsNullOrEmpty(id))
                {
                    log.Detail = $"{ActionFilter.GetMultilingualValue("Home_currnStep", true)}：{SysUserGuideDataService.FindByKey(id).TipTitle}";
                }
            });
            return await Task.FromResult(apiResult);
        }
    }
}
