﻿using Elegal.Flow.Api.Services.Application;
using Elegal.Flow.Api.Services.Paper;
using Elegal.Interface.Api.Common.Control.Filter;
using Elegal.Interface.Api.Common.Control.Web;
using Elegal.Interface.Api.Common.FuncHelper;
using Elegal.Interface.Api.Common.Model;
using Elegal.Interface.Api.Common.Model.Enum;
using Elegal.Interface.Api.Common.Model.ParaModel.FlowApi;
using Elegal.Interface.Api.Common.Model.ParaModel.MinioApi;
using Elegal.Interface.Api.Common.Model.ResultModel;
using Elegal.Interface.Api.Common.Model.ViewModel;
using Elegal.Interface.Api.Common.Model.ViewModel.FlowApi;
using Elegal.Interface.ApiData.Service.FuncService;
using Elegal.Interface.ApiData.Service.Model.DbModel;
using Elegal.Interface.ApiData.Service.Model.QueryCondition;
using Elegal.Interface.ApiData.Service.Model.ViewModel;
using Elegal.Orm;
using Elegal.Orm.Dtos;
using Elegal.Orm.Utils;
using Microsoft.AspNetCore.Http.Features;
using Microsoft.AspNetCore.Mvc;
using Minio;
using Minio.DataModel.Args;
using System.Text;
#nullable disable
namespace Elegal.Flow.Api.Controllers.Application
{
    /// <summary>
    /// 紙本进度追踪
    /// </summary>
    [Route("[controller]")]
    [ApiController]
    public class PaperTrackingController : BaseController
    {
        //當前操作用戶信息
        private UserInfoModel UserInfo;

        #region 構造函数，注入MinioClient、MinIOConfig
        private readonly MinioClient _minioClient;
        private readonly MinIOConfig _minIOConfig;
        /// <summary>
        /// 構造函数
        /// </summary>
        /// <param name="minioClient"></param>
        /// <param name="minIOConfig"></param>
        public PaperTrackingController(MinioClient minioClient, MinIOConfig minIOConfig)
        {
            PaperTrackingService._minioClient = _minioClient = minioClient;
            PaperTrackingService._minIOConfig = _minIOConfig = minIOConfig;
        }
        #endregion

        #region 获取申请单基本信息
        /// <summary>
        /// 获取申请单基本信息
        /// </summary>
        /// <param name="applyNumber">申请单号</param>
        /// <returns></returns>
        [HttpPost]
        [Route("GetBaseInformation")]
        public async Task<ApiResultModelByObject> GetBaseInformation(string applyNumber)
        {
            
            ApiResultModelByObject apiResult = new ApiResultModelByObject()
            {
                listData = PaperTrackingService.GetBaseInformation(applyNumber),
                rtnSuccess = true,
            };
            InitLogRecord(LogRecord => { 
                StringBuilder stringBuilder = new StringBuilder();
                stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("PaperTracking_applyNum", true)}：{applyNumber}");
                LogRecord.Detail = stringBuilder.ToString();
            });
            return await Task.FromResult(apiResult);
        }
        #endregion

        #region 新增紙本收發記錄
        /// <summary>
        /// 新增紙本收發記錄
        /// </summary>
        /// <param name="condition">查询参数</param>
        /// <returns></returns>
        [HttpPost]
        [Route("Add")]
        public async Task<ApiResultModelByObject> Add(PaperTrackingBathJobAddModel condition)
        {
            ApiResultModelByObject apiResult = new ApiResultModelByObject()
            {
                listData = PaperTrackingBathJobService.Add(condition),
                rtnSuccess = true,
            };
            InitLogRecord(log =>
            {
                StringBuilder stringBuilder = new StringBuilder();
                stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("PaperTracking_applyNum", true)}：{string.Join(",", condition.applyNum)}");
                stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("PaperTracking_paperTracking", true)}：{condition.paperTracking.FunName}");
                stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("PaperTracking_receiptType", true)}：{ActionFilter.GetMultilingualValue($"PaperTracking_receiptType{condition.receiptType}", true)}");
                if (!string.IsNullOrEmpty(condition.consignmentNumber)) stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("PaperTracking_consignmentNumber", true)}：{string.Join(",", condition.consignmentNumber)}");
                stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("PaperTracking_recipient", true)}：{condition.recipient.NameA}");
                stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("PaperTracking_receiptDate", true)}：{condition.receipt.ConvertDateByTimeZoneByUtc(MvcContext.UserInfo.time_zone).ToString("yyyy/MM/dd HH:mm")}");
                stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("PaperTracking_paperPiece", true)}：{condition.paperPiece}");
                if (!string.IsNullOrEmpty(condition.notes)) stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("PaperTracking_notes", true)}：{condition.notes}");
                log.Detail = stringBuilder.ToString();
            });
            return await Task.FromResult(apiResult);
        }
        #endregion

        #region 發送邮件提醒
        /// <summary>
        /// 發送邮件提醒
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("SendMail")]
        public async Task<ApiResultModel> SendMail(PaperTrackingBathJobViewModel model)
        {
            //发送前判断纸本追踪是否有变动
            var oldData = PaperTrackWorkDataService.Query(new PaperTrackWorkQueryCondition()
            {
                SearchItemGroup = new SearchItemGroup()
                {
                    Items = new List<SearchItem>
                    {
                        new SearchItem()
                        {
                            Field = "apply_number",
                            Compare = CompareOperator.ARRAYIN,
                            Logic = LogicOperator.And,
                            Values =[model.applyNum]
                        }
                    }
                },
                OrderBys = new List<OrderByParam>
                {
                    new OrderByParam()
                    {
                        Field = "receiving_date",//收件日倒序
                        Order = OrderBy.DESC,
                    }
                }
            }).FirstOrDefault();
            if (oldData == null || !model.paperTracking.Equals(oldData.TrackStatus))
            {
                return new ApiResultModel()
                {
                    messageContent = new List<string> { ActionFilter.GetMultilingualValue("custom:messageContent:dataChurn") },
                    messageType = MessageTypeUtils.Warning.ToString()
                };
            }
            var rlist = PaperTrackingBathJobService.SendMail([model], false);
            var mailres= rlist.FirstOrDefault();
            //匯總提示
            ApiResultModel apiResult = new ApiResultModel()
            {
                messageContent=[ActionFilter.GetMultilingualValue("custom:messageContent:mailSuccess")],
                rtnSuccess = rlist?.FirstOrDefault()?.succeed??false  
            };
            if (mailres != null && !mailres.succeed) {
                apiResult.messageType = MessageTypeUtils.Warning.ToString();
                apiResult.messageContent = new List<string> { mailres.Massage };
            }
            //InitLogRecord(log =>
            //{
            //    StringBuilder stringBuilder = new StringBuilder();
            //    log.Detail = string.Join(CommonUtil.GetDivisionLine() + "\r\n", new List<PaperTrackingBathJobViewModel>() { model }.Select(model =>
            //    {
            //        StringBuilder stringBuilder = new StringBuilder();
            //        stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("PaperTracking_applyNum", true)}：{model.applyNum}");
            //        stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("PaperTracking_recipient", true)}：{model.recipientEname}");
            //        return stringBuilder.ToString();
            //    }));
            //});
            return await Task.FromResult(apiResult);
        }
        #endregion

        #region 法務行政交接備注檔案上傳
        /// <summary>
        /// 文件上传(检查文件总大小)
        /// </summary>
        /// <param name="option"></param>
        /// <returns></returns>
        [HttpPost("UploadFileCheckSize")]
        public async Task<ApiResultModelByObject> UploadFileCheckSize([FromForm] SysUploadFileViewModel option)
        {
            ApiResultModelByObject model = new ApiResultModelByObject() { listData = -1 };
            //上傳的文件
            var uploadFiles = (await Request.ReadFormAsync(new FormOptions { })).Files;
            //申请单下所有法務行政交接備註文件
            var contractFiles = SysUploadFileDataService.Query(new SysUploadFileQueryCondition()
            {
                UploadType = option.UploadType,
                UploadKey = option.UploadKey.ToUpper(),
                ArchivePurposes = option.ArchivePurposes,
                IsWatermake = (int)YesOrNoUtils.No
            });
            //申请单下所有文件大小总和
            //文件大小总和+上传的文件大小总和不超過80M
            if (contractFiles.Sum(s => s.FileSize) + uploadFiles.Sum(f => f.Length) > 1024 * 1024 * 80)
            {
                model.messageTitle = ActionFilter.GetMultilingualValue("custom:messageTitle:fileUploadFail");
                model.messageContent.Add(ActionFilter.GetMultilingualValue("custom:messageContent:fileSizeMax"));
                model.messageType = MessageTypeUtils.Warning.ToString();
                model.rtnSuccess = false;
                return model;
            }
            return await Task.FromResult(await PaperTrackingService.UploadFileCheckSize(uploadFiles, option));
        }
        /// <summary>
        /// 判断文件重复上传
        /// </summary>
        /// <param name="option"></param>
        /// <returns></returns>
        [HttpPost("CheckFileUploadRepeat")]
        public async Task<ApiResultModelByObject> CheckFileUploadRepeat(SysUploadFileCheckModel option)
        {
            ApiResultModelByObject result = new ApiResultModelByObject()
            {
                listData = true,
                rtnSuccess = true,
                messageType = MessageTypeUtils.Success.ToString(),
            };
            var allFiles = SysUploadFileDataService.Query(new SysUploadFileQueryCondition()
            {
                UploadType = option.UploadType,
                ArchivePurposes = 4,
                UploadKey = option.UploadKey.ToUpper(),
                IsWatermake = 0
            });
            List<string> existsFile = allFiles.Where(w => option.FileNames.FirstOrDefault(f => f.Equals(w.FileName)) != null).Select(s => s.FileName).ToList();
            if (existsFile.Any())
            {
                result.listData = false;
                result.rtnSuccess = false;
                result.messageType = MessageTypeUtils.Warning.ToString();
                result.messageContent.Add(string.Format(ActionFilter.GetMultilingualValue("custom:messageContent:repeatUpload"), string.Join(",", existsFile)));
            }
            InitLogRecord(log =>{
                StringBuilder stringBuilder = new StringBuilder();
                stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue($"ArchivalAttachments_applyNumber", true)}：{option.UploadKey}");
                stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue($"ArchivalAttachments_fileList", true)}：{string.Join("，", option.FileNames)}");
                log.Detail = stringBuilder.ToString();
            });
            return await Task.FromResult(result);
        }
        #endregion

        #region 批量刪除文件
        /// <summary>
        /// 批量刪除文件
        /// </summary>
        /// <param name="fileIds">文件id</param>
        /// <returns></returns>
        [HttpPost("DeleteFile")]
        public async Task<ApiResultModelByObject> DeleteFile(List<int> fileIds)
        {
            if (fileIds.Count == 0)
            {
                return new ApiResultModelByObject()
                {
                    messageType = MessageTypeUtils.Warning.ToString(),
                    messageContent = new List<string>() { ActionFilter.GetMultilingualValue("custom:messageContent:selectDeleteFiles") },
                    messageTitle = ActionFilter.GetMultilingualValue("custom:messageTitle:fileDeleteFail")
                };
            }
            return await Task.FromResult(await PaperTrackingService.DeleteMultipleFile(fileIds, _minioClient, _minIOConfig));
        }
        #endregion

        #region 批量下載文件
        /// <summary>
        /// 批量下載文件
        /// </summary>
        /// <param name="fileIds">文件id</param>
        /// <returns></returns>
        [HttpPost("DownloadFiles")]
        public async Task<ApiResultModelByObject> DownloadFiles(List<int> fileIds)
        {
            UserInfo = MvcContext.UserInfo;
            List<FileContentResult> fileContentResults = [];
            List<HandoverFileHistory> fileHistory = [];
            if (fileIds.Count == 0)
            {
                return new ApiResultModelByObject()
                {
                    messageType = MessageTypeUtils.Warning.ToString(),
                    messageContent = new List<string>() { ActionFilter.GetMultilingualValue("custom:messageContent:selectDownloadFiles") },
                    messageTitle = ActionFilter.GetMultilingualValue("custom:messageTitle:fileDownloadFail")
                };
            }
            ApiResultModelByObject result = new ApiResultModelByObject() { listData = fileContentResults, rtnSuccess = true, messageType = MessageTypeUtils.Success.ToString() };
            //勾选的文件集合
            List<SysUploadFile> selectFiles = SysUploadFileDataService.Query(new SysUploadFileQueryCondition()
            {
                SearchItemGroup = new SearchItemGroup() { Items = [new SearchItem() { Compare = CompareOperator.ARRAYIN, Values = fileIds.Select(s => s.ToString()), Field = "fileid", Logic = LogicOperator.And }] }
            });
            if (selectFiles.Count < fileIds.Count)
                return new ApiResultModelByObject()
                {
                    rtnSuccess = false,
                    messageType = MessageTypeUtils.Warning.ToString(),
                    messageContent = [ActionFilter.GetMultilingualValue("custom:messageContent:dataChurn")],
                    messageTitle = ActionFilter.GetMultilingualValue("custom:messageTitle:fileDownloadFail")
                };
            //foreach (var file in selectFiles)
            //{
                
            //    var docMemory = new MemoryStream();
            //    try
            //    {
            //        await _minioClient.GetObjectAsync(new GetObjectArgs().WithBucket(_minIOConfig.Bucket).WithObject(file.FilePath).WithCallbackStream((stream) => { stream.CopyTo(docMemory); })).ConfigureAwait(false);
            //        fileContentResults.Add(File(docMemory.ToArray(), "application/json", file.FileName));
            //        fileHistory.Add(new HandoverFileHistory() { ActionId = 7, ApplyNumber = file.UploadKey, FileName = file.FileName, CreateUser = UserInfo.current_emp });
            //    }
            //    catch { docMemory.Close(); continue; }
            //}
            result.listData = selectFiles;
            //DbAccess.BatchCreateSql(fileHistory.ToArray());
            InitLogRecord(log => { 
                StringBuilder stringBuilder = new StringBuilder();
                stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("ArchivalAttachments_applyNumber", true)}：{selectFiles.FirstOrDefault().UploadKey}");
                stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("ArchivalAttachments_fileList", true)}：{string.Join("，", selectFiles.Select(s => s.FileName))}");
                log.Detail = stringBuilder.ToString();
            });
            return result;
        }


        /// <summary>
        /// 批量下載文件 成功后更新下载次数
        /// </summary>
        /// <param name="fileIds">文件id</param>
        /// <returns></returns>
        [HttpPost("DownFilesLogs")]
        public async Task<ApiResultModel> DownFilesLogs(List<int> fileIds)
        {
            UserInfo = MvcContext.UserInfo;
            List<HandoverFileHistory> fileHistory = [];

            ApiResultModel result = new ApiResultModel() {  messageType = MessageTypeUtils.Success.ToString() };
           

            try
            {
                //勾选的文件集合
                List<SysUploadFile> selectFiles = SysUploadFileDataService.Query(new SysUploadFileQueryCondition()
                {
                    SearchItemGroup = new SearchItemGroup() { Items = [new SearchItem() { Compare = CompareOperator.ARRAYIN, Values = fileIds.Select(s => s.ToString()), Field = "fileid", Logic = LogicOperator.And }] }
                });
                foreach (var file in selectFiles)
                {
                    fileHistory.Add(new HandoverFileHistory() { ActionId = 7, ApplyNumber = file.UploadKey, FileName = file.FileName, CreateUser = UserInfo.current_emp });
                }
                DbAccess.BatchCreateSql(fileHistory.ToArray());
                result.rtnSuccess = true;
            }
            catch (Exception)
            {
                result.rtnSuccess = false;
               
            }
           
            return result;
        }
        #endregion

        #region 保存法務行政交接備注信息
        /// <summary>
        /// 保存法務行政交接備注信息
        /// </summary>
        /// <param name="model">参数</param>
        /// <returns></returns>
        [HttpPost("SetNotes")]
        public async Task<ApiResultModelByObject> SetNotes(FormLegalHandoverViewModel model)
        {
            ApiResultModelByObject apiResult = new()
            {
                listData = PaperTrackingService.SetNotes(model.ApplyNumber, model.HandoverRemark),
                rtnSuccess = true
            };
            return await Task.FromResult(apiResult);
        }
        #endregion
    }
}
