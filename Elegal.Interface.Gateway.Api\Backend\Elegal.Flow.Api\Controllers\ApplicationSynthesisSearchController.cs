﻿using Elegal.Flow.Api.Services.ApplicationSearch;
using Elegal.Interface.Api.Common.Control.Filter;
using Elegal.Interface.Api.Common.Control.Web;
using Elegal.Interface.Api.Common.FuncHelper;
using Elegal.Interface.Api.Common.FuncService;
using Elegal.Interface.Api.Common.GetLogCommon;
using Elegal.Interface.Api.Common.Model;
using Elegal.Interface.Api.Common.Model.ParaModel.FlowApi.ApplicationSynthesisSearch;
using Elegal.Interface.Api.Common.Model.ResultModel;
using Elegal.Interface.ApiData.Service.FuncService;
using Elegal.Interface.ApiData.Service.Model.QueryCondition;
using Elegal.Orm.Dtos;
using Microsoft.AspNetCore.Mvc;
using System.Text;

namespace Elegal.Flow.Api.Controllers
{
    /// <summary>
    /// 資料查詢 -> 申請單綜合查詢
    /// </summary>
    [Route("[controller]")]
    [ApiController]
    public class ApplicationSynthesisSearchController : BaseController
    {
        #region 案件申請查詢
        /// <summary>
        /// 案件申請查詢
        /// </summary>
        /// <param name="asspm">查詢條件</param>
        /// <returns></returns>
        [HttpPost]
        [Route("ApplicationSynthesisSearchData")]
        public async Task<ApiResultModelByObject> ApplicationSynthesisSearchData([FromBody] ApplicationSynthesisSearchParaModel asspm)
        {
            ApiResultModelByObject apiResult = new ApiResultModelByObject();
            //预先存储到期日数据，在后续的查询过程中到期日数据会被修改
            var aspExpType = asspm.expType.ToList();
            var aspSignType = asspm.signType.ToList();
            var aspEffType = asspm.effType.ToList();

            int totalCount = 0;
            apiResult.listData = ApplicationSynthesisSearchService.GetApplicationData(asspm, MvcContext.UserInfo.current_emp, MvcContext.UserInfo.current_roles, out totalCount, MvcContext.UserInfo.logging_locale);
            apiResult.totalCount = totalCount;
            apiResult.rtnSuccess = true;

            #region 記錄日誌
            InitLogRecord(asspm, log =>
            {
                StringBuilder stringBuilder = new StringBuilder();
                //獲取下拉框選項
                SysParametersQueryCondition sysParametersQueryCondition = new SysParametersQueryCondition()
                {
                    LangType = MvcContext.UserInfo.logging_locale,
                    //ParaCode = new string[] { "applicationState" },
                    SearchItemGroup = new SearchItemGroup()
                    {
                        Items = new List<SearchItem>() { new SearchItem() { Field = "ParaCode", Values = new string[] { "applicationState", "contractType", "targetType", "egType", "archiveStatus", "amountStatus", "currency", "taxType", "accountType", "confidentStatus", "acknowledgeType", "dateOptions", "effDateOptions" }, Compare = CompareOperator.ARRAYIN } }
                    },
                    OrderBys = new List<Orm.Dtos.OrderByParam>()
                    {
                        new Orm.Dtos.OrderByParam(){Field="SortOrder",Order=Orm.Dtos.OrderBy.ASC}
                    }
                };
                var listData = SysParametersDataService.Query(sysParametersQueryCondition);
                List<DropDownListModel> flowStepListData = new List<DropDownListModel>();
                foreach (var item in ApplicationSearchService.GetFlowStepByParaCode(MvcContext.UserInfo.logging_locale))
                {
                    var keys = item.paraKey.Split(',');
                    foreach (var key in keys)
                    {
                        DropDownListModel dropDownListModel = new DropDownListModel()
                        {
                            paraKey = key,
                            paraValue = item.paraValue
                        };
                        flowStepListData.Add(dropDownListModel);
                    }
                }
                //签约日、生效日、到期日类型拆分
                var signType1 = listData.Where(x => x.ParaCode == "dateOptions" && (new List<string>() { "02", "05" }).Contains(x.FuncCode)).Select(x => new { x.FuncCode, x.FunName }).ToList();//签约日有日期
                var signType2 = listData.Where(x => x.ParaCode == "dateOptions" && !(new List<string>() { "02", "04", "05" }).Contains(x.FuncCode)).Select(x => new { x.FuncCode, x.FunName }).ToList();//签约日无日期
                var effType1 = listData.Where(x => x.ParaCode == "effDateOptions" && (new List<string>() { "02", "04" }).Contains(x.FuncCode)).Select(x => new { x.FuncCode, x.FunName }).ToList();//生效日有日期
                var effType2 = listData.Where(x => x.ParaCode == "effDateOptions" && !(new List<string>() { "02", "04" }).Contains(x.FuncCode)).Select(x => new { x.FuncCode, x.FunName }).ToList();//生效日无日期
                var expType1 = listData.Where(x => x.ParaCode == "dateOptions" && (new List<string>() { "02", "05" }).Contains(x.FuncCode)).Select(x => new { x.FuncCode, x.FunName }).ToList();//到期日有日期
                var expType2 = listData.Where(x => x.ParaCode == "dateOptions" && !(new List<string>() { "02", "05" }).Contains(x.FuncCode)).Select(x => new { x.FuncCode, x.FunName }).ToList();//到期日无日期
                stringBuilder.GetLogString(asspm.caseStatus, "ApplicationSearch_caseStatus")
                             .GetLogString("ApplicationSearch_applyType", SysParametersService.GetApplicationType(MvcContext.UserInfo.logging_locale).Where(x => asspm.applyType.Contains(x.paraKey)).Select(x => x.paraValue).ToList())
                             .GetLogString("ApplicationSearch_applicationState", listData.Where(x => x.ParaCode == "applicationState" && asspm.applicationState.Contains(x.FuncCode)).Select(x => x.FunName).ToList())
                             .GetLogString(asspm.applyNumber, "ApplicationSearch_applyNumber")
                             .GetLogString(asspm.applyTimeStart.ConvertDateByTimeZoneByUtc(MvcContext.UserInfo.time_zone)?.ToString("yyyy/MM/dd") ?? string.Empty, "ApplicationSearch_applyTimeStart")
                             .GetLogString(asspm.applyTimeEnd.ConvertDateByTimeZoneByUtc(MvcContext.UserInfo.time_zone)?.ToString("yyyy/MM/dd") ?? string.Empty, "ApplicationSearch_applyTimeEnd")
                             .GetLogString("ApplicationSearch_entityID", GetLogCommon.GetEntity(asspm.entityID))
                             .GetLogString(asspm.otherParty, "ApplicationSearch_otherParty")
                             .GetLogString(asspm.contractName, "ApplicationSearch_contractName")
                             .GetLogString(GetLogCommon.GetUserName(asspm.empData), "ApplicationSearch_empData")
                             .GetLogString(asspm.deptID, "ApplicationSearch_deptID")
                             .GetLogString("ApplicationSearch_contractType", listData.Where(x => x.ParaCode == "contractType" && asspm.contractType.Contains(x.FuncCode)).Select(x => x.FunName).ToList())
                             .GetLogString("ApplicationSearch_contractObj", listData.Where(x => x.ParaCode == "targetType" && asspm.contractObj.Contains(x.FuncCode)).Select(x => x.FunName).ToList())
                             .GetLogString("ApplicationSearch_signType1", signType1.Where(x => aspSignType.Contains(x.FuncCode)).Select(x => x.FunName).ToList())
                             .GetLogString(asspm.signDateStart, "ApplicationSearch_signDateStart")
                             .GetLogString(asspm.signDateEnd, "ApplicationSearch_signDateEnd")
                             .GetLogString("ApplicationSearch_signType2", signType2.Where(x => aspSignType.Contains(x.FuncCode)).Select(x => x.FunName).ToList())
                             .GetLogString(asspm.confirmSignDateStart, "ApplicationSearch_comfirmSignDateStart")
                             .GetLogString(asspm.confirmSignDateEnd, "ApplicationSearch_comfirmSignDateEnd")
                             .GetLogString("ApplicationSearch_effType1", effType1.Where(x => aspEffType.Contains(x.FuncCode)).Select(x => x.FunName).ToList())
                             .GetLogString(asspm.effDateStart, "ApplicationSearch_effDateStart")
                             .GetLogString(asspm.effDateEnd, "ApplicationSearch_effDateEnd")
                             .GetLogString("ApplicationSearch_effType2", effType2.Where(x => aspEffType.Contains(x.FuncCode)).Select(x => x.FunName).ToList())
                             .GetLogString(asspm.confirmEffDateStart, "ApplicationSearch_confirmEffDataStart")
                             .GetLogString(asspm.confirmEffDateEnd, "ApplicationSearch_confirmEffDataEnd");
                // 有到期日分為延展和不延展
                if (aspExpType.Contains("02"))
                {
                    List<string> strings = new List<string>();
                    //對2做特殊處理
                    //判斷是否有延展
                    if (asspm.hasExpExtend == 1) strings.Add("有延展");
                    if (asspm.hasExpExtend == 0) strings.Add("無延展");
                    if (asspm.hasExpExtend == -1) { strings.Add("有延展"); strings.Add("無延展"); }
                    strings.AddRange(expType1.Where(x => aspExpType.Contains(x.FuncCode) && x.FuncCode != "02").Select(x => x.FunName).ToList());
                    stringBuilder.GetLogString("ApplicationSearch_expType", strings)
                    .GetLogString(asspm.expDateStart, "ApplicationSearch_expDateStart")
                    .GetLogString(asspm.expDateEnd, "ApplicationSearch_expDateEnd")
                    .GetLogString("ApplicationSearch_expType2", expType2.Where(x => aspExpType.Contains(x.FuncCode)).Select(x => x.FunName).ToList());
                }
                else
                {
                    stringBuilder.GetLogString("ApplicationSearch_expType1", expType1.Where(x => aspExpType.Contains(x.FuncCode)).Select(x => x.FunName).ToList())
                    .GetLogString(asspm.expDateStart, "ApplicationSearch_expDateStart")
                    .GetLogString(asspm.expDateEnd, "ApplicationSearch_expDateEnd")
                    .GetLogString("ApplicationSearch_expType2", expType2.Where(x => aspExpType.Contains(x.FuncCode)).Select(x => x.FunName).ToList());
                }
                stringBuilder.GetLogString(asspm.expExtDateStart, "ApplicationSearch_expExtDateStart")
                .GetLogString(asspm.expExtDateEnd, "ApplicationSearch_expExtDateEnd");
                //處理確認到期日
                if (!string.IsNullOrEmpty(asspm.confirmExpDateEnd) || !string.IsNullOrEmpty(asspm.confirmExpDateStart))
                {
                    List<string> strings = new List<string>();
                    if (asspm.confirmHasExpExtend == -1) { strings.Add("有延展"); strings.Add("無延展"); }
                    if (asspm.confirmHasExpExtend == 0) strings.Add("無延展");
                    if (asspm.confirmHasExpExtend == 1) strings.Add("有延展");
                    stringBuilder.GetLogString("ApplicationSearch_confirmHasExpExtend", strings)
                             .GetLogString(asspm.confirmExpDateStart, "ApplicationSearch_confirmExpDateStart")
                             .GetLogString(asspm.confirmExpDateEnd, "ApplicationSearch_confirmExpDateEnd");
                }

                stringBuilder.GetLogString(asspm.originArchiveDateStart.ConvertDateByTimeZoneByUtc(MvcContext.UserInfo.time_zone)?.ToString("yyyy/MM/dd") ?? string.Empty, "ApplicationSearch_originArchiveDateStart")
                             .GetLogString(asspm.originArchiveDateEnd.ConvertDateByTimeZoneByUtc(MvcContext.UserInfo.time_zone)?.ToString("yyyy/MM/dd") ?? string.Empty, "ApplicationSearch_originArchiveDateEnd")
                             .GetLogString(asspm.approvedDateStart.ConvertDateByTimeZoneByUtc(MvcContext.UserInfo.time_zone)?.ToString("yyyy/MM/dd") ?? string.Empty, "ApplicationSearch_approvedDateStart")
                             .GetLogString(asspm.approvedDateEnd.ConvertDateByTimeZoneByUtc(MvcContext.UserInfo.time_zone)?.ToString("yyyy/MM/dd") ?? string.Empty, "ApplicationSearch_approvedDateEnd")
                             .GetLogString(asspm.closedDateStart.ConvertDateByTimeZoneByUtc(MvcContext.UserInfo.time_zone)?.ToString("yyyy/MM/dd") ?? string.Empty, "ApplicationSearch_closedDateStart")
                             .GetLogString(asspm.closedDateEnd.ConvertDateByTimeZoneByUtc(MvcContext.UserInfo.time_zone)?.ToString("yyyy/MM/dd") ?? string.Empty, "ApplicationSearch_closedDateEnd")
                             .GetLogString(asspm.contractNumber, "ApplicationSearch_contractNumber")
                             .GetLogString(asspm.mainContractNumber, "ApplicationSearch_mainContractNumber")
                             .GetLogString(asspm.localContractNumber, "ApplicationSearch_localContractNumber")
                             .GetLogString(asspm.othePrartyNumber, "ApplicationSearch_othePrartyNumber")
                             .GetLogString(asspm.earlyCeaseDateStrat, "ApplicationSearch_earlyCeaseDateStrat")
                             .GetLogString(asspm.earlyCeaseDateEnd, "ApplicationSearch_earlyCeaseDateEnd")
                             .GetLogString("ApplicationSearch_endorsement", listData.Where(x => x.ParaCode == "egType" && asspm.endorsement.Contains(x.FuncCode)).Select(x => x.FunName).ToList())
                             .GetLogString("ApplicationSearch_originArchiveType", listData.Where(x => x.ParaCode == "archiveStatus" && asspm.originArchiveType.Contains(x.FuncCode)).Select(x => x.FunName).ToList())
                             .GetLogString("ApplicationSearch_isHavingMoney", listData.Where(x => x.ParaCode == "amountStatus" && asspm.isHavingMoney.Contains(x.FuncCode)).Select(x => x.FunName).ToList())
                             .GetLogString("ApplicationSearch_currency", listData.Where(x => x.ParaCode == "currency" && asspm.currency.Contains(x.FuncCode)).Select(x => x.FunName).ToList())
                             .GetLogString("ApplicationSearch_taxesType", listData.Where(x => x.ParaCode == "taxType" && asspm.taxesType.Contains(x.FuncCode)).Select(x => x.FunName).ToList())
                             .GetLogString("ApplicationSearch_accountType", listData.Where(x => x.ParaCode == "accountType" && asspm.accountType.Contains(x.FuncCode)).Select(x => x.FunName).ToList())
                             .GetLogString(asspm.accountDeptid, "ApplicationSearch_accountDeptid")
                             .GetLogString(asspm.codeName, "ApplicationSearch_codeName")
                             .GetLogString("ApplicationSearch_confidenLevel", listData.Where(x => x.ParaCode == "confidentStatus" && asspm.confidenLevel.Contains(x.FuncCode)).Select(x => x.FunName).ToList())
                             .GetLogString("ApplicationSearch_acknowledgeType", listData.Where(x => x.ParaCode == "acknowledgeType" && asspm.acknowledgeType.Contains(x.FuncCode)).Select(x => x.FunName).ToList())
                             .GetLogString("ApplicationSearch_currentLevels", flowStepListData.Where(x => asspm.currentLevels.Contains(Convert.ToInt32(x.paraKey))).Select(x => x.paraValue).Distinct().ToList())
                             .GetLogString(GetLogCommon.GetUserName(asspm.currentSigner), "ApplicationSearch_currentSigner")
                             .GetLogString(GetLogCommon.GetUserName(asspm.signerData), "ApplicationSearch_signerData")
                             .GetLogString(asspm.signerDept, "ApplicationSearch_signerDept")
                             ;
                log.Detail = stringBuilder.ToString();
            });
            #endregion

            return await Task.FromResult<ApiResultModelByObject>(apiResult);
        }

        /// <summary>
        /// 案件申請查詢
        /// </summary>
        /// <param name="asspm">查詢條件</param>
        /// <returns></returns>
        [HttpPost]
        [Route("ApplicationSynthesisSearchDataExprot")]
        public  FileContentResult ApplicationSynthesisSearchDataExprot([FromBody] ApplicationSynthesisExportParaModel asspm)
        {
            ApiResultModelByObject apiResult = new ApiResultModelByObject();
            //预先存储到期日数据，在后续的查询过程中到期日数据会被修改
            var aspExpType = asspm.expType.ToList();
            var aspSignType = asspm.signType.ToList();
            var aspEffType = asspm.effType.ToList();

            var excelBytes = ApplicationSynthesisSearchService.GetApplicationDataExport(asspm, MvcContext.UserInfo.current_emp, MvcContext.UserInfo.current_roles, MvcContext.UserInfo.logging_locale);

            #region 記錄日誌
            InitLogRecord(asspm, log =>
            {
                StringBuilder stringBuilder = new StringBuilder();
                //獲取下拉框選項
                SysParametersQueryCondition sysParametersQueryCondition = new SysParametersQueryCondition()
                {
                    LangType = MvcContext.UserInfo.logging_locale,
                    //ParaCode = new string[] { "applicationState" },
                    SearchItemGroup = new SearchItemGroup()
                    {
                        Items = new List<SearchItem>() { new SearchItem() { Field = "ParaCode", Values = new string[] { "applicationState", "contractType", "targetType", "egType", "archiveStatus", "amountStatus", "currency", "taxType", "accountType", "confidentStatus", "acknowledgeType", "dateOptions", "effDateOptions" }, Compare = CompareOperator.ARRAYIN } }
                    },
                    OrderBys = new List<Orm.Dtos.OrderByParam>()
                    {
                        new Orm.Dtos.OrderByParam(){Field="SortOrder",Order=Orm.Dtos.OrderBy.ASC}
                    }
                };
                var listData = SysParametersDataService.Query(sysParametersQueryCondition);
                List<DropDownListModel> flowStepListData = new List<DropDownListModel>();
                foreach (var item in ApplicationSearchService.GetFlowStepByParaCode(MvcContext.UserInfo.logging_locale))
                {
                    var keys = item.paraKey.Split(',');
                    foreach (var key in keys)
                    {
                        DropDownListModel dropDownListModel = new DropDownListModel()
                        {
                            paraKey = key,
                            paraValue = item.paraValue
                        };
                        flowStepListData.Add(dropDownListModel);
                    }
                }
                //签约日、生效日、到期日类型拆分
                var signType1 = listData.Where(x => x.ParaCode == "dateOptions" && (new List<string>() { "02", "05" }).Contains(x.FuncCode)).Select(x => new { x.FuncCode, x.FunName }).ToList();//签约日有日期
                var signType2 = listData.Where(x => x.ParaCode == "dateOptions" && !(new List<string>() { "02", "04", "05" }).Contains(x.FuncCode)).Select(x => new { x.FuncCode, x.FunName }).ToList();//签约日无日期
                var effType1 = listData.Where(x => x.ParaCode == "effDateOptions" && (new List<string>() { "02", "04" }).Contains(x.FuncCode)).Select(x => new { x.FuncCode, x.FunName }).ToList();//生效日有日期
                var effType2 = listData.Where(x => x.ParaCode == "effDateOptions" && !(new List<string>() { "02", "04" }).Contains(x.FuncCode)).Select(x => new { x.FuncCode, x.FunName }).ToList();//生效日无日期
                var expType1 = listData.Where(x => x.ParaCode == "dateOptions" && (new List<string>() { "02", "05" }).Contains(x.FuncCode)).Select(x => new { x.FuncCode, x.FunName }).ToList();//到期日有日期
                var expType2 = listData.Where(x => x.ParaCode == "dateOptions" && !(new List<string>() { "02", "05" }).Contains(x.FuncCode)).Select(x => new { x.FuncCode, x.FunName }).ToList();//到期日无日期
                stringBuilder.GetLogString(asspm.caseStatus, "ApplicationSearch_caseStatus")
                             .GetLogString("ApplicationSearch_applyType", SysParametersService.GetApplicationType(MvcContext.UserInfo.logging_locale).Where(x => asspm.applyType.Contains(x.paraKey)).Select(x => x.paraValue).ToList())
                             .GetLogString("ApplicationSearch_applicationState", listData.Where(x => x.ParaCode == "applicationState" && asspm.applicationState.Contains(x.FuncCode)).Select(x => x.FunName).ToList())
                             .GetLogString(asspm.applyNumber, "ApplicationSearch_applyNumber")
                             .GetLogString(asspm.applyTimeStart.ConvertDateByTimeZoneByUtc(MvcContext.UserInfo.time_zone)?.ToString("yyyy/MM/dd") ?? string.Empty, "ApplicationSearch_applyTimeStart")
                             .GetLogString(asspm.applyTimeEnd.ConvertDateByTimeZoneByUtc(MvcContext.UserInfo.time_zone)?.ToString("yyyy/MM/dd") ?? string.Empty, "ApplicationSearch_applyTimeEnd")
                             .GetLogString("ApplicationSearch_entityID", GetLogCommon.GetEntity(asspm.entityID))
                             .GetLogString(asspm.otherParty, "ApplicationSearch_otherParty")
                             .GetLogString(asspm.contractName, "ApplicationSearch_contractName")
                             .GetLogString(GetLogCommon.GetUserName(asspm.empData), "ApplicationSearch_empData")
                             .GetLogString(asspm.deptID, "ApplicationSearch_deptID")
                             .GetLogString("ApplicationSearch_contractType", listData.Where(x => x.ParaCode == "contractType" && asspm.contractType.Contains(x.FuncCode)).Select(x => x.FunName).ToList())
                             .GetLogString("ApplicationSearch_contractObj", listData.Where(x => x.ParaCode == "targetType" && asspm.contractObj.Contains(x.FuncCode)).Select(x => x.FunName).ToList())
                             .GetLogString("ApplicationSearch_signType1", signType1.Where(x => aspSignType.Contains(x.FuncCode)).Select(x => x.FunName).ToList())
                             .GetLogString(asspm.signDateStart, "ApplicationSearch_signDateStart")
                             .GetLogString(asspm.signDateEnd, "ApplicationSearch_signDateEnd")
                             .GetLogString("ApplicationSearch_signType2", signType2.Where(x => aspSignType.Contains(x.FuncCode)).Select(x => x.FunName).ToList())
                             .GetLogString(asspm.confirmSignDateStart, "ApplicationSearch_comfirmSignDateStart")
                             .GetLogString(asspm.confirmSignDateEnd, "ApplicationSearch_comfirmSignDateEnd")
                             .GetLogString("ApplicationSearch_effType1", effType1.Where(x => aspEffType.Contains(x.FuncCode)).Select(x => x.FunName).ToList())
                             .GetLogString(asspm.effDateStart, "ApplicationSearch_effDateStart")
                             .GetLogString(asspm.effDateEnd, "ApplicationSearch_effDateEnd")
                             .GetLogString("ApplicationSearch_effType2", effType2.Where(x => aspEffType.Contains(x.FuncCode)).Select(x => x.FunName).ToList())
                             .GetLogString(asspm.confirmEffDateStart, "ApplicationSearch_confirmEffDataStart")
                             .GetLogString(asspm.confirmEffDateEnd, "ApplicationSearch_confirmEffDataEnd");
                // 有到期日分為延展和不延展
                if (aspExpType.Contains("02"))
                {
                    List<string> strings = new List<string>();
                    //對2做特殊處理
                    //判斷是否有延展
                    if (asspm.hasExpExtend == 1) strings.Add("有延展");
                    if (asspm.hasExpExtend == 0) strings.Add("無延展");
                    if (asspm.hasExpExtend == -1) { strings.Add("有延展"); strings.Add("無延展"); }
                    strings.AddRange(expType1.Where(x => aspExpType.Contains(x.FuncCode) && x.FuncCode != "02").Select(x => x.FunName).ToList());
                    stringBuilder.GetLogString("ApplicationSearch_expType", strings)
                    .GetLogString(asspm.expDateStart, "ApplicationSearch_expDateStart")
                    .GetLogString(asspm.expDateEnd, "ApplicationSearch_expDateEnd")
                    .GetLogString("ApplicationSearch_expType2", expType2.Where(x => aspExpType.Contains(x.FuncCode)).Select(x => x.FunName).ToList());
                }
                else
                {
                    stringBuilder.GetLogString("ApplicationSearch_expType1", expType1.Where(x => aspExpType.Contains(x.FuncCode)).Select(x => x.FunName).ToList())
                    .GetLogString(asspm.expDateStart, "ApplicationSearch_expDateStart")
                    .GetLogString(asspm.expDateEnd, "ApplicationSearch_expDateEnd")
                    .GetLogString("ApplicationSearch_expType2", expType2.Where(x => aspExpType.Contains(x.FuncCode)).Select(x => x.FunName).ToList());
                }
                stringBuilder.GetLogString(asspm.expExtDateStart, "ApplicationSearch_expExtDateStart")
                .GetLogString(asspm.expExtDateEnd, "ApplicationSearch_expExtDateEnd");
                //處理確認到期日
                if (!string.IsNullOrEmpty(asspm.confirmExpDateEnd) || !string.IsNullOrEmpty(asspm.confirmExpDateStart))
                {
                    List<string> strings = new List<string>();
                    if (asspm.confirmHasExpExtend == -1) { strings.Add("有延展"); strings.Add("無延展"); }
                    if (asspm.confirmHasExpExtend == 0) strings.Add("無延展");
                    if (asspm.confirmHasExpExtend == 1) strings.Add("有延展");
                    stringBuilder.GetLogString("ApplicationSearch_confirmHasExpExtend", strings)
                             .GetLogString(asspm.confirmExpDateStart, "ApplicationSearch_confirmExpDateStart")
                             .GetLogString(asspm.confirmExpDateEnd, "ApplicationSearch_confirmExpDateEnd");
                }

                stringBuilder.GetLogString(asspm.originArchiveDateStart.ConvertDateByTimeZoneByUtc(MvcContext.UserInfo.time_zone)?.ToString("yyyy/MM/dd") ?? string.Empty, "ApplicationSearch_originArchiveDateStart")
                             .GetLogString(asspm.originArchiveDateEnd.ConvertDateByTimeZoneByUtc(MvcContext.UserInfo.time_zone)?.ToString("yyyy/MM/dd") ?? string.Empty, "ApplicationSearch_originArchiveDateEnd")
                             .GetLogString(asspm.approvedDateStart.ConvertDateByTimeZoneByUtc(MvcContext.UserInfo.time_zone)?.ToString("yyyy/MM/dd") ?? string.Empty, "ApplicationSearch_approvedDateStart")
                             .GetLogString(asspm.approvedDateEnd.ConvertDateByTimeZoneByUtc(MvcContext.UserInfo.time_zone)?.ToString("yyyy/MM/dd") ?? string.Empty, "ApplicationSearch_approvedDateEnd")
                             .GetLogString(asspm.closedDateStart.ConvertDateByTimeZoneByUtc(MvcContext.UserInfo.time_zone)?.ToString("yyyy/MM/dd") ?? string.Empty, "ApplicationSearch_closedDateStart")
                             .GetLogString(asspm.closedDateEnd.ConvertDateByTimeZoneByUtc(MvcContext.UserInfo.time_zone)?.ToString("yyyy/MM/dd") ?? string.Empty, "ApplicationSearch_closedDateEnd")
                             .GetLogString(asspm.contractNumber, "ApplicationSearch_contractNumber")
                             .GetLogString(asspm.mainContractNumber, "ApplicationSearch_mainContractNumber")
                             .GetLogString(asspm.localContractNumber, "ApplicationSearch_localContractNumber")
                             .GetLogString(asspm.othePrartyNumber, "ApplicationSearch_othePrartyNumber")
                             .GetLogString(asspm.earlyCeaseDateStrat, "ApplicationSearch_earlyCeaseDateStrat")
                             .GetLogString(asspm.earlyCeaseDateEnd, "ApplicationSearch_earlyCeaseDateEnd")
                             .GetLogString("ApplicationSearch_endorsement", listData.Where(x => x.ParaCode == "egType" && asspm.endorsement.Contains(x.FuncCode)).Select(x => x.FunName).ToList())
                             .GetLogString("ApplicationSearch_originArchiveType", listData.Where(x => x.ParaCode == "archiveStatus" && asspm.originArchiveType.Contains(x.FuncCode)).Select(x => x.FunName).ToList())
                             .GetLogString("ApplicationSearch_isHavingMoney", listData.Where(x => x.ParaCode == "amountStatus" && asspm.isHavingMoney.Contains(x.FuncCode)).Select(x => x.FunName).ToList())
                             .GetLogString("ApplicationSearch_currency", listData.Where(x => x.ParaCode == "currency" && asspm.currency.Contains(x.FuncCode)).Select(x => x.FunName).ToList())
                             .GetLogString("ApplicationSearch_taxesType", listData.Where(x => x.ParaCode == "taxType" && asspm.taxesType.Contains(x.FuncCode)).Select(x => x.FunName).ToList())
                             .GetLogString("ApplicationSearch_accountType", listData.Where(x => x.ParaCode == "accountType" && asspm.accountType.Contains(x.FuncCode)).Select(x => x.FunName).ToList())
                             .GetLogString(asspm.accountDeptid, "ApplicationSearch_accountDeptid")
                             .GetLogString(asspm.codeName, "ApplicationSearch_codeName")
                             .GetLogString("ApplicationSearch_confidenLevel", listData.Where(x => x.ParaCode == "confidentStatus" && asspm.confidenLevel.Contains(x.FuncCode)).Select(x => x.FunName).ToList())
                             .GetLogString("ApplicationSearch_acknowledgeType", listData.Where(x => x.ParaCode == "acknowledgeType" && asspm.acknowledgeType.Contains(x.FuncCode)).Select(x => x.FunName).ToList())
                             .GetLogString("ApplicationSearch_currentLevels", flowStepListData.Where(x => asspm.currentLevels.Contains(Convert.ToInt32(x.paraKey))).Select(x => x.paraValue).Distinct().ToList())
                             .GetLogString(GetLogCommon.GetUserName(asspm.currentSigner), "ApplicationSearch_currentSigner")
                             .GetLogString(GetLogCommon.GetUserName(asspm.signerData), "ApplicationSearch_signerData")
                             .GetLogString(asspm.signerDept, "ApplicationSearch_signerDept")
                             ;
                log.Detail = stringBuilder.ToString();
            });
            #endregion

            return File(excelBytes, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "application_inquiry" + DateTime.UtcNow.ConvertDateByTimeZoneByUtc(MvcContext.UserInfo.time_zone).ToString("yyyyMMdd") + ".xlsx");
        }
        #endregion

        #region 其他申請查詢
        /// <summary>
        /// 其他申請查詢
        /// </summary>
        /// <param name="oasspm">查詢條件</param>
        /// <returns></returns>
        [HttpPost]
        [Route("OtherApplicationSynthesisSearchData")]
        public async Task<ApiResultModelByObject> OtherApplicationSynthesisSearchData([FromBody] OtherApplicationSynthesisSearchParaModel oasspm)
        {
            ApiResultModelByObject apiResult = new ApiResultModelByObject();

            int totalCount = 0;
            apiResult.listData = ApplicationSynthesisSearchService.GetOtherApplicationData(oasspm, MvcContext.UserInfo.current_emp, MvcContext.UserInfo.current_roles, out totalCount, MvcContext.UserInfo.logging_locale);
            apiResult.totalCount = totalCount;
            apiResult.rtnSuccess = true;
            #region 記錄日誌
            InitLogRecord(oasspm, log =>
            {
                //獲取下拉框選項
                SysParametersQueryCondition sysParametersQueryCondition = new SysParametersQueryCondition()
                {
                    LangType = MvcContext.UserInfo.logging_locale,
                    //ParaCode = new string[] { "applicationState" },
                    SearchItemGroup = new SearchItemGroup()
                    {
                        Items = new List<SearchItem>() { new SearchItem() { Field = "ParaCode", Values = new string[] { "applicationState", "formType_O", "otherDocType", "oSearchOtherFields" }, Compare = CompareOperator.ARRAYIN } }
                    },
                    OrderBys = new List<Orm.Dtos.OrderByParam>()
                    {
                        new Orm.Dtos.OrderByParam(){Field="SortOrder",Order=Orm.Dtos.OrderBy.ASC}
                    }
                };
                var listData = SysParametersDataService.Query(sysParametersQueryCondition);
                List<DropDownListModel> flowStepListData = new List<DropDownListModel>();
                foreach (var item in ApplicationSearchService.GetOtherFlowStepByParaCode(MvcContext.UserInfo.logging_locale))
                {
                    var keys = item.paraKey.Split(',');
                    foreach (var key in keys)
                    {
                        DropDownListModel dropDownListModel = new DropDownListModel()
                        {
                            paraKey = key,
                            paraValue = item.paraValue
                        };
                        flowStepListData.Add(dropDownListModel);
                    }
                }
                var entityData = ApplicationSearchService.GetFnpEntityByCaseStatus(3, oasspm.menuCode, MvcContext.UserInfo.current_emp, MvcContext.UserInfo.current_roles).DistinctBy(s => s.EntityId).Select(x => new { x.EntityId, x.Entity }).Concat(ApplicationSearchService.GetFnpEntityByCaseStatus(4, oasspm.menuCode, MvcContext.UserInfo.current_emp, MvcContext.UserInfo.current_roles).DistinctBy(s => s.EntityId).Select(x => new { x.EntityId, x.Entity })).DistinctBy(x => x.EntityId);
                StringBuilder stringBuilder = new StringBuilder();              
                stringBuilder.GetLogString(oasspm.applyNumber, "OtherApplicationSearch_applyNumber")
                             .GetLogString(oasspm.applyTimeStart.ConvertDateByTimeZoneByUtc(MvcContext.UserInfo.time_zone)?.ToString("yyyy/MM/dd") ?? string.Empty, "OtherApplicationSearch_applyTimeStart")
                             .GetLogString(oasspm.applyTimeEnd.ConvertDateByTimeZoneByUtc(MvcContext.UserInfo.time_zone)?.ToString("yyyy/MM/dd") ?? string.Empty, "OtherApplicationSearch_applyTimeEnd")
                             .GetLogString("OtherApplicationSearch_applicationState", listData.Where(x => x.ParaCode == "applicationState" && oasspm.applicationState.Contains(x.FuncCode)).Select(x => x.FunName).ToList())
                             .GetLogString("OtherApplicationSearch_formType", listData.Where(x => x.ParaCode == "formType_O" && oasspm.formType.Contains(x.FuncCode)).Select(x => x.FunName).ToList())
                             .GetLogString(GetLogCommon.GetUserName(oasspm.empData), "OtherApplicationSearch_empData")
                             .GetLogString(oasspm.deptID, "OtherApplicationSearch_deptID")
                             .GetLogString(oasspm.legalAdmin, "OtherApplicationSearch_legalAdmin")
                             .GetLogString(oasspm.exposeEmplid, "OtherApplicationSearch_exposeEmplid")
                             .GetLogString(oasspm.exposeOtherEmplid, "OtherApplicationSearch_exposeOtherEmplid")
                             .GetLogString(oasspm.isValidityPeriod == 1 ? oasspm.isValidityPeriod.ToString() : "", "OtherApplicationSearch_isValidityPeriod")
                             .GetLogString(oasspm.actualDateStartBegin.ConvertDateByTimeZoneByUtc(MvcContext.UserInfo.time_zone)?.ToString("yyyy/MM/dd") ?? string.Empty, "OtherApplicationSearch_actualDateStartBegin")
                             .GetLogString(oasspm.actualDateStartDone.ConvertDateByTimeZoneByUtc(MvcContext.UserInfo.time_zone)?.ToString("yyyy/MM/dd") ?? string.Empty, "OtherApplicationSearch_actualDateStartDone")
                             .GetLogString(oasspm.actualDateEndBegin.ConvertDateByTimeZoneByUtc(MvcContext.UserInfo.time_zone)?.ToString("yyyy/MM/dd") ?? string.Empty, "OtherApplicationSearch_actualDateEndBegin")
                             .GetLogString(oasspm.actualDateEndDone.ConvertDateByTimeZoneByUtc(MvcContext.UserInfo.time_zone)?.ToString("yyyy/MM/dd") ?? string.Empty, "OtherApplicationSearch_actualDateEndDone")
                             .GetLogString(oasspm.contractNumber, "OtherApplicationSearch_contractNumber")
                             .GetLogString("OtherApplicationSearch_groupEntity", entityData.Where(x => oasspm.entityID.Contains(x.EntityId)).Select(x => x.Entity).ToList())
                             .GetLogString(oasspm.retrieveApplyNumber, "OtherApplicationSearch_retrieveApplyNumber")
                             .GetLogString("OtherApplicationSearch_viewEntityID", entityData.Where(x => oasspm.viewEntityID.Contains(x.EntityId)).Select(x => x.Entity).ToList())
                             .GetLogString(oasspm.otherParty, "OtherApplicationSearch_otherParty")
                             .GetLogString("OtherApplicationSearch_docType", listData.Where(x => x.ParaCode == "otherDocType" && oasspm.docType.Contains(x.FuncCode)).Select(x => x.FunName).ToList())
                             .GetLogString(oasspm.closedDateStart.ConvertDateByTimeZoneByUtc(MvcContext.UserInfo.time_zone)?.ToString("yyyy/MM/dd") ?? string.Empty, "OtherApplicationSearch_closedDateStart")
                             .GetLogString(oasspm.closedDateEnd.ConvertDateByTimeZoneByUtc(MvcContext.UserInfo.time_zone)?.ToString("yyyy/MM/dd") ?? string.Empty, "OtherApplicationSearch_closedDateEnd")
                             .GetLogString("OtherApplicationSearch_currentLevels", flowStepListData.Where(x => oasspm.currentLevels.Contains(Convert.ToInt32(x.paraKey))).Select(x => x.paraValue).Distinct().ToList())
                             .GetLogString(GetLogCommon.GetUserName(oasspm.currentSigner), "OtherApplicationSearch_currentSigner")
                             .GetLogString(GetLogCommon.GetUserName(oasspm.signerData), "OtherApplicationSearch_signerData")
                             .GetLogString(oasspm.signerDept, "OtherApplicationSearch_signerDept")
                             ;
                log.Detail = stringBuilder.ToString();
            });
            #endregion

            return await Task.FromResult<ApiResultModelByObject>(apiResult);
        }
        #endregion
    }
}
