﻿using Elegal.Interface.Api.Common.Model.ParaModel.FlowApi.LendAppliction;
using Elegal.Interface.Api.Common.Model.ResultModel.FlowApi.LendAppliction;
using Elegal.Interface.Api.Common.Repository;
using System.Text;

namespace Elegal.Flow.Api.Repository.LendAppliction;

/// <summary>
///  資料查詢 -> 借出查詢 -> 歷史借出查詢
/// </summary>
public class LendSearchHisForAdminRepository : BaseRepository
{
    /// <summary>
    /// 實例化
    /// </summary>
    public LendSearchHisForAdminRepository() : base() { }

    #region 资料查询 历史借出
    /// <summary>
    /// 资料查询 历史借出
    /// </summary>
    /// <param name="para"></param>
    /// <param name="langType"></param>
    /// <param name="actFunc"></param>
    /// <returns></returns>
    public (List<LendAdminHis>, int) QueryLendHisForAdmin(LendHisForAdminPara para, string langType, Action<LendAdminHis> actFunc)
    {
        int totalCount = 0;
        Dictionary<string, object> sqlPara = new Dictionary<string, object>();

        #region 拼接查詢條件
        StringBuilder searchSql = new StringBuilder();
        //紙本/合約編號
        if (!string.IsNullOrWhiteSpace(para.ContractNumber))
        {
            searchSql.AppendLine(@" AND (paper_code LIKE CONCAT(N'%', @ContractNumber, N'%')
                                            OR fa.contract_number LIKE CONCAT(N'%', @ContractNumber, N'%')
                                           )"
                                 );
            sqlPara.Add("ContractNumber", para.ContractNumber);
        }
        //紙本/合約名稱
        if (!string.IsNullOrWhiteSpace(para.ContractName))
        {
            searchSql.AppendLine(@" AND (paper_name LIKE CONCAT(N'%', @ContractName, N'%')
                                            OR fa.[contract_name] LIKE CONCAT(N'%', @ContractName, N'%')
                                            )");
            sqlPara.Add("ContractName", para.ContractName);
        }

        //填单人
        if (para.LendFillEmplid.Count > 0)
        {
            int i = 0;
            string sqlEmplidsPara = string.Join(",", para.LendFillEmplid.Select(e =>
            {
                i++;
                sqlPara.Add("LendFillEmplid_" + i.ToString(), e.Emplid);
                return "@LendFillEmplid_" + i.ToString();
            })
                );
            searchSql.AppendLine(@$" AND lend_fill_emplid in ({sqlEmplidsPara})");
        }

        //經辦人
        if (para.LendHanderEmplid.Count > 0)
        {
            int i = 0;
            string sqlEmplidsPara = string.Join(",", para.LendHanderEmplid.Select(e =>
            {
                i++;
                sqlPara.Add("LendHanderEmplid_" + i.ToString(), e.Emplid);
                return "@LendHanderEmplid_" + i.ToString();
            })
                );
            searchSql.AppendLine(@$" AND  lend_handler_emplid in ({sqlEmplidsPara})");
        }

        //取件日期
        searchSql.AppendLine(@$"AND {para.PickupTime.ToSqlString("pickup_time", "pickup_time", sqlPara)}");
        //實際歸還日期  edit by SpringJiang 20241125 歷史案件取實際歸還日
        searchSql.AppendLine(@$"AND {para.ActualReturnTime.ToSqlString("actual_return_time", "actual_return_time", sqlPara)}");

        //借出/調閱單號
        if (!string.IsNullOrWhiteSpace(para.LendNumber))
        {
            searchSql.AppendLine("AND (lend_number LIKE @LendNumber OR retrieve_number LIKE @LendNumber)");
            sqlPara.Add("LendNumber", "%" + para.LendNumber + "%");
        }

        //申請單狀態
        if (para.LendStatus.Count > 0)
        {
            int i = 0;
            string sqlLendStatusPara = string.Join(" OR ", para.LendStatus.Select(e =>
            {
                i++;
                sqlPara.Add("LendStatu_" + i.ToString(), e.Key);
                return "lend_status = @LendStatu_" + i.ToString();
            })
                                                    );
            searchSql.AppendLine(@$"AND ({sqlLendStatusPara}) ");
        }


        //申請時間
        searchSql.AppendLine(@$"AND {para.ApplicationTime.ToSqlString("application_time", "application_time", sqlPara)}");
        #endregion

        #region 排序
        string orderSqlStr = string.Empty;
        if (para.Order != null && para.Order.Count > 0)
        {
            orderSqlStr = para.Order.ToString();
        }
        else
        {
            orderSqlStr = "order by LendNumber desc";
        }
        #endregion

        #region 分页
        string pagiSqlstr = string.Empty;
        if (para.Page != null)
        {
            pagiSqlstr = para.Page.ToSqlServerSqlString();
        }
        #endregion

        #region 拼接SQL語句語句
        string countSql = $@"SELECT
                            count(1)
                            FROM (
	                            SELECT DISTINCT
	                            lend_number,--借出單號
	                            retrieve_number,--調閱單號
	                            lend_fill_emplid,--填單人
	                            lend_handler_emplid,--經辦人
	                            application_time,--申請日期
	                            (SELECT COUNT(history_id) FROM paper_lending_history AS cplh WHERE cplh.lend_number = lend_number) AS lendCount,--共多少紙本
	                            lend_status,--申請單狀態
	                            (SELECT fun_name FROM sys_parameters WHERE para_code = N'lib_lendStatus' AND lang_type = N'{langType}' AND func_code = lend_status) AS lendStatusName--申請單狀態中文名稱
	                            FROM paper_lending_history AS plh
                                LEFT JOIN (SELECT basic_id,(SELECT apply_number FROM paper_application_data WHERE application_id = paper_applica_id) AS apply_number FROM paper_basic_data) AS pbd ON pbd.basic_id = plh.paper_basic_id
                                LEFT JOIN (SELECT apply_number,contract_number,contract_name FROM v_getunconfirmedapplication) AS fa ON fa.apply_number = pbd.apply_number
                                WHERE 1 = 1
                                    {searchSql}
                            ) AS lh";

        string querySql = $@"
                SELECT
                        lend_number as LendNumber,--借出單號
                        retrieve_number as RetrieveNumber,--調閱單號
                        lend_fill_emplid as LendFillEmplid,--填單人
                        fee.name AS LendFillCName,--填單人中文名
                        fee.name_a AS LendFillEName,--填單人英文名
                        lend_handler_emplid as LendHandlerEmplid,--經辦人
                        hee.name AS LendHandlerCName,--填單人中文名
                        hee.name_a AS LendHandlerEName,--填單人英文名
                        application_time as ApplicationTime,--申請日期
                        lend_status as LendStatus,--申請單狀態
                        lendStatusName as LendStatusName,--申請單狀態中文名稱
                        (SELECT COUNT(history_id) FROM paper_lending_history as cplh  WHERE cplh.lend_number = lh.lend_number AND is_return = 1) AS ReturnCount,--已還多少紙本
                        (SELECT COUNT(history_id) FROM paper_lending_history AS cplh WHERE cplh.lend_number = lh.lend_number and is_pickup_lend=1) AS LendCount,--共多少紙本
	                    (case 
								                    when exists(select 1 from paper_lending_history cplh where cplh.lend_number = lh.lend_number and cplh.overdue_day>0 and cplh.is_return=1)
								                    then 1
								                    else 0
								                    end) as IsOverdueAndReturn --是否逾期已归还
                        FROM (
	                        SELECT DISTINCT
	                        lend_number,--借出單號
	                        retrieve_number,--調閱單號
	                        lend_fill_emplid,--填單人
	                        lend_handler_emplid,--經辦人
	                        application_time,--申請日期
	                        lend_status,--申請單狀態
	                        (SELECT fun_name FROM sys_parameters WHERE para_code = N'lib_lendStatus' AND lang_type = N'{langType}' AND func_code = lend_status) AS lendStatusName--申請單狀態中文名稱
	                        FROM paper_lending_history AS plh
	                        LEFT JOIN (SELECT basic_id,(SELECT apply_number FROM paper_application_data WHERE application_id = paper_applica_id) AS apply_number FROM paper_basic_data) AS pbd ON pbd.basic_id = plh.paper_basic_id
	                        LEFT JOIN (SELECT apply_number,contract_number,contract_name FROM v_getunconfirmedapplication) AS fa ON fa.apply_number = pbd.apply_number
	                        WHERE 1 = 1
                                {searchSql}
                        ) AS lh
                        LEFT JOIN (SELECT emplid,name,name_a FROM ps_sub_ee_lgl_vw_a) AS fee ON fee.emplid = lh.lend_fill_emplid
                        LEFT JOIN (SELECT emplid,name,name_a FROM ps_sub_ee_lgl_vw_a) AS hee ON hee.emplid = lh.lend_handler_emplid
                        {orderSqlStr}
                        {pagiSqlstr}
                            ";
        #endregion

        totalCount = NpgsqlSearchBySingle<int>(countSql, sqlPara);
        List<LendAdminHis> data = NpgsqlSearchByList(querySql, sqlPara, actFunc);
        return (data, totalCount);
    }
    #endregion

    #region 资料查询，历史借出，导出
    /// <summary>
    /// 资料查询，历史借出，导出
    /// </summary>
    /// <param name="para"></param>
    /// <param name="langType"></param>
    /// <returns></returns>
    public List<LendAdminHisExport> QueryLendHisForAdminExprt(LendHisForAdminExportPara para, string langType)
    {
        Dictionary<string, object> sqlPara = new Dictionary<string, object>();

        #region 拼接查詢條件
        StringBuilder searchSql = new StringBuilder();
        //紙本/合約編號
        if (!string.IsNullOrWhiteSpace(para.ContractNumber))
        {
            searchSql.AppendLine(@" AND (plh.paper_code LIKE CONCAT(N'%', @ContractNumber, N'%')
                                                OR fa.contract_number LIKE CONCAT(N'%', @ContractNumber, N'%')
                                               )"
                                 );
            sqlPara.Add("ContractNumber", para.ContractNumber);
        }
        //紙本/合約名稱
        if (!string.IsNullOrWhiteSpace(para.ContractName))
        {
            searchSql.AppendLine(@" AND (plh.paper_name LIKE CONCAT(N'%', @ContractName, N'%')
                                                OR fa.contract_name LIKE CONCAT(N'%', @ContractName, N'%')
                                                )");
            sqlPara.Add("ContractName", para.ContractName);
        }

        //填单人
        if (para.LendFillEmplid.Count > 0)
        {
            int i = 0;
            string sqlEmplidsPara = string.Join(",", para.LendFillEmplid.Select(e =>
            {
                i++;
                sqlPara.Add("LendFillEmplid_" + i.ToString(), e.Emplid);
                return "@LendFillEmplid_" + i.ToString();
            })
                );
            searchSql.AppendLine(@$" AND plh.lend_fill_emplid in ({sqlEmplidsPara})");
        }

        //經辦人
        if (para.LendHanderEmplid.Count > 0)
        {
            int i = 0;
            string sqlEmplidsPara = string.Join(",", para.LendHanderEmplid.Select(e =>
            {
                i++;
                sqlPara.Add("LendHanderEmplid_" + i.ToString(), e.Emplid);
                return "@LendHanderEmplid_" + i.ToString();
            })
                );
            searchSql.AppendLine(@$" AND  plh.lend_handler_emplid in ({sqlEmplidsPara})");
        }

        //取件日期
        searchSql.AppendLine(@$"AND {para.PickupTime.ToSqlString("plh.pickup_time", "pickup_time", sqlPara)}");
        //實際歸還日期  edit by SpringJiang 20241114 修改為使用取唯一欄位方法，優先實際歸還日期
        searchSql.AppendLine(@$"AND (
                                          {para.ActualReturnTime.ToSqlByCoalesce(new List<string> { "actual_return_time", "should_return_time" }, "actual_return_time", sqlPara)}
                                          )
                                ");

        //借出/調閱單號
        if (!string.IsNullOrWhiteSpace(para.LendNumber))
        {
            searchSql.AppendLine("AND (plh.lend_number LIKE @LendNumber OR plh.retrieve_number LIKE @LendNumber)");
            sqlPara.Add("LendNumber", "%" + para.LendNumber + "%");
        }

        //申請單狀態
        if (para.LendStatus.Count > 0)
        {
            int i = 0;
            string sqlLendStatusPara = string.Join(" OR ", para.LendStatus.Select(e =>
            {
                i++;
                sqlPara.Add("LendStatu_" + i.ToString(), e.Key);
                return "plh.lend_status = @LendStatu_" + i.ToString();
            })
                                                    );
            searchSql.AppendLine(@$"AND ({sqlLendStatusPara}) ");
        }


        //申請時間
        searchSql.AppendLine(@$"AND {para.ApplicationTime.ToSqlString("plh.application_time", "application_time", sqlPara)}");
        #endregion

        #region 排序
        string orderSqlStr = string.Empty;
        if (para.Order != null && para.Order.Count > 0)
        {
            orderSqlStr = para.Order.ToString();
        }
        else
        {
            orderSqlStr = "order by lend_number";
        }
        #endregion

        string querySql = $@"
                             SELECT
	                                lend_number as LendNumber,--借出單號
	                                retrieve_number as RetrieveNumber,--調閱單號
	                                lend_fill_emplid as LendFillEmplid,--填單人
	                                fee.name AS LendFillCName,--填單人中文名
	                                fee.name_a AS LendFillEName,--填單人英文名
	                                lend_handler_emplid as LendHandlerEmplid,--經辦人
	                                hee.name AS LendHandlerCName,--填單人中文名
	                                hee.name_a AS LendHandlerEName,--填單人英文名
	                                application_time as ApplicationTime,--申請日期
	                                lend_status as LendStatus,--申請單狀態
	                                lendStatusName as LendStatusName,--申請單狀態中文名稱
	                                contract_number as ContractNumber,--合約編號
	                                paper_code as PaperCode,--紙本編號
	                                paper_name as PaperName,--紙本名稱
	                                actual_return_time as ActualReturnTime,--歸還日期
	                                returnStatusName as ReturnStatusName,--歸還現狀中文名稱
                                    (SELECT COUNT(history_id) FROM paper_lending_history as cplh  WHERE cplh.lend_number = lh.lend_number AND is_return = 1) AS ReturnCount,--已還多少紙本
                                    (SELECT COUNT(history_id) FROM paper_lending_history AS cplh WHERE cplh.lend_number = lh.lend_number and is_pickup_lend=1) AS LendCount,--共多少紙本
						            IsOverdueAndReturn, --是否逾期已归还
                                    overdue_day as OverdueDay
	                                FROM (
		                                SELECT DISTINCT
		                                plh.lend_number,--借出單號
		                                plh.retrieve_number,--調閱單號
		                                plh.lend_fill_emplid,--填單人
		                                plh.lend_handler_emplid,--經辦人
		                                plh.application_time,--申請日期
		                                (SELECT COUNT(history_id) FROM paper_lending_history AS cplh WHERE cplh.lend_number = plh.lend_number) AS lendCount,--共多少紙本
		                                plh.lend_status,--申請單狀態
		                                (SELECT fun_name FROM sys_parameters WHERE para_code = N'lib_lendStatus' AND lang_type = N'{langType}' AND func_code = plh.lend_status) AS lendStatusName,--申請單狀態中文名稱
		                                fa.contract_number,--合約編號
		                                plh.paper_code,--紙本編號
		                                plh.paper_name,--紙本名稱
		                                plh.actual_return_time,--歸還日期
		                                (SELECT STRING_AGG(fun_name, ', ') FROM sys_parameters WHERE para_code = N'lib_returnStatus' AND lang_type = N'{langType}' AND func_code IN(SELECT value  FROM STRING_SPLIT(plh.lend_return_status, ','))) AS returnStatusName,--歸還現狀中文名稱
							            iif(plh.overdue_day>0 and plh.is_return=1,1,0) as IsOverdueAndReturn, --是否逾期已归还
                                        plh.overdue_day
		                                FROM paper_lending_history AS plh
                                        LEFT JOIN (SELECT basic_id,(SELECT apply_number FROM paper_application_data WHERE application_id = paper_applica_id) AS apply_number FROM paper_basic_data) AS pbd ON pbd.basic_id = plh.paper_basic_id
                                        LEFT JOIN (SELECT apply_number,contract_number,contract_name FROM v_getunconfirmedapplication) AS fa ON fa.apply_number = pbd.apply_number
		                                WHERE 1 = 1
                                            {searchSql}
	                                ) 
                                AS lh
                                LEFT JOIN (SELECT emplid,name,name_a FROM ps_sub_ee_lgl_vw_a) AS fee ON fee.emplid = lh.lend_fill_emplid
                                LEFT JOIN (SELECT emplid,name,name_a FROM ps_sub_ee_lgl_vw_a) AS hee ON hee.emplid = lh.lend_handler_emplid
                            {orderSqlStr}
                            ";

        return NpgsqlSearchByList<LendAdminHisExport>(querySql, sqlPara);
    }
    #endregion
}
