﻿using Elegal.Flow.Api.Repository.Process;
using Elegal.Interface.Api.Common.Model.ViewModel.FlowApi;

namespace Elegal.Flow.Api.Services.Process
{
    /// <summary>
    /// 區域維護
    /// </summary>
    public class SysAreaService
    {
        private static readonly SysAreaRepository _repository = new();

        #region 獲取區域列表數據
        /// <summary>
        /// 獲取區域列表數據
        /// </summary>
        public static IEnumerable<SysAreaModel> GetSysAreaList()
        {
            return _repository.GetSysAreaList();
        }
        #endregion
    }
}
