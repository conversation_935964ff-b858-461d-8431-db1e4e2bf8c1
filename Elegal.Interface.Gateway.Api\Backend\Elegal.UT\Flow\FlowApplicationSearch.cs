﻿using Elegal.Flow.Api.Controllers;
using Elegal.Interface.Api.Common.Control;
using Elegal.Interface.Api.Common.Control.Filter;
using Elegal.Interface.Api.Common.Model.ParaModel.FlowApi.ApplicationSearch;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using System.IdentityModel.Tokens.Jwt;
using Elegal.Flow.Api.Services.ApplicationSearch;
using Elegal.Interface.Api.Common.Services;

namespace Elegal.UT.Flow
{
    /// <summary>
    /// 資料查詢 -> 申請單查詢 UT 測試
    /// </summary>
    [TestFixture]
    public class FlowApplicationSearch
    {
        private IHttpContextAccessor _httpContextAccessor;
        private HttpContext _httpContext;
        private ApplicationSearchRefactorService applicationSearchRefactorService;
        private ILogService logService;
        [SetUp]
        public void SetUp()
        {
            var Configuration = new ConfigurationBuilder().SetBasePath(Environment.CurrentDirectory).AddJsonFile("utsettings.json").Build();
            string token = Configuration["token"] ?? "";
            string user = Configuration["user"] ?? "";
            _httpContext = new DefaultHttpContext { };
            _httpContext.Items.Add("user", AnalysisToken.GetUserInfo(user, new JwtSecurityTokenHandler().ReadJwtToken(token)));
            _httpContext.Request.Headers.Append("logid", "000");
            _httpContext.Request.Headers.Append("Authorization", token);
            _httpContextAccessor = new HttpContextAccessor
            {
                HttpContext = _httpContext
            };
            MvcContext.Accessor = _httpContextAccessor;
        }

        /// <summary>
        /// 根據案件狀態查詢不同的主體 UT測試
        /// </summary>
        [Test]
        public void ApplicationSearchServiceGetFnpEntityByCaseStatus()
        {
            try
            {
                var returl = new ApplicationSearchController(applicationSearchRefactorService, logService);
                returl.GetFnpEntityByCaseStatus(0,0);
            }
            finally { Assert.Pass(); }
        }

        /// <summary>
        /// 獲取關聯合約編號 UT測試
        /// </summary>
        [Test]
        public void ApplicationSearchServiceGetGroupContractNumber()
        {
            try
            {
                var returl = new ApplicationSearchController(applicationSearchRefactorService, logService);
                returl.GetGroupContractNumber();
            }
            finally { Assert.Pass(); }
        }

        /// <summary>
        /// 獲取目前關卡，所有申請單關卡並集 UT測試
        /// </summary>
        [Test]
        public void ApplicationSearchServiceGetFlowStepByParaCode()
        {
            try
            {
                var returl = new ApplicationSearchController(applicationSearchRefactorService, logService);
                returl.GetFlowStepByParaCode();
            }
            finally { Assert.Pass(); }
        }

        /// <summary>
        /// 案件查詢 UT測試
        /// </summary>
        [Test]
        public void ApplicationSearchServiceApplicationSearchData()
        {
            try
            {
                var returl = new ApplicationSearchController(applicationSearchRefactorService, logService);
                returl.ApplicationSearchData(new ApplicationSearchParaModel { pageCount = 1, pageSize = 25 });
            }
            finally { Assert.Pass(); }
        }
    }
}
