﻿using Elegal.Flow.Api.Repository;
using Elegal.Interface.Api.Common.FuncHelper;
using Elegal.Interface.Api.Common.Model.ViewModel.PermissionApi.Home;

namespace Elegal.Flow.Api.Services
{
    /// <summary>
    /// 簽核中案件查詢
    /// </summary>
    public static class SignCaseService
    {
        private static SignerCaseRepository _signerCaseRepository = new SignerCaseRepository();

        #region 我的案件查詢
        /// <summary>
        /// 我的案件查詢
        /// </summary>
        /// <param name="emplID"></param>
        /// <param name="langType"></param>
        /// <returns></returns>
        public static List<HomeCaseViewModel> GetMyCase(string emplID, string langType = "ZH-TW")
        {
            List<HomeCaseViewModel> listNewData = new List<HomeCaseViewModel>();

            //申請單數據
            foreach (HomeCaseViewModel hcvm in _signerCaseRepository.GetMyCase(emplID, langType))
            {
                //極機密案件且當前案件狀態不為暫存(T)且不為進行中(I)，需要做隱碼作業
                if (hcvm.confiden_level.ToUpper() == "01".ToUpper() && hcvm.application_state.ToUpper() != "I".ToUpper() && hcvm.application_state.ToUpper() != "T".ToUpper())
                {
                    listNewData.Add(HiddenValueConvertHelper.ConvertToHiddenBySingle<HomeCaseViewModel>(hcvm));
                }
                else { listNewData.Add(hcvm); }
            }
            //轉單數據
            listNewData.AddRange(_signerCaseRepository.GetTransferPicMyCase(emplID, langType));

            return listNewData;
        }
        #endregion

        #region 會審人員查詢
        /// <summary>
        /// 會審人員查詢
        /// </summary>
        /// <param name="emplID"></param>
        /// <param name="langType"></param>
        /// <returns></returns>
        public static List<HomeCaseViewModel> GetSignerCase(string emplID, string langType = "ZH-TW")
        {
            List<HomeCaseViewModel> listNewData = new List<HomeCaseViewModel>();

            //申請單數據
            foreach (HomeCaseViewModel hcvm in _signerCaseRepository.GetSignerCase(emplID, langType))
            {
                //極機密案件且當前案件狀態不為暫存(T)且不為進行中(I)，需要做隱碼作業
                if (hcvm.confiden_level.ToUpper() == "01".ToUpper() && hcvm.application_state.ToUpper() != "I".ToUpper() && hcvm.application_state.ToUpper() != "T".ToUpper())
                {
                    listNewData.Add(HiddenValueConvertHelper.ConvertToHiddenBySingle<HomeCaseViewModel>(hcvm));
                }
                else { listNewData.Add(hcvm); }
            }
            //轉單數據
            listNewData.AddRange(_signerCaseRepository.GetTransferPicSignerCase(emplID, langType));

            return listNewData;
        }
        #endregion
    }
}
