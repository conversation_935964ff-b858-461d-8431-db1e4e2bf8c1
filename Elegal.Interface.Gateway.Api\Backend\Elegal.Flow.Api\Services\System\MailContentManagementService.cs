﻿using Elegal.Flow.Api.Repository.System;
using Elegal.Interface.Api.Common.Control.Filter;
using Elegal.Interface.Api.Common.Model.Enum;
using Elegal.Interface.Api.Common.Model.ParaModel.FlowApi;
using Elegal.Interface.Api.Common.Model.ResultModel;
using Elegal.Interface.Api.Common.Model.ResultModel.FlowApi;
using Elegal.Interface.Api.Common.Model.ViewModel;
using Elegal.Interface.Api.Common.Model.ViewModel.FlowApi;
using Elegal.Interface.ApiData.Service.FuncService;
using Elegal.Interface.ApiData.Service.Model.Condition;
using Elegal.Interface.ApiData.Service.Model.DbModel;
using Elegal.Interface.ApiData.Service.Model.QueryCondition;
using Elegal.Interface.ApiData.Service.Model.ViewModel;
using Elegal.Orm;
using Elegal.Orm.Dtos;
using Elegal.Orm.Utils;
using Newtonsoft.Json;
using System.Text;

namespace Elegal.Flow.Api.Services.System
{
    /// <summary>
    /// 郵件模板
    /// </summary>
    public static class MailContentManagementService
    {
        private static readonly MailContentManagementRepository _repository = new();

        /// <summary>
        /// 郵件模板頁面查詢
        /// </summary>
        /// <returns></returns>
        public static List<MailSearchContentModel> QueryMailContent(MailContentModel model)
        {
            List<MailSearchContentModel> mailContentModels = _repository.QueryMailContent(model);
            ActionFilter.InitLogRecord(model, log =>
            {
                StringBuilder stringBuilder = new StringBuilder();
                if (model.rowid > 0)
                    stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("MailContentManagement_mailSubject", true)}：{mailContentModels[0].mail_subject}");
                log.Detail = stringBuilder.ToString();
            });
            return mailContentModels;
        }
        /// <summary>
        /// 郵件模板修改
        /// </summary>
        /// <param name="condition">模板参数</param>
        /// <returns></returns>
        public static bool UpdateMailContent(MailContentModel condition)
        {
            UserInfoModel user = MvcContext.UserInfo;
            //表格json去掉已被删除的部分
            MailSearchContentModel mailSearchContentModel = _repository.QueryMailContent(condition)[0];
            //過濾已被刪除的表
            var _zh = condition.tableJsons.Where(w => condition.mail_zh_content.Split("\n").Contains(w.tableId)).DistinctBy(b => b.tableId);
            var _en = condition.tableJsons.Where(w => condition.mail_en_content.Split("\n").Contains(w.tableId)).DistinctBy(b => b.tableId);
            condition.tableJsons = _zh.Concat(_en).DistinctBy(b => b.tableId).ToList();

            UserEmailContentDataService.Update(new UserEmailContent()
            {
                Rowid = condition.rowid,
                MailSubject = condition.mail_subject,
                MailReType = JsonConvert.SerializeObject(condition.recipientList),
                MailCcType = JsonConvert.SerializeObject(condition.ccList),
                MailZhContent = condition.mail_zh_content,
                MailEnContent = condition.mail_en_content,
                MailContent = condition.mail_content,
                AdvanceDays = condition.advance_days,
                IntervalDays = condition.interval_days,
                MailReamrk = condition.mail_reamrk,
                IsReAgent = condition.is_re_agent,
                ModifyUser = user.current_emp,
                ModifyTime = DateTime.UtcNow,
                HavingTable = condition.tableJsons.Count != 0 ? 1 : 0,
                TableJson = JsonConvert.SerializeObject(condition.tableJsons),
                ZhContentJson = JsonConvert.SerializeObject(condition.zhContentJsons),
                EnContentJson = JsonConvert.SerializeObject(condition.enContentJsons),
            });

            MailSearchContentModel currentModel = _repository.QueryMailContent(condition)[0];
            List<UserEmailDictionary> userEmailDictionaries = UserEmailDictionaryDataService.Query(new UserEmailDictionaryQueryCondition()
            {
                FieldType = mailSearchContentModel.func_module
            });
            ActionFilter.InitLogRecord(condition, log =>
            {
                StringBuilder stringBuilder = new StringBuilder();
                StringBuilder stringBuilderHis = new StringBuilder();
                string ccHistory = GetMailRecipients(mailSearchContentModel.mail_cc_type);
                string reHistory = GetMailRecipients(mailSearchContentModel.mail_re_type);
                string cc = GetMailRecipients(currentModel.mail_cc_type);
                string re = GetMailRecipients(currentModel.mail_re_type);
                if (!string.IsNullOrEmpty(mailSearchContentModel.mail_subject)) {
                    userEmailDictionaries.ForEach(e => {
                        mailSearchContentModel.mail_subject = mailSearchContentModel.mail_subject
                        .Replace(@"[" + e.FieldCode + "_zh]", @"[" + e.FieldCname + "]")
                        .Replace(@"{" + e.FieldCode + "_zh}", @"{" + e.FieldCname + "}");
                    });
                    stringBuilderHis.AppendLine($"{ActionFilter.GetMultilingualValue("MailContentManagement_mailSubject", true)}：{mailSearchContentModel.mail_subject}");
                }
                if (!string.IsNullOrEmpty(reHistory))
                    stringBuilderHis.AppendLine($"{ActionFilter.GetMultilingualValue("MailContentManagement_reName", true)}：{reHistory}");
                if (!string.IsNullOrEmpty(ccHistory))
                    stringBuilderHis.AppendLine($"{ActionFilter.GetMultilingualValue("MailContentManagement_ccName", true)}：{ccHistory}");

                if (!string.IsNullOrEmpty(mailSearchContentModel.mail_zh_content)) {
                    userEmailDictionaries.ForEach(e => {
                        mailSearchContentModel.mail_zh_content = mailSearchContentModel.mail_zh_content
                        .Replace(@"["+ e.FieldCode +"_zh]", @"[" + e.FieldCname + "]")
                        .Replace(@"{"+ e.FieldCode +"_zh}", @"{" + e.FieldCname + "}");
                    });
                    stringBuilderHis.AppendLine($"{ActionFilter.GetMultilingualValue("MailContentManagement_mailZHContent", true)}：{mailSearchContentModel.mail_zh_content}");
                }
                if (!string.IsNullOrEmpty(mailSearchContentModel.mail_en_content)) {
                    userEmailDictionaries.ForEach(e => {
                        mailSearchContentModel.mail_en_content = mailSearchContentModel.mail_en_content
                        .Replace(@"[" + e.FieldCode + "_zh]", @"[" + e.FieldCname + "]")
                        .Replace(@"{" + e.FieldCode + "_en}", @"{" + e.FieldEname + "}");
                    });
                    stringBuilderHis.AppendLine($"{ActionFilter.GetMultilingualValue("MailContentManagement_mailENContent", true)}：{mailSearchContentModel.mail_en_content}");
                }
                    
                if (!string.IsNullOrEmpty(mailSearchContentModel.mail_reamrk))
                    stringBuilderHis.AppendLine($"{ActionFilter.GetMultilingualValue("MailContentManagement_mailRemark", true)}：{mailSearchContentModel.mail_reamrk}");

                if (!string.IsNullOrEmpty(currentModel.mail_subject)) {
                    userEmailDictionaries.ForEach(e => {
                        currentModel.mail_subject = currentModel.mail_subject
                        .Replace(@"[" + e.FieldCode + "_zh]", @"[" + e.FieldCname + "]")
                        .Replace(@"{" + e.FieldCode + "_zh}", @"{" + e.FieldCname + "}");
                    });
                    stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("MailContentManagement_mailSubject", true)}：{currentModel.mail_subject}");
                }
                if (!string.IsNullOrEmpty(re))
                    stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("MailContentManagement_reName", true)}：{re}");
                if (!string.IsNullOrEmpty(cc))
                    stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("MailContentManagement_ccName", true)}：{cc}");

                if (!string.IsNullOrEmpty(currentModel.mail_zh_content))
                {
                    userEmailDictionaries.ForEach(e => {
                        currentModel.mail_zh_content = currentModel.mail_zh_content
                        .Replace(@"[" + e.FieldCode + "_zh]", @"[" + e.FieldCname + "]")
                        .Replace(@"{" + e.FieldCode + "_zh}", @"{" + e.FieldCname + "}");
                    });
                    stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("MailContentManagement_mailZHContent", true)}：{currentModel.mail_zh_content}");
                }
                if (!string.IsNullOrEmpty(currentModel.mail_en_content))
                {
                    userEmailDictionaries.ForEach(e => {
                        currentModel.mail_en_content = currentModel.mail_en_content
                        .Replace(@"[" + e.FieldCode + "_zh]", @"[" + e.FieldCname + "]")
                        .Replace(@"{" + e.FieldCode + "_en}", @"{" + e.FieldEname + "}");
                    });
                    stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("MailContentManagement_mailENContent", true)}：{currentModel.mail_en_content}");
                }

                if (!string.IsNullOrEmpty(currentModel.mail_reamrk))
                    stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("MailContentManagement_mailRemark", true)}：{currentModel.mail_reamrk}");
                log.Detail = stringBuilder.ToString();
                log.DetailFormer = stringBuilderHis.ToString();

            });
            return true;
        }

        /// <summary>
        /// 获取开放栏位
        /// </summary>
        /// <returns></returns>
        public static IEnumerable<UserEmailDictionary> GetOpenFieldList()
        {
            return DbAccess.GetAll<UserEmailDictionary>();
        }

        /// <summary>
        /// 獲取收件者類型名
        /// </summary>
        /// <param name="mailRecipients">模板参数</param>
        /// <returns></returns>
        private static string GetMailRecipients(string mailRecipients)
        {
            List<KeyValuePair<string, List<string>>> list = JsonConvert.DeserializeObject<List<KeyValuePair<string, List<string>>>>(mailRecipients ?? "") ?? [];
            List<string> approvePerson = new List<string>();
            List<string> role = new List<string>();
            List<string> recipients = new List<string>();
            foreach (var item in list)
            {
                //關卡人員添加
                if (item.Key.Equals("01"))
                {
                    item.Value.ForEach(x =>
                    {
                        approvePerson.Add(x);
                    });
                }
                //角色添加
                else if (item.Key.Equals("02"))
                {
                    item.Value.ForEach(x =>
                    {
                        role.Add(x);
                    });
                }
                //收件人類型Key添加
                else
                {
                    recipients.Add(item.Key);
                }
            }
            return _repository.GetMailRecipients(string.Join(",", approvePerson), string.Join(",", role), string.Join(",", recipients));
        }
    }
}
