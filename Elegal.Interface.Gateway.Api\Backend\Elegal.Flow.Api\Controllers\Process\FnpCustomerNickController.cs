﻿using Elegal.Flow.Api.Services.Process;
using Elegal.Interface.Api.Common.Control.Filter;
using Elegal.Interface.Api.Common.Control.Web;
using Elegal.Interface.Api.Common.Model.Enum;
using Elegal.Interface.Api.Common.Model.ParaModel.FlowApi;
using Elegal.Interface.Api.Common.Model.ResultModel;
using Elegal.Interface.ApiData.Service.FuncService;
using Elegal.Interface.ApiData.Service.Model.DbModel;
using Microsoft.AspNetCore.Mvc;
using Minio;

namespace Elegal.Flow.Api.Controllers.Process
{
    /// <summary>
    /// 客户昵称
    /// </summary>
    [Route("[controller]")]
    [ApiController]
    public class FnpCustomerNickController : BaseController
    {
        private readonly MinioClient _client;

        #region 實例化
        /// <summary>
        /// 實例化
        /// </summary>
        /// <param name="client"></param>
        public FnpCustomerNickController(MinioClient client)
        {
            _client = client;
        }
        #endregion

        #region 查询数据
        /// <summary>
        /// 查询数据
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("SearchData")]
        public async Task<ApiResultModelByObject> SearchData()
        {
            return await Task.FromResult(FnpCustomerNickService.SearchData());
        }
        #endregion

        #region 根據fileid獲取excel中特定欄位的值
        /// <summary>
        /// 根據fileid獲取excel中特定欄位的值
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("GetNickNameByFileID")]
        public async Task<ApiResultModelByObject> GetNickNameByFileID(int fileID)
        {
            ApiResultModelByObject apiResult = new ApiResultModelByObject();

            List<string> errorMessage = new List<string>();
            //根據fileid查詢已上傳的文件
            SysUploadFile suf = SysUploadFileDataService.FindByKey(fileID);
            if (suf != null)
            {
                Dictionary<string, List<string>> dic = await FnpCustomerNickService.GetNickNameBySpecialCode(suf, "Code Name");
                errorMessage = dic["error"];
                if (!errorMessage.Any())
                {
                    apiResult.listData = dic["success"];
                    apiResult.rtnSuccess = true;
                }
            }
            else
            {
                errorMessage.Add(ActionFilter.GetMultilingualValue("custom:messageContent:nikeNameFileError"));
            }

            //表示操作失敗，需要進行消息提示
            if (!apiResult.rtnSuccess)
            {
                apiResult.messageType = MessageTypeUtils.Warning.ToString();
                apiResult.messageTitle = ActionFilter.GetMultilingualValue("commonWord:information");
                apiResult.messageContent = errorMessage;
            }

            return await Task.FromResult(apiResult);
        }
        #endregion

        #region 根據list集合新增數據
        /// <summary>
        /// 根據list集合新增數據
        /// </summary>
        /// <param name="nnpm"></param>
        /// <returns></returns>
        [HttpPut]
        [Route("InsertNikeName")]
        public async Task<ApiResultModel> InsertNikeName([FromBody] NikeNameParaModel nnpm)
        {
            ApiResultModel apiResult = new ApiResultModel();

            if (nnpm.fileID > 0)
            {
                apiResult.rtnSuccess = FnpCustomerNickService.InsertNikeName(nnpm.nikeName, MvcContext.UserInfo.current_emp);
            }
            else
            {
                apiResult.messageType = MessageTypeUtils.Warning.ToString();
                apiResult.messageTitle = ActionFilter.GetMultilingualValue("commonWord:information");
                apiResult.messageContent = new List<string> { ActionFilter.GetMultilingualValue("custom:messageContent:nikeMissPara") };
            }

            if (apiResult.rtnSuccess)
            {
                #region 記錄日誌
                InitLogRecord(nnpm, log =>
                {
                    if (string.IsNullOrEmpty(nnpm.fileName))
                    {
                        //根據fileid查詢已上傳的文件
                        SysUploadFile suf = SysUploadFileDataService.FindByKey(nnpm.fileID);
                        nnpm.fileName = suf.FileName;
                    }
                    log.Detail = string.Format(ActionFilter.GetMultilingualValue("FnpCustomerNick_nickFileName", true), nnpm.fileName);
                });
                #endregion
            }

            return await Task.FromResult(apiResult);
        }
        #endregion

        #region 下载范本
        /// <summary>
        /// 下载范本
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("DownloadTemplate")]
        public async Task<ApiResultModelByObject> DownloadTemplate()
        {
            ApiResultModelByObject apiResult = new ApiResultModelByObject();

            apiResult = await FnpCustomerNickService.DownloadTemplate(_client);
            if (apiResult.rtnSuccess)
                InitLogRecord<object>(new());

            return await Task.FromResult(apiResult);
        }
        #endregion
    }
}
