﻿using Elegal.Interface.Api.Common.Model.ParaModel.FlowApi.LendAppliction;
using Elegal.Interface.Api.Common.Model.ResultModel.FlowApi.LendAppliction;
using Elegal.Interface.Api.Common.Repository;
using System.Text;

namespace Elegal.Flow.Api.Repository.LendAppliction;

/// <summary>
/// 資料查詢 -> 借出查詢 -> 出借中查詢
/// </summary>
public class LendSearchForAdminRepository : BaseRepository
{
    /// <summary>
    /// 實例化
    /// </summary>
    public LendSearchForAdminRepository() : base() { }

    #region 资料查询，借出中查询
    /// <summary>
    /// 资料查询，借出中查询
    /// </summary>
    /// <param name="para"></param>
    /// <param name="langType"></param>
    /// <param name="actFunc"></param>
    /// <returns></returns>
    public (List<LendAdminModel>, int) QueryLendForAdmin(LendForAdminPara para, string langType = "ZH-TW", Action<LendAdminModel> actFunc = null)
    {
        int totalCount = 0;
        Dictionary<string, object> sqlPara = new Dictionary<string, object>();

        #region 拼接查詢條件
        StringBuilder searchSql = new StringBuilder();
        //紙本/合約編號
        if (!string.IsNullOrWhiteSpace(para.ContractNumber))
        {
            searchSql.AppendLine(@" AND (pldd.paper_code LIKE CONCAT(N'%', @ContractNumber, N'%')
                                            OR fa.contract_number LIKE CONCAT(N'%', @ContractNumber, N'%')
                                           )"
                                 );
            sqlPara.Add("ContractNumber", para.ContractNumber);
        }
        //紙本/合約名稱
        if (!string.IsNullOrWhiteSpace(para.ContractName))
        {
            searchSql.AppendLine(@" AND (pldd.paper_name LIKE CONCAT(N'%', @ContractName, N'%')
                                            OR fa.contract_name LIKE CONCAT(N'%', @ContractName, N'%')
                                            )");
            sqlPara.Add("ContractName", para.ContractName);
        }

        //填单人
        if (para.LendFillEmplid.Count > 0)
        {
            int i = 0;
            string sqlEmplidsPara = string.Join(",", para.LendFillEmplid.Select(e =>
            {
                i++;
                sqlPara.Add("LendFillEmplid_" + i.ToString(), e.Emplid);
                return "@LendFillEmplid_" + i.ToString();
            })
                );
            searchSql.AppendLine(@$" AND pla.lend_fill_emplid in ({sqlEmplidsPara})");
        }

        //經辦人
        if (para.LendHanderEmplid.Count > 0)
        {
            int i = 0;
            string sqlEmplidsPara = string.Join(",", para.LendHanderEmplid.Select(e =>
            {
                i++;
                sqlPara.Add("LendHanderEmplid_" + i.ToString(), e.Emplid);
                return "@LendHanderEmplid_" + i.ToString();
            })
                );
            searchSql.AppendLine(@$" AND  pla.lend_handler_emplid in ({sqlEmplidsPara})");
        }

        //應歸還時間
        searchSql.AppendLine(@$"AND {para.ShouldReturnTime.ToSqlString("pldd.should_return_time", "should_return_time", sqlPara)}");

        //借出/調閱單號
        if (!string.IsNullOrWhiteSpace(para.LendNumber))
        {
            searchSql.AppendLine("AND (pla.lend_number LIKE @LendNumber OR pld.retrieve_number LIKE @LendNumber)");
            sqlPara.Add("LendNumber", "%" + para.LendNumber + "%");
        }

        //申請單狀態
        if (para.LendStatus.Count > 0)
        {
            int i = 0;
            string sqlLendStatusPara = string.Join(" OR ", para.LendStatus.Select(e =>
            {
                i++;
                sqlPara.Add("LendStatu_" + i.ToString(), e.Key);
                return "pla.lend_status = @LendStatu_" + i.ToString();
            })
                                                    );
            searchSql.AppendLine(@$"AND ({sqlLendStatusPara}) ");
        }


        //取件日期
        searchSql.AppendLine(@$"AND {para.PickupTime.ToSqlString("pldd.pickup_time", "pickup_time", sqlPara)}");

        #endregion

        #region 排序
        string orderSqlStr = string.Empty;
        if (para.Order != null && para.Order.Count > 0)
        {
            orderSqlStr = para.Order.ToString();
        }
        else
        {
            orderSqlStr = "order by LendNumber ";
        }
        #endregion

        #region 分页
        string pagiSqlstr = string.Empty;
        if (para.Page != null)
        {
            pagiSqlstr = para.Page.ToSqlServerSqlString();
        }
        #endregion

        #region 拼接SQL語句
        string countsql = @$"
                            SELECT count(1)
                                        FROM (
                                            SELECT DISTINCT
                                            pla.lend_id as LendId,--主表id為子表查詢做要求
                                            pla.lend_number as LendNumber,--借出單號
                                            pld.retrieve_number as RetrieveNumber,--調閱單號
                                            pla.lend_fill_emplid,--填單人
                                            pla.lend_handler_emplid,--經辦人
                                            pla.application_time  as CreateTime,--申請日期(資料表中是utc時間，需要轉換為操作者的時區時間格式YYYY/MM/DD)
                                            (SELECT COUNT(detail_id) FROM paper_lending_detail WHERE paper_lend_id = pla.lend_id and is_pickup_lend=1) AS LendCount,--共多少紙本
                                            (SELECT COUNT(detail_id) FROM paper_lending_detail WHERE paper_lend_id = pla.lend_id AND is_return = 1) AS ReturnCount,--已還多少紙本
                                            pla.lend_status as LendStatus,--申請單狀態
                                            (SELECT fun_name FROM sys_parameters WHERE para_code = N'lib_lendStatus' AND lang_type = N'{langType}' AND func_code = pla.lend_status) AS LendStatusName --申請單狀態中文名稱
                                            FROM paper_lending_application AS pla
                                            LEFT JOIN paper_lending_demand AS pld ON pla.lend_id = pld.paper_lend_id
                                            LEFT JOIN paper_lending_detail AS pldd ON pla.lend_id = pldd.paper_lend_id
                                            LEFT JOIN (SELECT basic_id,(SELECT apply_number FROM paper_application_data WHERE application_id = paper_applica_id) AS apply_number FROM paper_basic_data) AS pbd ON pbd.basic_id = pldd.paper_basic_id
                                            LEFT JOIN (SELECT apply_number,contract_number,contract_name FROM v_getunconfirmedapplication) AS fa ON fa.apply_number = pbd.apply_number
                                            WHERE 1 = 1
                                                      {searchSql}
                                        ) AS ld;";
        string querySql = @$"SELECT
                                LendId,--主表id為子表查詢做要求
                                LendNumber,--借出單號
                                RetrieveNumber,--調閱單號
                                fee.name AS LendFillCName,--填單人中文名
                                fee.name_a AS LendFillEName,--填單人英文名
                                hee.name AS LendHandlerCName,--填單人中文名
                                hee.name_a AS LendHandlerEName,--填單人英文名
                                LendCount,--共多少紙本
                                ReturnCount,--已還多少紙本
                                LendStatus,--申請單狀態
                                LendStatusName,--申請單狀態中文名稱
                                CreateTime,
								(case when 
		                                exists(SELECT 1 FROM paper_lending_detail WHERE paper_lend_id = LendId 
		                                		and (
		                                				(overdue_day is not NULL  and overdue_day>0)
		                                			 or (should_return_time is not null and should_return_time is not null and CONVERT(date,should_return_time) < CONVERT(date, getutcdate()) )
		                                		) 
		                                )
		                            then 1
		                            else 0
		                            end
	                            )AS IsOverdueAndNotReturn--时候含有逾期未归还的
                                FROM (
	                                SELECT DISTINCT
	                                pla.lend_id as LendId,--主表id為子表查詢做要求
	                                pla.lend_number as LendNumber,--借出單號
	                                pld.retrieve_number as RetrieveNumber,--調閱單號
	                                pla.lend_fill_emplid,--填單人
	                                pla.lend_handler_emplid,--經辦人
	                                pla.application_time  as CreateTime,--申請日期(資料表中是utc時間，需要轉換為操作者的時區時間格式YYYY/MM/DD)
	                                (SELECT COUNT(detail_id) FROM paper_lending_detail WHERE paper_lend_id = pla.lend_id and is_pickup_lend=1) AS LendCount,--共多少紙本
	                                (SELECT COUNT(detail_id) FROM paper_lending_detail WHERE paper_lend_id = pla.lend_id AND is_return = 1) AS ReturnCount,--已還多少紙本
	                                pla.lend_status as LendStatus,--申請單狀態
	                                (SELECT fun_name FROM sys_parameters WHERE para_code = N'lib_lendStatus' AND lang_type = N'{langType}' AND func_code = pla.lend_status) AS LendStatusName --申請單狀態中文名稱
	                                FROM paper_lending_application AS pla
	                                LEFT JOIN paper_lending_demand AS pld ON pla.lend_id = pld.paper_lend_id
	                                LEFT JOIN paper_lending_detail AS pldd ON pla.lend_id = pldd.paper_lend_id
	                                LEFT JOIN (SELECT basic_id,(SELECT apply_number FROM paper_application_data WHERE application_id = paper_applica_id) AS apply_number FROM paper_basic_data) AS pbd ON pbd.basic_id = pldd.paper_basic_id
	                                LEFT JOIN (SELECT apply_number,contract_number,contract_name FROM v_getunconfirmedapplication) AS fa ON fa.apply_number = pbd.apply_number
	                                WHERE 1 = 1
                                           {searchSql}
                                ) AS ld
                                LEFT JOIN (SELECT emplid,name,name_a FROM ps_sub_ee_lgl_vw_a) AS fee ON fee.emplid = ld.lend_fill_emplid
                                LEFT JOIN (SELECT emplid,name,name_a FROM ps_sub_ee_lgl_vw_a) AS hee ON hee.emplid = ld.lend_handler_emplid
                                {orderSqlStr}
                                {pagiSqlstr}
                                ";
        #endregion

        totalCount = NpgsqlSearchBySingle<int>(countsql, sqlPara);
        List<LendAdminModel> data = NpgsqlSearchByList(querySql, sqlPara, actFunc);
        return (data, totalCount);
    }
    #endregion

    #region 资料查询，借出中查询  导出
    /// <summary>
    /// 资料查询，借出中查询  导出
    /// </summary>
    /// <returns></returns>
    public List<LendAdminModelExport> QueryLendForAdminExport(LendForAdminExportPara para, string langType = "ZH-TW")
    {
        Dictionary<string, object> sqlPara = new Dictionary<string, object>();

        #region 拼接查詢條件
        StringBuilder searchSql = new StringBuilder();
        //紙本/合約編號
        if (!string.IsNullOrWhiteSpace(para.ContractNumber))
        {
            searchSql.AppendLine(@" AND (pldd.paper_code LIKE CONCAT(N'%', @ContractNumber, N'%')
                                            OR fa.contract_number LIKE CONCAT(N'%', @ContractNumber, N'%')
                                           )"
                                 );
            sqlPara.Add("ContractNumber", para.ContractNumber);
        }
        //紙本/合約名稱
        if (!string.IsNullOrWhiteSpace(para.ContractName))
        {
            searchSql.AppendLine(@" AND (pldd.paper_name LIKE CONCAT(N'%', @ContractName, N'%')
                                            OR fa.contract_name LIKE CONCAT(N'%', @ContractName, N'%')
                                            )");
            sqlPara.Add("ContractName", para.ContractName);
        }

        //填单人
        if (para.LendFillEmplid.Count > 0)
        {
            int i = 0;
            string sqlEmplidsPara = string.Join(",", para.LendFillEmplid.Select(e =>
            {
                i++;
                sqlPara.Add("LendFillEmplid_" + i.ToString(), e.Emplid);
                return "@LendFillEmplid_" + i.ToString();
            })
                );
            searchSql.AppendLine(@$" AND pla.lend_fill_emplid in ({sqlEmplidsPara})");
        }

        //經辦人
        if (para.LendHanderEmplid.Count > 0)
        {
            int i = 0;
            string sqlEmplidsPara = string.Join(",", para.LendHanderEmplid.Select(e =>
            {
                i++;
                sqlPara.Add("LendHanderEmplid_" + i.ToString(), e.Emplid);
                return "@LendHanderEmplid_" + i.ToString();
            })
                );
            searchSql.AppendLine(@$" AND  pla.lend_handler_emplid in ({sqlEmplidsPara})");
        }

        //應歸還時間
        searchSql.AppendLine(@$"AND {para.ShouldReturnTime.ToSqlString("pldd.should_return_time", "should_return_time", sqlPara)}");

        //借出/調閱單號
        if (!string.IsNullOrWhiteSpace(para.LendNumber))
        {
            searchSql.AppendLine("AND (pla.lend_number LIKE @LendNumber OR pld.retrieve_number LIKE @LendNumber)");
            sqlPara.Add("LendNumber", "%" + para.LendNumber + "%");
        }

        //申請單狀態
        if (para.LendStatus.Count > 0)
        {
            int i = 0;
            string sqlLendStatusPara = string.Join(" OR ", para.LendStatus.Select(e =>
            {
                i++;
                sqlPara.Add("LendStatu_" + i.ToString(), e.Key);
                return "pla.lend_status = @LendStatu_" + i.ToString();
            })
                                                    );
            searchSql.AppendLine(@$"AND ({sqlLendStatusPara}) ");
        }


        //取件日期
        searchSql.AppendLine(@$"AND {para.PickupTime.ToSqlString("pldd.pickup_time", "pickup_time", sqlPara)}");

        #endregion

        #region 排序
        string orderSqlStr = string.Empty;
        if (para.Order != null && para.Order.Count > 0)
        {
            orderSqlStr = para.Order.ToString();
        }
        else
        {
            orderSqlStr = "order by CreateTime";
        }
        #endregion

        #region 拼接SQL語句
        string querySql = @$"
                            SELECT
                            LendId,
                            lend_number as LendNumber  ,--借出單號
                            retrieve_number as RetrieveNumber,--調閱單號
                            fee.name AS LendFillCName,--填單人中文名
                            fee.name_a AS LendFillEName,--填單人英文名
                            hee.name AS LendHandlerCName,--填單人中文名
                            hee.name_a AS LendHandlerEName,--填單人英文名
                            lend_status as LendStatus,--申請單狀態
                            lendStatusName as LendStatusName,--申請單狀態中文名稱
                            contract_number as ContractNumber,--合約編號
                            paper_code as PaperCode,--紙本編號
                            paper_name as PaperName,--紙本名稱
                            auth_date_end as AuthdateEnd,--調閱單號結束時間
                            should_return_time as ShouldReturnTime,--應歸還日期(當主表中，申請單狀態為待取件時，應歸還日期應該為vo.auth_date_end)
                            paper_return_status as AaperReturnStatus,--紙本現狀
                            returnStatusName as ReturnStatusName,--紙本現狀中文名稱
                            application_time as CreateTime, --申请日期
							(SELECT COUNT(detail_id) FROM paper_lending_detail WHERE paper_lend_id = LendId and is_pickup_lend=1) AS LendCount,--共多少紙本
	                        (SELECT COUNT(detail_id) FROM paper_lending_detail WHERE paper_lend_id =LendId AND is_return = 1) AS ReturnCount,--已還多少紙本
                            ActualReturnTime,
                            OverdueDay,
                            is_return as IsReturn,
                            PaperEntryStatus, --入库状态
                            ParerBorrowDays, --借阅日上限
                            PaperPosition --存放位置
                            FROM (
	                            SELECT DISTINCT
	                            pla.lend_id as LendId,
	                            pla.lend_number,--借出單號
	                            pld.retrieve_number,--調閱單號
	                            pla.lend_fill_emplid,--填單人
	                            pla.lend_handler_emplid,--經辦人
	                            pla.application_time,--申請日期(資料表中是utc時間，需要轉換為操作者的時區時間格式YYYY/MM/DD)
	                            pla.lend_status,--申請單狀態
	                            (SELECT fun_name FROM sys_parameters WHERE para_code = N'lib_lendStatus' AND lang_type = N'{langType}' AND func_code = pla.lend_status) AS lendStatusName,--申請單狀態中文名稱
	                            fa.contract_number,--合約編號
	                            pldd.paper_code,--紙本編號
	                            pldd.paper_name,--紙本名稱
	                            (SELECT DISTINCT auth_date_end FROM V_GetOtherRetrieveNumber WHERE retrieve_number = pld.retrieve_number) AS auth_date_end,--調閱單號結束時間
	                            pldd.should_return_time,--應歸還日期(當主表中，申請單狀態為待取件時，應歸還日期應該為vo.auth_date_end)
	                            pbd.paper_return_status,--紙本現狀
                                pldd.actual_return_time as ActualReturnTime,--实际归还日,
                                pldd.overdue_day as OverdueDay, --逾期天数
	                            (SELECT STRING_AGG(fun_name, ', ') FROM sys_parameters WHERE para_code = N'lib_returnStatus' AND lang_type = N'{langType}' AND func_code IN(SELECT value  FROM STRING_SPLIT(pbd.paper_return_status, ','))) AS returnStatusName,--紙本現狀中文名稱
                                pldd.is_return,
                                pbd.parer_borrow_days as ParerBorrowDays,
                                (SELECT fun_name FROM sys_parameters WHERE para_code = N'lib_paperEntryStatus' AND lang_type = N'{langType}' AND func_code =pbd.paper_entry_status) AS PaperEntryStatus,--紙本入库状态中文名稱
                                pbd.paper_position as PaperPosition
	                            FROM paper_lending_application AS pla
	                            LEFT JOIN paper_lending_demand AS pld ON pla.lend_id = pld.paper_lend_id
	                            LEFT JOIN paper_lending_detail AS pldd ON pla.lend_id = pldd.paper_lend_id
                                LEFT JOIN paper_basic_data AS pbd ON pldd.paper_basic_id = pbd.basic_id
                                LEFT JOIN (SELECT (SELECT application_id FROM paper_application_data AS pads WHERE pads.apply_number = af.apply_number) AS application_id,contract_number,contract_name FROM v_getunconfirmedapplication AS af) AS fa ON fa.application_id = pbd.paper_applica_id
	                            WHERE 1 = 1
                                    {searchSql}
                            ) AS ld
                            LEFT JOIN (SELECT emplid,name,name_a,phone_a,email_address_a FROM ps_sub_ee_lgl_vw_a) AS fee ON fee.emplid = ld.lend_fill_emplid
                            LEFT JOIN (SELECT emplid,name,name_a,phone_a,email_address_a FROM ps_sub_ee_lgl_vw_a) AS hee ON hee.emplid = ld.lend_handler_emplid
                                {orderSqlStr}
                                ";
        #endregion

        List<LendAdminModelExport> data = NpgsqlSearchByList<LendAdminModelExport>(querySql, sqlPara);
        return data;
    }
    #endregion

    #region 查询纸本借阅详情  管理员_借出中查询 子表
    /// <summary>
    /// 查询纸本借阅详情  管理员_借出中查询 子表
    /// </summary>
    /// <returns></returns>
    public List<PaperLendingDetail> QueryPaperLendingDetailListForAdmin(int paperLendId, string langType, Action<PaperLendingDetail> actFunc)
    {
        string sql = @$"SELECT
                                fa.contract_number as ContractNumber,--合約編號
                                pld.paper_code as PaperCode,--紙本編號
                                pld.paper_name as PaperName,--紙本名稱
                                pldd.retrieve_number as RetrieveNumber,--調閱單號
                                (SELECT DISTINCT auth_date_end FROM V_GetOtherRetrieveNumber WHERE retrieve_number = pldd.retrieve_number) AS AuthDateEnd,--調閱單號結束時間
                                pld.should_return_time as ShouldReturnTime,--應歸還日期(當主表中，申請單狀態為待取件時，應歸還日期應該為vo.auth_date_end)
                                pld.actual_return_time as ActualReturnTime,--实际归还日,
                                pld.overdue_day as OverdueDay, --逾期天数
                               pbd.paper_return_status as AaperReturnStatus,--紙本現狀
                                (SELECT STRING_AGG(fun_name, ', ') FROM sys_parameters WHERE para_code = N'lib_returnStatus' AND lang_type = N'{langType}' AND func_code IN(SELECT value  FROM STRING_SPLIT(pbd.paper_return_status, ','))) AS ReturnStatusName,--紙本現狀中文名稱
	                            pld.pickup_time as PickupTime,--取件日期
	                            ee.name AS PickupCName,--取件者中文名稱
	                            ee.name_a AS PickupEName,--取件者英文名稱
                                is_return as 'IsReturn',--歸還狀態1：已歸還；0：未歸還
                                is_pickup_lend as IsPickupLend
                                FROM paper_lending_detail AS pld
                                INNER JOIN paper_basic_data AS pbd ON pld.paper_basic_id = pbd.basic_id
                                LEFT JOIN paper_lending_demand AS pldd ON pld.paper_demand_id = pldd.demand_id
                                LEFT JOIN (SELECT (SELECT application_id FROM paper_application_data AS pads WHERE pads.apply_number = af.apply_number) AS application_id,contract_number,contract_name FROM v_getunconfirmedapplication AS af) AS fa ON fa.application_id = pbd.paper_applica_id
	                            LEFT JOIN (SELECT emplid,name,name_a FROM ps_sub_ee_lgl_vw_a) AS ee ON ee.emplid = pld.pickup_emplid
                                WHERE pld.paper_lend_id  = @paperLendId 
                           ";

        return NpgsqlSearchByList(sql, new { paperLendId }, actFunc);
    }
    #endregion

    #region 获取需求咨询信息(管理員) (借出检视页面所需)
    /// <summary>
    /// 获取需求咨询信息(管理員) (借出检视页面所需)
    /// </summary>
    /// <param name="lendId">出借主表主键</param>
    /// <returns></returns>
    public List<DemandInfo> QueryDemandInfoForAdmin(int lendId)
    {
        string sql = @"SELECT 
                            DISTINCT
                            pld.retrieve_number as RetrieveNumber,--調閱單號
                            pld.borrow_days as BorrowDays,--借出天數
                            pld.demand_reason as DemandReason,--申請原因
                            pld.retrieve_reason as RetrieveReason, --其他说明
                            vo.discloseperson as Discloseperson,--顯示內部同仁/其他同仁：00：無；01：其他同仁；10：內部同仁；11：內部同仁+其他同仁
                            vo.empid as Empid,--工號
                            vo.ename as EName,--英文名稱
                            vo.depid as Depid,--部門代碼
                            vo.company as Company,--部門公司別
                            vo.otherperson as Otherperson,--其他同仁，一串字符，直接顯示
                            vo.other as Other,--其他說明
                            pldd.actual_return_time as ActualReturnTime,--實際歸還日
                            vo.applynumber  as Applynumber,--調閱案件單號
                            vo.auth_date_start as AuthDateStart, --实际开放时间
                            vo.auth_date_end as AuthDateEnd --实际开放时间 
                            FROM paper_lending_demand AS pld
                            INNER JOIN paper_lending_detail AS pldd ON pldd.paper_demand_id = pld.demand_id
                            left JOIN V_GetOtherRetrieveNumber AS vo ON vo.retrieve_number = pld.retrieve_number
                            WHERE pld.paper_lend_id = @lendId
                            ";

        return NpgsqlSearchByList<DemandInfo>(sql, new { lendId });
    }
    #endregion
}
