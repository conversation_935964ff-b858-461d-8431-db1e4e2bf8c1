﻿using Elegal.Flow.Api.Repository.Paper;
using Elegal.Interface.Api.Common.Control.Filter;
using Elegal.Interface.Api.Common.FuncHelper;
using Elegal.Interface.Api.Common.Model.DBModel;
using Elegal.Interface.Api.Common.Model.ParaModel.FlowApi;
using Elegal.Interface.Api.Common.Model.ResultModel.FlowApi;
using Elegal.Interface.Api.Common.Model.ViewModel.FlowApi;
using Elegal.Orm;
using Elegal.Orm.Dtos;

namespace Elegal.Flow.Api.Services.Paper
{
    /// <summary>
    /// 紙本確認功能服務類
    /// 負責處理舊有紙本文件的確認、查詢和管理等相關功能
    /// </summary>
    public static class PaperOldDataService
    {
        // 資料庫存取層實例
        private static readonly PaperOldDataRepository _repository = new();
        private static readonly PaperBasicDataRepository _paperBasicDataRepository = new();
        private static readonly PaperBatchWorkRepository _paperBatchWorkRepository = new();

        #region 獲取舊有紙本資料列表
        /// <summary>
        /// 獲取舊有紙本資料列表
        /// </summary>
        /// <param name="qry">查詢條件參數</param>
        /// <returns>分頁後的紙本資料視圖模型列表</returns>
        public static PageResult<PaperOldDataViewModel> GetOldData(qryPaperOldData qry)
        {
            PageResult<PaperOldDataViewModel> res = _repository.GetOldData(qry);
            foreach (PaperOldDataViewModel item in res.Data)
            {
                // 處理操作時間，如果是1900/1/1則設為null
                if (item.operate_time == new DateTime(1900, 1, 1)) item.operate_time = null;
                // 根據用戶時區轉換操作時間
                if (item.operate_time != null) item.local_operate_time = item.operate_time.ConvertDateByTimeZoneByUtc(MvcContext.UserInfo.time_zone);
            }
            return res;
        }
        #endregion

        #region 獲取未確認申請單詳細資料
        /// <summary>
        /// 獲取未確認申請單詳細資料
        /// </summary>
        /// <param name="apply_number">申請單號</param>
        /// <returns>申請單詳細資料視圖模型</returns>
        /// <exception cref="Exception">當資料不存在時拋出異常</exception>
        public static PaperOldDataDetailViewModel GetOldApplicationData(string apply_number)
        {
            PaperOldDataDetailViewModel res = _repository.GetOldApplicationData(apply_number);

            if (res == null) throw new Exception(ActionFilter.GetMultilingualValue("custom:messageContent:dataChurn"));
            // 獲取未確認的紙本資料
            res.details = _repository.GetPaperUnconfirmedData(res.application_id);

            // 處理操作時間
            if (res.operate_time == new DateTime(1900, 1, 1)) res.operate_time = null;
            if (res.operate_time != null) res.local_operate_time = res.operate_time.ConvertDateByTimeZoneByUtc(MvcContext.UserInfo.time_zone);

            return res;
        }
        #endregion

        #region 獲取已確認申請單詳細資料
        /// <summary>
        /// 獲取已確認申請單詳細資料
        /// </summary>
        /// <param name="apply_number">申請單號</param>
        /// <returns>已確認的申請單詳細資料視圖模型</returns>
        /// <exception cref="Exception">當資料不存在時拋出異常</exception>
        public static PaperComfirmedOldDataDetailViewModel GetComfirmedOldApplicationData(string apply_number)
        {
            PaperComfirmedOldDataDetailViewModel res = _repository.GetComfirmedOldApplicationData(apply_number);

            if (res == null) throw new Exception(ActionFilter.GetMultilingualValue("custom:messageContent:dataChurn"));

            // 獲取紙本基本資料
            qryPaperBasicData qry = new qryPaperBasicData() { apply_number = apply_number };
            res.details = _paperBasicDataRepository.QueryPaperBasicData_New(qry).Data;

            // 處理操作時間
            if (res.operate_time == new DateTime(1900, 1, 1)) res.operate_time = null;
            if (res.operate_time != null) res.local_operate_time = res.operate_time.ConvertDateByTimeZoneByUtc(MvcContext.UserInfo.time_zone);

            return res;
        }
        #endregion

        #region 獲取無紙本申請單詳細資料
        /// <summary>
        /// 獲取無紙本申請單詳細資料
        /// </summary>
        /// <param name="apply_number">申請單號</param>
        /// <returns>非舊有申請單詳細資料視圖模型</returns>
        /// <exception cref="Exception">當資料不存在時拋出異常</exception>
        public static PaperOldDataDetailViewModel GetNonOldApplicationData(string apply_number)
        {
            PaperOldDataDetailViewModel res = _repository.GetNonOldApplicationData(apply_number);

            if (res == null) throw new Exception(ActionFilter.GetMultilingualValue("custom:messageContent:dataChurn"));

            // 處理操作時間
            if (res.operate_time == new DateTime(1900, 1, 1)) res.operate_time = null;
            if (res.operate_time != null) res.local_operate_time = res.operate_time.ConvertDateByTimeZoneByUtc(MvcContext.UserInfo.time_zone);

            return res;
        }
        #endregion

        #region 新增非紙本資料(事務處理)
        /// <summary>
        /// 新增非紙本資料
        /// </summary>
        /// <param name="apply_number">申請單號</param>
        /// <param name="application_remark">申請備註</param>
        /// <returns>是否新增成功</returns>
        /// <exception cref="Exception">當資料驗證失敗或操作失敗時拋出異常</exception>
        public static bool AddNonPaperData(string apply_number, string? application_remark)
        {
            // 檢查是否為未確認資料
            if (!CheckUncomfirmData(apply_number))
                throw new Exception(ActionFilter.GetMultilingualValue("custom:messageContent:dataChurn"));

            // 獲取紙本申請資料
            PaperApplicationDataViewModel main = new PaperApplicationDataViewModel();
            main = _paperBasicDataRepository.GetPaperApplicationData(apply_number);

            // 獲取舊有申請資料
            V_GetUnConfirmedApplication oldData = _paperBasicDataRepository.GetOldApplication(apply_number);

            // 驗證資料是否存在
            if (main == null && oldData == null)
                throw new Exception(ActionFilter.GetMultilingualValue("custom:messageContent:getPaperDataFail"));

            DbAccess.PerformInTransaction(context =>
            {
                // 如果主表不存在，則新增資料
                if (main == null)
                {
                    // 建立新的申請資料
                    paper_application_data newData = new paper_application_data
                    {
                        apply_number = oldData.apply_number,
                        my_entity_id = oldData.entity_id,
                        party_a = oldData.party_a,
                        pic_emplid = oldData.emplid,
                        pic_deptid = oldData.deptid,
                        contract_number = oldData.contract_number,
                        contract_name = oldData.contract_name,
                        having_paper = 0,
                        is_old_application = 1,
                        application_status = "03",
                        application_remarks = application_remark,
                        create_user = MvcContext.UserInfo.current_emp,
                        create_time = DateTime.UtcNow
                    };

                    if (!_paperBasicDataRepository.InsertPaperApplicationDataToTransaction(newData, context))
                        throw new Exception(ActionFilter.GetMultilingualValue("custom:messageTitle:createFail"));
                }
                else
                {
                    // 如果是未確認狀態，更新申請資料
                    if (main.application_status == "01" && oldData != null)
                    {
                        paper_application_data newData = new paper_application_data
                        {
                            apply_number = oldData.apply_number,
                            my_entity_id = oldData.entity_id,
                            party_a = oldData.party_a,
                            pic_emplid = oldData.emplid,
                            pic_deptid = oldData.deptid,
                            contract_number = oldData.contract_number,
                            contract_name = oldData.contract_name,
                            modify_user = MvcContext.UserInfo.current_emp,
                            modify_time = DateTime.UtcNow
                        };

                        if (!_paperBasicDataRepository.UpdatePaperApplicationDataToTransaction(newData, context))
                            throw new Exception(ActionFilter.GetMultilingualValue("custom:messageTitle:createFail"));
                    }

                    // 更新備註和狀態
                    if (!string.IsNullOrEmpty(application_remark))
                        _paperBasicDataRepository.UpdatePaperApplicationDataRemarkToTransaction(apply_number, application_remark, context);
                    if (!_repository.UpdateApplicationStatusToTransaction(main.application_id, "03", 0, context))
                        throw new Exception(ActionFilter.GetMultilingualValue("custom:messageTitle:updateFail"));
                }
            });

            return true;
        }
        #endregion

        #region 新增紙本資料(事務處理)
        /// <summary>
        /// 新增紙本資料
        /// </summary>
        /// <param name="insData">要新增的紙本資料</param>
        /// <returns>錯誤的紙本代碼列表</returns>
        /// <exception cref="Exception">當資料驗證失敗或操作失敗時拋出異常</exception>
        public static List<string> AddPaperData(insertPaperOldData insData)
        {
            #region 數據驗證
            List<paper_basic_data> basics = insData.basics;
            string? application_remark = insData.application_remark;

            // 驗證輸入資料
            if (basics == null || basics.Count == 0)
                throw new Exception(ActionFilter.GetMultilingualValue("custom:messageContent:nullParaValue"));

            string applyNumber = basics.FirstOrDefault()?.apply_number;

            // 檢查未確認資料
            if (!CheckUncomfirmData(applyNumber))
                throw new Exception(ActionFilter.GetMultilingualValue("custom:messageContent:dataChurn"));

            // 檢查紙本是否已存在
            List<string> errorList = new List<string>();
            foreach (paper_basic_data basic in basics)
            {
                paper_basic_data data = _paperBasicDataRepository.GetPaperBasicData(basic.paper_code);
                if (data != null)
                {
                    if (!errorList.Contains(data.paper_code)) errorList.Add(data.paper_code);
                    continue;
                }
            }
            if (errorList.Count != 0) return errorList;

            // 檢查紙本是否有重複
            errorList = new List<string>();
            var duplicatePaperCodes = basics.GroupBy(basic => basic.paper_code)
                                          .Where(group => group.Count() > 1)
                                          .Select(group => group.Key);
            foreach (var duplicatePaperCode in duplicatePaperCodes)
            {
                errorList.Add(duplicatePaperCode);
            }
            if (errorList.Count != 0) return errorList;

            // 處理申請資料
            PaperApplicationDataViewModel main = _paperBasicDataRepository.GetPaperApplicationData(applyNumber);
            V_GetUnConfirmedApplication oldData = _paperBasicDataRepository.GetOldApplication(applyNumber);

            if (main == null && oldData == null)
                throw new Exception(ActionFilter.GetMultilingualValue("custom:messageContent:getPaperDataFail"));
            #endregion

            DbAccess.PerformInTransaction(context =>
            {
                // 如果主表不存在，則新增資料
                if (main == null)
                {
                    paper_application_data newData = new paper_application_data
                    {
                        apply_number = oldData.apply_number,
                        my_entity_id = oldData.entity_id,
                        party_a = oldData.party_a,
                        pic_emplid = oldData.emplid,
                        pic_deptid = oldData.deptid,
                        contract_number = oldData.contract_number,
                        contract_name = oldData.contract_name,
                        having_paper = 1,
                        is_old_application = 1,
                        application_status = "02",
                        application_remarks = application_remark,
                        create_user = MvcContext.UserInfo.current_emp,
                        create_time = DateTime.UtcNow
                    };

                    if (!_paperBasicDataRepository.InsertPaperApplicationDataToTransaction(newData, context))
                        throw new Exception(ActionFilter.GetMultilingualValue("custom:messageTitle:createFail"));

                    main = _paperBasicDataRepository.GetPaperApplicationDataToTransaction(applyNumber, context);
                }
                else
                {
                    // 更新申請資料
                    if (main.application_status == "01" && oldData != null)
                    {
                        paper_application_data newData = new paper_application_data
                        {
                            apply_number = oldData.apply_number,
                            my_entity_id = oldData.entity_id,
                            party_a = oldData.party_a,
                            pic_emplid = oldData.emplid,
                            pic_deptid = oldData.deptid,
                            contract_number = oldData.contract_number,
                            contract_name = oldData.contract_name,
                            modify_user = MvcContext.UserInfo.current_emp,
                            modify_time = DateTime.UtcNow
                        };

                        if (!_paperBasicDataRepository.UpdatePaperApplicationDataToTransaction(newData, context))
                            throw new Exception(ActionFilter.GetMultilingualValue("custom:messageTitle:createFail"));
                    }

                    // 更新備註和狀態
                    if (!string.IsNullOrEmpty(application_remark))
                        _paperBasicDataRepository.UpdatePaperApplicationDataRemarkToTransaction(applyNumber, application_remark, context);
                    if (!_repository.UpdateApplicationStatusToTransaction(main.application_id, "02", 1, context))
                        throw new Exception(ActionFilter.GetMultilingualValue("custom:messageTitle:updateFail"));
                }

                // 移除舊的未確認資料
                _repository.DeletePaperUnconfirmDataToTransaction(main.application_id, context);

                // 處理每筆紙本資料
                errorList = new List<string>();
                foreach (paper_basic_data basic in basics)
                {
                    // 檢查紙本是否已存在
                    paper_basic_data data = _paperBasicDataRepository.GetPaperBasicDataToTransaction(basic.paper_code, context);
                    if (data != null)
                    {
                        errorList.Add(data.paper_code);
                        continue;
                    }

                    // 設置紙本資料屬性
                    basic.paper_applica_id = main.application_id;
                    basic.paper_remarks = application_remark;
                    basic.create_user = MvcContext.UserInfo.current_emp;
                    basic.create_time = DateTime.UtcNow;

                    // 設置預設的紙本現狀
                    if (string.IsNullOrEmpty(basic.paper_return_status)) basic.paper_return_status = "05";

                    // 新增紙本基本資料
                    if (!_paperBasicDataRepository.InsertPaperBasicDataToTransaction(basic, context))
                        throw new Exception(ActionFilter.GetMultilingualValue("custom:messageTitle:createFail"));

                    // 獲取新增的紙本資料
                    paper_basic_data basic_data = _paperBasicDataRepository.GetPaperBasicDataToTransaction(basic.paper_code, context);
                    if (basic == null)
                        throw new Exception(ActionFilter.GetMultilingualValue("custom:messageTitle:createFail"));

                    // 新增紙本歷史記錄
                    paper_history_data history = new paper_history_data
                    {
                        paper_basic_id = basic_data.basic_id.Value,
                        paper_entry_status = basic_data.paper_entry_status,
                        course_emplid = MvcContext.UserInfo.current_emp,
                        paper_remarks = basic_data.paper_remarks,
                        create_user = MvcContext.UserInfo.current_emp,
                        create_time = DateTime.UtcNow
                    };

                    if (!_paperBasicDataRepository.InsertPaperHistoryDataToTransaction(history, context))
                        throw new Exception(ActionFilter.GetMultilingualValue("custom:messageTitle:createFail"));
                }
            });

            return errorList;
        }
        #endregion

        #region 更新紙本資料(事務處理)
        /// <summary>
        /// 更新紙本資料
        /// </summary>
        /// <param name="data">更新的資料內容</param>
        /// <returns>是否更新成功</returns>
        /// <exception cref="Exception">當資料不存在或更新失敗時拋出異常</exception>
        public static bool UpdatePaperData(updatePaperOldData data)
        {
            paper_application_data appliData = _paperBasicDataRepository.GetPaperApplicationDataByApplicationId(data.application_id);
            if (appliData == null)
                throw new Exception(ActionFilter.GetMultilingualValue("custom:messageContent:dataChurn"));

            DbAccess.PerformInTransaction(context =>
            {
                // 更新申請狀態
                if (!_repository.UpdatePaperApplicationStatusToTransaction(data.application_id, data.application_status, data.application_remark, 1, context))
                    throw new Exception(ActionFilter.GetMultilingualValue("custom:messageTitle:updateFail"));
                // 新增未確認資料
                if (!_repository.InsertPaperUnconfirmDataToTransaction(data.application_id, data.application_remark, context))
                    throw new Exception(ActionFilter.GetMultilingualValue("custom:messageTitle:createFail"));
                // 批次刪除紙本基本資料
                if (!_paperBasicDataRepository.BatchDeletePaperBasicDataToTransaction(data.application_id, context))
                    throw new Exception(ActionFilter.GetMultilingualValue("custom:messageTitle:updateFail"));
            });

            return true;
        }
        #endregion

        #region 更新無紙本資料
        /// <summary>
        /// 更新無紙本資料
        /// </summary>
        /// <param name="data">更新的資料內容</param>
        /// <returns>是否更新成功</returns>
        /// <exception cref="Exception">當資料不存在或狀態不正確時拋出異常</exception>
        public static bool UpdateNonPaperData(updatePaperOldData data)
        {
            paper_application_data appliData = _paperBasicDataRepository.GetPaperApplicationDataByApplicationId(data.application_id);
            if (appliData == null) throw new Exception(ActionFilter.GetMultilingualValue("custom:messageContent:dataChurn"));
            if (appliData.application_status != "03") throw new Exception(ActionFilter.GetMultilingualValue("custom:messageContent:dataChurn"));

            // 更新申請狀態
            if (!_repository.UpdatePaperApplicationStatus(data.application_id, data.application_status, data.application_remark, 0))
                throw new Exception(ActionFilter.GetMultilingualValue("custom:messageTitle:updateFail"));

            return true;
        }
        #endregion

        #region 根據申請ID獲取紙本申請資料
        /// <summary>
        /// 根據申請ID獲取紙本申請資料
        /// </summary>
        /// <param name="application_id">申請ID</param>
        /// <returns>紙本申請資料</returns>
        public static paper_application_data GetPaperApplicationDataByApplicationId(int application_id)
        {
            return _paperBasicDataRepository.GetPaperApplicationDataByApplicationId(application_id);
        }
        #endregion

        #region 檢查指定申請單號是否為未確認狀態
        /// <summary>
        /// 檢查指定申請單號是否為未確認狀態
        /// </summary>
        /// <param name="applyNumber">申請單號</param>
        /// <returns>如果是未確認狀態返回true，否則返回false</returns>
        private static bool CheckUncomfirmData(string applyNumber)
        {
            return GetOldData(new qryPaperOldData()
            {
                apply_number_start = applyNumber,
                paperComfirmType = "01"
            }).Data.Count() > 0;
        }
        #endregion
    }
}
