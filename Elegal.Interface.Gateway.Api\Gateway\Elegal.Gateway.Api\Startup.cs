using Elegal.Gateway.Api.Application.ActionFilters;
using Elegal.Gateway.Api.Application.Authorization;
using Elegal.Gateway.Api.Application.HubFilters;
using Elegal.Gateway.Api.Application.MiddlewareService;
using Elegal.Gateway.Api.Application.ResultFilters;
using Elegal.Gateway.Api.UI;
using Elegal.Gateway.Api.UI.Application.ActionFilters;
using Elegal.Gateway.Api.UI.Application.ExceptionFilters;
using Elegal.Gateway.Api.UI.Application.HubFilters;
using Elegal.Gateway.Api.UI.Application.ResultFilters;
using Elegal.Gateway.Api.UI.Authorization;
using Elegal.Gateway.Api.UI.Hubs;
using Elegal.Gateway.Api.UI.Middleware;
using Elegal.Interface.Api.Common.Control.Filter;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Http.Features;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Server.Kestrel.Core;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.IdentityModel.Tokens;
using Microsoft.OpenApi.Models;
using System;
using System.Net.Http;
using System.Threading;

namespace Elegal.Gateway.Api
{
    public class Startup
    {
        public Startup(IConfiguration configuration)
        {
            Configuration = configuration;
        }

        public IConfiguration Configuration { get; }

        // This method gets called by the runtime. Use this method to add services to the container.
        public void ConfigureServices(IServiceCollection services)
        {
            // Optimized HttpClientHandler configuration
            var handler = new HttpClientHandler()
            {
                UseDefaultCredentials = false,
                Credentials = System.Net.CredentialCache.DefaultCredentials,
                AllowAutoRedirect = true,
                MaxConnectionsPerServer = 100, // Increase connection pool
                UseCookies = false // Disable cookies for better performance
            };

            services.AddSingleton(sp => new HttpClient(handler)
            {
                //BaseAddress = new Uri("http://elegal-backend-permission.k8s-dev.k8s.wistron.com:443")
                Timeout = TimeSpan.FromSeconds(10) // Reduced timeout for faster failure detection
            });
            services.AddSingleton<IHttpContextAccessor, HttpContextAccessor>();

            #region 跨域配置
            services.AddCors(options =>
            {
                options.AddPolicy("any", builder => { builder.SetIsOriginAllowed(_ => true).AllowAnyMethod().AllowAnyHeader().AllowCredentials(); });
            });
            #endregion

            //設置請求體大小
            services.Configure<FormOptions>(x => x.MultipartBodyLengthLimit = 100 * 1024 * 1024);
            services.Configure<KestrelServerOptions>(options =>
            {
                options.Limits.MaxRequestBodySize = 100 * 1024 * 1024; // 设置最大请求体大小为100MB
                options.Limits.KeepAliveTimeout = TimeSpan.FromSeconds(120); // 2分钟保活
                options.Limits.RequestHeadersTimeout = TimeSpan.FromSeconds(30); // 30秒请求头超时
                options.Limits.RequestBodyTimeout = TimeSpan.FromSeconds(300); // 5分钟请求体超时（文件上传）
            });

            //网关在接收到上传请求时，不会将整个文件加载到内存，而是边接收边转发到后端API
            services.Configure<FormOptions>(options =>
            {
                options.MemoryBufferThreshold = int.MaxValue; // 禁用内存缓冲，使用流式处理
                options.BufferBody = false;
            });

            //If you want to use the Api Gateway's Authorization, you can do this
            services.AddScoped<IGatewayAuthorization, AuthorizationService>();
            services.AddScoped<IGetOrHeadGatewayAuthorization, GetAuthorizationService>();

            //Action filters
            services.AddScoped<IGatewayActionFilter, ValidationActionFilterService>();
            services.AddScoped<IPostGatewayActionFilter, PostValidationActionFilterService>();

            //Exception filters
            services.AddScoped<IGatewayExceptionFilter, ExceptionFilterService>();
            services.AddScoped<IPostGatewayExceptionFilter, PostExceptionFilterService>();

            //Result filters
            services.AddScoped<IGatewayResultFilter, ResultFilterService>();
            services.AddScoped<IPostGatewayResultFilter, PostResultFilterService>();

            //Hub filters
            services.AddScoped<IGatewayHubFilter, GatewayHubFilterService>();

            //Middleware service
            services.AddTransient<IGatewayMiddleware, GatewayMiddlewareService>();

            //Token caching service for performance optimization
            services.AddMemoryCache();
            services.AddSingleton<Elegal.Gateway.Api.UI.Services.ITokenCacheService, Elegal.Gateway.Api.UI.Services.TokenCacheService>();

            //Api gateway
            services.AddApiGateway(options =>
            {
                options.UseResponseCaching = false;
                options.ResponseCacheSettings = new ApiGatewayResponseCacheSettings
                {
                    Duration = 120,
                    Location = ResponseCacheLocation.Any
                };
            });

            services.AddControllers();

            services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme)
                    .AddJwtBearer(options =>
                    {
                        // 設置驗證參數
                        options.TokenValidationParameters = new TokenValidationParameters
                        {
                            ValidateIssuerSigningKey = false, // 不驗證簽名金鑰
                            ValidateIssuer = false,           // 不驗證發行者
                            ValidateAudience = false,         // 不驗證觀眾
                            ValidateLifetime = false,         // 不驗證生存期
                            ClockSkew = TimeSpan.Zero         // 完全不容忍時間偏移
                        };
                    }).AddCookie(options =>
                    {
                        options.Cookie.HttpOnly = true; // 阻止客户端脚本访问Cookie  
                        options.Cookie.SecurePolicy = CookieSecurePolicy.Always; // 确保Cookie仅通过HTTPS发送  
                        options.Cookie.SameSite = SameSiteMode.Lax; // 根据你的需要设置SameSite模式

                    }); ;

            services.AddSwaggerGen(c =>
            {
                c.SwaggerDoc("v1", new OpenApiInfo { Title = "elegal-gateway-api", Version = "v1" });
                // 添加 Swagger 的授權設定
                c.AddSecurityDefinition("Authorization", new OpenApiSecurityScheme
                {
                    Description = "idToken",
                    Name = "Authorization",
                    In = ParameterLocation.Header,
                    Type = SecuritySchemeType.ApiKey,
                    Scheme = "Authorization"
                });

                // 添加用戶驗證的範例
                c.AddSecurityRequirement(new OpenApiSecurityRequirement
                {
                    {
                        new OpenApiSecurityScheme
                        {
                            Reference = new OpenApiReference
                            {
                                Type = ReferenceType.SecurityScheme,
                                Id = "Authorization"
                            }
                        },
                        new string[] { }
                    }
                });
            });
        }

        public void Configure(IApplicationBuilder app, IWebHostEnvironment env, IHttpContextAccessor accessor)
        {
            MvcContext.Accessor = accessor;
            if (env.IsDevelopment())
            {
                app.UseDeveloperExceptionPage();
                app.UseSwagger();
                app.UseSwaggerUI(c =>
                {
                    c.SwaggerEndpoint("/swagger/v1/swagger.json", "elegal-gateway-api");
                });
            }

            app.UseCors("any");

            // 添加性能測量中間件
            app.UseMiddleware<PerformanceMeasurementMiddleware>();

            //Api gateway
            app.UseApiGateway(orchestrator => ApiOrchestration.Create(orchestrator, app));

            app.UseHttpsRedirection();

            app.UseRouting();

            app.UseAuthorization();

            app.UseAuthentication();

            app.Use(async (context, next) =>
            {
                await next().ConfigureAwait(false); // 正确使用异步
            });

            app.UseEndpoints(endpoints =>
            {
                //GatewayHub endpoint
                endpoints.MapHub<GatewayHub>("/gatewayhub");
                endpoints.MapControllers();
            });
        }
    }
}
