﻿using Elegal.Interface.Api.Common.Model.ParaModel.FlowApi;
using Elegal.Interface.Api.Common.Model.ViewModel.FlowApi;
using Elegal.Orm;
using Elegal.Orm.Utils;
using System.Text;

namespace Elegal.Flow.Api.Repository.Process
{
    /// <summary>
    /// 签核层级设定
    /// </summary>
    public class ApproverLevelSettingRepository
    {
        /// <summary>
        /// 查询列表数据
        /// </summary>
        /// <param name="condition">查询参数</param>
        /// <returns></returns>
        public IEnumerable<EntityCompanyModel> GetDataList(ApproverLevelSettingSearchModel condition)
        {
            string sql = $@"select
                                fsl.rowid Rowid,
	                            com.company Company,
	                            com.descrshort Descrshort,
	                            isnull(fsl.level_start,5) LevelStart,
	                            isnull(fsl.level_end,2) LevelEnd,
	                            isnull(fsl.modify_time,fsl.create_time) operate_time,
	                            ee.name operate_cuser,
	                            ee.name_a operate_euser
                            from ps_sub_comp_vw_a com
                            left join flow_sign_level fsl on fsl.company = com.company
                            left join (select emplid,name,name_a from ps_sub_ee_lgl_vw_a) ee on ee.emplid = isnull(fsl.modify_user,fsl.create_user)
                            {(condition.Companys.Count != 0? $"inner join (select value company from string_split('{string.Join(",", condition.Companys)}',',')) com2 on com.company = com2.company":"")}
                            order by com.company";
            return DbAccess.Database.SqlQuery<EntityCompanyModel>(sql);
        }
    }
}
