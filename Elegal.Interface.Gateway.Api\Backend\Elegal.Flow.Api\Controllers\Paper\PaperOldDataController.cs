﻿using Elegal.Flow.Api.Services.Paper;
using Elegal.Interface.Api.Common.Control.Filter;
using Elegal.Interface.Api.Common.Control.Web;
using Elegal.Interface.Api.Common.Model.DBModel;
using Elegal.Interface.Api.Common.Model.ParaModel.FlowApi;
using Elegal.Interface.Api.Common.Model.ResultModel;
using Elegal.Interface.Api.Common.Model.ViewModel.FlowApi;
using Elegal.Orm.Dtos;
using Microsoft.AspNetCore.Mvc;
using System.Text;

namespace Elegal.Flow.Api.Controllers.Paper
{
    /// <summary>
    /// 紙本舊資料作業
    /// </summary>
    [Route("[controller]")]
    [ApiController]
    public class PaperOldDataController : BaseController
    {
        /// <summary>
        /// 查詢舊資料
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [Route("Query")]
        public async Task<ApiResultModelByObject> QueryOldData([FromBody] qryPaperOldData qry)
        {
            ApiResultModelByObject apiResult = new ApiResultModelByObject();

            PageResult<PaperOldDataViewModel> result = PaperOldDataService.GetOldData(qry);
            apiResult.listData = result.Data;
            apiResult.totalCount = result.TotalRows;
            apiResult.rtnSuccess = true;
            InitLogRecord(qry, logRecord =>
            {
                StringBuilder stringBuilder = new();
                string comfirmType = "";
                switch (qry.paperComfirmType)
                {
                    case "01":
                        comfirmType = "未確認";
                        break;
                    case "02":
                        comfirmType = "已確認";
                        break;
                    case "03":
                        comfirmType = "無紙本";
                        break;
                }
                stringBuilder.AppendLine($"紙本確認狀態:  {comfirmType}");
                if (!string.IsNullOrEmpty(qry.contract_number_start)) stringBuilder.AppendLine($"合約編號開始:  {qry.contract_number_start}");
                if (!string.IsNullOrEmpty(qry.contract_number_end)) stringBuilder.AppendLine($"合約編號結束:  {qry.contract_number_end}");
                if (!string.IsNullOrEmpty(qry.apply_number_start)) stringBuilder.AppendLine($"申請單號開始:  {qry.apply_number_start}");
                if (!string.IsNullOrEmpty(qry.apply_number_end)) stringBuilder.AppendLine($"申請單號結束:  {qry.apply_number_end}");
                if (!string.IsNullOrEmpty(qry.my_entity_id)) stringBuilder.AppendLine($"我方主體: {PaperBasicDataService.GetEntityString(qry.my_entity_id)}");
                if (!string.IsNullOrEmpty(qry.filed_type)) stringBuilder.AppendLine($"正本歸檔狀態: {PaperBasicDataService.GetTypeString("archiveStatus", qry.filed_type)}");

                logRecord.Detail = stringBuilder.ToString();
            });

            return await Task.FromResult(apiResult);
        }

        /// <summary>
        /// 查詢舊資料明細
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("GetOldApplicationData")]
        public async Task<ApiResultModelByObject> GetOldApplicationData([FromQuery] string apply_number)
        {
            ApiResultModelByObject apiResult = new ApiResultModelByObject();

            PaperOldDataDetailViewModel result = PaperOldDataService.GetOldApplicationData(apply_number);
            apiResult.listData = result;
            apiResult.rtnSuccess = true;
            InitLogRecord<object>(new(), logRecord =>
            {
                StringBuilder stringBuilder = new();
                stringBuilder.AppendLine($"申請單號:  {apply_number}");

                logRecord.Detail = stringBuilder.ToString();
            });

            return await Task.FromResult(apiResult);
        }

        /// <summary>
        /// 查詢已確認舊資料明細
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("GetComfirmedOldApplicationData")]
        public async Task<ApiResultModelByObject> GetComfirmedOldApplicationData([FromQuery] string apply_number)
        {
            ApiResultModelByObject apiResult = new ApiResultModelByObject();

            PaperComfirmedOldDataDetailViewModel result = PaperOldDataService.GetComfirmedOldApplicationData(apply_number);
            apiResult.listData = result;
            apiResult.rtnSuccess = true;
            InitLogRecord<object>(new(), logRecord =>
            {
                StringBuilder stringBuilder = new();
                stringBuilder.AppendLine($"申請單號:  {apply_number}");

                logRecord.Detail = stringBuilder.ToString();
            });

            return await Task.FromResult(apiResult);
        }

        /// <summary>
        /// 查詢無紙本舊資料明細
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("GetNonOldApplicationData")]
        public async Task<ApiResultModelByObject> GetNonOldApplicationData([FromQuery] string apply_number)
        {
            ApiResultModelByObject apiResult = new ApiResultModelByObject();

            PaperOldDataDetailViewModel result = PaperOldDataService.GetNonOldApplicationData(apply_number);
            apiResult.listData = result;
            apiResult.rtnSuccess = true;
            InitLogRecord<object>(new(), logRecord =>
            {
                StringBuilder stringBuilder = new();
                stringBuilder.AppendLine($"申請單號:  {apply_number}");

                logRecord.Detail = stringBuilder.ToString();
            });

            return await Task.FromResult(apiResult);
        }

        /// <summary>
        /// 紙本確認功能(有紙本)
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [Route("AddPaperData")]
        public async Task<ApiResultModelByObject> AddPaperData([FromBody] insertPaperOldData data)
        {
            ApiResultModelByObject apiResult = new ApiResultModelByObject();

            List<string> errorList = PaperOldDataService.AddPaperData(data);
            if (errorList.Count > 0)
            {
                apiResult.messageTitle = ActionFilter.GetMultilingualValue("custom:messageTitle:doNotUpdate");
                apiResult.messageContent = new List<string>() { string.Format(ActionFilter.GetMultilingualValue("custom:messageContent:OldPaperData_repeat"), string.Join(",", errorList)) };
                apiResult.listData = false;
                apiResult.rtnSuccess = false;
            }
            else
            {
                apiResult.listData = true;
                apiResult.rtnSuccess = true;
                InitLogRecord(data, logRecord =>
                {
                    StringBuilder stringBuilder = new();
                    stringBuilder.AppendLine($"有無紙本: 有");
                    foreach (paper_basic_data basic in data.basics)
                    {
                        if (!string.IsNullOrEmpty(basic.apply_number)) stringBuilder.AppendLine($"申請單號:  {basic.apply_number}");
                        if (!string.IsNullOrEmpty(basic.paper_code)) stringBuilder.AppendLine($"紙本編號:  {basic.paper_code}");
                        if (!string.IsNullOrEmpty(basic.paper_name)) stringBuilder.AppendLine($"紙本名稱: {basic.paper_name}");
                        if (!string.IsNullOrEmpty(basic.paper_type)) stringBuilder.AppendLine($"紙本類型:  {PaperBasicDataService.GetTypeString("lib_paperType", basic.paper_type)}");
                        if (!string.IsNullOrEmpty(basic.paper_entry_status)) stringBuilder.AppendLine($"入庫狀態:  {PaperBasicDataService.GetTypeString("lib_paperEntryStatus", basic.paper_entry_status)}");
                        if (!string.IsNullOrEmpty(basic.paper_confiden_level)) stringBuilder.AppendLine($"機密等級:  {PaperBasicDataService.GetTypeString("confidentStatus", basic.paper_confiden_level)}");
                    }
                    if (!string.IsNullOrEmpty(data.application_remark)) stringBuilder.AppendLine($"紙本確認備註: {data.application_remark}");

                    logRecord.Detail = stringBuilder.ToString();
                });
            }

            return await Task.FromResult(apiResult);
        }

        /// <summary>
        /// 紙本確認功能(無紙本)
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [Route("AddNonPaperData")]
        public async Task<ApiResultModelByObject> AddNonPaperData([FromQuery] string apply_number, [FromQuery] string? application_remark)
        {
            ApiResultModelByObject apiResult = new ApiResultModelByObject();

            apiResult.listData = PaperOldDataService.AddNonPaperData(apply_number, application_remark);
            apiResult.rtnSuccess = true;
            InitLogRecord<object>(new(), logRecord =>
            {
                StringBuilder stringBuilder = new();
                stringBuilder.AppendLine($"有無紙本: 無");
                stringBuilder.AppendLine($"申請單號: {apply_number}");
                if (!string.IsNullOrEmpty(application_remark)) stringBuilder.AppendLine($"紙本確認備註: {application_remark}");
                logRecord.Detail = stringBuilder.ToString();
            });

            return await Task.FromResult(apiResult);
        }

        /// <summary>
        /// 有紙本狀態時，修改功能
        /// </summary>
        /// <returns></returns>
        [HttpPut]
        [Route("UpdatePaperData")]
        public async Task<ApiResultModelByObject> UpdatePaperData([FromBody] updatePaperOldData data)
        {
            ApiResultModelByObject apiResult = new ApiResultModelByObject();

            var oldData = PaperOldDataService.GetPaperApplicationDataByApplicationId(data.application_id);

            apiResult.listData = PaperOldDataService.UpdatePaperData(data);
            apiResult.rtnSuccess = true;
            InitLogRecord(data, logRecord =>
            {
                StringBuilder stringBuilder = new();
                stringBuilder.AppendLine($"紙本狀態修改功能(有紙本):");
                if (!string.IsNullOrEmpty(data.application_status)) stringBuilder.AppendLine($"紙本確認狀態:  {PaperBasicDataService.GetTypeString("lib_applicationStatus", data.application_status)}");
                if (!string.IsNullOrEmpty(data.application_remark)) stringBuilder.AppendLine($"紙本確認備註: {data.application_remark}");

                logRecord.Detail = stringBuilder.ToString();

                if (oldData != null)
                {
                    stringBuilder = new();
                    if (!string.IsNullOrEmpty(oldData.application_status)) stringBuilder.AppendLine($"紙本確認狀態:  {PaperBasicDataService.GetTypeString("lib_applicationStatus", oldData.application_status)}");
                    if (!string.IsNullOrEmpty(oldData.application_remarks)) stringBuilder.AppendLine($"紙本確認備註: {oldData.application_remarks}");

                    logRecord.DetailFormer = stringBuilder.ToString();
                }
            });

            return await Task.FromResult(apiResult);
        }


        /// <summary>
        /// 無紙本狀態時，修改功能
        /// </summary>
        /// <returns></returns>
        [HttpPut]
        [Route("UpdateNonPaperData")]
        public async Task<ApiResultModelByObject> UpdateNonPaperData([FromBody] updatePaperOldData data)
        {
            ApiResultModelByObject apiResult = new ApiResultModelByObject();

            var oldData = PaperOldDataService.GetPaperApplicationDataByApplicationId(data.application_id);

            apiResult.listData = PaperOldDataService.UpdateNonPaperData(data);
            apiResult.rtnSuccess = true;
            InitLogRecord(data, logRecord =>
            {
                StringBuilder stringBuilder = new();
                stringBuilder.AppendLine($"紙本狀態修改功能(無紙本):");
                if (!string.IsNullOrEmpty(data.application_status)) stringBuilder.AppendLine($"紙本確認狀態:  {PaperBasicDataService.GetTypeString("lib_applicationStatus", data.application_status)}");
                if (!string.IsNullOrEmpty(data.application_remark)) stringBuilder.AppendLine($"紙本確認備註: {data.application_remark}");

                logRecord.Detail = stringBuilder.ToString();

                if (oldData != null)
                {
                    stringBuilder = new();
                    if (!string.IsNullOrEmpty(oldData.application_status)) stringBuilder.AppendLine($"紙本確認狀態:  {PaperBasicDataService.GetTypeString("lib_applicationStatus", oldData.application_status)}");
                    if (!string.IsNullOrEmpty(oldData.application_remarks)) stringBuilder.AppendLine($"紙本確認備註: {oldData.application_remarks}");

                    logRecord.DetailFormer = stringBuilder.ToString();
                }
            });

            return await Task.FromResult(apiResult);
        }
    }
}
