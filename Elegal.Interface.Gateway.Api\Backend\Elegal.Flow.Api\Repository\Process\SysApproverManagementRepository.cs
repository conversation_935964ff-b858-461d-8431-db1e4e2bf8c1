﻿using Elegal.Interface.Api.Common.Control.Filter;
using Elegal.Interface.Api.Common.Model.ParaModel.FlowApi;
using Elegal.Interface.Api.Common.Model.ViewModel.FlowApi;
using Elegal.Interface.Api.Common.Repository;
using Elegal.Interface.ApiData.Service.Model.ViewModel;
using Elegal.Orm;
using Elegal.Orm.Utils;
using System.Text;

namespace Elegal.Flow.Api.Repository.Process
{
    /// <summary>
    /// 關卡人員管理
    /// </summary>
    public class SysApproverManagementRepository : BaseRepository
    {
        private readonly string likeStr = DbAccess.Database.GetDbType().Equals(DbTypeEnum.PostgreSQL) ? "ilike" : "like";

        #region 查詢列表數據
        /// <summary>
        /// 查詢列表數據
        /// </summary>
        /// <param name="condition"></param>
        /// <returns></returns>
        public IEnumerable<SysApproverManagementModel> GetDataList(SysApproverManagementSeachModel condition)
        {
            StringBuilder sqlBuilder = new();
            List<string> whereList = new();
            condition.LangType = MvcContext.UserInfo.logging_locale.ToString();
            sqlBuilder.Append(@$"SELECT
                                    sam.rowid AS Rowid,
                                    sam.approver_type AS ApproverType,
                                    sam.emp_id AS EmpId,
                                    ISNULL(sam.modify_time,sam.create_time) AS operate_time,
                                    sp1.fun_name AS ApproverManagement,
                                    sp2.fun_name AS Contract,
                                    sp3.fun_name AS Category,
                                    sa.area_name AS AreaName,
                                    fe.entity AS FnpEntity,
                                    ee.deptid AS Deptid,
                                    ee.name AS Name,
                                    ee.name_a AS NameA,
                                    ee.prefix_dial_code_a AS PrefixDialCodeA,
                                    ee.phone_a AS PhoneA,
                                    ee.Status,
                                    sam.site_id_a AS Site,--CR：269+270 當地承辦財務+總部承辦財務添加欄位顯示
                                    sam.exclude_deptid AS ExcludeDeptid,--CR：271 總經理關卡添加查詢欄位
                                    ee2.name AS operate_cuser,
                                    ee2.name_a AS operate_euser
                                FROM (SELECT rowid,approver_type,emp_id,site_id_a,create_time,entity_id,modify_user,create_user,modify_time,contract_fnid,pay_style,exclude_deptid FROM dbo.sys_approver_management) AS sam
                                INNER JOIN (SELECT entity,entity_id,area_id FROM dbo.fnp_entity) AS fe ON fe.entity_id = sam.entity_id
                                LEFT JOIN (SELECT area_id,area_name FROM dbo.sys_area) AS sa ON sa.area_id = fe.area_id
                                LEFT JOIN (SELECT emplid,name,name_a,deptid,TRIM(ISNULL(prefix_dial_code_a,'0000')) AS prefix_dial_code_a,TRIM(ISNULL(phone_a, '')) AS phone_a,CASE WHEN ISNULL(termination,N'') = N'' THEN 1 ELSE 0 END AS Status FROM dbo.ps_sub_ee_lgl_vw_a) AS ee ON ee.emplid = sam.emp_id
                                LEFT JOIN (SELECT emplid,name,name_a FROM dbo.ps_sub_ee_lgl_vw_a) ee2 ON ee2.emplid = ISNULL(sam.modify_user,sam.create_user)
                                LEFT JOIN (SELECT sort_order,func_code,fun_name FROM dbo.sys_parameters WHERE para_code = N'Approvermanagement' AND lang_type = N'{condition.LangType}' AND is_used = 1) AS sp1 ON sp1.func_code = sam.approver_type
                                LEFT JOIN (SELECT sort_order,func_code,fun_name FROM dbo.sys_parameters WHERE para_code = N'contractType' AND lang_type = N'{condition.LangType}' AND is_used = 1) AS sp2 ON sp2.func_code = sam.contract_fnid
                                LEFT JOIN (SELECT sort_order,func_code,fun_name FROM dbo.sys_parameters WHERE para_code = N'accountType' AND lang_type = N'{condition.LangType}' AND is_used = 1) AS sp3 ON sp3.func_code = sam.pay_style");
            if (!string.IsNullOrEmpty(condition.ApproverManagement.Key)) whereList.Add($"sam.approver_type = {DbProperties.Prefix}ApproverManagement");
            if (!string.IsNullOrEmpty(condition.Contract.Key)) whereList.Add($"sam.contract_fnid = {DbProperties.Prefix}Contract");
            if (!string.IsNullOrEmpty(condition.Category.Key)) whereList.Add($"sam.pay_style = {DbProperties.Prefix}Category");
            if (condition.Area.Key != null) whereList.Add($"sa.area_id = {DbProperties.Prefix}AreaId");
            if (!string.IsNullOrEmpty(condition.FnpEntity.Key)) whereList.Add($"sam.entity_id = {DbProperties.Prefix}FnpEntity");
            if (!string.IsNullOrEmpty(condition.Name)) whereList.Add($"(ee.emplid {DbProperties.LikeStr}(N'%{condition.Name}%') OR ee.name {DbProperties.LikeStr}(N'%{condition.Name}%') OR ee.name_a {DbProperties.LikeStr}(N'%{condition.Name}%'))");
            if (string.IsNullOrEmpty(condition.ApproverManagement.Key) && condition.ApproverManagementList.Any())
                whereList.Add($"sam.approver_type IN(N'{string.Join("','", condition.ApproverManagementList)}')");
            //CR：269+270 當地承辦財務+總部承辦財務添加查詢欄位
            if (!string.IsNullOrEmpty(condition.Site.Key)) whereList.Add($"sam.site_id_a = {DbProperties.Prefix}Site");
            //CR：271 總經理關卡添加查詢欄位
            if (!string.IsNullOrEmpty(condition.ExcludeDeptid.Key)) whereList.Add($"sam.exclude_deptid = {DbProperties.Prefix}ExcludeDeptid");

            //拼接查詢條件
            if (whereList.Any()) sqlBuilder.Append($" WHERE {string.Join(" AND ", whereList)}");
            sqlBuilder.Append(" ORDER BY fe.entity,sp1.sort_order,sp2.sort_order,sp3.sort_order,STUFF(sam.emp_id,1,PATINDEX('%[A-z]%',SUBSTRING(sam.emp_id,1,1))-1,'') ASC, LEN(sam.emp_id) DESC,sam.emp_id DESC;");
            var result = DbAccess.Database.SqlQuery<SysApproverManagementModel>(sqlBuilder.ToString(), new
            {
                ApproverManagement = condition.ApproverManagement.Key,
                Contract = condition.Contract.Key,
                Category = condition.Category.Key,
                FnpEntity = condition.FnpEntity.Key,
                AreaId = condition.Area.Key,
                Site = condition.Site.Key,//CR：269+270 當地承辦財務+總部承辦財務添加查詢欄位
                ExcludeDeptid = condition.ExcludeDeptid.Key//CR：271 總經理關卡添加查詢欄位
            });
            return result;
        }
        #endregion

        #region 驗證關卡是否存在相同數據
        /// <summary>
        /// 驗證關卡是否存在相同數據
        /// </summary>
        /// <param name="samvm"></param>
        /// <returns></returns>
        public bool CheckApproverTypeByPara(SysApproverManagementViewModel samvm)
        {
            string checkSql = @"SELECT COUNT(1) FROM dbo.sys_approver_management WHERE approver_type = @approver_type AND emp_id = @emp_id AND entity_id = @entity_id";
            if (!string.IsNullOrEmpty(samvm.ContractFnid)) { checkSql += @" AND contract_fnid = @contract_fnid"; }
            if (!string.IsNullOrEmpty(samvm.PayStyle)) { checkSql += @" AND pay_style = @pay_style"; }
            //特殊處理 -> site_id_a 存在和不存在的數據可以並存
            if (samvm.ApproverType.ToLower() == "ck_local_finance" || samvm.ApproverType.ToLower() == "ck_whq_finance_pro")
            {
                if (!string.IsNullOrEmpty(samvm.SiteIdA)) { checkSql += @" AND site_id_a = @site_id_a"; }
                else { checkSql += @" AND ISNULL(site_id_a,N'') = N''"; }
            }
            //特殊處理 -> exclude_deptid 存在和不存在的數據可以並存
            if (samvm.ApproverType.ToLower() == "ck_ceo")
            {
                if (!string.IsNullOrEmpty(samvm.ExcludeDeptid)) { checkSql += @" AND exclude_deptid = @exclude_deptid"; }
                else { checkSql += @" AND ISNULL(exclude_deptid,N'') = N''"; }
            }

            return Convert.ToInt32(this.NpgsqlSearchBySingleValue(checkSql, new
            {
                approver_type = samvm.ApproverType,
                emp_id = samvm.EmpId,
                entity_id = samvm.EntityId,
                contract_fnid = samvm.ContractFnid,
                pay_style = samvm.PayStyle,
                site_id_a = samvm.SiteIdA,
                exclude_deptid = samvm.ExcludeDeptid
            })) > 0;
        }
        #endregion
    }
}
