﻿using Elegal.Flow.Api.Services.LendAppliction;
using Elegal.Interface.Api.Common.Control.Filter;
using Elegal.Interface.Api.Common.Control.Web;
using Elegal.Interface.Api.Common.FuncHelper;
using Elegal.Interface.Api.Common.Model.ParaModel.FlowApi.LendAppliction;
using Elegal.Interface.Api.Common.Model.ResultModel;
using Elegal.Interface.Api.Common.Model.ResultModel.FlowApi;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;

namespace Elegal.Flow.Api.Controllers.LendAppliction;

/// <summary>
/// 资料查询，我的借出（用户自己的借出）【主表，子表，导出，检视】
/// </summary>
[Route("[controller]")]
[ApiController]
public class LendSearchForUserController : BaseController
{
    /// <summary>
    /// 借出查詢 我的借出，當前借出
    /// </summary>
    /// <param name="paraModel"></param>
    /// <returns></returns>
    [HttpPost]
    [Route("QueryLendForUser")]
    public async Task<ApiResultModelByObject> QueryLendForUser([FromBody] LendForUserPara paraModel)
    {
        ApiResultModelByObject apiResult = new ApiResultModelByObject();
        List<LendUserModel> data = LendSearchForUserService.QueryLendForUser(paraModel, MvcContext.UserInfo);
        apiResult.listData = data;
        apiResult.rtnSuccess = true;

        #region 記錄日誌
        InitLogRecord(paraModel, log =>
            {
                List<string> getLog = new List<string>();
                getLog.Add(string.Format(ActionFilter.GetMultilingualValue("LendSearch_currentUser", true), MvcContext.UserInfo.current_emp));
                if (!string.IsNullOrWhiteSpace(paraModel.ContractNumber))
                {
                    getLog.Add(string.Format(ActionFilter.GetMultilingualValue("LendSearch_contractNumber", true), paraModel.ContractNumber));
                }
                if (!string.IsNullOrWhiteSpace(paraModel.ContractName))
                {
                    getLog.Add(string.Format(ActionFilter.GetMultilingualValue("LendSearch_contractName", true), paraModel.ContractName));
                }
                if (paraModel.LendTakeTime.Start.HasValue || paraModel.LendTakeTime.End.HasValue)
                {
                    string d1 = string.Empty;
                    string d2 = string.Empty;
                    if (paraModel.LendTakeTime.Start.HasValue)
                    {
                        d1 = TimeZoneInfoConvertHelper.ConvertDateByTimeZoneByLocal(paraModel.LendTakeTime.Start.Value, MvcContext.UserInfo.time_zone).ToString("yyyy/MM/dd");
                    }
                    if (paraModel.LendTakeTime.End.HasValue)
                    {
                        d2 = TimeZoneInfoConvertHelper.ConvertDateByTimeZoneByLocal(paraModel.LendTakeTime.End.Value, MvcContext.UserInfo.time_zone).ToString("yyyy/MM/dd");
                    }

                    getLog.Add(string.Format(ActionFilter.GetMultilingualValue("LendSearch_lendTakeTime", true), d1, d2));
                }
                if (paraModel.PickupEmplids.Count > 0)
                {
                    getLog.Add(string.Format(ActionFilter.GetMultilingualValue("LendSearch_pickupEmplids", true), string.Join(",", paraModel.PickupEmplids)));
                }
                if (!string.IsNullOrWhiteSpace(paraModel.LendNumber))
                {
                    getLog.Add(string.Format(ActionFilter.GetMultilingualValue("LendSearch_lendNumber", true), paraModel.LendNumber));
                }
                if (paraModel.LendStatus.Count > 0)
                {
                    getLog.Add(string.Format(ActionFilter.GetMultilingualValue("LendSearch_lendStatus", true), string.Join(","
                                                                                                                            , paraModel.LendStatus.Select(e => {
                                                                                                                                if (e == "02") return "待取件";
                                                                                                                                else return "出借中";
                                                                                                                              })
                                                                                                                          )
                                            )
                              );
                }

                log.Detail = string.Join("\r\n", getLog.Select(s => s));
            });
            #endregion

        return await Task.FromResult<ApiResultModelByObject>(apiResult);
    }

    /// <summary>
    ///  查询纸本借阅详情
    /// </summary>
    /// <param name="paperLendId">借出申請(主表ID)</param>
    /// <returns></returns>
    [HttpGet]
    [Route("QueryPaperLendListForUser")]
    public async Task<ApiResultModelByObject> QueryPaperLendListForUser([FromQuery] int paperLendId)
    {
        ApiResultModelByObject apiResult = new ApiResultModelByObject();

        var data = LendSearchForUserService.QueryPaperLendListForUser(paperLendId, MvcContext.UserInfo.time_zone);
        apiResult.listData = data;
        apiResult.totalCount = data.Count;
        apiResult.rtnSuccess = true;

        return await Task.FromResult<ApiResultModelByObject>(apiResult);
    }

    /// <summary>
    /// 我的借出，當前借出導出
    /// </summary>
    /// <param name="para"></param>
    /// <returns></returns>
    [HttpPost]
    [Route("QueryLendForUserExport")]
    public FileContentResult QueryLendForUserExport([FromBody] LendForUserExportPara para)
    {
        ApiResultModelByObject apiResult = new ApiResultModelByObject();
        var excelBytes = LendSearchForUserService.QueryLendForUserExport(para, MvcContext.UserInfo);
        #region 記錄日誌
        InitLogRecord(para, log =>
        {
            List<string> getLog = new List<string>();
            getLog.Add(string.Format(ActionFilter.GetMultilingualValue("LendSearch_currentUser", true), MvcContext.UserInfo.current_emp));
            if (!string.IsNullOrWhiteSpace(para.ContractNumber))
            {
                getLog.Add(string.Format(ActionFilter.GetMultilingualValue("LendSearch_contractNumber", true), para.ContractNumber));
            }
            if (!string.IsNullOrWhiteSpace(para.ContractName))
            {
                getLog.Add(string.Format(ActionFilter.GetMultilingualValue("LendSearch_contractName", true), para.ContractName));
            }
            if (para.LendTakeTime.Start.HasValue || para.LendTakeTime.End.HasValue)
            {
                string d1 = string.Empty;
                string d2 = string.Empty;
                if (para.LendTakeTime.Start.HasValue)
                {
                    d1 = TimeZoneInfoConvertHelper.ConvertDateByTimeZoneByLocal(para.LendTakeTime.Start.Value, MvcContext.UserInfo.time_zone).ToString("yyyy/MM/dd");
                }
                if (para.LendTakeTime.End.HasValue)
                {
                    d2 = TimeZoneInfoConvertHelper.ConvertDateByTimeZoneByLocal(para.LendTakeTime.End.Value, MvcContext.UserInfo.time_zone).ToString("yyyy/MM/dd");
                }

                getLog.Add(string.Format(ActionFilter.GetMultilingualValue("LendSearch_lendTakeTime", true), d1, d2));
            }
            if (para.PickupEmplids.Count > 0)
            {
                getLog.Add(string.Format(ActionFilter.GetMultilingualValue("LendSearch_pickupEmplids", true), string.Join(",", para.PickupEmplids)));
            }
            if (!string.IsNullOrWhiteSpace(para.LendNumber))
            {
                getLog.Add(string.Format(ActionFilter.GetMultilingualValue("LendSearch_lendNumber", true), para.LendNumber));
            }
            if (para.LendStatus.Count > 0)
            {
                getLog.Add(string.Format(ActionFilter.GetMultilingualValue("LendSearch_lendStatus", true), string.Join(","
                                                                                                                        , para.LendStatus.Select(e => {
                                                                                                                            if (e == "02") return "待取件";
                                                                                                                            else return "出借中";
                                                                                                                        })
                                                                                                                      )
                                        )
                          );
            }

            log.Detail = string.Join("\r\n", getLog.Select(s => s));
        });
        #endregion
        return File(excelBytes, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", MvcContext.UserInfo.current_name + "_当前借出清單" + DateTime.UtcNow.ConvertDateByTimeZoneByUtc(MvcContext.UserInfo.time_zone).ToString("yyyyMMdd") + ".xlsx");
    }

    /// <summary>
    /// 借出信息檢視
    /// </summary>
    /// <param name="lendId"></param>
    /// <returns></returns>
    [HttpGet]
    [Route("QueryLoanDetailsView")]
    public async Task<ApiResultModelByObject> GetLoanDetailsView([FromQuery] int lendId)
    {
        ApiResultModelByObject apiResult = new ApiResultModelByObject();
        var obj= LendSearchForUserService.GetLoanDetailsView(lendId, MvcContext.UserInfo);
        apiResult.listData = obj;
        apiResult.rtnSuccess = true;
        #region 記錄日誌
        InitLogRecord(lendId, log =>
        {
            log.Detail = "申請單號 : " + obj?.PersonnelInfo?.LendNumber ?? lendId.ToString();
        });

        #endregion
        return await Task.FromResult<ApiResultModelByObject>(apiResult);
    }
}
