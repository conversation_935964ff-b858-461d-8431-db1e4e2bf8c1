﻿using Elegal.Flow.Common.Services.FlowStep;
using Elegal.Interface.Api.Common.Control.Web;
using Elegal.Interface.Api.Common.Model.ParaModel.FlowApi.FlowStep;
using Elegal.Interface.Api.Common.Model.ResultModel;
using Elegal.Interface.Api.Common.Model.ResultModel.FlowApi.FlowStep;
using Elegal.Interface.ApiData.Service.Model.DbModel;
using Elegal.Orm;
using Microsoft.AspNetCore.Mvc;
using System.Text;

namespace Elegal.Flow.Api.Controllers
{
    /// <summary>
    /// 申請單簽核流程
    /// </summary>
    [Route("[controller]")]
    [ApiController]
    public class FlowStepController : BaseController
    {
        #region FormApply
        /// <summary>
        /// Approve
        /// </summary>
        [HttpPut]
        [Route("Approve")]
        public async Task<ApiResultModelByObject> FormApply([FromBody] FormApproveParaModel para)
        {
            ApiResultModelByObject apiResult = new();
            FormApproveParaModel oldData = new FormApproveParaModel();
            FlowStepParaModel flowData = FlowStepService.GetApplicationFlowData(para);
            (apiResult.listData, oldData) = FlowStepService.FormApply_ApproveToTransaction(flowData, para, out string errorMsg);
            apiResult.rtnSuccess = true;

            if (!string.IsNullOrEmpty(errorMsg)) apiResult.messageContent.Add(errorMsg);

            InitLogRecord(para, logRecord =>
            {
                StringBuilder stringBuilder = new();
                StringBuilder oldstringBuilder = new();
                if (!string.IsNullOrEmpty(para.apply_number)) stringBuilder.AppendLine($"申請單號: {para.apply_number}");
                if (!string.IsNullOrEmpty(para.step_opinion)) stringBuilder.AppendLine($"簽核意見: {para.step_opinion}");
                
                //logRecord.Detail = stringBuilder.AppendLine(FlowStepService.GetApproveLogStr(flowData, para, oldData)).ToString();
                string newstr = string.Empty;
                string oldstr = string.Empty;
                (newstr, oldstr) = FlowStepService.GetApproveLogStr(flowData, para, oldData);
                if (!string.IsNullOrEmpty(newstr)) stringBuilder.AppendLine(newstr);
                if (!string.IsNullOrEmpty(oldstr))
                {
                    if (!string.IsNullOrEmpty(para.apply_number)) oldstringBuilder.AppendLine($"申請單號: {para.apply_number}");
                    //if (!string.IsNullOrEmpty(para.step_opinion)) stringBuilder.AppendLine($"簽核意見: {para.step_opinion}");
                    oldstringBuilder.AppendLine(oldstr);
                }
                logRecord.Detail = stringBuilder.ToString();
                logRecord.DetailFormer = oldstringBuilder.ToString();
            });

            return await Task.FromResult(apiResult);
        }
        #endregion

        #region FormReject
        /// <summary>
        /// FormReject
        /// </summary>
        [HttpPut]
        [Route("Reject")]
        public async Task<ApiResultModelByObject> FormReject(FormApproveParaModel para)
        {
            ApiResultModelByObject apiResult = new();

            apiResult.listData = FlowStepService.FormApply_RejectToTransaction(para);
            apiResult.rtnSuccess = true;

            InitLogRecord(para, logRecord =>
            {
                StringBuilder stringBuilder = new();
                if (!string.IsNullOrEmpty(para.apply_number)) stringBuilder.AppendLine($"申請單號: {para.apply_number}");
                if (!string.IsNullOrEmpty(para.step_opinion)) stringBuilder.AppendLine($"簽核意見: {para.step_opinion}");
                logRecord.Detail = stringBuilder.ToString();
            });

            return await Task.FromResult(apiResult);
        }
        #endregion

        #region FormWithdraw
        /// <summary>
        /// FormWithdraw
        /// </summary>
        /// <param name="para"></param>
        /// <returns></returns>
        [HttpPut]
        [Route("Withdraw")]
        public async Task<ApiResultModelByObject> FormWithdraw(FormApproveParaModel para)
        {
            ApiResultModelByObject apiResult = new();

            apiResult.listData = FlowStepService.FormApply_WithdrawToTransaction(para);
            apiResult.rtnSuccess = true;

            InitLogRecord(para, logRecord =>
            {
                StringBuilder stringBuilder = new();
                if (!string.IsNullOrEmpty(para.apply_number)) stringBuilder.AppendLine($"申請單號: {para.apply_number}");
                if (!string.IsNullOrEmpty(para.step_opinion)) stringBuilder.AppendLine($"簽核意見: {para.step_opinion}");
                logRecord.Detail = stringBuilder.ToString();
            });

            return await Task.FromResult(apiResult);
        }
        #endregion

        #region FormEnd
        /// <summary>
        /// FormEnd
        /// </summary>
        /// <param name="para"></param>
        /// <returns></returns>
        [HttpPut]
        [Route("End")]
        public async Task<ApiResultModelByObject> FormEnd([FromBody] FormEndParaModel para)
        {
            ApiResultModelByObject apiResult = new();

            //最後請替換為 申請單類型，前台傳遞
            switch (para.apply_type.ToLower())
            {
                case "c":
                case "o":
                    apiResult.listData = FlowStepService.FormApply_EndToTransaction(para);
                    break;
                case "r":
                    apiResult.listData = FlowStepService.FormApply_EndRToTransaction(para);
                    break;
                case "ar":
                    apiResult.listData = FlowStepService.FormApply_EndARToTransaction(para);
                    break;
            }

            apiResult.rtnSuccess = true;

            InitLogRecord(para, logRecord =>
            {
                StringBuilder stringBuilder = new();
                if (!string.IsNullOrEmpty(para.apply_number)) stringBuilder.AppendLine($"申請單號: {para.apply_number}");
                if (!string.IsNullOrEmpty(para.step_opinion)) stringBuilder.AppendLine($"簽核意見: {para.step_opinion}");
                logRecord.Detail = stringBuilder.ToString();
            });

            return await Task.FromResult(apiResult);
        }
        #endregion

        #region FormDelete
        /// <summary>
        /// FormDelete
        /// </summary>
        /// <param name="para"></param>
        /// <returns></returns>
        [HttpPut]
        [Route("Delete")]
        public async Task<ApiResultModelByObject> FormDelete(FormApproveParaModel para)
        {
            ApiResultModelByObject apiResult = new();

            apiResult.listData = FlowStepService.FormApply_DeleteToTransaction(para);
            apiResult.rtnSuccess = true;
            InitLogRecord(para, logRecord =>
            {
                StringBuilder stringBuilder = new();
                if (!string.IsNullOrEmpty(para.apply_number)) stringBuilder.AppendLine($"申請單號: {para.apply_number}");
                if (!string.IsNullOrEmpty(para.step_opinion)) stringBuilder.AppendLine($"簽核意見: {para.step_opinion}");
                logRecord.Detail = stringBuilder.ToString();
            });
            return await Task.FromResult(apiResult);
        }
        #endregion

        #region FormAcknowledge
        /// <summary>
        /// FormAcknowledge
        /// </summary>
        /// <param name="para"></param>
        /// <returns></returns>
        [HttpPut]
        [Route("Acknowledge")]
        public async Task<ApiResultModelByObject> FormAcknowledge(FormApproveParaModel para)
        {
            ApiResultModelByObject apiResult = new();

            apiResult.listData = FlowStepService.FormApply_AcknowledgeToTransaction(para);
            apiResult.rtnSuccess = true;

            InitLogRecord(para, logRecord =>
            {
                StringBuilder stringBuilder = new();
                if (!string.IsNullOrEmpty(para.apply_number)) stringBuilder.AppendLine($"申請單號: {para.apply_number}");
                if (!string.IsNullOrEmpty(para.step_opinion)) stringBuilder.AppendLine($"簽核意見: {para.step_opinion}");
                logRecord.Detail = stringBuilder.ToString();
            });

            return await Task.FromResult(apiResult);
        }
        #endregion

        #region FormReturn
        /// <summary>
        /// FormReturn
        /// </summary>
        /// <param name="para"></param>
        /// <returns></returns>
        [HttpPut]
        [Route("Return")]
        public async Task<ApiResultModelByObject> FormReturn(FormApproveParaModel para)
        {
            ApiResultModelByObject apiResult = new();

            apiResult.listData = FlowStepService.FormApply_ReturnToTransaction(para);
            apiResult.rtnSuccess = true;

            InitLogRecord(para, logRecord =>
            {
                StringBuilder stringBuilder = new();
                if (!string.IsNullOrEmpty(para.apply_number)) stringBuilder.AppendLine($"申請單號: {para.apply_number}");
                if (!string.IsNullOrEmpty(para.step_opinion)) stringBuilder.AppendLine($"簽核意見: {para.step_opinion}");
                logRecord.Detail = stringBuilder.ToString();
            });

            return await Task.FromResult(apiResult);
        }
        #endregion

        #region FormInvite
        /// <summary>
        /// FormInvite
        /// </summary>
        /// <param name="para"></param>
        /// <returns></returns>
        [HttpPut]
        [Route("Invite")]
        public async Task<ApiResultModelByObject> FormInvite(FormInviteParaModel para)
        {
            ApiResultModelByObject apiResult = new();

            List<InviteSignerResultModel> oldInvitee = FlowStepService.GetFormApplyInvitee(para.apply_number);

            apiResult.listData = FlowStepService.FormApply_InviteToTransaction(para);
            apiResult.rtnSuccess = true;
            

            InitLogRecord(para, logRecord =>
            {
                StringBuilder stringBuilder = new();
                if (!string.IsNullOrEmpty(para.apply_number)) stringBuilder.AppendLine($"申請單號: {para.apply_number}");
                if (para.inviteeList != null && para.inviteeList.Count > 0)
                {
                    stringBuilder.AppendLine($"加簽人員: {FlowStepService.GetNameList(para.inviteeList.Select(x => x.invitee_emplid).ToList())}");
                    if (!string.IsNullOrEmpty(para.step_opinion)) stringBuilder.AppendLine($"加簽原因: {para.step_opinion}");
                }
                else
                {
                    stringBuilder.AppendLine($"加簽人員: -");
                }
                logRecord.Detail = stringBuilder.ToString();

                stringBuilder = new();
                if (oldInvitee != null && oldInvitee.Count > 0)
                {
                    if(!string.IsNullOrEmpty(para.apply_number)) stringBuilder.AppendLine($"申請單號: {para.apply_number}");
                    stringBuilder.AppendLine($"加簽人員: {FlowStepService.GetNameList(oldInvitee.Select(x => x.invitee_emplid).ToList())}");
                    if (!string.IsNullOrEmpty(para.step_opinion)) stringBuilder.AppendLine($"加簽原因: {oldInvitee[0].invitee_remark}");
                } 
                logRecord.DetailFormer = stringBuilder.ToString();
            });

            return await Task.FromResult(apiResult);
        }
        #endregion

        #region FormInviteSubmit
        /// <summary>
        /// FormInviteSubmit
        /// </summary>
        /// <param name="para"></param>
        /// <returns></returns>
        [HttpPut]
        [Route("InviteSubmit")]
        public async Task<ApiResultModelByObject> FormInviteSubmit(FormApproveParaModel para)
        {
            ApiResultModelByObject apiResult = new();

            apiResult.listData = FlowStepService.FormApply_InviteSubmitToTransaction(para);
            apiResult.rtnSuccess = true;

            InitLogRecord(para, logRecord =>
            {
                StringBuilder stringBuilder = new();
                if (!string.IsNullOrEmpty(para.apply_number)) stringBuilder.AppendLine($"申請單號: {para.apply_number}");
                if (!string.IsNullOrEmpty(para.step_opinion)) stringBuilder.AppendLine($"簽核意見: {para.step_opinion}");
                logRecord.Detail = stringBuilder.ToString();
            });

            return await Task.FromResult(apiResult);
        }
        #endregion

        #region FormSpecialSubmit
        /// <summary>
        /// FormSpecialSubmit
        /// </summary>
        /// <param name="para"></param>
        /// <returns></returns>
        [HttpPut]
        [Route("SpecialSubmit")]
        public async Task<ApiResultModelByObject> FormSpecialSubmit(FormSpecialSubmitParaModel para)
        {
            ApiResultModelByObject apiResult = new();

            //最後請替換為 申請單類型，前台傳遞
            switch (para.apply_type.ToLower())
            {
                case "c":
                    apiResult.listData = FlowStepService.FormApply_SpecialInviteSubmitByCToTransaction(para);
                    break;
                case "r":
                    apiResult.listData = FlowStepService.FormApply_SpecialInviteSubmitByRToTransaction(para);
                    break;
            }

            apiResult.rtnSuccess = true;

            InitLogRecord(para, logRecord =>
            {
                StringBuilder stringBuilder = new();
                if (!string.IsNullOrEmpty(para.apply_number)) stringBuilder.AppendLine($"申請單號: {para.apply_number}");
                if (!string.IsNullOrEmpty(para.step_opinion)) stringBuilder.AppendLine($"簽核意見: {para.step_opinion}");
                logRecord.Detail = stringBuilder.ToString();
            });

            return await Task.FromResult(apiResult);
        }
        #endregion

        #region FormCopy
        /// <summary>
        /// FormCopy
        /// </summary>
        /// <param name="para"></param>
        /// <returns></returns>
        [HttpPut]
        [Route("FormCopy")]
        public async Task<ApiResultModel> FormCopy([FromBody] FormApproveParaModelForCopy para)
        {
            ApiResultModel apiResult = new();

            apiResult.rtnSuccess = FlowStepService.FormApply_CopyToTransaction(para);

            InitLogRecord(para, logRecord =>
            {
                StringBuilder stringBuilder = new();
                var istemp = DbAccess.Database.ExecuteScalar($"select emplid from ps_sub_ee_lgl_vw_a where emplid ='{para.apply_number_copy}'")?.ToString() ?? "";
                if (string.IsNullOrEmpty(istemp))
                {
                    if (!string.IsNullOrEmpty(para.apply_number_copy)) stringBuilder.AppendLine($"申請單號: {para.apply_number_copy}");
                }
                else
                {
                    stringBuilder.AppendLine($"申請單號: -");
                }
                
                //if (!string.IsNullOrEmpty(para.step_opinion)) stringBuilder.AppendLine($"簽核意見: {para.step_opinion}");
                logRecord.Detail = stringBuilder.ToString();
            });

            return await Task.FromResult(apiResult);
        }
        #endregion

        #region GetFormApplyInvitee
        /// <summary>
        /// GetFormApplyInvitee
        /// </summary>
        /// <param name="apply_number"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetFormApplyInvitee")]
        public async Task<ApiResultModelByObject> GetFormApplyInvitee([FromQuery] string apply_number)
        {
            ApiResultModelByObject apiResult = new();

            apiResult.listData = FlowStepService.GetFormApplyInvitee(apply_number);
            apiResult.rtnSuccess = true;

            return await Task.FromResult(apiResult);
        }
        #endregion

        #region GetFlowHistoryView
        /// <summary>
        /// GetFlowHistoryView
        /// </summary>
        /// <param name="apply_number"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetFlowHistoryView")]
        public async Task<ApiResultModelByObject> GetFlowHistoryView([FromQuery] string apply_number)
        {
            ApiResultModelByObject apiResult = new();

            apiResult.listData = FlowStepService.GetFlowHistoryView(apply_number);
            apiResult.rtnSuccess = true;

            InitLogRecord<object>(new(), LogRecord =>
            {
                LogRecord.Detail = @$"申請單號: {apply_number}";
            });

            return await Task.FromResult(apiResult);
        }
        #endregion

        #region GetFlowHistoryView
        /// <summary>
        /// GetFlowHistoryView
        /// </summary>
        /// <param name="apply_type"></param>
        /// <param name="apply_number"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetFlowStepPreview")]
        public async Task<ApiResultModelByObject> GetFlowStepPreview([FromQuery] string apply_type, string apply_number)
        {
            ApiResultModelByObject apiResult = new();

            apiResult.listData = FlowStepService.GetFlowStepPreview(apply_type, apply_number);
            apiResult.rtnSuccess = true;

            InitLogRecord<object>(new(), LogRecord =>
            {
                //判断是否是暂存单(按查詢當前單號是否是工號，如果是工號則表示當前單據是暫存單)
                var istemp = DbAccess.Database.ExecuteScalar($"select emplid from ps_sub_ee_lgl_vw_a where emplid ='{apply_number}'")?.ToString() ?? "";
                if (string.IsNullOrEmpty(istemp))
                {
                    LogRecord.Detail = @$"申請單號: {apply_number}";
                }
                else
                {
                    LogRecord.Detail = @$"申請單號: -";
                }
                
            });

            return await Task.FromResult(apiResult);
        }
        #endregion

        #region InsertContractHistory
        /// <summary>
        /// InsertContractHistory
        /// </summary>
        /// <param name="para"></param>
        /// <returns></returns>
        [HttpPut]
        [Route("InsertContractHistory")]
        public async Task<ApiResultModelByObject> FormInsertContractHistory(FormSpecialParaModel para)
        {
            ApiResultModelByObject apiResult = new();

            apiResult.listData = FlowStepService.FormApply_InsertContractHistory(para);
            apiResult.rtnSuccess = true;

            return await Task.FromResult(apiResult);
        }
        #endregion

        #region 取得補簽人員 -> 已經不使用了
        /// <summary>
        /// 取得補簽人員 -> 已經不使用了
        /// </summary>
        /// <param name="apply_number"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetSupplementarySignerList")]
        public async Task<ApiResultModelByObject> GetSupplementarySignerList([FromQuery] string apply_number)
        {
            ApiResultModelByObject apiResult = new();

            apiResult.listData = FlowStepService.GetSupplementarySignerList(apply_number, 113);
            apiResult.rtnSuccess = true;

            return await Task.FromResult(apiResult);
        }
        #endregion
    }
}
