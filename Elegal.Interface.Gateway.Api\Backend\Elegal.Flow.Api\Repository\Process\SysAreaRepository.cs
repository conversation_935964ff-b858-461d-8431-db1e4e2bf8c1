﻿using Elegal.Interface.Api.Common.Model.ViewModel.FlowApi;
using Elegal.Interface.ApiData.Service.Model.DbModel;
using Elegal.Orm;

namespace Elegal.Flow.Api.Repository.Process
{
    /// <summary>
    /// 區域維護
    /// </summary>
    public class SysAreaRepository
    {
        #region 獲取區域列表數據
        /// <summary>
        /// 獲取區域列表
        /// </summary>
        /// <returns></returns>
        public IEnumerable<SysAreaModel> GetSysAreaList()
        {
            string sql = @$"select 
                                area_id,
                                area_name,
                                isnull(area.modify_time,area.create_time) operate_time,
                                ee.name operate_cuser,
                                ee.name_a operate_euser
                            from sys_area area
                            left join (select emplid,name,name_a from ps_sub_ee_lgl_vw_a) ee on ee.emplid = isnull(area.modify_user,area.create_user)
                            order by area_name";
            return DbAccess.Database.SqlQuery<SysAreaModel>(sql, new { });
        }
        #endregion
    }
}
