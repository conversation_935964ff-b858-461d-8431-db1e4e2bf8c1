﻿using Elegal.Interface.Api.Common.Model.ParaModel;
using Elegal.Interface.Api.Common.Model.ParaModel.FlowApi;
using Elegal.Interface.Api.Common.Model.ResultModel.FlowApi;
using Elegal.Interface.Api.Common.Model.ViewModel.FlowApi;
using Elegal.Interface.Api.Common.Repository;
using Elegal.Orm;
using System.Text;

namespace Elegal.Flow.Api.Repository.Paper
{
    /// <summary>
    /// 紙本歸還作業
    /// </summary>
    public class PaperReturnJobRepository : BaseRepository
    {
        /// <summary>
        /// 實例化
        /// </summary>
        public PaperReturnJobRepository() : base() { }

        #region 掃碼查詢歸還紙本信息
        /// <summary>
        /// 掃碼查詢歸還紙本信息
        /// </summary>
        /// <param name="paperCode">紙本編號</param>
        /// <param name="langType">語係</param>
        /// <returns></returns>
        public List<PaperReturnJobViewModel> QueryReturnDataByScan(string paperCode, string langType = "ZH-TW")
        {
            string getScanPaperSql = @"
                         SELECT
                         pld.paper_basic_id AS paperBasicID,--紙本基本資料id
                         pld.paper_lend_id AS paperLendID,--紙本借出申請主表id
                         pla.lend_number AS lendNumber,--借出單號
                         pldd.retrieve_number AS retrieveNumber,--調閱單號(如果調閱單號不為空，類型為“調閱”，無就為空)
                         pld.paper_code AS paperCode,--紙本編號
                         REPLACE(REPLACE(fa.contract_number, CHAR(31), ''), CHAR(30), '') AS contractNumber,--合約編號
                         pld.paper_name AS paperName,--紙本名稱
                         (SELECT paper_return_status FROM dbo.paper_basic_data WHERE basic_id = pld.paper_basic_id)AS paperReturnStatus,--紙本歸還現狀(只做修改時的拼接，不需要顯示在頁面上)
                         pla.lend_handler_emplid AS lendHandlerEmplid,--借出人工號
                         ee.name AS cName,--借出人中文名稱
                         ee.name_a AS eName,--借出人英文名稱
                         (CASE pld.pickup_status WHEN N'02' THEN pld.pickup_time ELSE pld.actual_pickup_time END) AS actualPickupTime,--實際取件日期(借出日期)
                         pld.should_return_time AS shouldReturnTime,--應歸還日期
                         pld.actual_return_time AS actualReturnTime--實際歸還日
                         FROM (SELECT paper_basic_id,paper_lend_id,paper_demand_id,contract_number,contract_name,paper_code,paper_name,actual_pickup_time,should_return_time,actual_return_time,pickup_status,pickup_time FROM paper_lending_detail WHERE is_pickup_lend = 1 AND (ISNULL(is_return, N'') = N'' OR is_return <> 1) AND paper_code = N'' + @paperCode) AS pld
                         INNER JOIN (SELECT lend_id,lend_number,lend_handler_emplid FROM paper_lending_application WHERE lend_status = N'03') AS pla ON pla.lend_id = pld.paper_lend_id
                         INNER JOIN dbo.paper_lending_demand AS pldd ON pldd.demand_id = pld.paper_demand_id
                         INNER JOIN (SELECT emplid,name,name_a FROM dbo.ps_sub_ee_lgl_vw_a) AS ee ON pla.lend_handler_emplid = ee.emplid
                         LEFT JOIN (SELECT basic_id,(SELECT apply_number FROM dbo.paper_application_data WHERE application_id = paper_applica_id) AS apply_number FROM dbo.paper_basic_data) AS pbd ON pbd.basic_id = pld.paper_basic_id
                         LEFT JOIN (SELECT apply_number,contract_number,contract_name FROM dbo.V_GetApplicationSearch) AS fa ON fa.apply_number = pbd.apply_number
                         ORDER BY pla.lend_number,pld.paper_code ASC;";
            return NpgsqlSearchByList<PaperReturnJobViewModel>(getScanPaperSql, new
            {
                paperCode,
                langType
            });
        }
        #endregion

        #region 查詢歸還紙本信息
        /// <summary>
        /// 查詢歸還紙本信息
        /// </summary>
        /// <param name="prpm">查詢參數集合</param>
        /// <param name="langType">語係</param>
        /// <returns></returns>
        public List<PaperReturnJobViewModel> QueryReturnDataByPara(PaperReturnParaModel prpm, string langType = "ZH-TW")
        {
            string getBatchPaperSql = @"
                         SELECT
                         pld.paper_basic_id AS paperBasicID,--紙本基本資料id
                         pld.paper_lend_id AS paperLendID,--紙本借出申請主表id
                         pla.lend_number AS lendNumber,--借出單號
                         (SELECT retrieve_number FROM dbo.paper_lending_demand WHERE demand_id = paper_demand_id) AS retrieveNumber,--調閱單號(如果調閱單號不為空，類型為“調閱”，無就為空)
                         pld.paper_code AS paperCode,--紙本編號
                         REPLACE(REPLACE(fa.contract_number, CHAR(31), ''), CHAR(30), '') AS contractNumber,--合約編號
                         pld.paper_name AS paperName,--紙本名稱
                         (SELECT paper_return_status FROM dbo.paper_basic_data WHERE basic_id = pld.paper_basic_id)AS paperReturnStatus,--紙本歸還現狀(只做修改時的拼接，不需要顯示在頁面上)
                         pla.lend_handler_emplid AS lendHandlerEmplid,--借出人工號
                         ee.name AS cName,--借出人中文名稱
                         ee.name_a AS eName,--借出人英文名稱
                         (CASE pld.pickup_status WHEN N'02' THEN pld.pickup_time ELSE pld.actual_pickup_time END) AS actualPickupTime,--實際取件日期(借出日期)
                         pld.should_return_time AS shouldReturnTime,--應歸還日期
                         pld.actual_return_time AS actualReturnTime--實際歸還日
                         FROM (SELECT paper_basic_id,paper_lend_id,paper_code,paper_name,pickup_status,pickup_time,actual_pickup_time,should_return_time,actual_return_time,paper_demand_id FROM dbo.paper_lending_detail WHERE is_pickup_lend = 1 AND (ISNULL(is_return, N'') = N'' OR is_return <> 1)) AS pld
                         INNER JOIN (SELECT lend_id,lend_number,lend_handler_emplid FROM dbo.paper_lending_application WHERE lend_status = N'03') AS pla ON pla.lend_id = pld.paper_lend_id
                         LEFT JOIN (SELECT emplid,name,name_a FROM dbo.ps_sub_ee_lgl_vw_a) AS ee ON pla.lend_handler_emplid = ee.emplid
                         LEFT JOIN (SELECT basic_id,(SELECT apply_number FROM dbo.paper_application_data WHERE application_id = paper_applica_id) AS apply_number FROM dbo.paper_basic_data) AS pbd ON pbd.basic_id = pld.paper_basic_id
                         LEFT JOIN (SELECT apply_number,contract_number,contract_name FROM dbo.V_GetApplicationSearch) AS fa ON fa.apply_number = pbd.apply_number
                         WHERE 1 = 1";
            if (!string.IsNullOrEmpty(prpm.lendNumber))
            { getBatchPaperSql += @" AND pla.lend_number LIKE CONCAT(N'%', @lendNumber, N'%') "; }
            if (prpm.actualPickupTimeStart.HasValue)
            { getBatchPaperSql += @" AND CONVERT(date,(CASE pld.pickup_status WHEN N'02' THEN pld.pickup_time ELSE pld.actual_pickup_time END)) >= CONVERT(date,@actualPickupTimeStart) "; }
            if (prpm.actualPickupTimeEnd.HasValue)
            { getBatchPaperSql += @" AND CONVERT(date,(CASE pld.pickup_status WHEN N'02' THEN pld.pickup_time ELSE pld.actual_pickup_time END)) <= CONVERT(date,@actualPickupTimeEnd) "; }
            if (!string.IsNullOrEmpty(prpm.paperCode))
            { getBatchPaperSql += @" AND (pld.paper_code LIKE CONCAT(N'%', @paperCode, N'%') OR fa.contract_number LIKE CONCAT(N'%', @paperCode, N'%')) "; }
            if (!string.IsNullOrEmpty(prpm.empValue))
            { getBatchPaperSql += @" AND (ee.emplid LIKE CONCAT(N'%', @empValue, N'%') OR ee.name LIKE CONCAT(N'%', @empValue, N'%') OR ee.name_a LIKE CONCAT(N'%', @empValue, N'%')) "; }
            if (!string.IsNullOrEmpty(prpm.paperName))
            { getBatchPaperSql += @" AND (pld.paper_name LIKE CONCAT(N'%', @paperName, N'%') OR fa.contract_name LIKE CONCAT(N'%', @paperName, N'%')) "; }
            getBatchPaperSql += @" ORDER BY pla.lend_number,pld.paper_code ASC; ";
            return NpgsqlSearchByList<PaperReturnJobViewModel>(getBatchPaperSql, new
            {
                prpm.lendNumber,
                prpm.actualPickupTimeStart,
                prpm.actualPickupTimeEnd,
                prpm.paperCode,
                prpm.empValue,
                prpm.paperName,
                langType
            });
        }
        #endregion

        #region 查詢歸還的lendid是否存在非借出單號
        /// <summary>
        /// 查詢歸還的lendid是否存在非借出單號
        /// </summary>
        /// <param name="listLendID"></param>
        /// <returns></returns>
        public bool CheckLendReturnData(List<int> listLendID)
        {
            string getLendReturnSql = @"
                         SELECT COUNT(lend_id) FROM dbo.paper_lending_application AS pla WHERE lend_status <> N'03' AND
                         EXISTS(SELECT 1 FROM (SELECT value AS lend_id FROM STRING_SPLIT(@lendIds, ',')) AS li WHERE li.lend_id = pla.lend_id)";

            return Convert.ToInt32(NpgsqlSearchBySingleValue(getLendReturnSql, new { lendIds = string.Join(',', listLendID.Select(s => s)) })) > 0;
        }
        #endregion

        #region 歸還作業
        /// <summary>
        /// 歸還作業
        /// </summary>
        /// <param name="listPrjpm">操作對象</param>
        /// <param name="emplid">操作者工號</param>
        /// <param name="context"></param>
        /// <returns></returns>
        public bool ReturnLendPaperJob(List<PaperReturnJobParaModel> listPrjpm, string emplid, IDbContext context)
        {
            StringBuilder sbSql = new StringBuilder();
            //循環遍歷數據，操作資料庫
            foreach (PaperReturnJobParaModel prjpm in listPrjpm)
            {
                //根據歸還類型修改紙本明細數據
                sbSql.AppendFormat(@"
                         UPDATE dbo.paper_basic_data SET 
                         paper_entry_status = N'{0}',--入庫狀態，參數表 para_code = lib_paperEntryStatus
                         paper_return_status = N'{1}',--紙本歸還現狀，可多值，參數表 para_code = lib_returnStatus
                         paper_remarks = CASE WHEN ISNULL(paper_remarks,N'') = N'' THEN CONCAT(paper_remarks,N'{2}') ELSE CONCAT(paper_remarks,N';',N'{2}') END,--備註
                         modify_user = N'{3}', modify_time = getutcdate()
                         WHERE basic_id = {4};", prjpm.paperEntryStatus, prjpm.newReturnStatus, prjpm.paperRemark, emplid, prjpm.paperBasicID).AppendLine();
                //如果遺失日期不為最小值，則需要進行修改
                if (prjpm.paperLostTime != DateTime.MinValue)
                {
                    sbSql.AppendFormat(@"
                         UPDATE dbo.paper_basic_data SET
                         lost_time = N'{0}',--遺失日期
                         modify_user = N'{1}', modify_time = getutcdate()
                         WHERE basic_id = {2};", prjpm.paperLostTime, emplid, prjpm.paperBasicID).AppendLine();
                }
                //修改借出申請中對應數據的紙本狀態
                sbSql.AppendFormat(@"
                         UPDATE dbo.paper_lending_detail SET
                         is_return = 1,--已經歸還
                         lend_return_status = N'{3}',--紙本歸還狀態，可多值，參數表 para_code = lib_returnStatus
                         actual_return_time = getutcdate(),--實際歸還時間為當前時間
                         overdue_day = CASE WHEN CONVERT(date,should_return_time) < CONVERT(date,getutcdate()) THEN datediff(DAY,should_return_time,getutcdate()) ELSE 0 END,--逾期天數為實際歸還時間-應歸還天數
                         modify_user = N'{0}',modify_time = getutcdate()
                         WHERE paper_lend_id = {1} AND paper_basic_id = {2};", emplid, prjpm.paperLendID, prjpm.paperBasicID, prjpm.paperReturnStatus).AppendLine();
                //修改借出申請單中其他說明/備註欄位
                sbSql.AppendFormat(@"
                         UPDATE dbo.paper_lending_demand SET
                         retrieve_reason = CASE WHEN ISNULL(retrieve_reason,N'') = N'' THEN CONCAT(retrieve_reason,N'{0}') ELSE CONCAT(retrieve_reason,N';',N'{0}') END,--備註
                         modify_user = N'{1}', modify_time = getutcdate()
                         WHERE paper_lend_id = {2};", prjpm.paperReturnName, emplid, prjpm.paperLendID).AppendLine();
                //插入歷程資料
                sbSql.AppendFormat(@"INSERT INTO dbo.paper_history_data(paper_basic_id, paper_entry_status, course_emplid, paper_remarks, borrow_applynumber, create_user) VALUES({0}, N'{1}', N'{2}', N'{3}', N'{4}', N'{2}');", prjpm.paperBasicID, prjpm.paperEntryStatus, emplid, prjpm.paperRemark, prjpm.lendNumber, emplid).AppendLine();
            }
            sbSql.AppendLine(@"SELECT @@ROWCOUNT;");
            return ExecuteCommandToTransaction(sbSql.ToString(), context) > 0;
        }
        #endregion

        #region 獲取是否存在借出的紙本數據
        /// <summary>
        /// 獲取是否存在借出的紙本數據(實際借出的數據)
        /// </summary>
        /// <param name="lendID">主表id</param>
        /// <param name="context"></param>
        /// <returns></returns>
        public bool HasOnLendData(int lendID, IDbContext context)
        {
            string strSql = @"SELECT COUNT(paper_basic_id) FROM dbo.paper_lending_detail WHERE paper_lend_id = @lendID AND is_pickup_lend = 1 AND (is_return = 0 OR ISNULL(is_return,'') = '');";
            return Convert.ToInt32(NpgsqlSearchBySingleValueToTransaction(strSql, context, new { lendID })) <= 0;
        }
        #endregion

        #region 修改申請單主表狀態
        /// <summary>
        /// 修改申請單主表狀態
        /// </summary>
        /// <param name="lendID">主表id</param>
        /// <param name="emplID">操作者工號</param>
        /// <param name="context"></param>
        /// <returns></returns>
        public bool UpdatePaperLendStatus(int lendID, string emplID, IDbContext context)
        {
            string strSql = @"
                         UPDATE dbo.paper_lending_application SET lend_status = N'04',modify_user = @emplID,modify_time = getutcdate() WHERE lend_id = @lendID;
                         SELECT @@ROWCOUNT;";
            return ExecuteCommandToTransaction(strSql, context, new { lendID, emplID }) > 0;
        }
        #endregion

        #region 插入已結案後的歷史數據
        /// <summary>
        /// 插入已結案後的歷史數據
        /// </summary>
        /// <param name="lendID">主表id</param>
        /// <param name="emplID">操作者工號</param>
        /// <param name="context"></param>
        /// <returns></returns>
        public bool InsertLendHistory(int lendID, string emplID, IDbContext context)
        {
            string strSql = @"
                         INSERT INTO dbo.paper_lending_history(
                         lend_number,lend_fill_emplid,lend_fill_deptid,lend_fill_bu,lend_fill_bg,
                         lend_handler_emplid,lend_handler_deptid,lend_handler_bu,lend_handler_bg,
                         application_time,lend_status,void_reason,
                         retrieve_number,borrow_days,demand_reason,retrieve_reason,
                         paper_basic_id,paper_code,paper_name,contract_number,contract_name,
                         is_return,should_return_time,actual_return_time,pickup_status,pickup_time,
                         consignment_number,pickup_emplid,actual_pickup_time,overdue_day,actual_borrow_days,
                         loan_due_date,create_user,is_pickup_lend,lend_return_status)
                         SELECT
                         pla.lend_number,pla.lend_fill_emplid,pla.lend_fill_deptid,pla.lend_fill_bu,pla.lend_fill_bg,
                         pla.lend_handler_emplid,pla.lend_handler_deptid,pla.lend_handler_bu,pla.lend_handler_bg,
                         pla.application_time,pla.lend_status,pla.void_reason,
                         pldd.retrieve_number, pldd.borrow_days,pldd.demand_reason,pldd.retrieve_reason,
                         pld.paper_basic_id,pld.paper_code,pld.paper_name,pld.contract_number,pld.contract_name,
                         pld.is_return,pld.should_return_time,pld.actual_return_time,pld.pickup_status,pld.pickup_time,
                         pld.consignment_number,pld.pickup_emplid,pld.actual_pickup_time,pld.overdue_day,pld.actual_borrow_days,
                         pld.loan_due_date,@emplID AS create_user,pld.is_pickup_lend,lend_return_status
                         FROM (SELECT paper_demand_id,paper_lend_id,paper_basic_id, paper_code, paper_name, contract_number, contract_name, is_return, should_return_time, actual_return_time, pickup_status, pickup_time, consignment_number, pickup_emplid, actual_pickup_time, overdue_day, actual_borrow_days, loan_due_date,is_pickup_lend,lend_return_status FROM dbo.paper_lending_detail WHERE paper_lend_id = @lendID) AS pld
                         INNER JOIN dbo.paper_lending_application AS pla ON pla.lend_id = pld.paper_lend_id 
                         INNER JOIN dbo.paper_lending_demand AS pldd ON pldd.demand_id = pld.paper_demand_id;";
            return ExecuteCommandToTransaction(strSql, context, new { lendID, emplID }) > 0;
        }
        #endregion

        #region 結案郵件 已經作廢使用，後期看情況剔除

        #region 發送人員

        #region 根據 lendid 獲取需要發送揭露人員(多個人員，已拼接為一筆數據，只發送一封郵件)
        /// <summary>
        /// 根據 lendid 獲取需要發送揭露人員(多個人員)
        /// </summary>
        /// <param name="lendID">借出申請單id</param>
        /// <returns></returns>
        public List<LendClosedMailModel> GetLendClosedExposeData(int lendID)
        {
            string getLendClosedExposeSql = @"
                         SELECT
                         STRING_AGG(CONCAT(name,N'(',name_a,N')'),N'、') AS oCEname,
                         STRING_AGG(email_address_a,N';') AS oEmail
                         FROM (
                         SELECT DISTINCT oee.name,oee.name_a,oee.email_address_a FROM (
                         SELECT retrieve_number FROM paper_lending_history
                         WHERE lend_status = N'04'
                         AND lend_number = (SELECT lend_number FROM paper_lending_application WHERE lend_status = N'04' AND lend_id = @lendID)
                         ) AS plh
                         INNER JOIN (SELECT retrieve_number,empid FROM v_getotherretrievenumber) AS orv ON orv.retrieve_number = plh.retrieve_number
                         LEFT JOIN (SELECT emplid,name,name_a,email_address_a FROM ps_sub_ee_lgl_vw_a) AS oee ON oee.emplid = orv.empid
                         ) AS cmData;";
            return NpgsqlSearchByList<LendClosedMailModel>(getLendClosedExposeSql, new
            {
                lendID
            });
        }
        #endregion

        #region 根據 lendid 獲取需要發送揭露人員(單一經辦人)
        /// <summary>
        /// 根據 lendid 獲取需要發送揭露人員(單一經辦人)
        /// </summary>
        /// <param name="lendID"></param>
        /// <returns></returns>
        public List<LendClosedMailModel> GetLendClosedHandleData(int lendID)
        {
            string getLendClosedHandleSql = @"
                         SELECT
                         STRING_AGG(CONCAT(name,N'(',name_a,N')'),N'、') AS oCEname,
                         STRING_AGG(email_address_a,N';') AS oEmail
                         FROM (
                         SELECT DISTINCT hee.name,hee.name_a,hee.email_address_a FROM (
                         SELECT lend_handler_emplid FROM paper_lending_history
                         WHERE lend_status = N'04'
                         AND lend_number = (SELECT lend_number FROM paper_lending_application WHERE lend_status = N'04' AND lend_id = @lendID)
                         ) AS plh
                         LEFT JOIN (SELECT emplid,name,name_a,email_address_a FROM ps_sub_ee_lgl_vw_a) AS hee ON hee.emplid = plh.lend_handler_emplid
                         ) AS cmData;";

            return NpgsqlSearchByList<LendClosedMailModel>(getLendClosedHandleSql, new
            {
                lendID
            });
        }
        #endregion

        #endregion

        #region 根據 lendID 獲取 填單人、填單人代理人、經辦人郵箱(抄送人員)
        /// <summary>
        /// 根據 lendID 獲取 填單人、填單人代理人、經辦人郵箱
        /// </summary>
        /// <param name="lendID">借出申請單id</param>
        /// <returns></returns>
        public string GetLendClosedCc(int lendID)
        {
            string getCcSql = @"
                         SELECT STRING_AGG(cemail, ';') AS ccEmail FROM (
                         --填單人郵箱
                         SELECT (SELECT email_address_a FROM ps_sub_ee_lgl_vw_a AS fee WHERE fee.emplid = plh.lend_fill_emplid) AS cemail FROM paper_lending_history AS plh
                         WHERE lend_status = N'04'
                         AND lend_number = (SELECT lend_number FROM paper_lending_application WHERE lend_status = N'04' AND lend_id = @lendID)
                         UNION
                         --填單人代理人郵箱
                         SELECT (SELECT email_address_a FROM ps_sub_ee_lgl_vw_a AS faee WHERE faee.emplid = sa.agent_empid) AS cemail FROM (SELECT agent_empid,auth_empid FROM sys_agent WHERE CONVERT(date,start_time) <= CONVERT(date,getutcdate()) AND CONVERT(date,end_time) >= CONVERT(date,getutcdate())) AS sa
                         INNER JOIN (
                         SELECT lend_fill_emplid FROM paper_lending_history AS plh
                         WHERE lend_status = N'04'
                         AND lend_number = (SELECT lend_number FROM paper_lending_application WHERE lend_status = N'04' AND lend_id = @lendID)
                         ) plh ON sa.auth_empid = plh.lend_fill_emplid
                         UNION
                         --經辦人郵箱
                         SELECT (SELECT email_address_a FROM ps_sub_ee_lgl_vw_a AS hee WHERE hee.emplid = plh.lend_handler_emplid) AS cemail FROM paper_lending_history AS plh
                         WHERE lend_status = N'04'
                         AND lend_number = (SELECT lend_number FROM paper_lending_application WHERE lend_status = N'04' AND lend_id = @lendID)
                         ) AS ccData
                         WHERE cemail <> N'NULL';";
            return NpgsqlSearchBySingle<string>(getCcSql, new
            {
                lendID
            });
        }
        #endregion

        #region 根據 lendID 獲取 借出單號，申請日期，其他申請單號，經辦人(總數據)
        /// <summary>
        /// 根據 lendID 獲取 借出單號，申請日期，其他申請單號，經辦人
        /// </summary>
        /// <param name="lendID">借出申請單id</param>
        /// <returns></returns>
        public LendClosedMailModel GetLendClosedData(int lendID)
        {
            string getLendClosedSql = @"
                         SELECT lend_number,application_time,retrieve_number,hee.name AS hCName,hee.name_a AS hEName,hee.email_address_a AS hEmail FROM (
                         SELECT DISTINCT lend_number,application_time,retrieve_number,lend_handler_emplid FROM paper_lending_history
                         WHERE lend_status = N'04'
                         AND lend_number = (SELECT lend_number FROM paper_lending_application WHERE lend_status = N'04' AND lend_id = @lendID)
                         ) AS plh
                         LEFT JOIN (SELECT emplid,name,name_a,email_address_a FROM ps_sub_ee_lgl_vw_a) AS hee ON hee.emplid = plh.lend_handler_emplid;";
            return NpgsqlSearchBySingle<LendClosedMailModel>(getLendClosedSql, new
            {
                lendID
            });
        }
        #endregion

        #region 根據 lendID 獲取 借出單號對應的數據集合(只獲取已經借出的明細數據)
        /// <summary>
        /// 根據 lendID 獲取 借出單號對應的數據集合
        /// </summary>
        /// <param name="lendID">借出申請單id</param>
        /// <returns></returns>
        public List<LendClosedMailModel> GetLendClosedDetailData(int lendID)
        {
            string getLendClosedDetailSql = @"
                         SELECT DISTINCT history_id,fa.contract_number,paper_code,paper_name,
                         (SELECT STRING_AGG(fun_name, ',<br/>') FROM sys_parameters WHERE para_code = N'lib_returnStatus' AND lang_type = N'ZH-TW' AND func_code IN(SELECT value  FROM STRING_SPLIT(lend_return_status, ','))) AS returnStatusName,--中文狀態
                         (SELECT STRING_AGG(fun_name, ',<br/>') FROM sys_parameters WHERE para_code = N'lib_returnStatus' AND lang_type = N'EN-US' AND func_code IN(SELECT value  FROM STRING_SPLIT(lend_return_status, ','))) AS returnStatusEName,--英文狀態
                         actual_return_time FROM (SELECT history_id,paper_code,paper_name,paper_basic_id,actual_return_time,lend_return_status FROM paper_lending_history WHERE lend_status = N'04' AND is_pickup_lend = 1 AND lend_number = (SELECT lend_number FROM paper_lending_application WHERE lend_status = N'04' AND lend_id = @lendID)) as plh
                         LEFT JOIN (SELECT basic_id,(SELECT apply_number FROM paper_application_data WHERE application_id = paper_applica_id) AS apply_number FROM paper_basic_data) AS pbd ON pbd.basic_id = plh.paper_basic_id
                         LEFT JOIN (SELECT apply_number,contract_number FROM V_GetApplicationSearch) AS fa ON fa.apply_number = pbd.apply_number;";
            return NpgsqlSearchByList<LendClosedMailModel>(getLendClosedDetailSql, new
            {
                lendID
            });
        }
        #endregion

        #region 根據 lendID 獲取借出單號
        /// <summary>
        /// 根據 lendID 獲取借出單號
        /// </summary>
        /// <param name="lendID">借出申請單id</param>
        /// <returns></returns>
        public string GetLendNumber(int lendID)
        {
            return (string)this.NpgsqlSearchBySingleValue(@"SELECT lend_number FROM dbo.paper_lending_application WHERE lend_status = N'04' AND lend_id = @lendID;", new { lendID = lendID });
        }
        #endregion

        #endregion
    }
}
