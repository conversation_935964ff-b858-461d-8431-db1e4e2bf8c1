﻿using Elegal.Flow.Api.Services.LendAppliction;
using Elegal.Interface.Api.Common.Control.Filter;
using Elegal.Interface.Api.Common.Control.Web;
using Elegal.Interface.Api.Common.FuncHelper;
using Elegal.Interface.Api.Common.Model.ParaModel.FlowApi.LendAppliction;
using Elegal.Interface.Api.Common.Model.ResultModel;
using Microsoft.AspNetCore.Mvc;
using System.Text;

namespace Elegal.Flow.Api.Controllers.LendAppliction;

/// <summary>
/// 资料查询，借出中查询（管理员角色）【主表，子表，导出，检视】
/// </summary>
[Route("[controller]")]
[ApiController]
public class LendSearchForAdminController : BaseController
{
    /// <summary>
    /// 资料查询，借出中查询
    /// </summary>
    /// <param name="para"></param>
    /// <returns></returns>
    [HttpPost]
    [Route("QueryLendForAdmin")]
    public async Task<ApiResultModelByObject> QueryLendForAdmin(LendForAdminPara para)
    {
        ApiResultModelByObject apiResult = new ApiResultModelByObject();
        var (data, count) = LendSearchForAdminService.QueryLendForAdmin(para, MvcContext.UserInfo);
        apiResult.listData = data;
        apiResult.totalCount = count;   
        apiResult.rtnSuccess = true;
        #region 記錄日誌
        InitLogRecord(para, log =>
        {
            StringBuilder logstrb = new StringBuilder();
            if (!string.IsNullOrWhiteSpace(para.ContractNumber))
            {
                logstrb.AppendLine(string.Format(ActionFilter.GetMultilingualValue("LendSearch_contractNumber", true), para.ContractNumber));
            }
            if (!string.IsNullOrWhiteSpace(para.ContractName))
            {
                logstrb.AppendLine(string.Format(ActionFilter.GetMultilingualValue("LendSearch_contractName", true), para.ContractName));
            }
            if (para.LendFillEmplid.Count > 0)
            {
                string names = string.Join(",", para.LendFillEmplid.Select(e => e.Emplid));
                logstrb.AppendLine(string.Format(ActionFilter.GetMultilingualValue("LendSearch_lendFillEmplid", true), names));
            }
            if (para.LendHanderEmplid.Count > 0)
            {
                string names = string.Join(",", para.LendHanderEmplid.Select(e => e.Emplid));
                logstrb.AppendLine(string.Format(ActionFilter.GetMultilingualValue("LendSearch_lendHanderEmplid", true), names));
            }

            if (!string.IsNullOrWhiteSpace(para.LendNumber))
            {
                logstrb.AppendLine(string.Format(ActionFilter.GetMultilingualValue("LendSearch_lendNumber", true), para.LendNumber));
            }
            if (para.LendStatus.Count > 0)
            {
                logstrb.AppendLine(string.Format(ActionFilter.GetMultilingualValue("LendSearch_lendStatus", true), string.Join(",", para.LendStatus.Select(e => e.Value))));
            }

            if (para.PickupTime.Start.HasValue || para.PickupTime.End.HasValue) {
                string d1 = string.Empty;
                string d2 = string.Empty;
                if (para.PickupTime.Start.HasValue)
                {
                    d1 = TimeZoneInfoConvertHelper.ConvertDateByTimeZoneByLocal(para.PickupTime.Start.Value, MvcContext.UserInfo.time_zone).ToString("yyyy/MM/dd");
                }
                if (para.PickupTime.End.HasValue)
                {
                    d2 = TimeZoneInfoConvertHelper.ConvertDateByTimeZoneByLocal(para.PickupTime.End.Value, MvcContext.UserInfo.time_zone).ToString("yyyy/MM/dd");
                }

                logstrb.AppendLine(string.Format(ActionFilter.GetMultilingualValue("LendSearch_lendTakeTime", true), d1, d2));
            }

            if (para.ShouldReturnTime.Start.HasValue || para.ShouldReturnTime.End.HasValue)
            {
                string d1 = string.Empty;
                string d2 = string.Empty;
                if (para.ShouldReturnTime.Start.HasValue)
                {
                    d1 = TimeZoneInfoConvertHelper.ConvertDateByTimeZoneByLocal(para.ShouldReturnTime.Start.Value, MvcContext.UserInfo.time_zone).ToString("yyyy/MM/dd");
                }
                if (para.ShouldReturnTime.End.HasValue)
                {
                    d2 = TimeZoneInfoConvertHelper.ConvertDateByTimeZoneByLocal(para.ShouldReturnTime.End.Value, MvcContext.UserInfo.time_zone).ToString("yyyy/MM/dd");
                }

                logstrb.AppendLine(string.Format(ActionFilter.GetMultilingualValue("LendSearch_ShouldReturnTime", true), d1, d2));
            }


            log.Detail = logstrb.ToString();
        });
        #endregion

        return await Task.FromResult<ApiResultModelByObject>(apiResult);
    }

    /// <summary>
    ///  查询纸本借阅详情(管理員) (管理員查詢子表)
    /// </summary>
    /// <param name="paperLendId">借出申請(主表ID)</param>
    /// <returns></returns>
    [HttpGet]
    [Route("QueryPaperLendingDetailListForAdmin")]
    public async Task<ApiResultModelByObject> QueryPaperLendingDetailListForAdmin([FromQuery] int paperLendId)
    {
        ApiResultModelByObject apiResult = new ApiResultModelByObject();
        var data = LendSearchForAdminService.QueryPaperLendingDetailListForAdmin(paperLendId, MvcContext.UserInfo);
        apiResult.listData = data;
        apiResult.totalCount = data.Count;
        apiResult.rtnSuccess = true;

        return await Task.FromResult<ApiResultModelByObject>(apiResult);
    }

    /// <summary>
    /// 资料查询，借出中查询 导出
    /// </summary>
    /// <param name="para"></param>
    /// <returns></returns>
    [HttpPost]
    [Route("QueryLendForAdminExport")]
    public FileContentResult QueryLendForAdminExport([FromBody] LendForAdminExportPara para)
    {
        ApiResultModelByObject apiResult = new ApiResultModelByObject();
        var excelBytes = LendSearchForAdminService.QueryLendForAdminExport(para, MvcContext.UserInfo);
        #region 記錄日誌
        InitLogRecord(para, log =>
        {
            StringBuilder logstrb = new StringBuilder();
            if (!string.IsNullOrWhiteSpace(para.ContractNumber))
            {
                logstrb.AppendLine(string.Format(ActionFilter.GetMultilingualValue("LendSearch_contractNumber", true), para.ContractNumber));
            }
            if (!string.IsNullOrWhiteSpace(para.ContractName))
            {
                logstrb.AppendLine(string.Format(ActionFilter.GetMultilingualValue("LendSearch_contractName", true), para.ContractName));
            }
            if (para.LendFillEmplid.Count > 0)
            {
                string names = string.Join(",", para.LendFillEmplid.Select(e => e.Emplid));
                logstrb.AppendLine(string.Format(ActionFilter.GetMultilingualValue("LendSearch_lendFillEmplid", true), names));
            }
            if (para.LendHanderEmplid.Count > 0)
            {
                string names = string.Join(",", para.LendHanderEmplid.Select(e => e.Emplid));
                logstrb.AppendLine(string.Format(ActionFilter.GetMultilingualValue("LendSearch_lendHanderEmplid", true), names));
            }

            if (!string.IsNullOrWhiteSpace(para.LendNumber))
            {
                logstrb.AppendLine(string.Format(ActionFilter.GetMultilingualValue("LendSearch_lendNumber", true), para.LendNumber));
            }
            if (para.LendStatus.Count > 0)
            {
                logstrb.AppendLine(string.Format(ActionFilter.GetMultilingualValue("LendSearch_lendStatus", true), string.Join(",", para.LendStatus.Select(e => e.Value))));
            }

            if (para.PickupTime.Start.HasValue || para.PickupTime.End.HasValue)
            {
                string d1 = string.Empty;
                string d2 = string.Empty;
                if (para.PickupTime.Start.HasValue)
                {
                    d1 = TimeZoneInfoConvertHelper.ConvertDateByTimeZoneByLocal(para.PickupTime.Start.Value, MvcContext.UserInfo.time_zone).ToString("yyyy/MM/dd");
                }
                if (para.PickupTime.End.HasValue)
                {
                    d2 = TimeZoneInfoConvertHelper.ConvertDateByTimeZoneByLocal(para.PickupTime.End.Value, MvcContext.UserInfo.time_zone).ToString("yyyy/MM/dd");
                }

                logstrb.AppendLine(string.Format(ActionFilter.GetMultilingualValue("LendSearch_lendTakeTime", true), d1, d2));
            }

            if (para.ShouldReturnTime.Start.HasValue || para.ShouldReturnTime.End.HasValue)
            {
                string d1 = string.Empty;
                string d2 = string.Empty;
                if (para.ShouldReturnTime.Start.HasValue)
                {
                    d1 = TimeZoneInfoConvertHelper.ConvertDateByTimeZoneByLocal(para.ShouldReturnTime.Start.Value, MvcContext.UserInfo.time_zone).ToString("yyyy/MM/dd");
                }
                if (para.ShouldReturnTime.End.HasValue)
                {
                    d2 = TimeZoneInfoConvertHelper.ConvertDateByTimeZoneByLocal(para.ShouldReturnTime.End.Value, MvcContext.UserInfo.time_zone).ToString("yyyy/MM/dd");
                }

                logstrb.AppendLine(string.Format(ActionFilter.GetMultilingualValue("LendSearch_ShouldReturnTime", true), d1, d2));
            }


            log.Detail = logstrb.ToString();
        });
        #endregion
        return File(excelBytes, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "借出查詢_出借中查詢清單" + DateTime.UtcNow.ConvertDateByTimeZoneByUtc(MvcContext.UserInfo.time_zone).ToString("yyyyMMdd") + ".xlsx");
    }

    /// <summary>
    /// 借出信息檢視(管理員用戶_借出中查詢_檢視)
    /// </summary>
    /// <param name="lendId"></param>
    /// <returns></returns>
    [HttpGet]
    [Route("QueryLendDetailsViewForAdmin")]
    public async Task<ApiResultModelByObject> QueryLendDetailsViewForAdmin([FromQuery] int lendId)
    {
        ApiResultModelByObject apiResult = new ApiResultModelByObject();

        var obj = LendSearchForAdminService.QueryLendDetailsViewForAdmin(lendId, MvcContext.UserInfo);
        apiResult.listData = obj;
        apiResult.rtnSuccess = true;
        #region 記錄日誌
        InitLogRecord(lendId, log =>
        {
            log.Detail = "申請單號:" + obj?.PersonnelInfo?.LendNumber??lendId.ToString();
        });
        #endregion

        return await Task.FromResult<ApiResultModelByObject>(apiResult);
    }
}
