﻿using Elegal.Interface.Api.Common.Control.Filter;
using Elegal.Interface.Api.Common.Model;
using Elegal.Interface.Api.Common.Model.ParaModel.FlowApi;
using Elegal.Interface.Api.Common.Model.ResultModel.FlowApi;
using Elegal.Interface.Api.Common.Repository;
using iText.StyledXmlParser.Jsoup.Nodes;
using Microsoft.IdentityModel.Tokens;
using System.Text;

namespace Elegal.Flow.Api.Repository
{
    /// <summary>
    /// 我方公司/主體管理
    /// </summary>
    public class EntityManageRepository : BaseRepository
    {
        /// <summary>
        /// 實例化
        /// </summary>
        public EntityManageRepository() : base() { }

        #region 獲取主體公司簡稱
        /// <summary>
        /// 獲取主體公司簡稱
        /// </summary>
        /// <returns></returns>
        public List<DropDownListModel> GetPrincipalCompanyAbbreviation()
        {
            string sql = @"SELECT entity_id AS paraKey,entity  AS paraValue FROM fnp_entity ORDER BY entity_id ASC";
            return this.NpgsqlSearchByList<DropDownListModel>(sql, null);
        }
        #endregion

        #region 獲取區域
        /// <summary>
        /// 獲取區域
        /// </summary>
        /// <returns></returns>
        public List<DropDownListModel> GetArea()
        {
            string sql = @"SELECT area_id AS paraKey,area_name AS paraValue FROM sys_area ORDER BY area_id ASC";
            return this.NpgsqlSearchByList<DropDownListModel>(sql, null);
        }
        #endregion

        #region 根據 areaid 集合 獲取區域
        /// <summary>
        /// 根據 areaid 集合 獲取區域 
        /// </summary>
        /// <returns></returns>
        public string GetAreaByAreaId(string area_id)
        {
            string sql = @"SELECT area_name FROM sys_area where area_id in (@area_id)";
            return (string)this.NpgsqlSearchBySingleValue(sql, new { area_id = area_id });
        }
        #endregion

        #region 查詢流程當前關卡
        /// <summary>
        /// 查詢流程當前關卡
        /// </summary>
        /// <returns></returns>
        public int QueryEntityCurrentStep(int entity_id)
        {
            string sql = @"SELECT entity_next_stepid FROM sys_entity_manage_step WHERE entity_id = @entity_id";
            return Convert.ToInt32(this.NpgsqlSearchBySingleValue(sql, new { entity_id = entity_id.ToString() }));
        }
        #endregion

        #region 主體查詢
        /// <summary>
        /// 主體查詢
        /// </summary>
        /// <returns></returns>
        public List<EntityManage> QueryEntityManage(EntityManageModel entityManageModel, string logging_locale = "ZH-TW", string timeZone = "Taipei Standard Time")
        {
            StringBuilder sql = new StringBuilder();
            string deliveryDateString = string.Empty;
            sql.Append(@"SELECT
            DISTINCT
            fe.entity_id,
            fe.entity,--主體公司簡稱
            fe.entity_namec,--主體中文名稱
            fe.entity_namee,--主體英文名稱
            sa.area_name,--區域
            fe.status,--狀態
            (SELECT fun_name FROM sys_parameters WHERE para_code = 'entityStatus' AND lang_type = @logging_locale AND func_code = CONVERT(NVARCHAR,fe.status)) AS entityStatus,--狀態頁面顯示
            fe.stoptype,--停用原因
            (SELECT fun_name FROM sys_parameters WHERE para_code = 'entityStopReason' AND lang_type = @logging_locale AND func_code = CONVERT(NVARCHAR,fe.stoptype)) AS entityStopReason,--停用原因頁面顯示
            CONVERT(varchar, fe.deliverydate) AS deliverydate,--交割/獨立日期
            ISNULL(fe.modify_time,fe.create_time) AS operate_time,--更新時間
            ee.[name] AS operate_cuser,--更新者中文名
            ee.name_a AS operate_euser,--更新者英文名
            fe.comment as Comment,
            is_sign_legalperson as IsSignLegalperson,
            before_rename_entity as BeforeRenameEntity,
            fe.is_send_mail,
            fe.is_search_data
            FROM fnp_entity AS fe
            LEFT JOIN sys_area AS sa ON fe.area_id = sa.area_id
            LEFT JOIN (SELECT emplid,[name],name_a FROM ps_sub_ee_lgl_vw_a) AS ee ON emplid = ISNULL(fe.modify_user,fe.create_user)
            LEFT JOIN (select entity_id,company from fnp_ec_relation fer 
						union
						select entity_id,company from fnp_ac_relation far ) AS fsl on fe.entity_id = fsl.entity_id
            WHERE 1 = 1");

            if (!string.IsNullOrEmpty(entityManageModel.entitys))
                sql.Append(@$" AND fe.entity_id IN(@entitys)");
            if (!string.IsNullOrEmpty(entityManageModel.areaids))
                sql.Append(@$" AND sa.area_id IN(@areaids)");
            if (!string.IsNullOrEmpty(entityManageModel.company))
                sql.Append(@$" AND fsl.company = @company");
            if (!string.IsNullOrEmpty(entityManageModel.actual_company))
                sql.Append(@$" AND fsl.company = @actual_company");
            if (entityManageModel.status != null)
                sql.Append(@$" AND fe.status = @status");
            if (entityManageModel.stoptype != null)
                sql.Append(@$" AND fe.stoptype = @stoptype AND fe.status != 0");
            if (entityManageModel.deliverydate != null)
            {
                deliveryDateString = TimeZoneInfo.ConvertTimeFromUtc(entityManageModel.deliverydate.Value, TimeZoneInfo.FindSystemTimeZoneById(timeZone)).ToString("yyyy-MM-dd");
                TimeZoneInfo timeZoneInfo = TimeZoneInfo.FindSystemTimeZoneById(timeZone);
                string baseUtcOffset = timeZoneInfo.BaseUtcOffset.ToString();
                if (baseUtcOffset.IndexOf("-") > -1)
                {
                    baseUtcOffset = baseUtcOffset.Substring(0, 6);
                }
                else
                {
                    baseUtcOffset = "+" + baseUtcOffset.Substring(0, 5);
                }
                sql.Append(@$" AND CONVERT(VARCHAR(100),SWITCHOFFSET(fe.deliverydate, '{baseUtcOffset}'),23) = @deliverydate AND status = 1;");
            }
            return this.NpgsqlSearchByList<EntityManage>(sql.ToString(), new
            {
                entitys = entityManageModel.entitys,
                areaids = entityManageModel.areaids,
                company = entityManageModel.company,
                status = entityManageModel.status,
                stoptype = entityManageModel.stoptype,
                logging_locale = logging_locale,
                deliverydate = deliveryDateString,
                actual_company = entityManageModel.actual_company
            },
                "time_zone", timeZone);
        }
        #endregion

        #region 查詢授權公司代碼
        /// <summary>
        /// 查詢授權公司代碼
        /// </summary>
        /// <returns></returns>
        public List<EntityAuthCompany> QueryAuthCompany()
        {
            string sql = @"SELECT
                            company,--授權公司代碼
                            descr,--公司名稱
                            isAffCom,--關企公司
                            hrCode,--對應之HR公司代碼
                            descrshort
                            FROM 
                            (
                            SELECT company,descr,descrshort,'N' AS isAffCom,'' AS hrCode FROM ps_sub_comp_vw_a
                            UNION
                            SELECT aff_company_code AS company,'' as descrshort,aff_company_abb AS descr,'Y' AS isAffCom,aff_hrcompany_code AS hrCode FROM affiliate_company where aff_status = '1'
                            ) AS comData ORDER BY company ASC";
            return this.NpgsqlSearchByList<EntityAuthCompany>(sql, null);
        }
        #endregion

        #region 檢查主體重複
        /// <summary>
        /// 檢查主體重複
        /// </summary>
        /// <returns></returns>
        public bool VerifyEntityRepeat(ModifyEntityInfo createEntityInfo)
        {
            string sql = @$"SELECT COUNT(entity_id)  FROM fnp_entity WHERE entity = @entity";
            return Convert.ToInt32(this.NpgsqlSearchBySingleValue(sql, new { entity = createEntityInfo.Entity.ToString() })) == 0;
        }
        #endregion

        #region 建立主體
        /// <summary>
        /// 建立主體
        /// </summary>
        /// <returns></returns>
        public int InsertEntityInfo(ModifyEntityInfo model)
        {
            StringBuilder sql = new StringBuilder();
            sql.Append(@$"BEGIN TRY
                    BEGIN TRANSACTION
                    DECLARE @entity_id INT
                    DECLARE @group_entityid NVARCHAR(10)
                    SELECT @entity_id = MAX(CONVERT(INT,entity_id)) + 1 FROM fnp_entity
                    SELECT @group_entityid = case when @before_rename_entity = '' then CONCAT(N'', @entity_id) else COALESCE(group_entityid,entity_id)  end
                                                    from fnp_entity where @before_rename_entity = entity

                    INSERT INTO fnp_entity (entity_id, entity, entity_namec, entity_namee, status, create_user, stoptype, area_id, comment, is_sign_legalperson, before_rename_entity, group_entityid, is_send_mail, is_search_data)
                    VALUES (@entity_id,N''+@Entity,N''+@EntityNamec,N''+@EntityNamee, 1,N''+@CreateUser, -1, @AreaId,@Comment,@IsSignLegalperson,@before_rename_entity,@group_entityid,@is_send_mail,@is_search_data);
                    INSERT INTO legal_assign_entitytype_mapping
                                (entity_type, entity_id, entity)
                                VALUES(N'1', @entity_id, N''+@Entity);");

            if (!string.IsNullOrEmpty(model.company))
                sql.Append(@$"
                    --新建臨時表來分割company
                    CREATE TABLE #tempCompany (company NVARCHAR(1000));
                    INSERT INTO #tempCompany (company)
                    SELECT value FROM STRING_SPLIT(@company, ',');
                    INSERT INTO fnp_ec_relation (entity_id, company, create_user)
                    SELECT @entity_id, company, N''+@CreateUser FROM #tempCompany;
                    DROP TABLE #tempCompany;");

            if (!string.IsNullOrEmpty(model.actual_company))
                sql.Append(@"
                    CREATE TABLE #tempActualCompany (actual_company NVARCHAR(1000));
                    INSERT INTO #tempActualCompany (actual_company)
                    SELECT value FROM STRING_SPLIT(@actual_company, ',');
                    INSERT INTO fnp_ac_relation (entity_id, company, create_user)
                    SELECT @entity_id, actual_company, N''+@CreateUser FROM #tempActualCompany;
                    DROP TABLE #tempActualCompany;");

            sql.Append(@"
                --插入主體合約管理人角色 -> 內置數據
                INSERT INTO fnp_entity_role (role_id, entity_id, create_user)
                    VALUES((select Min(r_id+1) from p_role pr where 
                        not EXISTS (select 1 from p_role pr2 where pr2.r_id = pr.r_id+1)), CAST(@entity_id AS NVARCHAR(10)),N''+@CreateUser);

                    INSERT INTO fnp_contract_view (r_id, selecttype, entity_id,create_user)
                    VALUES((select Min(r_id+1) from p_role pr where 
                        not EXISTS (select 1 from p_role pr2 where pr2.r_id = pr.r_id+1)), 2, CAST(@entity_id AS NVARCHAR(10)),N''+@CreateUser);

                    INSERT INTO p_role (r_id, r_name, role_type, create_user, create_time, is_builtin)
                    VALUES ((SELECT MIN(r_id+1) FROM p_role pr WHERE NOT EXISTS (SELECT 1 FROM p_role pr2 WHERE pr2.r_id = pr.r_id+1)),CONCAT(N'', @Entity, N'合約管理人Contract Admin'), 1, CONCAT(N'', @CreateUser), getutcdate(), 1);

                    INSERT INTO fnp_news
                    (leaf_id, news_subject, new_time, news_start_time, news_end_time, news_status, is_global, create_user, create_time,  modify_time, is_builtin)
                    VALUES(N''+'02', CONCAT(N'', @Entity, N'合約管理人Contract Admin'), getutcdate(), null, null, 0, 0, N''+ 'system', getutcdate(), null , 1);

                    INSERT INTO fnp_news_entity
                            (newid, entity_id, create_user, create_time, builtin_entity_id)
                            VALUES(SCOPE_IDENTITY(), @entity_id, N''+'system', getutcdate(), @entity_id);

                    --插入主體當地財務角色 -> 內置數據
                    INSERT INTO fnp_entity_role (role_id, entity_id, create_user)
                    VALUES((select Min(r_id+1) from p_role pr where 
                        not EXISTS (select 1 from p_role pr2 where pr2.r_id = pr.r_id+1)), CAST(@entity_id AS NVARCHAR(10)),N''+@CreateUser);

                    INSERT INTO fnp_contract_view (r_id, selecttype, entity_id,create_user)
                    VALUES((select Min(r_id+1) from p_role pr where 
                        not EXISTS (select 1 from p_role pr2 where pr2.r_id = pr.r_id+1)), 2,CAST(@entity_id AS NVARCHAR(10)),N''+@CreateUser);

                    INSERT INTO p_role (r_id, r_name, role_type, create_user, create_time, is_builtin)
                    VALUES ((SELECT MIN(r_id+1) FROM p_role pr WHERE NOT EXISTS (SELECT 1 FROM p_role pr2 WHERE pr2.r_id = pr.r_id+1)),CONCAT(N'', @Entity, N'當地財務'), 1, CONCAT(N'', @CreateUser), getutcdate(), 1);

                    INSERT INTO fnp_contract_view_entity (r_id, entity_id, create_user, create_time)  
                    SELECT r_id, entity_id, CONCAT(N'',@CreateUser) AS create_user, GETUTCDATE() AS create_time
                    FROM fnp_contract_view fcv   
                    WHERE entity_id = CAST(@entity_id AS NVARCHAR(10))

                    --主體檢視權限
                    INSERT INTO fnp_contract_view_entity (r_id, entity_id, create_user, create_time)  
                    SELECT pr.r_id, CAST(@entity_id AS NVARCHAR(10)) AS entity_id, CONCAT(N'',@CreateUser) AS create_user, GETUTCDATE() AS create_time
                    FROM fnp_contract_view fcv 
                    inner join p_role pr on fcv.r_id = pr.r_id
                    WHERE pr.special_code = 0
                    AND selecttype = 2;

                    --新增角色與主體的對應關係，非內置角色
                    INSERT INTO fnp_entity_role (role_id, entity_id, create_user, create_time,is_builtin)
                    SELECT r_id as role_id, CAST(@entity_id AS NVARCHAR(10)) AS entity_id, CONCAT(N'',@CreateUser) AS create_user, GETUTCDATE() AS create_time,0 AS is_builtin FROM p_role pr WHERE pr.special_code = 0

                    COMMIT TRANSACTION
                                    END TRY
                                    BEGIN CATCH
                                        IF @@TRANCOUNT > 0
                                            ROLLBACK TRANSACTION
                                    END CATCH
                    
                    -- 驗證新增是否成功
                    IF EXISTS (SELECT 1 FROM fnp_entity WHERE @entity_id = entity_id)
	                BEGIN
	                    SELECT @entity_id as entity_id
	                END
	                ELSE
	                BEGIN
	                    SELECT 0 AS entity_id
	                END");
            return Convert.ToInt32(this.NpgsqlSearchBySingleValue(sql.ToString(), new { Entity = model.Entity, EntityNamec = model.EntityNamec, EntityNamee = model.EntityNamee, CreateUser = model.CreateUser, AreaId = model.AreaId, company = model.company, Comment = model.Comment, actual_company = model.actual_company, IsSignLegalperson = model.IsSignLegalperson, before_rename_entity = model.BeforeRenameEntity, is_send_mail = model.is_send_mail, is_search_data = model.is_search_data }));
        }
        #endregion

        #region 根據主體id查詢對應信息
        /// <summary>
        /// 根據主體id查詢對應信息
        /// </summary>
        /// <returns></returns>
        public EntityManage QueryEntityInfoById(int entity_id, string logging_locale = "ZH-TW", string timeZone = "Taipei Standard Time")
        {
            string sql = $@"SELECT 
                            entity_id,
                            entity,
                            entity_namec,
                            entity_namee,
                            area_id,
                            (SELECT area_name FROM sys_area sa WHERE sa.area_id = fe.area_id) AS area_name,
                            STUFF((SELECT ',' + company FROM fnp_ec_relation AS fer WHERE fer.entity_id = fe.entity_id FOR XML PATH('')), 1, 1, '') AS company,
                            STUFF((SELECT ',' + company FROM fnp_ac_relation AS far WHERE far.entity_id = fe.entity_id FOR XML PATH('')), 1, 1, '') AS actual_company,
                            status,
                            stoptype,
                            (SELECT fun_name FROM sys_parameters WHERE para_code = 'entityStopReason' AND is_used = 1 AND lang_type = @logging_locale AND func_code = CONVERT(NVARCHAR,fe.stoptype)) AS stopName,
                            deliverydate,
                            Comment,
                            is_sign_legalperson as IsSignLegalperson,
                            before_rename_entity as BeforeRenameEntity,
                            is_send_mail,
                            is_search_data
                        FROM 
                            fnp_entity fe
                        WHERE 
                            entity_id = @entity_id";
            return this.NpgsqlSearchBySingle<EntityManage>(sql, new { entity_id = entity_id.ToString(), logging_locale = logging_locale },
                "time_zone", timeZone);
        }
        #endregion

        #region 變更主體數據
        /// <summary>
        /// 變更主體數據
        /// </summary>
        /// <returns></returns>
        public int UpdateEntityInfo(ModifyEntityInfo model, int oldStatus)
        {
            DateTime? deliverydate = model.Deliverydate;
            StringBuilder sql = new();
            sql.Append($@"
                        BEGIN TRY
                        BEGIN TRANSACTION
                        
                        DECLARE @group_entityid NVARCHAR(10)
                        SELECT @group_entityid = case when (@before_rename_entity = '' or @before_rename_entity is null) then CONCAT(N'', @EntityId) else COALESCE(group_entityid,entity_id) end
                                                    from fnp_entity where @before_rename_entity = entity

                        UPDATE fnp_entity SET entity_namec =  N''+@EntityNamec, entity_namee =  N''+@entity_namee, status = {model.Status}, stoptype = @stoptype, before_rename_entity = @before_rename_entity, group_entityid = @group_entityid");
            if (model.Deliverydate != null)
                sql.Append(@$", deliverydate = @Deliverydate");
            if (model.Deliverydate == null)
                sql.Append(@$", deliverydate = NULL");
            if (model.Comment != null && !string.IsNullOrEmpty(model.Comment))
                sql.Append(@$", Comment = @Comment");
            if (model.IsSignLegalperson != null)
                sql.Append(@$", is_sign_legalperson = @IsSignLegalperson");
            if (model.is_send_mail != null)
                sql.Append(@$", is_send_mail = @is_send_mail");
            if (model.is_search_data != null)
                sql.Append(@$", is_search_data = @is_search_data");
            sql.Append(@$", area_id = @AreaId, modify_user =  N''+@ModifyUser, modify_time = getutcdate() WHERE entity_id =  N''+@EntityId;
                        delete from fnp_ec_relation where entity_id =  N''+@EntityId;
                        delete from fnp_ac_relation where entity_id =  N''+@EntityId;
");

            //識別場景—主體由啟用變成停用
            Boolean scene = oldStatus == 0 && model.Status != 0;
            if (!string.IsNullOrEmpty(model.actual_company))
            {
                sql.Append(@$"--新建臨時表來分割actual_company
                        CREATE TABLE #tempActualCompany (actual_company NVARCHAR(1000));
                        INSERT INTO #tempActualCompany (actual_company)
                        SELECT value FROM STRING_SPLIT(@actual_company, ',');
                        INSERT INTO fnp_ac_relation (entity_id, company, create_user)
                        SELECT  N''+@EntityId, actual_company,  N''+@CreateUser FROM #tempActualCompany;
                        DROP TABLE #tempActualCompany;");
            }

            if (!string.IsNullOrEmpty(model.company) && model.is_delete_other == 0)
            {
                sql.Append(@$"--新建臨時表來分割company
                        CREATE TABLE #tempCompany (company NVARCHAR(1000));
                        INSERT INTO #tempCompany (company)
                        SELECT value FROM STRING_SPLIT(@company, ',');
                        INSERT INTO fnp_ec_relation (entity_id, company, create_user)
                        SELECT  N''+@EntityId, company,  N''+@CreateUser FROM #tempCompany;
                        DROP TABLE #tempCompany;");
            }
            if (oldStatus == 0 && model.Status != 0)
                sql.Append(@"UPDATE flow_sign_level set is_used = 0 where entity_id =  N''+@EntityId;");
            sql.Append(@"COMMIT TRANSACTION
                                    END TRY
                                    BEGIN CATCH
                                        IF @@TRANCOUNT > 0
                                            ROLLBACK TRANSACTION
                                    END CATCH");
            return this.ExecuteCommand(sql.ToString(), new { EntityNamec = model.EntityNamec, entity_namee = model.EntityNamee, Deliverydate = deliverydate, AreaId = model.AreaId, ModifyUser = model.ModifyUser, EntityId = model.EntityId, company = model.company, CreateUser = model.CreateUser, Status = model.Status, stoptype = model.Stoptype, Comment = model.Comment, actual_company = model.actual_company, IsSignLegalperson = model.IsSignLegalperson, before_rename_entity = model.BeforeRenameEntity, is_send_mail = model.is_send_mail, is_search_data = model.is_search_data });
        }
        #endregion

        #region 變更主體流程進度
        /// <summary>
        /// 變更主體流程進度
        /// </summary>
        /// <returns></returns>
        public int ModifyPushStep(ModifyEntityInfo model)
        {
            string sql = $@"MERGE INTO sys_entity_manage_step AS tgt
            USING (select N''+@EntityId as entity_id, @entity_stepid as entity_stepid,@entity_next_stepid as entity_next_stepid , N''+@CreateUser as create_user, N''+@ModifyUser as modify_user) AS src
            ON (tgt.entity_id=src.entity_id)
            WHEN MATCHED
            THEN UPDATE SET
            tgt.entity_stepid=src.entity_stepid, tgt.entity_next_stepid=src.entity_next_stepid,  tgt.modify_user=src.modify_user, tgt.modify_time=getutcdate()
            WHEN NOT MATCHED
            THEN INSERT (entity_id, entity_stepid, entity_next_stepid, create_user, create_time)
            VALUES (src.entity_id, src.entity_stepid, src.entity_next_stepid, src.create_user, getutcdate());";
            return this.ExecuteCommand(sql, new
            {
                EntityId = model.EntityId,
                entity_stepid = model.entity_stepid,
                entity_next_stepid = model.entity_next_stepid,
                CreateUser = model.CreateUser,
                ModifyUser = model.ModifyUser
            });
        }
        #endregion

        #region 根據授權公司代碼顯示數據
        /// <summary>
        /// 根據授權公司代碼顯示數據
        /// </summary>
        /// <returns></returns>
        public List<EntityManage> QueryAuthCompanyByEntity(int entity_id, string timeZone = "Taipei Standard Time")
        {
            string sql = $@"SELECT
                            fer.company,--授權公司碼
                            (select descrshort from ps_sub_comp_vw_a where company=fer.company) company_name,
                            ISNULL(fsl.level_start,5) AS level_start,--起始簽核層級
                            ISNULL(fsl.level_end,2) AS level_end,--最高簽核層級
                            ISNULL(fsl.modify_time,ISNULL(fsl.create_time,fer.create_time)) AS operate_time,--更新時間
                            ee.name AS operate_cuser,--更新人中文名
                            ee.name_a AS operate_euser--更新人英文名
                            FROM (select * from fnp_ec_relation 
						            union
						          select * from fnp_ac_relation) AS fer
                            LEFT JOIN (select * from flow_sign_level where is_used = 1) AS fsl ON fer.entity_id = fsl.entity_id AND fer.company = fsl.company
                            LEFT JOIN (SELECT emplid,name,name_a FROM ps_sub_ee_lgl_vw_a) AS ee ON ee.emplid = ISNULL(modify_user,ISNULL(fsl.create_user,fer.create_user))
                            WHERE fer.entity_id = @entity_id order by fer.company";
            return this.NpgsqlSearchByList<EntityManage>(sql, new { entity_id = entity_id.ToString() },
                "time_zone", timeZone);
        }
        #endregion

        #region 更新授權公司數據
        /// <summary>
        /// 更新授權公司數據
        /// </summary>
        /// <returns></returns>
        public int ModifyAuthCompanyAndEntity(ModifyEntityInfo modifyEntity)
        {
            string sql = $@"BEGIN
                                IF EXISTS (SELECT 1 FROM flow_sign_level WHERE entity_id = N''+@EntityId
                                    AND company = N''+@company AND level_start = @level_start AND level_end = @level_end)
                                BEGIN
	                                select 1 
                                END
                                ELSE
                                BEGIN
                                IF EXISTS (SELECT 1 FROM flow_sign_level WHERE entity_id = N''+@EntityId
                                    AND company = N''+@company AND (level_start != @level_start or level_end != @level_end))
                                    BEGIN 
	                                UPDATE flow_sign_level SET modify_user=N'' + @CreateUser, modify_time=getutcdate() where entity_id = (N''+@EntityId) AND company = (N''+@company)
                                END
                                END 
                            END;

                            MERGE INTO flow_sign_level AS tgt
                            USING (select  N''+@EntityId as entity_id, N''+@company as company, @level_start as level_start,
                            @level_end as level_end,  N'' + @CreateUser as create_user,  N'' + @ModifyUser as modify_user, 1 as is_used) AS src
                            ON (tgt.entity_id=src.entity_id and tgt.company=src.company)
                            WHEN MATCHED
                            THEN UPDATE SET
                            tgt.level_start=src.level_start, tgt.level_end=src.level_end
                            WHEN NOT MATCHED
                            THEN INSERT (entity_id, company, level_start, level_end, create_user, create_time, is_used)
                            VALUES (src.entity_id, src.company, src.level_start, src.level_end, src.create_user, getutcdate(), src.is_used);";
            return this.ExecuteCommand(sql, new
            {
                EntityId = modifyEntity.EntityId,
                company = modifyEntity.company,
                level_start = modifyEntity.level_start,
                level_end = modifyEntity.level_end,
                CreateUser = modifyEntity.CreateUser,
                ModifyUser = modifyEntity.ModifyUser
            });
        }
        #endregion

        #region 根據主體ID查詢關卡詳情
        /// <summary>
        /// 根據主體ID查詢關卡詳情
        /// </summary>
        /// <returns></returns>
        public List<ApproveManagementResult> QueryApproveManagement(int entity_id, string logging_locale = "ZH-TW")
        {
            string sql = $@"SELECT
                            rowid,
                            (SELECT DISTINCT fun_name FROM sys_parameters WHERE para_code = 'ApproverManagement' AND lang_type = @logging_locale AND is_used = 1 AND func_code = sam.approver_type) AS step_name,
                            (SELECT DISTINCT entity FROM fnp_entity WHERE entity_id = sam.entity_id) AS entity,
                            pselva.deptid AS deptid,
                            emp_id AS emplid,
                            pselva.name_a AS emp_namee,
                            pselva.name AS emp_namec,
                            (CASE pselva.phone WHEN '' THEN '' ELSE pselva.area_code + N'+' + phone END) AS phone,
                            CASE WHEN ISNULL(pselva.termination,'') = '' THEN N'在職(1)' ELSE N'離職(0)' END AS work_status,
                            operate_time AS modify_time,
                            ee.name AS modify_usere ,
                            ee.name_a AS modify_userc
                            FROM (
                            SELECT rowid,approver_type,emp_id,entity_id,contract_fnid,pay_style,ISNULL(modify_time,create_time) AS operate_time,ISNULL(modify_user,create_user) AS operate_user FROM sys_approver_management WHERE 1 = 1
                            AND entity_id = @entity_id
                            ) AS sam
                            LEFT JOIN (SELECT DISTINCT emplid,name,name_a,deptid,termination,CASE RTRIM(LTRIM(ISNULL(prefix_dial_code_a, ''))) WHEN '' THEN '0000' ELSE RTRIM(LTRIM(ISNULL(prefix_dial_code_a, ''))) END AS area_code,RTRIM(LTRIM(ISNULL(phone_a, ''))) AS phone FROM ps_sub_ee_lgl_vw_a) AS pselva ON sam.emp_id = pselva.emplid
                            LEFT JOIN (SELECT DISTINCT emplid,name,name_a FROM ps_sub_ee_lgl_vw_a) AS ee ON ee.emplid = sam.operate_user";
            return this.NpgsqlSearchByList<ApproveManagementResult>(sql, new { entity_id = entity_id.ToString(), logging_locale = logging_locale });
        }
        #endregion

        #region 顯示主體角色與權限
        /// <summary>
        /// 顯示主體角色與權限
        /// </summary>
        /// <returns></returns>
        public List<EntityUserRoleResult> QueryEntityRoleAndUser(int entity_id, string timeZone = "Taipei Standard Time")
        {
            string sql = $@"select * from (
SELECT 
                            roleData.r_id as RId,
                            roleData.role_type as RoleType,--類型
                            roleData.r_name as RName,--角色名稱
                            roleData.is_builtin as IsBuiltin,
                            (STUFF((SELECT ', ' + sal_location_a FROM p_role_site AS prs WHERE roleData.r_id = prs.r_id FOR XML PATH('')), 1, 1, '')) AS SalLocationA,--地區
                            CASE roleData.is_builtin
                            WHEN 1 THEN N'系統內置'
                            ELSE '' END AS LevelStatus,--關卡
                            CASE (SELECT COUNT(rowid) FROM p_role_function_action AS prfa WHERE prfa.r_id = roleData.r_id)
                            WHEN 0 THEN 0
                            ELSE 1 END AS IsUpdate,--是否需要被修改，0：需要，1：不需要
                            case (select count(1) from fnp_entity_role WHERE entity_id = N'' + @entity_id and role_id = roleData.r_id) when 0 then 0 else 1 end AS ShowUpdate,
                            roleData.operate_time as operate_time,--更新時間
                            ee.name AS operate_cuser,--更新人中文名
                            ee.name_a AS operate_euser,--更新人英文名
                            roleData.special_code as SpecialCode
                            FROM (
                            SELECT r_id,role_type,r_name,is_builtin,ISNULL(pr.modify_time,pr.create_time) AS operate_time,ISNULL(pr.modify_user,pr.create_user) AS operate_user,special_code FROM p_role AS pr WHERE EXISTS (SELECT 1 FROM (
                            SELECT role_id FROM fnp_entity_role WHERE entity_id = N'' + @entity_id
							union
							SELECT r_id AS role_id FROM fnp_contract_view_entity WHERE entity_id = N'' + @entity_id) AS fer WHERE fer.role_id = pr.r_id
							)
                            ) AS roleData
                            LEFT JOIN (SELECT emplid,name,name_a FROM ps_sub_ee_lgl_vw_a) AS ee ON ee.emplid = roleData.operate_user
                            UNION 
                            SELECT 
                            roleData.r_id as RId,
                            roleData.role_type as RoleType,--類型
                            roleData.r_name as RName,--角色名稱
                            roleData.is_builtin as IsBuiltin,
                            (STUFF((SELECT ', ' + sal_location_a FROM p_role_site AS prs WHERE roleData.r_id = prs.r_id FOR XML PATH('')), 1, 1, '')) AS SalLocationA,--地區
                            CASE roleData.is_builtin
                            WHEN 1 THEN N'系統內置'
                            ELSE '' END AS LevelStatus,--關卡
                            1 AS IsUpdate,--是否需要被修改，0：需要，1：不需要
                            0 AS ShowUpdate,
                            roleData.operate_time as operate_time,--更新時間
                            ee.name AS operate_cuser,--更新人中文名
                            ee.name_a AS operate_euser,--更新人英文名
                            roleData.special_code as SpecialCode
                            FROM (
                            SELECT r_id,role_type,r_name,is_builtin,ISNULL(pr.modify_time,pr.create_time) AS operate_time,ISNULL(pr.modify_user,pr.create_user) AS operate_user,special_code FROM p_role AS pr WHERE EXISTS (SELECT 1 FROM (select r_id from fnp_contract_view where selecttype = 0 and r_id != 3 and r_id != 8) AS fer WHERE fer.r_id = pr.r_id
							)
                            ) AS roleData
                            LEFT JOIN (SELECT emplid,name,name_a FROM ps_sub_ee_lgl_vw_a) AS ee ON ee.emplid = roleData.operate_user
)a
                            ORDER BY RoleType ASC,CASE WHEN (RName = N'ADMIN' OR RName = N'OTHERS') THEN 0 ELSE 1 END ASC,RName ASC
                           ";
            return this.NpgsqlSearchByList<EntityUserRoleResult>(sql, new { entity_id = entity_id.ToString() },
                "time_zone", timeZone);
        }
        #endregion

        #region 顯示角色使用者授權
        /// <summary>
        /// 顯示角色使用者授權
        /// </summary>
        /// <returns></returns>
        public List<UserRoleAuthorization> QueryUserRoleAuthorization(UserRoleAuthorizationModel model, string timeZone = "Taipei Standard Time")
        {
            StringBuilder sql = new StringBuilder();
            sql.Append($@"SELECT * from (");
            sql.Append($@"SELECT pu.rowid AS Rowid,pu.r_id AS RId,
                            (SELECT DISTINCT r_name FROM p_role WHERE r_id = pu.r_id) AS RoleTypeName,--角色名稱
                            u_id as UId,--員工工號
                            ue.name as Name,--員工中文名
                            ue.name_a as NameA,--員工英文名
                            (select DISTINCT descrshort from ps_sub_comp_vw_a where company = ue.company) as Company,--員工公司
                            (SELECT DISTINCT DESCR40 FROM ps_sub_ee_lgl_vw_a WHERE emplid = pu.u_id) AS cname,--所屬公司(英文簡稱)
                            CASE WHEN ISNULL((SELECT DISTINCT termination FROM ps_sub_ee_lgl_vw_a WHERE emplid = pu.u_id),'') = '' THEN 1 ELSE 0 END AS Status,--任職狀態
                            --(SELECT DISTINCT sta_time FROM other_rights_role_user_time WHERE apply_number = pu.apply_number AND user_id = pu.u_id AND r_id = pu.r_id) AS ViewTimeStart,--檢視期限起始
                            null AS ViewTimeStart,--檢視期限起始
                            --(SELECT DISTINCT end_time FROM other_rights_role_user_time WHERE apply_number = pu.apply_number AND user_id = pu.u_id AND r_id = pu.r_id) AS ViewTimeEnd,--檢視期限結束
                            null AS ViewTimeEnd,--檢視期限結束
                            case (select count(1) from fnp_entity_role WHERE entity_id = N'' + @entity_id and role_id = pu.r_id) when 0 then 1 else 0 end AS ShowUpdate,
                            ISNULL(modify_time,create_time) AS operate_time,--更新時間
                            ee.name AS operate_cuser,--更新人中文
                            ee.name_a AS operate_euser,--更新人英文
                            (select special_code from p_role WHERE r_id = pu.r_id) as SpecialCode
                            FROM (SELECT rowid,u_id,r_id,apply_number,create_user,create_time,modify_user,modify_time FROM p_user_role AS pue WHERE EXISTS(SELECT 1 FROM (select role_id,entity_id from fnp_entity_role union select r_id as role_id,entity_id from fnp_contract_view_entity) AS fer WHERE fer.role_id = pue.r_id ");
            if (model.entity_id != null && !string.IsNullOrEmpty(model.entity_id))
                sql.Append($@" AND fer.entity_id = @entity_id ");
            if (model.r_id > 0)
                sql.Append($@" AND pue.r_id = @r_id ");
            sql.Append($@")) AS pu
                            LEFT JOIN (SELECT emplid,name,name_a,company FROM ps_sub_ee_lgl_vw_a) AS ue ON ue.emplid = pu.u_id
                            LEFT JOIN (SELECT emplid,name,name_a FROM ps_sub_ee_lgl_vw_a) AS ee ON ee.emplid = ISNULL(modify_user,create_user) WHERE 1 = 1");
            if (model.emplid != null && !string.IsNullOrEmpty(model.emplid))
                sql.Append($@"AND (ue.name LIKE CONCAT(N'%', @emplid, N'%') OR ue.name_a LIKE CONCAT(N'%', @emplid, N'%') OR u_id LIKE CONCAT(N'%', @emplid, N'%'))");
            sql.Append($@" union 
                            SELECT pu.rowid AS Rowid,pu.r_id AS RId,
                            (SELECT DISTINCT r_name FROM p_role WHERE r_id = pu.r_id) AS RoleTypeName,--角色名稱
                            u_id as UId,--員工工號
                            ue.name as Name,--員工中文名
                            ue.name_a as NameA,--員工英文名
                            (select DISTINCT descrshort from ps_sub_comp_vw_a where company = ue.company) as Company,--員工公司
                            (SELECT DISTINCT DESCR40 FROM ps_sub_ee_lgl_vw_a WHERE emplid = pu.u_id) AS cname,--所屬公司(英文簡稱)
                            CASE WHEN ISNULL((SELECT DISTINCT termination FROM ps_sub_ee_lgl_vw_a WHERE emplid = pu.u_id),'') = '' THEN 1 ELSE 0 END AS Status,--任職狀態
                            --(SELECT DISTINCT sta_time FROM other_rights_role_user_time WHERE apply_number = pu.apply_number AND user_id = pu.u_id AND r_id = pu.r_id) AS ViewTimeStart,--檢視期限起始
                            null AS ViewTimeStart,--檢視期限起始
                            --(SELECT DISTINCT end_time FROM other_rights_role_user_time WHERE apply_number = pu.apply_number AND user_id = pu.u_id AND r_id = pu.r_id) AS ViewTimeEnd,--檢視期限結束
                            null AS ViewTimeEnd,--檢視期限結束
                            1 as ShowUpdate,
                            ISNULL(modify_time,create_time) AS operate_time,--更新時間
                            ee.name AS operate_cuser,--更新人中文
                            ee.name_a AS operate_euser,--更新人英文
                            (select special_code from p_role WHERE r_id = pu.r_id) as SpecialCode
                            FROM (SELECT rowid,u_id,r_id,apply_number,create_user,create_time,modify_user,modify_time FROM
                            p_user_role AS pue 
                            WHERE EXISTS(SELECT 1 FROM (select fcv.r_id,'-1' as entity_id  from fnp_contract_view fcv join p_role pr on fcv.r_id = pr.r_id where selecttype = 0 and fcv.r_id != 3 and fcv.r_id != 8 and pr.role_type != 2) AS fer 
                            WHERE fer.r_id = pue.r_id");
            if (model.entity_id != null && !string.IsNullOrEmpty(model.entity_id))
                sql.Append($@" AND (fer.entity_id = @entity_id or fer.entity_id = '-1'))");
            if (model.r_id > 0)
                sql.Append($@" AND pue.r_id = @r_id ");
            sql.Append($@") AS pu
                            LEFT JOIN (SELECT emplid,name,name_a,company FROM ps_sub_ee_lgl_vw_a) AS ue ON ue.emplid = pu.u_id
                            LEFT JOIN (SELECT emplid,name,name_a FROM ps_sub_ee_lgl_vw_a) AS ee ON ee.emplid = ISNULL(modify_user,create_user) WHERE 1 = 1");
            if (model.emplid != null && !string.IsNullOrEmpty(model.emplid))
                sql.Append($@"AND (ue.name LIKE CONCAT(N'%', @emplid, N'%') OR ue.name_a LIKE CONCAT(N'%', @emplid, N'%') OR u_id LIKE CONCAT(N'%', @emplid, N'%'))");
            sql.Append(")a order by RoleTypeName,stuff(UId,1,patindex('%[A-z]%',substring(UId,1,1))-1,'') asc, len(UId) desc,UId desc");
            return this.NpgsqlSearchByList<UserRoleAuthorization>(sql.ToString(), new { entity_id = model.entity_id, r_id = model.r_id, emplid = model.emplid },
                "time_zone", timeZone);
        }
        #endregion

        #region 啟用主體
        /// <summary>
        /// 啟用主體
        /// </summary>
        /// <returns></returns>
        public int EnableEneity(int entity_id, string current_emp)
        {
            string sql = $@"
                        --修改主體狀態
                        UPDATE fnp_entity SET status = 0,stoptype = NULL,deliverydate=NULL,modify_user =  N''+@current_emp,modify_time = getutcdate()  WHERE entity_id = @entity_id
                        --刪除主體步驟信息
                        DELETE FROM sys_entity_manage_step WHERE entity_id = @entity_id;
                        UPDATE flow_sign_level set is_used = 1 where entity_id =  N''+@entity_id;";
            return this.ExecuteCommand(sql, new { entity_id = entity_id.ToString(), current_emp = current_emp });
        }
        #endregion

        #region 變更主體關聯合約管理公告狀態
        /// <summary>
        /// 變更主體關聯合約管理公告狀態
        /// </summary>
        /// <returns></returns>
        public int UpdateEntityContractStatus(string entity_id, int news_status, string current_emp)
        {
            string sql = $@"
                        UPDATE fnp_news SET news_status = @news_status,modify_user =  N''+@current_emp,modify_time = getutcdate()
                        WHERE newid in (select fn.newid from fnp_news_entity fne
                          join fnp_entity fe 
                          on fne.entity_id = fe.entity_id 
                          join fnp_news fn
                          on fn.newid = fne.newid 
                          where fne.entity_id = @entity_id
                          and fn.news_subject = CONCAT(N'', fe.entity , N'合約管理人Contract Admin')) and is_builtin = 1 and leaf_id = '02'";
            return this.ExecuteCommand(sql, new { entity_id = entity_id, news_status = news_status, current_emp = current_emp });
        }
        #endregion

        #region 檢查是否有總部行政
        /// <summary>
        /// 檢查是否有總部行政
        /// </summary>
        /// <returns></returns>
        public bool CheckCkWhqLowAdmin(int entity_id)
        {
            string sql = $@"select count(1) from sys_approver_management where entity_id = N''+@entity_id and 'ck_whq_low_admin' = approver_type";
            return Convert.ToInt32(this.NpgsqlSearchBySingleValue(sql, new { entity_id = entity_id.ToString() })) > 0;
        }
        #endregion

        #region 檢查區域是否存在
        /// <summary>
        /// 檢查區域是否存在
        /// </summary>
        /// <returns></returns>
        public bool VerifyArea(int? areaId)
        {
            string sql = $@"select count(1) from sys_area where area_id = @area_id";
            return Convert.ToInt32(this.NpgsqlSearchBySingleValue(sql, new { area_id = areaId })) > 0;
        }
        #endregion

        #region 匯出
        /// <summary>
        /// 匯出
        /// </summary>
        /// <param name="entities"></param>
        /// <param name="logging_locale"></param>
        /// <returns></returns>
        public List<ExportEntity> QueryEntityInfo(string entities, string logging_locale = "ZH-TW")
        {
            StringBuilder sql = new StringBuilder();
            sql.Append($@"select en.*,com.company,com.level_start,com.level_end from (
                    SELECT entity_id,entity AS entity,(entity_namec + '/' + entity_namee) AS entity_name,
                    (SELECT fun_name FROM sys_parameters WHERE para_code = 'entityStopReason' AND lang_type = @logging_locale AND func_code = CONVERT(NVARCHAR,fe.stoptype)) as stop_reason FROM fnp_entity fe WHERE 1 = 1");
            if (!string.IsNullOrEmpty(entities))
                sql.Append($@"and entity_id in (SELECT value FROM STRING_SPLIT(@entities, ','))");
            sql.Append($@")en
                    join
                    (SELECT
                    fer.entity_id,
                    fer.company AS company,
                    ISNULL(fsl.level_start,5) AS level_start,
                    ISNULL(fsl.level_end,2) AS level_end
                    FROM fnp_ec_relation AS fer
                    LEFT JOIN flow_sign_level AS fsl ON fer.entity_id = fsl.entity_id AND fer.company = fsl.company
                    WHERE 1=1");
            if (!string.IsNullOrEmpty(entities))
                sql.Append($@"and fer.entity_id in (SELECT value FROM STRING_SPLIT(@entities, ','))");

            sql.Append($@")com
                    on en.entity_id = com.entity_id where 1 = 1");
            if (!string.IsNullOrEmpty(entities))
                sql.Append($@"and com.entity_id in (SELECT value FROM STRING_SPLIT(@entities, ','))");
            return this.NpgsqlSearchByList<ExportEntity>(sql.ToString(), new { entities = entities, logging_locale = logging_locale });
        }
        #endregion

        /// <summary>
        /// 查詢主体信息
        /// </summary>
        /// <returns></returns>
        public List<EntityEmployeeInfo> QueryEntityEmpInfo(string entities)
        {
            StringBuilder sql = new StringBuilder();
            sql.Append($@"SELECT entity_id,approver_type,STRING_AGG((ee.name + '/' + ee.name_a),',') AS emp_info FROM (
                SELECT DISTINCT entity_id,approver_type,emp_id FROM sys_approver_management 
                WHERE 1=1");
            if (!string.IsNullOrEmpty(entities))
                sql.Append($@"AND entity_id in (SELECT value FROM STRING_SPLIT(@entities, ','))");

            sql.Append($@"UNION ALL
                SELECT DISTINCT pr.entity_id,'contractUser' AS approver_type,u_id AS emp_id FROM p_user_role AS pur
                INNER JOIN (SELECT pr.r_id,fer.entity_id FROM p_role AS pr 
                join fnp_entity_role fer
                on pr.r_id = fer.role_id
                WHERE pr.r_name LIKE N'%合約管理人%'");
            if (!string.IsNullOrEmpty(entities))
                sql.Append($@"and fer.entity_id in (SELECT value FROM STRING_SPLIT(@entities, ','))");

            sql.Append($@") AS pr ON pr.r_id = pur.r_id
                ) AS userData
                LEFT JOIN (SELECT emplid,name,name_a FROM ps_sub_ee_lgl_vw_a) AS ee ON ee.emplid = userData.emp_id
                group by entity_id,approver_type");
            return this.NpgsqlSearchByList<EntityEmployeeInfo>(sql.ToString(), new { entities = entities });
        }

        #region 獲取主體流程
        /// <summary>
        /// 獲取主體流程
        /// </summary>
        /// <param name="entity_stepid"></param>
        /// <param name="logging_locale"></param>
        /// <returns></returns>
        public string GetStep(int? entity_stepid, string logging_locale = "ZH-TW")
        {
            string sql = @"select para_name from sys_parameters 
                                where func_code = CONVERT(NVARCHAR,@entity_stepid)
                                and para_code = 'entityManageStep'
                                and lang_type = @lang_type";
            return (string)this.NpgsqlSearchBySingleValue(sql, new { entity_stepid = entity_stepid, lang_type = logging_locale });
        }
        #endregion

        #region 查詢原始授權公司數據
        /// <summary>
        /// 查詢原始授權公司數據
        /// </summary>
        /// <returns></returns>
        public List<ModifyEntityInfo> QueryAuthCompanyAndEntity(string entity_id)
        {
            string sql = $@"SELECT
                            fer.entity_id,
                            fer.company,--授權公司碼
                            (select descrshort from ps_sub_comp_vw_a where company=fer.company) company_name,
                            ISNULL(fsl.level_start,5) AS level_start,--起始簽核層級
                            ISNULL(fsl.level_end,2) AS level_end,--最高簽核層級
                            ISNULL(fsl.modify_time,ISNULL(fsl.create_time,fer.create_time)) AS operate_time,--更新時間
                            ee.name AS operate_cuser,--更新人中文名
                            ee.name_a AS operate_euser--更新人英文名
                            FROM (select * from fnp_ec_relation 
						            union
						          select * from fnp_ac_relation) AS fer
                            LEFT JOIN (select * from flow_sign_level where is_used = 1) AS fsl ON fer.entity_id = fsl.entity_id AND fer.company = fsl.company
                            LEFT JOIN (SELECT emplid,name,name_a FROM ps_sub_ee_lgl_vw_a) AS ee ON ee.emplid = ISNULL(modify_user,ISNULL(fsl.create_user,fer.create_user))
                            WHERE fer.entity_id = @entity_id order by fer.company";
            return this.NpgsqlSearchByList<ModifyEntityInfo>(sql, new { entity_id = entity_id });
        }
        #endregion

        #region 清理主體停用導致的實際公司代碼移除邏輯
        /// <summary>
        /// 清理主體停用導致的實際公司代碼移除邏輯
        /// </summary>
        /// <returns></returns>
        public int ClearActualCompanyRelationData(string entity_id)
        {
            string sql = @"	BEGIN TRY
                    			BEGIN TRANSACTION   
								-- 创建临时表，结构要和CTE结果集匹配
								CREATE TABLE #com (
								    entity_id INT ,  -- 根据实际数据类型调整
								    company VARCHAR(50) COLLATE Chinese_Taiwan_Stroke_CI_AS  -- 根据实际数据类型调整
								);
								
								INSERT INTO #com
							--启用主体
							SELECT DISTINCT fe.entity_id, com.company
							FROM fnp_ec_relation com
							JOIN fnp_entity fe ON com.entity_id = fe.entity_id
							--篩選原主體的實際公司代碼
							WHERE company IN (
							    SELECT company
							    FROM fnp_ac_relation
							    WHERE entity_id = @entity_id
							)
							--篩選狀態為啟用的
							AND fe.status = 0
							--排除掉停用的主體本身
							AND com.entity_id!= @entity_id
							--篩選啟用主體刪除後不能為空公司代碼
							AND (
							    SELECT COUNT(1)
							    FROM (
							        SELECT *
							        FROM fnp_ec_relation  where company not in (SELECT company
							                                                    FROM fnp_ac_relation
							                                                    WHERE entity_id = @entity_id)
							        UNION
							        SELECT *
							        FROM fnp_ac_relation
							    ) c
							    WHERE c.company NOT IN (
							        SELECT company
							        FROM fnp_ac_relation
							        WHERE entity_id = @entity_id
							    )
							    AND com.entity_id = c.entity_id
							) > 0
							UNION
							--停用主體可以直接刪除關聯公司代碼
							SELECT DISTINCT fe.entity_id, com.company
							FROM fnp_ec_relation com
							JOIN fnp_entity fe ON com.entity_id = fe.entity_id
							--篩選原主體的實際公司代碼
							WHERE company IN (
							    SELECT company
							    FROM fnp_ac_relation
							    WHERE entity_id = @entity_id
							)
							--篩選狀態為停用的
							AND fe.status = 1
							AND com.entity_id!= @entity_id;

                            delete from fnp_ec_relation where entity_id = @entity_id;
								    
                           --20250218 uat issue165 注釋掉停用場景刪除其他主體的實際公司代碼
                           --delete fnp_ac_relation from fnp_ac_relation 
                           --join #com on #com.entity_id = fnp_ac_relation.entity_id and #com.company = fnp_ac_relation.company;
                            
                           delete fnp_ec_relation from fnp_ec_relation  
                            join #com on #com.entity_id = fnp_ec_relation.entity_id and #com.company = fnp_ec_relation.company;
                           
                           update flow_sign_level set is_used = 0 where company in 
                                    (select company from fnp_ac_relation where entity_id = @entity_id)

                           
                           
                           drop table #com
                           
                           COMMIT TRANSACTION
                END TRY
                BEGIN CATCH
                    IF @@TRANCOUNT > 0
                        ROLLBACK TRANSACTION
                END CATCH
                           ";
            return this.ExecuteCommand(sql, new { entity_id = entity_id });
        }
        #endregion

        #region 查詢實際公司代碼關聯的其他主體
        /// <summary>
        /// 查詢實際公司代碼關聯的其他主體
        /// </summary>
        /// <returns></returns>
        public string QueryOtherEntityRelationCompany(string entityId, string actual_company)
        {
            string sql = @"select string_agg(entity,', ') from 
									(
									--啟用主體
                                    select DISTINCT fe.entity,fe.entity_id from fnp_ec_relation  com
                                    join fnp_entity fe on com.entity_id = fe.entity_id
                                    --篩選原主體的實際公司代碼
                                    where company in (select value from STRING_SPLIT(@actual_company,','))
                                    --篩選狀態為啟用的
                                    and fe.status = 0 
                                    --排除掉停用的主體本身
                                    and com.entity_id != @entityId
                                    --篩選啟用主體刪除後不能為空公司代碼
                                    and (select count (1) from (select * from fnp_ec_relation where company not in (select value from STRING_SPLIT(@actual_company,','))
							                                    union
							                                    select * from fnp_ac_relation) c
                                                                where com.entity_id = c.entity_id 
                                                                ) > 0 
								   UNION 
								   --停用主體可以直接刪除關聯公司代碼
								    select DISTINCT fe.entity,fe.entity_id from fnp_ec_relation com
	                                join fnp_entity fe on com.entity_id = fe.entity_id
	                                --篩選原主體的實際公司代碼
		                            where company in (select value from STRING_SPLIT(@actual_company,','))
		                            --篩選狀態為停用的
		                            and fe.status = 1
								    and com.entity_id != @entityId )a	";
            return (string)this.NpgsqlSearchBySingleValue(sql, new { entityId = entityId, actual_company = actual_company });
        }
        #endregion

        #region 查詢其他啟用主體關聯的公司代碼僅只有要移除的公司代碼
        /// <summary>
        /// 查詢其他啟用主體關聯的公司代碼僅只有要移除的公司代碼
        /// </summary>
        /// <returns></returns>
        internal string QueryOtherEntityOnlyRelationCompany(string entityId, string actual_company)
        {
            string sql = @"select string_agg(entity,', ') from 
									(
									--啟用主體
                                    select DISTINCT fe.entity,fe.entity_id from fnp_ec_relation  com
                                    join fnp_entity fe on com.entity_id = fe.entity_id
                                    --篩選原主體的實際公司代碼
                                    where company in (select value from STRING_SPLIT(@actual_company,','))
                                    --篩選狀態為啟用的
                                    and fe.status = 0 
                                    --排除掉停用的主體本身
                                    and com.entity_id != @entityId
                                    --篩選啟用主體刪除後不能為空公司代碼
                                    and (select count (1) from (select * from fnp_ec_relation where company not in (select value from STRING_SPLIT(@actual_company,','))
							                                    union
							                                    select * from fnp_ac_relation) c
                                                                where com.entity_id = c.entity_id 
                                                                ) = 0
								   )a";
            return (string)this.NpgsqlSearchBySingleValue(sql, new { entityId = entityId, actual_company = actual_company });
        }
        #endregion

        #region 校驗實際公司代碼有沒有被作為其他主體的實際公司代碼
        /// <summary>
        /// 校驗實際公司代碼有沒有被作為其他主體的實際公司代碼
        /// </summary>
        /// <returns></returns>
        public bool VerifyActualCompany(string actual_company, string entity_id = "null")
        {
            string sql = @"select count(1) from fnp_ac_relation where entity_id != @entity_id and company in (SELECT value FROM STRING_SPLIT(@actual_company, ','))";
            return this.NpgsqlSearchBySingleValue(sql, new { actual_company = actual_company, entity_id = entity_id }).Equals(0);
        }
        #endregion

        #region 校驗實際公司代碼有沒有被作為其他主體的實際公司代碼
        /// <summary>
        /// 校驗實際公司代碼有沒有被作為其他主體的實際公司代碼
        /// </summary>
        /// <returns></returns>
        public string QueryActualCompany(string actual_company)
        {
            string sql = @"select (select entity from fnp_entity where entity_id = ac.entity_id) from fnp_ac_relation ac where company in (SELECT value FROM STRING_SPLIT(@actual_company, ','))";
            return (string)this.NpgsqlSearchBySingleValue(sql, new { actual_company = actual_company });
        }
        #endregion

        #region 查詢簽核層級發生變化的數據
        /// <summary>
        /// 查詢簽核層級發生變化的數據
        /// </summary>
        /// <returns></returns>
        public List<ModifyEntityInfo> QueryRevisedAuthCompanyAndEntity(List<ModifyEntityInfo> modifyEntityInfos)
        {
            string entity_id = "";
            if (modifyEntityInfos != null)
            {
                ModifyEntityInfo? modifyEntityInfo = modifyEntityInfos.FirstOrDefault();
                if (modifyEntityInfo != null)
                {
                    entity_id = modifyEntityInfo.EntityId;
                }
            }
            StringBuilder sb = new StringBuilder();
            sb.Append(@"DECLARE @temp TABLE (  
                            id INT IDENTITY(1,1),
                            entity_id NVARCHAR(10) COLLATE Chinese_Taiwan_Stroke_CI_AS,
                            company NVARCHAR(10) COLLATE Chinese_Taiwan_Stroke_CI_AS,
                            level_start int,
                            level_end int
                        )  ");

            modifyEntityInfos.ForEach(m =>
            {
                sb.Append(@$"INSERT INTO @temp (entity_id,company,level_start,level_end)  
                            values ('{entity_id}','{m.company}',{m.level_start},{m.level_end});
");
            });

            sb.Append(@$"select t.* from @temp t join (
                        SELECT
                        							fer.entity_id,
                                                    fer.company,--授權公司碼
                                                    ISNULL(fsl.level_start,5) AS level_start,--起始簽核層級
                                                    ISNULL(fsl.level_end,2) AS level_end--最高簽核層級
                                                    FROM (select * from fnp_ec_relation 
						                                    union
						                                  select * from fnp_ac_relation) AS fer
                                                    LEFT JOIN (select * from flow_sign_level where is_used = 1) AS fsl ON fer.entity_id = fsl.entity_id AND fer.company = fsl.company
                                                    LEFT JOIN (SELECT emplid,name,name_a FROM ps_sub_ee_lgl_vw_a) AS ee ON ee.emplid = ISNULL(modify_user,ISNULL(fsl.create_user,fer.create_user))
                                                    WHERE fer.entity_id = @entity_id)history
                        on history.entity_id = t.entity_id and history.company = t.company
                        where (t.level_start != history.level_start or t.level_end != history.level_end)
                        order by t.company");
            return this.NpgsqlSearchByList<ModifyEntityInfo>(sb.ToString(), new { entity_id = entity_id });
        }
        #endregion

        #region 查詢修改之前的簽核層級數據
        /// <summary>
        /// 查詢修改之前的簽核層級數據
        /// </summary>
        /// <returns></returns>
        public List<ModifyEntityInfo> QueryFormerAuthCompanyAndEntity(string entity_id, string company)
        {
            string sql = @"SELECT
                        							fer.entity_id,
                                                    fer.company,--授權公司碼
                                                    ISNULL(fsl.level_start,5) AS level_start,--起始簽核層級
                                                    ISNULL(fsl.level_end,2) AS level_end--最高簽核層級
                                                    FROM (select * from fnp_ec_relation 
						                                    union
						                                  select * from fnp_ac_relation) AS fer
                                                    LEFT JOIN (select * from flow_sign_level where is_used = 1) AS fsl ON fer.entity_id = fsl.entity_id AND fer.company = fsl.company
                                                    WHERE fer.entity_id = @entity_id and fer.company in (SELECT value FROM STRING_SPLIT(@company, ','))";
            return this.NpgsqlSearchByList<ModifyEntityInfo>(sql, new { entity_id = entity_id, company = company });
        }
        #endregion

        /// <summary>
        /// 檢查合約管理員是否至少一人
        /// </summary>
        /// <returns></returns>
        public bool CheckContractManager(int entity_id)
        {
            string sql = @"SELECT count(1)
                             FROM p_role AS pr 
                             join fnp_entity_role fer
                             on pr.r_id = fer.role_id
                             join fnp_entity fe 
                             on fe.entity_id = fer.entity_id 
                             join p_user_role pur 
                             on pur.r_id = pr.r_id 
                             join ps_sub_ee_lgl_vw_a ee
                             on ee.emplid = pur.u_id
                             WHERE pr.r_name = concat(fe.entity,N'合約管理人Contract Admin') 
                             and ee.termination is NULL
                             and fe.entity_id = @entity_id";
            return Convert.ToInt32(this.NpgsqlSearchBySingleValue(sql.ToString(), new { entity_id = entity_id })) > 0 ? true : false;
        }

        /// <summary>
        /// 查詢其他申請單關聯人員部門和申請單
        /// </summary>
        /// <returns></returns>
        internal List<EntityRelationRole> QueryOtherEntityRelation(string entityId)
        {
            string sql = @"select a.emp_info,fe.entity,a.deptid,a.apply_number,a.reason from 
(select DISTINCT null as emp_info,deptid,entity_id,apply_number,N'特殊主體授權部門移除' as reason  from fnp_entity_dept_special 
                            where entity_id = @entityId
                            UNION 
                           select DISTINCT concat(ee.name,'(',ee.name_a,')') as emp_info,null as deptid,entity_id,apply_number,N'特殊主體授權人員移除' as reason  from fnp_entity_user_special feus
                            join (select emplid ,name, name_a from ps_sub_ee_lgl_vw_a) ee on feus.emplid = ee.emplid 
                            where feus.entity_id = @entityId)a 
                            join fnp_entity fe on a.entity_id = fe.entity_id ";
            return this.NpgsqlSearchByList<EntityRelationRole>(sql.ToString(), new { entityId = entityId });
        }

        /// <summary>
        /// 清理主體關聯特殊
        /// </summary>
        /// <returns></returns>
        internal int ClearEntityRelationRole(string entityId)
        {
            string logging_emp = MvcContext.UserInfo.logging_emp;
            string sql = @"delete from fnp_entity_dept_special where entity_id = @entityId;
                            delete from fnp_entity_user_special where entity_id = @entityId;

                            UPDATE eu
                            SET eu.deptid = feu.deptid,
                            eu.modify_user = @logging_emp,
                            eu.modify_time = GETUTCDATE() 
                            FROM fnp_entity_dept eu
                            JOIN (
                                SELECT deptid, STRING_AGG(entity_id, ',') AS entity_id
                                FROM fnp_entity_dept
                                WHERE entity_id LIKE concat(N'%',@entityId,N'%')
                                GROUP BY deptid
                            ) feu ON eu.deptid = feu.deptid;

                            UPDATE eu
                            SET eu.entity_id = feu.entity_id,
                            eu.modify_user = @logging_emp,
                            eu.modify_time = GETUTCDATE() 
                            FROM fnp_entity_user eu
                            JOIN (
                                SELECT emplid, STRING_AGG(entity_id, ',') AS entity_id
                                FROM fnp_entity_user
                                WHERE entity_id LIKE concat(N'%',@entityId,N'%')
                                GROUP BY emplid
                            ) feu ON eu.emplid = feu.emplid;";
            return this.ExecuteCommand(sql.ToString(), new { entityId = entityId , logging_emp = logging_emp });
        }
    }
}
