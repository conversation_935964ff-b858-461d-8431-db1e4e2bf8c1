﻿using Elegal.Flow.Api.Repository.Application;
using Elegal.Flow.Api.Services.Paper;
using Elegal.Flow.Common.Repository.FlowStep;
using Elegal.Interface.Api.Common.Control.Filter;
using Elegal.Interface.Api.Common.FuncHelper;
using Elegal.Interface.Api.Common.Model;
using Elegal.Interface.Api.Common.Model.Enum;
using Elegal.Interface.Api.Common.Model.ResultModel;
using Elegal.Interface.Api.Common.Model.ViewModel.FlowApi;
using Elegal.Interface.ApiData.Service.FuncService;
using Elegal.Interface.ApiData.Service.Model.Condition;
using Elegal.Interface.ApiData.Service.Model.DbModel;
using Elegal.Interface.ApiData.Service.Model.QueryCondition;
using Elegal.Interface.ApiData.Service.Model.ViewModel;
using Elegal.Orm;
using Elegal.Orm.Dtos;
using Minio;
using Minio.DataModel.Args;
using System.Text;

#nullable disable
namespace Elegal.Flow.Api.Services.Application
{
    /// <summary>
    /// 紙本进度追踪
    /// </summary>
    public class PaperTrackingService
    {
        #region 申請單簽核關卡匯總
        /// <summary>
        /// 申請單簽核關卡匯總
        /// </summary>
        private static readonly List<string> signOffStep = AppSettingHelper.GetValue("signOffStep").Split(",").ToList();
        #endregion

        //紙本进度追踪倉儲
        private static readonly PaperTrackingRepository _repository = new PaperTrackingRepository();
        private static readonly FlowStepRepository _flowRepository = new FlowStepRepository();

        #region 注入MinioClient、MinIOConfig
        /// <summary>
        /// 注入MinioClient
        /// </summary>
        public static MinioClient _minioClient;
        /// <summary>
        /// 注入MinioClient、MinIOConfig
        /// </summary>
        public static MinIOConfig _minIOConfig;
        #endregion

        #region 获取申请单基本信息
        /// <summary>
        /// 获取申请单基本信息
        /// </summary>
        /// <param name="applyNumber">申请单号</param>
        /// <returns></returns>
        public static PaperTrackingViewModel GetBaseInformation(string applyNumber)
        {
            #region 獲取需用到的參數列表
            var sysParameters = SysParametersDataService.Query(new SysParametersQueryCondition()
            {
                LangType = MvcContext.UserInfo.logging_locale,
                SearchItemGroup = new SearchItemGroup()
                {
                    Items = new List<SearchItem>() {
                        new SearchItem() {
                            Field = "para_code",
                            Values = [
                                "capitalContractStep",//資金合約申請簽核關卡
                                "hrContractStep",//人力資源合約申請簽核關卡
                                "generalContractStep",//一般合約申請簽核關卡
                                "rdContractStep",//資料建檔申請簽核關卡
                                "acknowledgeStep"//會簽簽核關卡
                                ],
                            Compare = CompareOperator.ARRAYIN
                        }
                    }
                }
            });
            #endregion

            #region 紙本進度基本數據
            PaperTrackingViewModel result = _repository.GetBaseInformation(applyNumber);
            result.handoverFileHistorys = _repository.GetFileHistoryData(applyNumber);
            result.sysUploadFiles = _repository.GetFileList(applyNumber, result.applicationType);
            result.paperTrackingJobs = _repository.GetPaperTrackWorkData(applyNumber);
            bool notSendEmail = false;
            //取最新收件日數據的郵件發送提醒內容
            result.btnMailNotice = PaperTrackingBathJobService.SendMailNotice(result.paperTrackingJobs.LastOrDefault()?.paperTracking, result.applicationType, ref notSendEmail);
            result.notSendEmail = notSendEmail;
            //一般加簽原因
            result.inviteeRemark = DbAccess.Query<FlowStepSignerInvitee>(new FlowStepSignerInviteeCondition { ApplyNumber = applyNumber, InviteeType = 1 }).FirstOrDefault()?.InviteeRemark ?? "";
            #endregion

            #region 應簽核人&當前關卡
            var users = new List<PsSubEeLglVwA>();
            //應簽核
            var flowStepSigners = DbAccess.Query<FlowStepSigner>(new FlowStepSignerCondition { ApplyNumber = applyNumber });
            if (flowStepSigners.Length != 0)
            {
                users = DbAccess.Query<PsSubEeLglVwA>(new PsSubEeLglVwAQueryCondition()
                {
                    SearchItemGroup = new SearchItemGroup()
                    {
                        Items =
                        [
                            new()
                            {
                                Compare = CompareOperator.ARRAYIN,
                                Field = "emplid",
                                Values =  flowStepSigners.Select(s=>s.SignerEmplid)
                            }
                        ]
                    }
                }).ToList();
                if (users.Count != 0) result.shouldSigners = string.Join(", ", users.Select(u => u.NameA));
                //Issue：238 根據會簽參數顯示當前關卡
                if (Convert.ToInt32(flowStepSigners.FirstOrDefault()?.StepId.ToString()) == _flowRepository.GetAcknowledgeStepid(Convert.ToInt32(flowStepSigners.FirstOrDefault()?.FlowId.ToString()))
                    && flowStepSigners.FirstOrDefault()?.AcknowledgeStep != null)
                {
                    result.currStepName = sysParameters.FirstOrDefault(f => f.FuncCode.Equals(flowStepSigners.FirstOrDefault()?.AcknowledgeStep.ToString()))?.FunName;
                }
                else
                {
                    result.currStepName = sysParameters.FirstOrDefault(f => f.FuncCode.Equals(flowStepSigners.FirstOrDefault()?.StepId.ToString()))?.FunName;
                }
                //result.currStepName = sysParameters.Where(w => w.ParaCode.Equals("rdContractStep") || w.ParaCode.Equals("generalContractStep") || w.ParaCode.Equals("hrContractStep") || w.ParaCode.Equals("capitalContractStep"))
                //    .FirstOrDefault(f => f.FuncCode.Equals(flowStepSigners.FirstOrDefault()?.StepId.ToString()))?.FunName;
            }
            #endregion

            #region 應簽核加簽人
            var flowStepSignerInvitees = DbAccess.Query<FlowStepSignerInvitee>(new FlowStepSignerInviteeCondition { ApplyNumber = applyNumber });
            //備注：加簽和特殊加簽不會同時存在
            if (flowStepSignerInvitees.Length != 0)
            {
                //篩選當前簽核層級的人
                flowStepSignerInvitees = flowStepSignerInvitees.Where(w => w.SpInviteLevel.Equals(flowStepSignerInvitees.OrderBy(b => b.SpInviteLevel).FirstOrDefault().SpInviteLevel)).ToArray();
                var inviteeUsers = new List<PsSubEeLglVwA>();
                inviteeUsers = DbAccess.Query<PsSubEeLglVwA>(new PsSubEeLglVwAQueryCondition()
                {
                    SearchItemGroup = new SearchItemGroup()
                    {
                        Items =
                        [
                            new()
                            {
                                Compare = CompareOperator.ARRAYIN,
                                Field = "emplid",
                                Values =  flowStepSignerInvitees.Select(s=>s.InviteeEmplid)
                            }
                        ]
                    }
                }).ToList();
                #region 一般加簽
                if (flowStepSignerInvitees.FirstOrDefault()?.InviteeType == 1)
                {
                    result.inviteeUser = string.Join(", ", inviteeUsers.Select(u => u.NameA));
                }
                #endregion
                #region 特殊加簽
                if (flowStepSignerInvitees.FirstOrDefault()?.InviteeType == 2)
                {
                    result.specialInviteeUser = string.Join(", ", inviteeUsers.Select(u => u.NameA));
                }
                #endregion
            }
            #endregion

            return result;
        }
        #endregion

        #region 法務行政交接備注檔案上傳
        /// <summary>
        /// 文件上传(检查文件总大小)
        /// </summary>
        /// <param name="formFile">上传的文件</param>
        /// <param name="option">文件参数</param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public static async Task<ApiResultModelByObject> UploadFileCheckSize(IFormFileCollection formFile, SysUploadFileViewModel option)
        {
            //記錄上傳成功、失敗的文件數據(内存超出、文件重名)
            List<string> successFiles = [], memoryExceededFiles = [], repeatFiles = [];
            foreach (var file in formFile)
            {
                #region minio文件上傳
                //minio存放路径
                string minioPath = option.FilePath + "/" + DateTime.Now.ToString("yyyyMMddHHmmss") + file.FileName;
                using (var stream = new MemoryStream())
                {
                    //minio存储文件
                    await file.CopyToAsync(stream);
                    stream.Seek(0, SeekOrigin.Begin);
                    var putObjectArgs = new PutObjectArgs()
                    .WithBucket(_minIOConfig.Bucket)
                    .WithObject(minioPath)
                    .WithStreamData(stream)
                    .WithObjectSize(stream.Length)
                    .WithContentType("application/octet-stream");
                    await _minioClient.PutObjectAsync(putObjectArgs).ConfigureAwait(false);
                }
                #endregion

                #region DB存儲數據
                //存儲之前判斷文件是否已存在
                if (DbAccess.Exists<SysUploadFile>(new SysUploadFile() { UploadKey = option.UploadKey, UploadType = option.UploadType, FileName = file.FileName, ArchivePurposes = option.ArchivePurposes }))
                {
                    repeatFiles.Add(file.FileName);
                    continue;
                }
                //文件縂大小超80M,停止上傳
                var contractFiles = SysUploadFileDataService.Query(new SysUploadFileQueryCondition()
                {
                    UploadType = option.UploadType,
                    UploadKey = option.UploadKey.ToUpper(),
                    ArchivePurposes = option.ArchivePurposes,
                    IsWatermake = (int)YesOrNoUtils.No
                });
                if (file.Length + contractFiles.Sum(s => s.FileSize) <= 1024 * 1024 * 80)
                {
                    SysUploadFile sysUploadFile = new()
                    {
                        UploadType = option.UploadType,
                        UploadKey = option.UploadKey.ToUpper(),
                        FileName = file.FileName,
                        FileType = Path.GetExtension(file.FileName).TrimStart('.'),
                        FilePath = minioPath,
                        FileSize = file.Length,
                        IsTemporary = option.IsTemporary,
                        CreateUser = MvcContext.UserInfo.current_emp,
                        ArchivePurposes = option.ArchivePurposes
                    };
                    //存儲之前判斷文件是否已存在
                    if (DbAccess.Exists<SysUploadFile>(new SysUploadFile() { UploadKey = option.UploadKey, UploadType = option.UploadType, FileName = file.FileName, ArchivePurposes = option.ArchivePurposes }))
                    {
                        repeatFiles.Add(file.FileName);
                        continue;
                    }
                    DbAccess.Create(sysUploadFile);
                    successFiles.Add(file.FileName);
                    int? fileId = DbAccess.Find<SysUploadFile>(sysUploadFile).Fileid;
                    //档案历程
                    HandoverFileHistoryDataService.Create(new HandoverFileHistory()
                    {
                        ApplyNumber = option.UploadKey.ToUpper(),
                        ActionId = 3,//代表新增
                        FileName = file.FileName,
                        CreateUser = MvcContext.UserInfo.current_emp,
                        CreateTime = DateTime.UtcNow
                    });
                }
                else memoryExceededFiles.Add(file.FileName);
                #endregion
            }
            ApiResultModelByObject result = new ApiResultModelByObject()
            {
                rtnSuccess = true,
                messageTitle = ActionFilter.GetMultilingualValue("custom:messageTitle:uploadSuccess"),
                messageContent = [ActionFilter.GetMultilingualValue("custom:messageContent:fileUpload")],
                listData = successFiles.Count,
            };
            //文件全部上傳失敗
            if (memoryExceededFiles.Count + repeatFiles.Count == formFile.Count)
                result = new ApiResultModelByObject()
                {
                    messageTitle = ActionFilter.GetMultilingualValue("custom:messageTitle:fileUploadFail"),
                    messageType = MessageTypeUtils.Warning.ToString()
                };
            if (memoryExceededFiles.Count > 0) result.messageContent.Add($"{ActionFilter.GetMultilingualValue($"ArchivalAttachments_sizeUpper", true)}：{string.Join("、", memoryExceededFiles)}");
            if (repeatFiles.Count > 0) result.messageContent.Add($"{ActionFilter.GetMultilingualValue($"ArchivalAttachments_fileExist", true)}：{string.Join("、", repeatFiles)}");
            ActionFilter.InitLogRecord(log => {
                StringBuilder stringBuilder = new StringBuilder();
                stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue($"ArchivalAttachments_applyNumber", true)}：{option.UploadKey}");
                stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue($"ArchivalAttachments_fileList", true)}：{string.Join("，", formFile.Select(s =>
                {
                    if (s.Length / 1024 > 1000) return $"{s.FileName}({Math.Round((decimal)s.Length / 1024 / 1024, 2)}MB)";
                    else if (s.Length > 1024) return $"{s.FileName}({Math.Round((decimal)s.Length / 1024, 0)}KB)";
                    else return $"{s.FileName}({s.Length}B)";
                }))}");
                log.Detail = stringBuilder.ToString();
            });
            return result;
        }
        #endregion

        #region 批量刪除文件
        /// <summary>
        /// 删除文件
        /// </summary>
        /// <param name="fileIds">文件id</param>
        /// <param name="minioClient">minio上下文</param>
        /// <param name="minIOConfig">minio配置</param>
        /// <returns></returns>
        public static async Task<ApiResultModelByObject> DeleteMultipleFile(List<int> fileIds, MinioClient minioClient, MinIOConfig minIOConfig)
        {
            ApiResultModelByObject result = new ApiResultModelByObject() { rtnSuccess = true, };
            //文件列表
            var sysUploadFileList = SysUploadFileDataService.Query(new SysUploadFileQueryCondition()
            {
                SearchItemGroup = new SearchItemGroup()
                {
                    Items = new List<SearchItem>()
                    {
                        new SearchItem() { Compare = CompareOperator.ARRAYIN,Values = fileIds.Select(s=>s.ToString()),Field = "fileid",Logic = LogicOperator.And}
                    }
                }
            });
            //文件异动
            if (fileIds.Except(sysUploadFileList.Select(s => Convert.ToInt32(s.Fileid))).Any())
            {
                return new ApiResultModelByObject()
                {
                    messageType = MessageTypeUtils.Warning.ToString(),
                    messageContent = new List<string> { ActionFilter.GetMultilingualValue("custom:messageContent:dataChurn") },
                    messageTitle = ActionFilter.GetMultilingualValue("custom:messageTitle:fileDeleteFail")
                };
            }
            List<string> deleteFile = new List<string>(), updateFile = new List<string>();
            List<HandoverFileHistory> fileHistory = [];
            foreach (var sysUploadFile in sysUploadFileList)
            {
                //删除minio文件&数据记录
                await minioClient.RemoveObjectAsync(new RemoveObjectArgs().WithBucket(minIOConfig.Bucket).WithObject(sysUploadFile.FilePath));
                deleteFile.Add(sysUploadFile.Fileid.ToString());
                //记录文件历程
                fileHistory.Add(new HandoverFileHistory() { ApplyNumber = sysUploadFile.UploadKey, ActionId = 4, CreateUser = MvcContext.UserInfo.current_emp, FileName = sysUploadFile.FileName });
            }
            DbAccess.BatchDeleteSql<SysUploadFile, string>([.. deleteFile]);
            DbAccess.BatchCreateSql(fileHistory.ToArray());
            ActionFilter.InitLogRecord(log =>
            {
                StringBuilder stringBuilder = new StringBuilder();
                stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue($"ArchivalAttachments_applyNumber", true)}：{fileHistory.FirstOrDefault()?.ApplyNumber}");
                stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue($"ArchivalAttachments_fileList", true)}：{string.Join("，", fileHistory.Select(s =>s.FileName))}");
                log.Detail = stringBuilder.ToString();
            });
            return result;
        }
        #endregion

        #region 保存法務行政交接備注信息
        /// <summary>
        /// 保存法務行政交接備注信息
        /// </summary>
        /// <param name="applyNumber"></param>
        /// <param name="notes"></param>
        /// <returns></returns>
        public static bool SetNotes(string applyNumber, string notes)
        {
            var data = FormLegalHandoverDataService.FindByKey(applyNumber);
            string oldNotes = "";
            //新增
            if (data == null) FormLegalHandoverDataService.Create(new FormLegalHandover()
            {
                ApplyNumber = applyNumber,
                HandoverRemark = notes,
                CreateUser = MvcContext.UserInfo.current_emp
            });
            //更新
            else
            {
                oldNotes = data.HandoverRemark;
                data.HandoverRemark = notes;
                data.ModifyTime = DateTime.UtcNow;
                data.ModifyUser = MvcContext.UserInfo.current_emp;
                FormLegalHandoverDataService.Update(data);
            }
            ActionFilter.InitLogRecord(log =>
            {
                if (data != null)
                    log.DetailFormer = $"{ActionFilter.GetMultilingualValue("PaperTracking_applyNum", true)}：{applyNumber}\r\n{ActionFilter.GetMultilingualValue("PaperTracking_notes", true)}：{oldNotes}";
                log.Detail = $"{ActionFilter.GetMultilingualValue("PaperTracking_applyNum", true)}：{applyNumber}\r\n{ActionFilter.GetMultilingualValue("PaperTracking_notes", true)}：{notes}";
            });
            return true;
        }
        #endregion
    }
}
