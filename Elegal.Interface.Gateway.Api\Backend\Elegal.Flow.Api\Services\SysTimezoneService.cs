﻿using Elegal.Interface.Api.Common.FuncHelper;
using Elegal.Interface.Api.Common.Repository;
using System.IdentityModel.Tokens.Jwt;

namespace Elegal.Flow.Api.Services
{
    /// <summary>
    /// 時區設定
    /// </summary>
    public class SysTimezoneService
    {
        private static readonly TokenRepository _repository = new();

        public static void UpdateTokenLocale(string localToken, string? locale, string? timezone)
        {
            try
            {
                JwtSecurityTokenHandler handler = new JwtSecurityTokenHandler();
                JwtSecurityToken decodedToken = handler.ReadJwtToken(localToken);

                //透過localToken取得當前登入人員工號
                string emplid = decodedToken.Payload["logging_emp"].ToString();

                //透過localToken的工號取得當前token
                string dbToken = _repository.GetUserToken(emplid);
                if (string.IsNullOrEmpty(dbToken)) return;

                decodedToken = handler.ReadJwtToken(dbToken);
                if (!string.IsNullOrEmpty(locale)) decodedToken.Payload["locale"] = locale;
                if (!string.IsNullOrEmpty(timezone)) decodedToken.Payload["timezone"] = timezone;

                string idToken = handler.WriteToken(decodedToken);

                //如果是IT測試帳號，則不需要更新Token
                if (!decodedToken.Payload.TryGetValue("it_user", out object it_user))
                {
                    _repository.RefreshToken(emplid, idToken);
                }
            }
            catch (Exception ex)
            {
                // 解碼過程中發生錯誤，處理錯誤邏輯...
                // 例如，拋出自定義的例外或進行其他錯誤處理操作
                throw new Exception("Failed to decode ID Token: " + ex.Message);
            }
        }

        #region 獲取時區id中是否為夏令時以及與utc時間比對的偏移量
        /// <summary>
        /// 獲取時區id中是否為夏令時以及與utc時間比對的偏移量
        /// </summary>
        /// <param name="timeZoneId">時區ID</param>
        /// <returns></returns>
        public static Dictionary<string, object> GetTimeZoneDaylight(string timeZoneId)
        {
            //根據前端傳遞的時區id將utc時間轉換為對應的時間
            DateTime summerDatet = TimeZoneInfoConvertHelper.ConvertDateByTimeZoneByUtc(DateTime.UtcNow, timeZoneId);
            //根據前端傳遞的時區id獲取TimeZoneInfo中對應的時區數據
            TimeZoneInfo tzi = TimeZoneInfo.FindSystemTimeZoneById(timeZoneId);
            Dictionary<string, object> dt = new Dictionary<string, object>();
            //驗證是否為夏令時 true：是；false：否
            dt["isDaylight"] = tzi.IsDaylightSavingTime(summerDatet);
            //獲取選中的時區id比utc時間存在多少偏移量
            dt["utcOffset"] = tzi.GetUtcOffset(DateTime.UtcNow).TotalHours;
            return dt;
        }
        #endregion
    }
}
