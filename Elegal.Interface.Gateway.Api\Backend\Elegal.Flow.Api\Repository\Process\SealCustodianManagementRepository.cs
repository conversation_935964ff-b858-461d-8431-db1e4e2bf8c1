﻿using Elegal.Interface.Api.Common.Model.ParaModel.FlowApi;
using Elegal.Interface.Api.Common.Model.ResultModel.FlowApi;
using Elegal.Interface.Api.Common.Repository;
using System.Text;

namespace Elegal.Flow.Api.Repository.Process
{
    /// <summary>
    /// 用印人管理
    /// </summary>
    public class SealCustodianManagementRepository : BaseRepository
    {
        /// <summary>
        /// 實例化
        /// </summary>
        public SealCustodianManagementRepository() : base() { }

        /// <summary>
        /// 印鑑保管人管理頁面查詢
        /// </summary>
        /// <returns></returns>
        public List<SealCustodianResult> QuerySealCustodian(SealCustodianSearchModel sealCustodian, string logging_locale, string time_zone)
        {
            StringBuilder sqlBuilder = new();

            sqlBuilder.Append(@$"SELECT
                                scm.rowid,
                                fe.entity,--主體公司簡稱
                                sa.area_name,--區域
                                sa.area_id,
                                (SELECT fun_name FROM dbo.sys_parameters WHERE para_code = N'sealType' AND lang_type = @logging_locale AND func_code = scm.seal_type) AS seal_name,--用印種類
                                scm.emp_id,--員工工號
                                ee.name,--員工中文名稱
                                ee.name_a,--員工英文名稱
                                CASE WHEN RTRIM(LTRIM(ISNULL(ee.prefix_dial_code_a,''))) = N'' THEN '0000' ELSE RTRIM(LTRIM(ISNULL(ee.prefix_dial_code_a,''))) END AS prefix_dial_code_a,--區碼
                                RTRIM(LTRIM(ISNULL(ee.phone_a, ''))) AS phone_a,--分機
                                ee.email_address_a,--郵箱地址
                                CASE WHEN ISNULL(ee.termination,'') = '' THEN 1 ELSE 0 END AS emp_status,--任職狀態 在職(1)/離職(0)
                                scm.seal_remark,--備註
                                mee.name AS operate_cuser,--更新人中文名
                                mee.name_a AS operate_euser,--更新人英文名
                                ISNULL(scm.modify_time,scm.create_time) AS operate_time--更新時間
                                FROM (select rowid,emp_id,seal_type,seal_remark,entity_id,modify_time,create_time,modify_user,create_user from seal_custodian_management where 1=1 ");
            if (!string.IsNullOrEmpty(sealCustodian.entity.Key))
                sqlBuilder.Append(@$" and entity_id = @entity_id");
            string keysAsString = string.Empty;
            if (sealCustodian.seal_types != null && sealCustodian.seal_types.Count > 0)
            {
                keysAsString = string.Join(",", sealCustodian.seal_types.Select(kvp => kvp.Key));
                sqlBuilder.Append(@$" AND seal_type in (SELECT value FROM STRING_SPLIT(@seal_type, ',')) ");
            }
            sqlBuilder.Append(@$" ) AS scm
                                LEFT JOIN (SELECT entity_id,entity,area_id FROM dbo.fnp_entity) AS fe ON fe.entity_id = scm.entity_id
                                LEFT JOIN (SELECT area_id,area_name FROM dbo.sys_area");
            sqlBuilder.Append(@$" ) AS sa ON sa.area_id = fe.area_id
                                LEFT JOIN (SELECT emplid,name,name_a,email_address_a,termination,prefix_dial_code_a,phone_a FROM dbo.ps_sub_ee_lgl_vw_a) AS ee ON ee.emplid = scm.emp_id
                                LEFT JOIN (SELECT emplid,name,name_a FROM dbo.ps_sub_ee_lgl_vw_a) AS mee ON mee.emplid = ISNULL(scm.modify_user,scm.create_user)
                                WHERE 1 = 1");
            if (sealCustodian.area.Key != 0 && sealCustodian.area.Key != null)
                sqlBuilder.Append(@$" and fe.area_id = @area_id");
            if (!string.IsNullOrEmpty(sealCustodian.emp_id))
                sqlBuilder.Append(@$" AND ((ee.emplid = CONCAT(N'', @emplid) OR ee.emplid LIKE CONCAT(N'%', @emplid, N'%')) OR (ee.name = CONCAT(N'', @emplid) OR ee.name LIKE CONCAT(N'%', @emplid, N'%')) OR (ee.name_a = CONCAT(N'', @emplid) OR ee.name_a LIKE CONCAT(N'%',@emplid, N'%')))");
            sqlBuilder.Append(@$" order by sa.area_name, fe.entity, seal_name, stuff(scm.emp_id,1,patindex('%[A-z]%',substring(scm.emp_id,1,1))-1,'') asc, len(scm.emp_id) desc,scm.emp_id desc");
            return NpgsqlSearchByList<SealCustodianResult>(sqlBuilder.ToString(), new { area_id = sealCustodian.area.Key, entity_id = sealCustodian.entity.Key, seal_type = keysAsString, emplid = sealCustodian.emp_id, logging_locale },
                "time_zone", time_zone);
        }

        /// <summary>
        /// 删除数据
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        internal int DeleteByKey(int rowid)
        {
            string sql = @"delete from seal_custodian_management where rowid = @rowid";
            return ExecuteCommand(sql, new { rowid });
        }
    }
}
