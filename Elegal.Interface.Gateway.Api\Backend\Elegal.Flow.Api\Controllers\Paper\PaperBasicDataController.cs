﻿using Elegal.Flow.Api.Services.Paper;
using Elegal.Interface.Api.Common.Control.Filter;
using Elegal.Interface.Api.Common.Control.Web;
using Elegal.Interface.Api.Common.Model.DBModel;
using Elegal.Interface.Api.Common.Model.ParaModel.FlowApi;
using Elegal.Interface.Api.Common.Model.ResultModel;
using Elegal.Interface.Api.Common.Model.ViewModel.FlowApi;
using Elegal.Orm.Dtos;
using Elegal.Orm.Exceptions;
using Microsoft.AspNetCore.Mvc;
using System.Text;

namespace Elegal.Flow.Api.Controllers.Paper
{
    /// <summary>
    /// 紙本基本資料
    /// </summary>
    [Route("[controller]")]
    [ApiController]
    public class PaperBasicDataController : BaseController
    {
        /// <summary>
        /// 查詢存放位置清單
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("GetPaperPositionList")]
        public async Task<ApiResultModelByObject> GetPaperPositionList()
        {
            ApiResultModelByObject apiResult = new();

            apiResult.listData = PaperBasicDataService.GetPaperPositionList();
            apiResult.rtnSuccess = true;
            InitLogRecord<object>(new(), logRecord =>
            {
                StringBuilder stringBuilder = new();
                stringBuilder.AppendLine($"查詢存放位置清單");
                logRecord.Detail = stringBuilder.ToString();
            });

            return await Task.FromResult(apiResult);
        }

        /// <summary>
        /// 查詢紙本基本資料
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [Route("Query")]
        public async Task<ApiResultModelByObject> QueryPaperBasicData([FromBody] qryPaperBasicData qry)
        {
            ApiResultModelByObject apiResult = new ApiResultModelByObject();

            PageResult<PaperBasicDataViewModel> result = PaperBasicDataService.QueryPaperBasicData(qry);
            apiResult.listData = result.Data;
            apiResult.totalCount = result.TotalRows;
            apiResult.rtnSuccess = true;

            InitLogRecord(qry, logRecord =>
            {
                StringBuilder stringBuilder = new();
                if (!string.IsNullOrEmpty(qry.paper_code)) stringBuilder.AppendLine($"紙本/合約編號:  {qry.paper_code}");
                if (!string.IsNullOrEmpty(qry.paper_name)) stringBuilder.AppendLine($"紙本/合約名稱:  {qry.paper_name}");
                if (!string.IsNullOrEmpty(qry.paper_type)) stringBuilder.AppendLine($"紙本類型:  {PaperBasicDataService.GetTypeString("lib_paperType", qry.paper_type)}");
                if (!string.IsNullOrEmpty(qry.apply_number)) stringBuilder.AppendLine($"申請單號:  {qry.apply_number}");
                if (!string.IsNullOrEmpty(qry.my_entity_id)) stringBuilder.AppendLine($"我方主體:  {PaperBasicDataService.GetEntityString(qry.my_entity_id)}");
                if (!string.IsNullOrEmpty(qry.paper_entry_status)) stringBuilder.AppendLine($"入庫狀態:  {PaperBasicDataService.GetTypeString("lib_paperEntryStatus", qry.paper_entry_status)}");
                if (!string.IsNullOrEmpty(qry.paper_confiden_level)) stringBuilder.AppendLine($"機密等級:  {PaperBasicDataService.GetTypeString("confidentStatus", qry.paper_confiden_level)}");
                string paper_position = string.Empty;
                if (qry.paper_position != null && qry.paper_position.Count > 0)
                {
                    paper_position = string.Join(",", qry.paper_position);
                }
                if (!string.IsNullOrEmpty(paper_position)) stringBuilder.AppendLine($"存放位置:  {paper_position}");

                logRecord.Detail = stringBuilder.ToString();
            });

            return await Task.FromResult(apiResult);
        }

        /// <summary>
        /// 匯出紙本基本資料
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [Route("Export")]
        public FileContentResult ExportPaperBasicData([FromBody] qryPaperBasicData qry)
        {
            var excelBytes = PaperBasicDataService.ExportPaperBasicData(qry);
            InitLogRecord(qry, logRecord =>
            {
                StringBuilder stringBuilder = new();
                if (!string.IsNullOrEmpty(qry.paper_code)) stringBuilder.AppendLine($"紙本/合約編號:  {qry.paper_code}");
                if (!string.IsNullOrEmpty(qry.paper_name)) stringBuilder.AppendLine($"紙本/合約名稱:  {qry.paper_name}");
                if (!string.IsNullOrEmpty(qry.paper_type)) stringBuilder.AppendLine($"紙本類型:  {PaperBasicDataService.GetTypeString("lib_paperType", qry.paper_type)}");
                if (!string.IsNullOrEmpty(qry.apply_number)) stringBuilder.AppendLine($"申請單號:  {qry.apply_number}");
                if (!string.IsNullOrEmpty(qry.my_entity_id)) stringBuilder.AppendLine($"我方主體:  {PaperBasicDataService.GetEntityString(qry.my_entity_id)}");
                if (!string.IsNullOrEmpty(qry.paper_entry_status)) stringBuilder.AppendLine($"入庫狀態:  {PaperBasicDataService.GetTypeString("lib_paperEntryStatus", qry.paper_entry_status)}");
                if (!string.IsNullOrEmpty(qry.paper_confiden_level)) stringBuilder.AppendLine($"機密等級:  {PaperBasicDataService.GetTypeString("confidentStatus", qry.paper_confiden_level)}");
                string paper_position = string.Empty;
                if (qry.paper_position != null && qry.paper_position.Count > 0)
                {
                    paper_position = string.Join(",", qry.paper_position);
                }
                if (!string.IsNullOrEmpty(paper_position)) stringBuilder.AppendLine($"存放位置:  {paper_position}");

                logRecord.Detail = stringBuilder.ToString();
            });
            return File(excelBytes, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "紙本基本資料列表" + DateTime.Now.ToString("yyyyMMdd") + ".xlsx");
        }

        /// <summary>
        /// 新增紙本基本資料
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [Route("AddPaperBasicData")]
        public async Task<ApiResultModelByObject> AddPaperBasicData([FromBody] paper_basic_data data)
        {
            ApiResultModelByObject apiResult = new ApiResultModelByObject();

            bool result = PaperBasicDataService.AddPaperApplicationData(data);
            apiResult.rtnSuccess = result;
            InitLogRecord(data, logRecord =>
            {
                StringBuilder stringBuilder = new();
                stringBuilder.AppendLine($"紙本編號:  {data.paper_code}");
                stringBuilder.AppendLine($"紙本名稱:  {data.paper_name}");
                stringBuilder.AppendLine($"申請單號:  {data.apply_number}");
                if (!string.IsNullOrEmpty(data.paper_entry_status)) stringBuilder.AppendLine($"入庫狀態:  {PaperBasicDataService.GetTypeString("lib_paperEntryStatus", data.paper_entry_status)}");
                if (!string.IsNullOrEmpty(data.paper_confiden_level)) stringBuilder.AppendLine($"機密等級:  {PaperBasicDataService.GetTypeString("confidentStatus", data.paper_confiden_level)}");
                if (!string.IsNullOrEmpty(data.paper_type)) stringBuilder.AppendLine($"紙本類型:  {PaperBasicDataService.GetTypeString("lib_paperType", data.paper_type)}");
                if (!string.IsNullOrEmpty(data.paper_return_status)) stringBuilder.AppendLine($"紙本現狀:  {PaperBasicDataService.GetTypeString("lib_returnStatus", data.paper_return_status)}");
                if (data.destroy_time != null) stringBuilder.AppendLine($"銷毀日期:  {data.destroy_time.Value.ToString("yyyy/MM/dd")}");
                if (!string.IsNullOrEmpty(data.destroy_reason)) stringBuilder.AppendLine($"銷毀原因:  {data.destroy_reason}");
                if (data.lost_time != null) stringBuilder.AppendLine($"遺失日期:  {data.lost_time.Value.ToString("yyyy/MM/dd")}");
                if (!string.IsNullOrEmpty(data.lost_reason)) stringBuilder.AppendLine($"遺失原因:  {data.lost_reason}");
                string isExcel = data.is_excel == 1 ? "是" : "否";
                stringBuilder.AppendLine($"是否 Excel 匯入:  {isExcel}");
                if (!string.IsNullOrEmpty(data.paper_remarks)) stringBuilder.AppendLine($"紙本備註:  {data.paper_remarks}");

                logRecord.Detail = stringBuilder.ToString();
            });

            return await Task.FromResult(apiResult);
        }

        /// <summary>
        /// 修改紙本基本資料
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [Route("ChangePaperBasicData")]
        public async Task<ApiResultModelByObject> ChangePaperBasicData([FromBody] paper_basic_data data)
        {
            ApiResultModelByObject apiResult = new ApiResultModelByObject();

            var oldData = PaperBasicDataService.GetPaperBasicDataByBasicID(data.basic_id.ToString());
            bool result = PaperBasicDataService.UpdatePaperBasicData(data);
            apiResult.rtnSuccess = result;
            InitLogRecord(data, logRecord =>
            {
                StringBuilder stringBuilder = new();
                stringBuilder.AppendLine($"紙本編號:  {data.paper_code}");
                stringBuilder.AppendLine($"紙本名稱:  {data.paper_name}");
                stringBuilder.AppendLine($"申請單號:  {data.apply_number}");
                stringBuilder.AppendLine($"存放位置:  {data.paper_position}");
                if (!string.IsNullOrEmpty(data.paper_entry_status)) stringBuilder.AppendLine($"入庫狀態: {PaperBasicDataService.GetTypeString("lib_paperEntryStatus", data.paper_entry_status)}");
                if (!string.IsNullOrEmpty(data.paper_confiden_level)) stringBuilder.AppendLine($"機密等級: {PaperBasicDataService.GetTypeString("confidentStatus", data.paper_confiden_level)}");
                if (!string.IsNullOrEmpty(data.paper_type)) stringBuilder.AppendLine($"紙本類型: {PaperBasicDataService.GetTypeString("lib_paperType", data.paper_type)}");
                if (!string.IsNullOrEmpty(data.paper_return_status)) stringBuilder.AppendLine($"紙本現狀: {PaperBasicDataService.GetTypeString("lib_returnStatus", data.paper_return_status)}");
                if (data.destroy_time != null) stringBuilder.AppendLine($"銷毀日期:  {data.destroy_time.Value.ToString("yyyy/MM/dd")}");
                if (!string.IsNullOrEmpty(data.destroy_reason)) stringBuilder.AppendLine($"銷毀原因:  {data.destroy_reason}");
                if (data.lost_time != null) stringBuilder.AppendLine($"遺失日期:  {data.lost_time.Value.ToString("yyyy/MM/dd")}");
                if (!string.IsNullOrEmpty(data.lost_reason)) stringBuilder.AppendLine($"遺失原因:  {data.lost_reason}");
                string isExcel = data.is_excel == 1 ? "是" : "否";
                stringBuilder.AppendLine($"是否 Excel 匯入:  {isExcel}");
                if (!string.IsNullOrEmpty(data.paper_remarks)) stringBuilder.AppendLine($"紙本備註:  {data.paper_remarks}");

                logRecord.Detail = stringBuilder.ToString();

                stringBuilder = new();
                stringBuilder.AppendLine($"紙本編號:  {oldData.paper_code}");
                stringBuilder.AppendLine($"紙本名稱:  {oldData.paper_name}");
                stringBuilder.AppendLine($"申請單號:  {oldData.apply_number}");
                stringBuilder.AppendLine($"存放位置:  {oldData.paper_position}");
                if (!string.IsNullOrEmpty(oldData.paper_entry_status)) stringBuilder.AppendLine($"入庫狀態:  {PaperBasicDataService.GetTypeString("lib_paperEntryStatus", oldData.paper_entry_status)}");
                if (!string.IsNullOrEmpty(oldData.paper_confiden_level)) stringBuilder.AppendLine($"機密等級:  {PaperBasicDataService.GetTypeString("confidentStatus", oldData.paper_confiden_level)}");
                if (!string.IsNullOrEmpty(oldData.paper_type)) stringBuilder.AppendLine($"紙本類型:  {PaperBasicDataService.GetTypeString("lib_paperType", oldData.paper_type)}");
                if (!string.IsNullOrEmpty(oldData.paper_return_status)) stringBuilder.AppendLine($"紙本現狀:  {PaperBasicDataService.GetTypeString("lib_returnStatus", oldData.paper_return_status)}");
                if (oldData.destroy_time != null) stringBuilder.AppendLine($"銷毀日期:  {oldData.destroy_time.Value.ToString("yyyy/MM/dd")}");
                if (!string.IsNullOrEmpty(oldData.destroy_reason)) stringBuilder.AppendLine($"銷毀原因:  {oldData.destroy_reason}");
                if (oldData.lost_time != null) stringBuilder.AppendLine($"遺失日期:  {oldData.lost_time.Value.ToString("yyyy/MM/dd")}");
                if (!string.IsNullOrEmpty(oldData.lost_reason)) stringBuilder.AppendLine($"遺失原因:  {oldData.lost_reason}");
                isExcel = oldData.is_excel == 1 ? "是" : "否";
                stringBuilder.AppendLine($"是否 Excel 匯入:  {isExcel}");
                if (!string.IsNullOrEmpty(oldData.paper_remarks)) stringBuilder.AppendLine($"紙本備註:  {oldData.paper_remarks}");

                logRecord.DetailFormer = stringBuilder.ToString();
            });

            return await Task.FromResult(apiResult);
        }

        /// <summary>
        /// 查詢紙本基本資料(For AutoComplete)
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("GetPaperApplicationDataList")]
        public async Task<ApiResultModelByObject> GetPaperApplicationDataList([FromQuery] string apply_number)
        {
            ApiResultModelByObject apiResult = new ApiResultModelByObject();

            var res = PaperBasicDataService.GetPaperApplicationDataList(apply_number);
            apiResult.listData = res;
            apiResult.totalCount = res.Count();
            apiResult.rtnSuccess = true;

            return await Task.FromResult(apiResult);
        }

        /// <summary>
        /// 查詢紙本歷程
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("GetPaperHistoryData")]
        public async Task<ApiResultModelByObject> GetPaperHistoryData([FromQuery] string paper_basic_id)
        {
            ApiResultModelByObject apiResult = new ApiResultModelByObject();

            var basic = PaperBasicDataService.GetPaperBasicData(paper_basic_id);
            if (basic == null) throw new CustomException(ActionFilter.GetMultilingualValue("custom:messageContent:dataChurn"));

            var res = PaperBasicDataService.GetPaperHistoryData(paper_basic_id);
            apiResult.listData = res;
            apiResult.totalCount = res.Count();
            apiResult.rtnSuccess = true;
            InitLogRecord<object>(new(), logRecord =>
            {
                StringBuilder stringBuilder = new();
                stringBuilder.AppendLine($"紙本編號: {basic.paper_code}");

                logRecord.Detail = stringBuilder.ToString();
            });

            return await Task.FromResult(apiResult);
        }

        /// <summary>
        /// 刪除紙本基本資料
        /// </summary>
        /// <returns></returns>
        [HttpDelete]
        [Route("DeletePaperBasicData")]
        public async Task<ApiResultModelByObject> DeletePaperBasicData([FromQuery] string paper_basic_id)
        {
            ApiResultModelByObject apiResult = new ApiResultModelByObject();
            var basic = PaperBasicDataService.GetPaperBasicData(paper_basic_id);
            if (basic == null) throw new Exception(ActionFilter.GetMultilingualValue("custom:messageContent:dataChurn"));

            var res = PaperBasicDataService.DeletePaperBasicData(paper_basic_id);
            apiResult.listData = res;
            apiResult.rtnSuccess = true;
            InitLogRecord<object>(new(), logRecord =>
            {
                StringBuilder stringBuilder = new();
                stringBuilder.AppendLine($"紙本編號:  {basic.paper_code}");
                stringBuilder.AppendLine($"紙本名稱:  {basic.paper_name}");
                stringBuilder.AppendLine($"申請單號:  {basic.apply_number}");
                if (!string.IsNullOrEmpty(basic.paper_entry_status)) stringBuilder.AppendLine($"入庫狀態:  {PaperBasicDataService.GetTypeString("lib_paperEntryStatus", basic.paper_entry_status)}");
                if (!string.IsNullOrEmpty(basic.paper_confiden_level)) stringBuilder.AppendLine($"機密等級:  {PaperBasicDataService.GetTypeString("confidentStatus", basic.paper_confiden_level)}");
                if (!string.IsNullOrEmpty(basic.paper_type)) stringBuilder.AppendLine($"紙本類型:  {PaperBasicDataService.GetTypeString("lib_paperType", basic.paper_type)}");
                if (!string.IsNullOrEmpty(basic.paper_return_status)) stringBuilder.AppendLine($"紙本現狀:  {PaperBasicDataService.GetTypeString("lib_returnStatus", basic.paper_return_status)}");
                if (basic.destroy_time != null) stringBuilder.AppendLine($"銷毀日期:  {basic.destroy_time.Value.ToString("yyyy/MM/dd")}");
                if (!string.IsNullOrEmpty(basic.destroy_reason)) stringBuilder.AppendLine($"銷毀原因:  {basic.destroy_reason}");
                if (basic.lost_time != null) stringBuilder.AppendLine($"遺失日期:  {basic.lost_time.Value.ToString("yyyy/MM/dd")}");
                if (!string.IsNullOrEmpty(basic.lost_reason)) stringBuilder.AppendLine($"遺失原因:  {basic.lost_reason}");

                string isExcel = basic.is_excel == 1 ? "是" : "否";
                stringBuilder.AppendLine($"是否 Excel 匯入:  {isExcel}");
                if (!string.IsNullOrEmpty(basic.paper_remarks)) stringBuilder.AppendLine($"紙本備註:  {basic.paper_remarks}");

                logRecord.Detail = stringBuilder.ToString();
            });

            return await Task.FromResult(apiResult);
        }

        /// <summary>
        /// 查詢紙本基本資料
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("GetPaperBasicDataByBasicID")]
        public async Task<ApiResultModelByObject> GetPaperBasicDataByBasicID([FromQuery] string basic_id)
        {
            ApiResultModelByObject apiResult = new ApiResultModelByObject();

            var res = PaperBasicDataService.GetPaperBasicDataByBasicID(basic_id);
            apiResult.listData = res;
            apiResult.rtnSuccess = true;
            InitLogRecord<object>(new(), logRecord =>
            {
                StringBuilder stringBuilder = new();
                stringBuilder.AppendLine($"紙本編號:  {res.paper_code}");

                logRecord.Detail = stringBuilder.ToString();
            });

            return await Task.FromResult(apiResult);
        }

        /// <summary>
        /// 查詢紙本基本資料(列印前確認，不紀錄系統日誌)
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("GetPaperBasicData")]
        public async Task<ApiResultModelByObject> GetPaperBasicData([FromQuery] string basic_id)
        {
            ApiResultModelByObject apiResult = new ApiResultModelByObject();

            var res = PaperBasicDataService.GetPaperBasicData(basic_id);
            apiResult.listData = res;
            apiResult.rtnSuccess = true;

            return await Task.FromResult(apiResult);
        }

        /// <summary>
        /// 取得下一個紙本編號
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("GetNextPaperCode")]
        public async Task<ApiResultModelByObject> GetNextPaperCode([FromQuery] string apply_number)
        {
            ApiResultModelByObject apiResult = new ApiResultModelByObject();

            var res = PaperBasicDataService.GetNextPaperCode(apply_number);
            apiResult.listData = res;
            apiResult.rtnSuccess = true;

            return await Task.FromResult(apiResult);
        }
    }
}
