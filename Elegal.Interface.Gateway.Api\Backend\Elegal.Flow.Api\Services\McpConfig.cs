﻿

using Elegal.Interface.Api.Common.FuncHelper;

namespace Elegal.Flow.Api.Services
{
    public class McpConfig
    {
        /// <summary>
        /// serviceID
        /// </summary>
        public static string serviceid = AppSettingHelper.Configuration["McpInfo:MobileAPName"];
        /// <summary>
        /// authCode
        /// </summary>
        public static string authid = AppSettingHelper.Configuration["McpInfo:MobileAuthID"];
        /// <summary>
        /// Elegal Approval Notification
        /// </summary>
        public static string title = AppSettingHelper.Configuration["McpInfo:MobileTitle"];
        /// <summary>
        /// xml note system name
        /// </summary>
        public static string insystemid = "legal";
        public static string AdminEmail = AppSettingHelper.Configuration["McpInfo:AdminEmail"];
        public static string InsertApprovalForm = AppSettingHelper.Configuration["McpInfo:InsertApprovalForm"];
        public static string CancelApprovalForm = AppSettingHelper.Configuration["McpInfo:CancelApprovalForm"];
        public static string AddApprovalFile = AppSettingHelper.Configuration["McpInfo:AddApprovalFile"];
        public static string AttInfo = AppSettingHelper.Configuration["McpInfo:AttInfo"];
        public static string FileService = AppSettingHelper.Configuration["McpInfo:FileService"];
        public static string SendEmailPageUrl = AppSettingHelper.Configuration["McpInfo:SendEmailPageUrl"];
        public static string FileServiceUrl = AppSettingHelper.Configuration["McpInfo:FileServiceUrl"];
        public static string XmlPath = AppSettingHelper.Configuration["McpInfo:XmlPath"];
        public static string MCPFileLimit = AppSettingHelper.Configuration["McpInfo:MCPFileLimit"];
    }
}
