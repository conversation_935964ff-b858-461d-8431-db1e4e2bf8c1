﻿using Elegal.Interface.Api.Common.Model.ViewModel.FlowApi;
using Elegal.Orm;

namespace Elegal.Flow.Api.Repository.Individual
{
    /// <summary>
    /// 首页区块管理
    /// </summary>
    public class SysHomeBlockRepository
    {
        /// <summary>
        /// 获取区块信息
        /// </summary>
        /// <returns></returns>
        public IEnumerable<SysHomeBlockModel> GetList()
        {
            string sql = @"select 
                                block.rowid Rowid,
                                block.code Code,
                                block.c_title CTitle,
                                block.e_title ETitle,
                                block.remark Remark,
                                block.closeable Closeable,
                                block.sort_order SortOrder,
                                block.create_user CreateUser,
                                block.create_time CreateTime,
                                block.modify_user ModifyUser,
                                block.modify_time ModifyTime,
                                block.group_index GroupIndex,
                                isnull(block.modify_time,block.create_time) operate_time,
                                ee.name operate_cuser,
                                ee.name_a operate_euser
                            from sys_home_block block
                            left join ps_sub_ee_lgl_vw_a ee on ee.emplid = isnull(block.modify_user,block.create_user)
                            order by block.sort_order,block.code";
            return DbAccess.Database.SqlQuery<SysHomeBlockModel>(sql);
        }
    }
}
