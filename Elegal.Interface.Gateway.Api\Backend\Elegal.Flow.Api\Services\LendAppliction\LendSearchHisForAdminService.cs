﻿using Elegal.Flow.Api.Repository.LendAppliction;
using Elegal.Interface.Api.Common.Control.Filter;
using Elegal.Interface.Api.Common.FuncHelper;
using Elegal.Interface.Api.Common.FuncService;
using Elegal.Interface.Api.Common.Model.ParaModel.FlowApi.LendAppliction;
using Elegal.Interface.Api.Common.Model.ResultModel.FlowApi.LendAppliction;
using Elegal.Interface.Api.Common.Model.ViewModel;
using NPOI.SS.UserModel;
using NPOI.XSSF.UserModel;
using System.Drawing;

namespace Elegal.Flow.Api.Services.LendAppliction;

public static class LendSearchHisForAdminService
{
    private static LendSearchHisForAdminRepository _LendSearchRepository = new LendSearchHisForAdminRepository();

    /// <summary>
    /// 分页查询  歷史借出 
    /// </summary>
    /// <param name="para"></param>
    /// <param name="user"></param>
    /// <returns></returns>
    public static (List<LendAdminHis>, int) QueryLendHisForAdmin(LendHisForAdminPara para, UserInfoModel user)
    {
        para.PickupTime.SetUserTimeZone(user.time_zone);
        para.ActualReturnTime.SetUserTimeZone(user.time_zone);
        para.ApplicationTime.SetUserTimeZone (user.time_zone);
        return _LendSearchRepository.QueryLendHisForAdmin(para, user.logging_locale, o =>
        {
            o.ApplicationTime = o.ApplicationTime.ConvertDateByTimeZoneByUtc(user.time_zone);
        });
    }



    /// <summary>
    /// 资料查询，历史借出，导出
    /// </summary>
    /// <param name="para"></param>
    /// <param name="user"></param>
    /// <returns></returns>
    public static byte[] QueryLendHisForAdminExprt(LendHisForAdminExportPara para, UserInfoModel user)
    {
        para.ActualReturnTime.SetUserTimeZone(user.time_zone);
        para.PickupTime.SetUserTimeZone(user.time_zone);
        para.ApplicationTime.SetUserTimeZone(user.time_zone);
        List<LendAdminHisExport> data = _LendSearchRepository.QueryLendHisForAdminExprt(para, user.logging_locale);

        //创建工作表
        IWorkbook workbook = new XSSFWorkbook();
        ISheet sheet = workbook.CreateSheet("Sheet1");
        //定义一个字体
        IFont font = workbook.CreateFont().configFont(f => {
            f.FontHeightInPoints = 12;
            f.FontName = "Calibri";
        });

        //定义一个字体
        IFont fontRed = workbook.CreateFont().configFont(f => {
            f.FontHeightInPoints = 12;
            f.FontName = "Calibri";
            f.Color = IndexedColors.Red.Index;
        });
        //定义一个样式
        ICellStyle redStyle = workbook.CreateCellStyle().SetDefaultBorder().AddFont(fontRed);
        ICellStyle defaultstyle = sheet.Workbook.CreateCellStyle().SetDefaultBorder().AddFont(font);
        ICellStyle styleCenter = sheet.Workbook.CreateCellStyle().SetDefaultBorder().HorizontallyCenter().AddFont(font);
        ICellStyle redCenter = workbook.CreateCellStyle().SetDefaultBorder().AddFont(fontRed).HorizontallyCenter();
        //设置表头
        List<string> heards = new List<string>()
        {
            ActionFilter.GetMultilingualValue("export:lendId"),
            ActionFilter.GetMultilingualValue("export:accessNumber"),
            ActionFilter.GetMultilingualValue("export:filler"),
            ActionFilter.GetMultilingualValue("export:handler"),
            ActionFilter.GetMultilingualValue("export:applyDate"),
            ActionFilter.GetMultilingualValue("export:applyStatus"),
            ActionFilter.GetMultilingualValue("export:contractNum"),
            ActionFilter.GetMultilingualValue("export:paperId"),
            ActionFilter.GetMultilingualValue("export:paperName"),
            ActionFilter.GetMultilingualValue("export:returnDate"),
            ActionFilter.GetMultilingualValue("export:overdueDays"),
            ActionFilter.GetMultilingualValue("export:returnPaperStatus"),
        };
        sheet.SetHeader(heards);
        //设置列宽
        sheet.SetColumnListWidth(new List<int>() {4000,4000,7000,7000,4000,4000,5000,5000,10000,4000,3000,4000 });
        //写入数据
        sheet.WriteData(data, (item, row) =>
        {
            if (!string.IsNullOrEmpty(item.ContractNumber)) item.ContractNumber = item.ContractNumber.Replace("\u001f", "");

            item.ContractNumber = item.ContractNumber?.Replace("\u001f", "") ?? string.Empty;
            row.CreateElegalDefaultCell(0, defaultstyle).SetCellValue(item.LendNumber);
            row.CreateElegalDefaultCell(1, defaultstyle).SetCellValue(item.RetrieveNumber);
            row.CreateElegalDefaultCell(2, defaultstyle).SetCellValue($"{item.LendFillEName} ({item.LendFillCName})");
            row.CreateElegalDefaultCell(3, defaultstyle).SetCellValue($"{item.LendHandlerEName} ({item.LendHandlerCName})");
            row.CreateElegalDefaultCell(4, defaultstyle).SetCellValue(item.ApplicationTime.ConvertDateByTimeZoneByUtc(user.time_zone).ToString("yyyy/MM/dd"));
            row.CreateElegalDefaultCell(5, defaultstyle).SetCellValue(item.LendStatusName);
            row.CreateElegalDefaultCell(6, defaultstyle).SetCellValue(item.ContractNumber);
            row.CreateElegalDefaultCell(7, defaultstyle).SetCellValue(item.PaperCode);
            row.CreateElegalDefaultCell(8, defaultstyle).SetCellValue(item.PaperName);
            row.CreateElegalDefaultCell(9, defaultstyle).SetCellValue(item.ActualReturnTime.ConvertDateByTimeZoneByUtc(user.time_zone).ToString("yyyy/MM/dd"));
            row.CreateElegalDefaultCell(10, styleCenter).SetCellValue(item.OverdueDay);
            row.CreateElegalDefaultCell(11, defaultstyle).SetCellValue(item.ReturnStatusName);
            if (item.IsOverdueAndReturn)
            {
                row.Cells[9].CellStyle = redStyle;
                row.Cells[10].CellStyle = redCenter;
            }
           
        });
        return workbook.ToBytes();
    }
}
