﻿using Elegal.Interface.Api.Common.Control.Filter;
using Elegal.Interface.Api.Common.Control.Web;
using Elegal.Interface.Api.Common.Model;
using Elegal.Interface.Api.Common.Model.ResultModel;
using Elegal.Interface.Api.Common.Model.ResultModel.PermissionApi;
using Elegal.Interface.ApiData.Service.FuncService;
using Elegal.Interface.ApiData.Service.Model.DbModel;
using Elegal.Interface.ApiData.Service.Model.QueryCondition;
using Elegal.Orm;
using Elegal.Flow.Api.Services;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
#nullable disable
namespace Elegal.Flow.Api.Controllers
{
    /// <summary>
    /// 首頁定位佈局參照表
    /// </summary>
    [Route("[controller]")]
    [ApiController]
    public class SysHomePositionController(SysHomePositionRefactorService _SysHomePositionService) : BaseController
    {
        /// <summary>
        /// 获取定位信息
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("Get")]
        public async Task<ApiResultModelByObject> Get()
        {
            ApiResultModelByObject apiResult = new()
            {
                listData = await _SysHomePositionService.GetSysHomePositionView(),
                rtnSuccess = true
            };
            return await Task.FromResult(apiResult);
        }

        /// <summary>
        /// 设置定位信息
        /// </summary>
        /// <param name="sysHomePositionView"></param>
        /// <returns></returns>
        [HttpPut]
        [Route("Set")]
        public async Task<ApiResultModelByObject> Set(SysHomePositionView sysHomePositionView)
        {
            ApiResultModelByObject apiResult = new();

            SysHomePosition sysHomePosition = SysHomePositionDataService.FindByKey(MvcContext.UserInfo.current_emp);
            //新增
            if (sysHomePosition == null)
                apiResult.listData = SysHomePositionDataService.Create(new()
                {
                    CreateTime = DateTime.UtcNow,
                    CreateUser = MvcContext.UserInfo.current_emp,
                    Emplid = MvcContext.UserInfo.current_emp,
                    HomePosition = JsonConvert.SerializeObject(sysHomePositionView.SysHomePositionModelList),
                    HomeLock = sysHomePositionView.HomeLock ? 1 : 0
                });
            else
            {
                sysHomePosition.HomePosition = JsonConvert.SerializeObject(sysHomePositionView.SysHomePositionModelList);
                sysHomePosition.ModifyTime = DateTime.UtcNow;
                sysHomePosition.ModifyUser = MvcContext.UserInfo.current_emp;
                sysHomePosition.HomeLock = sysHomePositionView.HomeLock ? 1 : 0;
                apiResult.listData = SysHomePositionDataService.Update(sysHomePosition);
            }
            apiResult.rtnSuccess = true;
            InitLogRecord(log =>
            {
                log.Detail = $@"{ActionFilter.GetMultilingualValue("Home_homeLock", true)}：{(sysHomePositionView.HomeLock ? "鎖定" : "解鎖")}";
            });
            return await Task.FromResult(apiResult);
        }

        /// <summary>
        /// 关闭布局提醒
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [Route("CloseHomeRemind")]
        public async Task<ApiResultModelByObject> CloseHomeRemind()
        {
            ApiResultModelByObject apiResult = new()
            {
                listData = SysHomePositionService.CloseHomeRemind(),
                rtnSuccess = true
            };
            return await Task.FromResult(apiResult);
        }
    }
}
