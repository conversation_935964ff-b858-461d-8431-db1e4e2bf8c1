﻿using Elegal.Interface.Api.Common.Model.ViewModel.FlowApi;
using Elegal.Orm;

namespace Elegal.Flow.Api.Repository
{
    /// <summary>
    /// Con-Current Jobs
    /// </summary>
    public class PsCjTruckVwARepository
    {
        private readonly string Prefix = DbAccess.Database.GetParameterPrefix();
        /// <summary>
        /// 获取部门信息
        /// </summary>
        /// <param name="empid">工号</param>
        /// <returns></returns>
        public IEnumerable<PsCjTruckVwAModel> GetPsCjTruckVwAList(string empid)
        {
            string sql = @$"select
	                            cj.emplid Empid,
	                            cj.empl_rcd EmplRcd,
	                            cj.deptid Deptid,
	                            cj.supervisor_id SupervisorId,
	                            cj.is_delete IsDelete,
	                            og.descr Descr,
	                            og.descr_a DescrA
                            from
	                            ps_cj_truck_vw_a cj
                            left join ps_sub_og_lgl_vw_a og on
	                            cj.deptid = og.deptid
                            where
	                            cj.is_delete is null
	                            and emplid = {Prefix}empid";
            return DbAccess.Database.SqlQuery<PsCjTruckVwAModel>(sql, new { empid });
        }
    }
}
