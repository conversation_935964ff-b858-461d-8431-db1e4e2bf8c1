﻿using Elegal.Interface.Api.Common.Control;
using Elegal.Interface.Api.Common.Control.Filter;
using Elegal.Interface.Api.Common.Control.Web;
using Elegal.Interface.Api.Common.FuncHelper;
using Elegal.Interface.Api.Common.FuncService;
using Elegal.Interface.Api.Common.Model.Enum;
using Elegal.Interface.Api.Common.Model.ParaModel.PermissionApi;
using Elegal.Interface.Api.Common.Model.ResultModel;
using Elegal.Interface.Api.Common.Model.ViewModel.SysParam;
using Elegal.Interface.ApiData.Service.FuncService;
using Elegal.Interface.ApiData.Service.Model.DbModel;
using Elegal.Interface.ApiData.Service.Model.QueryCondition;
using Microsoft.AspNetCore.Mvc;

namespace Elegal.Flow.Api.Controllers
{
    /// <summary>
    /// 參數管理
    /// </summary>
    [Route("[controller]")]
    [ApiController]
    public class SysParametersController(SysParametersRefactorService sysParametersRefactorService) : BaseController
    {
        #region 下拉數據源
        /// <summary>
        /// 下拉數據源
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [Route("GetSelectData")]
        public async Task<ApiResultModelByObject> GetSelectData(SysParametersQueryCondition sysParametersQueryCondition)
        {
            ApiResultModelByObject apiResult = new();

            sysParametersQueryCondition.LangType = MvcContext.UserInfo.logging_locale;
            apiResult.listData = SysParametersDataService.Query(sysParametersQueryCondition);
            apiResult.rtnSuccess = true;

            return await Task.FromResult(apiResult);
        }
        #endregion

        #region 系统记录保留天数
        /// <summary>
        /// 系统记录保留天数
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("GetRecordDays")]
        public async Task<ApiResultModelByObject> GetRecordDays()
        {
            return await Task.FromResult(SysParametersService.GetRecordDays());
        }
        #endregion

        #region 设置系统记录保留天数
        /// <summary>
        /// 设置系统记录保留天数
        /// </summary>
        /// <param name="rowid">主键标识</param>
        /// <param name="day">设置值</param>
        /// <returns></returns>
        [HttpPut]
        [Route("SetRecordDays")]
        public async Task<ApiResultModelByObject> SetRecordDays(int rowid, string day)
        {
            SysParameters sysParameters = SysParametersDataService.FindByKey(rowid);
            if (sysParameters == null)
                return new ApiResultModelByObject()
                {
                    messageTitle = ActionFilter.GetMultilingualValue("custom:messageTitle:dataNotexist"),
                    messageContent = new List<string> { ActionFilter.GetMultilingualValue("custom:messageContent:dataChurn") },
                    rtnSuccess = true,
                    messageType = MessageTypeUtils.Warning.ToString(),
                };
            var oldValue = sysParameters.FuncCode;
            sysParameters.FuncCode = day;
            sysParameters.ModifyUser = MvcContext.UserInfo.current_emp;
            sysParameters.ModifyTime = DateTime.Now;
            SysParametersDataService.Update(sysParameters);
            InitLogRecord<object>(new(), logRecord =>
            {
                logRecord.Detail = $"系統記錄保留天數：{day}";
                logRecord.DetailFormer = $"系統記錄保留天數：{oldValue}";
            });
            return await Task.FromResult(new ApiResultModelByObject()
            {
                listData = true,
                rtnSuccess = true,
            });
        }
        #endregion

        #region 參數管理初始加載/模糊查詢
        /// <summary>
        /// 參數管理初始加載/模糊查詢
        /// </summary>
        /// <param name="paraContent"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetSysParameters")]
        public async Task<ApiResultModelByObject> GetSysParameters(string paraContent = "")
        {
            ApiResultModelByObject apiResult = new ApiResultModelByObject();

            apiResult.listData = SysParametersService.GetSysParametersTables(MvcContext.UserInfo.time_zone, paraContent);
            apiResult.rtnSuccess = true;

            #region 增加日誌
            InitLogRecord(paraContent, log =>
                {
                    log.Detail = !string.IsNullOrEmpty(paraContent) ? string.Format(ActionFilter.GetMultilingualValue("SysParameters_paraContent", true), paraContent) : "";
                });
            #endregion

            return await Task.FromResult<ApiResultModelByObject>(apiResult);
        }
        #endregion

        #region 根據參數類型獲取修改數據
        /// <summary>
        /// 根據參數類型獲取修改數據
        /// </summary>
        /// <param name="paraCode"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetInspectionDataByParaCode")]
        public async Task<ApiResultModelByObject> GetInspectionDataByParaCode(string paraCode)
        {
            ApiResultModelByObject apiResult = new ApiResultModelByObject();

            apiResult.listData = SysParametersService.GetInspectionDataByParaCode(paraCode);
            apiResult.rtnSuccess = true;

            return await Task.FromResult<ApiResultModelByObject>(apiResult);
        }
        #endregion

        #region 參數管理新增/修改
        /// <summary>
        /// 參數管理新增/修改
        /// </summary>
        /// <returns></returns>
        [HttpPut]
        [Route("HandleSysParameters")]
        public async Task<ApiResultModel> HandleSysParameters([FromBody] SysParaHandleModel sphm)
        {
            ApiResultModel apiResult = new ApiResultModel();
            SysParamInspectionModel oldData = SysParametersService.GetInspectionDataByParaCode(sphm.sysParam.para_code);
            //驗證傳遞參數是否正確
            List<string> errorMessage = SysParametersService.CheckSysParameters(sphm, oldData.listValue);
            if (errorMessage.Count > 0)
            {
                apiResult.messageType = MessageTypeUtils.Warning.ToString();
                apiResult.messageTitle = ActionFilter.GetMultilingualValue("commonWord:information");
                apiResult.messageContent = errorMessage;
            }
            else
            {
                apiResult.rtnSuccess = SysParametersService.HandleSysParameters(sphm, MvcContext.UserInfo.current_emp);
            }
            if (apiResult.rtnSuccess)
            {
                InsertLogRecord(oldData, sphm, sphm.sysParam.isModify);
            }
            return await Task.FromResult<ApiResultModel>(apiResult);
        }

        #region 插入log日誌
        /// <summary>
        /// 插入log日誌
        /// </summary>
        /// <param name="oldData">修改前數據</param>
        /// <param name="newData">修改後數據</param>
        /// <param name="isModify">是否只修改參數中文名或英文名</param>
        private void InsertLogRecord(SysParamInspectionModel oldData, SysParaHandleModel newData, int isModify)
        {
            string enable = ActionFilter.GetMultilingualValue("SysParameters_enable", true);
            string disable = ActionFilter.GetMultilingualValue("SysParameters_disable", true);
            switch (newData.sysParam.para_type)
            {
                case 0://單值
                    InitLogRecord(newData.sysParam, log =>
                    {
                        #region 修改後數據
                        List<string> getNewLog = new List<string>();
                        //參數中文名稱
                        getNewLog.Add(string.Format(ActionFilter.GetMultilingualValue("SysParameters_paraCName", true), newData.sysParam.para_name));
                        //參數英文名稱
                        getNewLog.Add(string.Format(ActionFilter.GetMultilingualValue("SysParameters_paraEName", true), newData.sysParam.para_ename));
                        //參數值
                        getNewLog.Add(string.Format(ActionFilter.GetMultilingualValue("SysParameters_paraCode", true), newData.sysParam.func_code));//edit by SpringJiang 20240125 系統記錄單值修改傳參錯誤
                        //備註
                        getNewLog.Add(string.Format(ActionFilter.GetMultilingualValue("SysParameters_paraRemark", true), newData.sysParam.func_remarks));
                        //參數狀態
                        getNewLog.Add(string.Format(ActionFilter.GetMultilingualValue("SysParameters_paraStatus", true), newData.sysParam.is_used == 1 ? enable : disable));
                        log.Detail = string.Join("\r\n", getNewLog.Select(s => s));
                        #endregion

                        #region 修改前數據
                        List<string> getOldLog = new List<string>();
                        //參數中文名稱
                        getOldLog.Add(string.Format(ActionFilter.GetMultilingualValue("SysParameters_paraCName", true), oldData.sysParam.para_name));
                        //參數英文名稱
                        getOldLog.Add(string.Format(ActionFilter.GetMultilingualValue("SysParameters_paraEName", true), oldData.sysParam.para_ename));
                        //參數值
                        getOldLog.Add(string.Format(ActionFilter.GetMultilingualValue("SysParameters_paraCode", true), oldData.sysParam.func_code));//edit by SpringJiang 20240125 系統記錄單值修改傳參錯誤
                        //備註
                        getOldLog.Add(string.Format(ActionFilter.GetMultilingualValue("SysParameters_paraRemark", true), oldData.sysParam.func_remarks));
                        //參數狀態
                        getOldLog.Add(string.Format(ActionFilter.GetMultilingualValue("SysParameters_paraStatus", true), oldData.sysParam.is_used == 1 ? enable : disable));
                        log.DetailFormer = string.Join("\r\n", getOldLog.Select(s => s));
                        #endregion
                    });
                    break;
                case 1://多值
                    InsertMultLog(oldData, newData, isModify);
                    break;
            }
        }

        #region 多值Log插入
        /// <summary>
        /// 多值Log插入
        /// </summary>
        /// <param name="oldData">修改前數據</param>
        /// <param name="newData">修改後數據</param>
        /// <param name="isModify">是否只修改參數中文名或英文名</param>
        private void InsertMultLog(SysParamInspectionModel oldData, SysParaHandleModel newData, int isModify)
        {
            string enable = ActionFilter.GetMultilingualValue("SysParameters_enable", true);
            string disable = ActionFilter.GetMultilingualValue("SysParameters_disable", true);
            InitLogRecord(newData, log =>
            {
                #region 修改後數據
                List<string> getNewLog = GetNewParaLog(newData);
                log.Detail = string.Join("\r\n", getNewLog.Select(s => s));
                #endregion

                #region 修改前數據
                List<string> getOldLog = GetOldParaLog(oldData, newData);
                log.DetailFormer = string.Join("\r\n", getOldLog.Select(s => s));
                #endregion

                //表示沒有對參數值進行變動
                if (isModify == 1 && newData.listUpdateValue.Count() == 0 && newData.listAddValue.Count() == 0)
                {
                    #region 修改後數據
                    List<string> getParaNewLog = new List<string>();
                    //參數中文名稱
                    getParaNewLog.Add(string.Format(ActionFilter.GetMultilingualValue("SysParameters_paraCName", true), newData.sysParam.para_name));
                    //參數英文名稱
                    getParaNewLog.Add(string.Format(ActionFilter.GetMultilingualValue("SysParameters_paraEName", true), newData.sysParam.para_ename));
                    log.Detail = string.Join("\r\n", getParaNewLog.Select(s => s));
                    #endregion

                    #region 修改前數據
                    List<string> getParaOldLog = new List<string>();
                    //參數中文名稱
                    getParaOldLog.Add(string.Format(ActionFilter.GetMultilingualValue("SysParameters_paraCName", true), oldData.sysParam.para_name));
                    //參數英文名稱
                    getParaOldLog.Add(string.Format(ActionFilter.GetMultilingualValue("SysParameters_paraEName", true), oldData.sysParam.para_ename));
                    log.DetailFormer = string.Join("\r\n", getParaOldLog.Select(s => s));
                    #endregion
                }
            });
        }

        #region 拼接修改後的log
        /// <summary>
        /// 拼接修改後的log
        /// </summary>
        /// <param name="newData"></param>
        /// <returns></returns>
        private List<string> GetNewParaLog(SysParaHandleModel newData)
        {
            string enable = ActionFilter.GetMultilingualValue("SysParameters_enable", true);
            string disable = ActionFilter.GetMultilingualValue("SysParameters_disable", true);
            List<string> getNewLog = new List<string>();
            //參數中文名稱
            getNewLog.Add(string.Format(ActionFilter.GetMultilingualValue("SysParameters_paraCName", true), newData.sysParam.para_name));
            //參數英文名稱
            getNewLog.Add(string.Format(ActionFilter.GetMultilingualValue("SysParameters_paraEName", true), newData.sysParam.para_ename));

            #region 修改數據
            foreach (ParamValueModel nm in newData.listUpdateValue)
            {
                //參數值編碼
                string nmFunCode = string.Format(ActionFilter.GetMultilingualValue("SysParameters_funCode", true), nm.func_code);
                //參數值中文名稱
                string nmFunCName = string.Format(ActionFilter.GetMultilingualValue("SysParameters_funCName", true), nm.func_name);
                //參數值英文名稱
                string nmFunEName = string.Format(ActionFilter.GetMultilingualValue("SysParameters_funEName", true), nm.func_ename);
                //參數值備註
                string nmFunRemark = string.Format(ActionFilter.GetMultilingualValue("SysParameters_funRemark", true), nm.func_remarks);
                //參數值狀態
                string nmFunStatus = string.Format(ActionFilter.GetMultilingualValue("SysParameters_funStatus", true), nm.is_used == 1 ? enable : disable);
                //將獲取值拼接
                string updateFunCountent = string.Format(@"[{0}、{1}、{2}、{3}、{4}]", nmFunCode, nmFunCName, nmFunEName, nmFunRemark, nmFunStatus);
                //添加到log日誌中
                getNewLog.Add(string.Format(ActionFilter.GetMultilingualValue("SysParameters_paraCode", true), updateFunCountent));
                getNewLog.Add("-------------------------------------");
            }
            #endregion

            #region 新增數據
            foreach (ParamValueModel na in newData.listAddValue)
            {
                //參數值編碼
                string naFunCode = string.Format(ActionFilter.GetMultilingualValue("SysParameters_funCode", true), na.func_code);
                //參數值中文名稱
                string naFunCName = string.Format(ActionFilter.GetMultilingualValue("SysParameters_funCName", true), na.func_name);
                //參數值英文名稱
                string naFunEName = string.Format(ActionFilter.GetMultilingualValue("SysParameters_funEName", true), na.func_ename);
                //參數值備註
                string naFunRemark = string.Format(ActionFilter.GetMultilingualValue("SysParameters_funRemark", true), na.func_remarks);
                //參數值狀態
                string naFunStatus = string.Format(ActionFilter.GetMultilingualValue("SysParameters_funStatus", true), na.is_used == 1 ? enable : disable);
                //將獲取值拼接
                string addFunCountent = string.Format(@"[{0}、{1}、{2}、{3}、{4}]", naFunCode, naFunCName, naFunEName, naFunRemark, naFunStatus);
                //添加到log日誌中
                getNewLog.Add(string.Format(ActionFilter.GetMultilingualValue("SysParameters_paraCode", true), addFunCountent));
                getNewLog.Add("-------------------------------------");
            }
            #endregion

            return getNewLog;
        }
        #endregion

        #region 拼接修改前的log
        /// <summary>
        /// 拼接修改前的log
        /// </summary>
        /// <param name="oldData"></param>
        /// <param name="newData"></param>
        /// <returns></returns>
        private List<string> GetOldParaLog(SysParamInspectionModel oldData, SysParaHandleModel newData)
        {
            string enable = ActionFilter.GetMultilingualValue("SysParameters_enable", true);
            string disable = ActionFilter.GetMultilingualValue("SysParameters_disable", true);
            List<string> getOldLog = new List<string>();
            //獲取新增+修改時操作的參數值
            List<string> logFuncCode = newData.listUpdateValue.Concat(newData.listAddValue).Select(s => s.func_code).Distinct().ToList();

            //參數中文名稱
            getOldLog.Add(string.Format(ActionFilter.GetMultilingualValue("SysParameters_paraCName", true), oldData.sysParam.para_name));
            //參數英文名稱
            getOldLog.Add(string.Format(ActionFilter.GetMultilingualValue("SysParameters_paraEName", true), oldData.sysParam.para_ename));
            //遍歷數據
            foreach (ParamValueModel pvm in oldData.listValue)
            {
                if (logFuncCode.Where(na => na.ToLower() == pvm.func_code.ToLower()).Count() > 0)
                {
                    //參數值編碼
                    string funCode = string.Format(ActionFilter.GetMultilingualValue("SysParameters_funCode", true), pvm.func_code);
                    //參數值中文名稱
                    string funCName = string.Format(ActionFilter.GetMultilingualValue("SysParameters_funCName", true), pvm.func_name);
                    //參數值英文名稱
                    string funEName = string.Format(ActionFilter.GetMultilingualValue("SysParameters_funEName", true), pvm.func_ename);
                    //參數值備註
                    string funRemark = string.Format(ActionFilter.GetMultilingualValue("SysParameters_funRemark", true), pvm.func_remarks);
                    //參數值狀態
                    string funStatus = string.Format(ActionFilter.GetMultilingualValue("SysParameters_funStatus", true), pvm.is_used == 1 ? enable : disable);
                    //將獲取值拼接
                    string oldFunCountent = string.Format(@"[{0}、{1}、{2}、{3}、{4}]", funCode, funCName, funEName, funRemark, funStatus);
                    //添加到log日誌中
                    getOldLog.Add(string.Format(ActionFilter.GetMultilingualValue("SysParameters_paraCode", true), oldFunCountent));
                    getOldLog.Add("-------------------------------------");
                }
            }
            return getOldLog;
        }
        #endregion

        #endregion

        #endregion

        #endregion

        #region 查询申请单状态
        /// <summary>
        /// 查询申请单状态
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("GetRequisitionStatus")]
        public async Task<ApiResultModelByObject> GetRequisitionStatus()
        {
            ApiResultModelByObject apiResult = new();

            apiResult.listData = SysParametersService.GetRequisitionStatus(MvcContext.UserInfo.logging_locale);
            apiResult.rtnSuccess = true;

            return await Task.FromResult(apiResult);
        }
        #endregion

        #region 獲取申請單類別層級
        /// <summary>
        /// 獲取申請單類別層級
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("GetApplicationType")]
        public async Task<ApiResultModelByObject> GetApplicationType()
        {
            ApiResultModelByObject apiResult = new();
            
            var userInfoModel = AppStaticServices.GetUserInfoFromHttpContext(this.HttpContext);
            apiResult.listData = await sysParametersRefactorService.GetApplicationType(userInfoModel.logging_locale);
            apiResult.rtnSuccess = true;
            
            return apiResult;
        }
        #endregion
    }
}
