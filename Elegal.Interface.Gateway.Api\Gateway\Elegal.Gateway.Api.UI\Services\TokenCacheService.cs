using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Logging;
using System;
using System.IdentityModel.Tokens.Jwt;
using System.Threading.Tasks;

namespace Elegal.Gateway.Api.UI.Services
{
    public interface ITokenCacheService
    {
        Task<bool> IsTokenValidAsync(string token);
        void CacheTokenValidation(string token, bool isValid, TimeSpan? expiry = null);
        void InvalidateToken(string token);
    }

    public class TokenCacheService : ITokenCacheService
    {
        private readonly IMemoryCache _cache;
        private readonly ILogger<TokenCacheService> _logger;
        private readonly TimeSpan _defaultCacheExpiry = TimeSpan.FromMinutes(5); // Cache for 5 minutes

        public TokenCacheService(IMemoryCache cache, ILogger<TokenCacheService> logger)
        {
            _cache = cache;
            _logger = logger;
        }

        public async Task<bool> IsTokenValidAsync(string token)
        {
            if (string.IsNullOrEmpty(token))
                return false;

            var cacheKey = GetCacheKey(token);
            
            if (_cache.TryGetValue(cacheKey, out bool cachedResult))
            {
                _logger.LogDebug("Token validation cache hit for key: {CacheKey}", cacheKey);
                return cachedResult;
            }

            _logger.LogDebug("Token validation cache miss for key: {CacheKey}", cacheKey);
            return false; // Not in cache, needs validation
        }

        public void CacheTokenValidation(string token, bool isValid, TimeSpan? expiry = null)
        {
            if (string.IsNullOrEmpty(token))
                return;

            var cacheKey = GetCacheKey(token);
            var cacheExpiry = expiry ?? _defaultCacheExpiry;

            // If token is valid, cache it for the specified time
            // If token is invalid, cache for a shorter time to avoid repeated validation attempts
            var actualExpiry = isValid ? cacheExpiry : TimeSpan.FromMinutes(1);

            var cacheOptions = new MemoryCacheEntryOptions
            {
                AbsoluteExpirationRelativeToNow = actualExpiry,
                Priority = CacheItemPriority.Normal
            };

            _cache.Set(cacheKey, isValid, cacheOptions);
            _logger.LogDebug("Cached token validation result: {IsValid} for key: {CacheKey}, expiry: {Expiry}", 
                isValid, cacheKey, actualExpiry);
        }

        public void InvalidateToken(string token)
        {
            if (string.IsNullOrEmpty(token))
                return;

            var cacheKey = GetCacheKey(token);
            _cache.Remove(cacheKey);
            _logger.LogDebug("Invalidated token cache for key: {CacheKey}", cacheKey);
        }

        private string GetCacheKey(string token)
        {
            // Use a hash of the token to avoid storing the full token in cache keys
            // Take first 16 characters of token as a simple hash (in production, use proper hashing)
            var tokenHash = token.Length > 16 ? token.Substring(0, 16) : token;
            return $"token_validation_{tokenHash}";
        }
    }
}
