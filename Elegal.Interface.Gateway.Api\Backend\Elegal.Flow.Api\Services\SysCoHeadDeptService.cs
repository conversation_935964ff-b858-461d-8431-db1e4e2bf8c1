﻿using Elegal.Flow.Api.Repository;
using Elegal.Interface.Api.Common.Control.Filter;
using Elegal.Interface.Api.Common.Model.ParaModel.FlowApi;
using Elegal.Interface.Api.Common.Model.ViewModel.FlowApi;
using Elegal.Orm.Dtos;

/// <summary>
/// Co-Head部門簽核設定服務層
/// 處理部門主管和Co-Head的簽核權限設定及託管部門管理
/// </summary>
public class SysCoHeadDeptService
{
    private static readonly SysCoHeadDeptRepository _repository = new();

    /// <summary>
    /// 查詢部門層級清單
    /// </summary>
    /// <returns>所有部門層級編號列表，依層級遞增排序</returns>
    internal static IEnumerable<string> GetTreeLevelList()
    {
        return _repository.GetTreeLevelList();
    }

    /// <summary>
    /// 查詢Co-Head部門設定資料，包含部門基本資料、主管資訊及簽核設定
    /// </summary>
    /// <param name="qry">查詢條件，支援部門代號、主管、狀態等條件</param>
    /// <returns>分頁的Co-Head部門資料</returns>
    internal static PageResult<SysCoheadDeptViewModel> QueryCoHeadDept(qrySysCoHeadDeptModel qry)
    {
        PageResult<SysCoheadDeptViewModel> res = _repository.QueryCoHeadDept(qry);
        return res;
    }

    /// <summary>
    /// 查詢指定部門的上層部門清單
    /// 用於建立部門層級關係
    /// </summary>
    /// <param name="startDept">起始部門代碼</param>
    /// <returns>上層部門資料列表</returns>
    internal static IEnumerable<SysCoheadDeptViewModel> QueryUpperDept(string startDept)
    {
        IEnumerable<SysCoheadDeptViewModel> res = _repository.QueryUpperDept(startDept);
        return res;
    }

    /// <summary>
    /// 更新Co-Head簽核設定及託管部門狀態
    /// </summary>
    /// <param name="req">更新資料模型，包含部門代號、簽核類型、託管狀態</param>
    /// <param name="modifiedUser">修改人工號</param>
    /// <returns>更新是否成功</returns>
    /// <remarks>
    /// 處理兩種更新情況：
    /// 1. 簽核類型(sign_type)更新：設定Co-Head的簽核方式
    /// 2. 託管狀態(is_hosted)更新：設定部門是否為託管部門
    /// 
    /// 更新邏輯：
    /// - 兩種更新可同時進行
    /// - 任一更新失敗都會拋出異常
    /// - 若參數為null則跳過該項更新
    /// </remarks>
    internal static bool UpdateCoHeadDept(updateSysCoHeadDeptModel req, string modifiedUser)
    {
        // 更新簽核類型設定
        if (req.sign_type != null)
        {
            if (_repository.UpdateCoHeadDept(req.deptid, req.sign_type.Value, modifiedUser) == false)
                throw new Exception(ActionFilter.GetMultilingualValue("custom:messageTitle:updateFail"));
        }
        // 更新託管部門狀態
        if (req.is_hosted != null)
        {
            if (_repository.UpdateHostedDept(req.deptid, req.is_hosted.Value, modifiedUser) == false)
                throw new Exception(ActionFilter.GetMultilingualValue("custom:messageTitle:updateFail"));
        }
        return true;
    }

    /// <summary>
    /// 根據部門代碼取得單一部門的Co-Head設定
    /// </summary>
    /// <param name="deptid">部門代碼</param>
    /// <returns>部門的Co-Head設定資料，若無資料則返回null</returns>
    internal static SysCoheadDeptViewModel GetCoHeadDeptByDeptid(string deptid)
    {
        qrySysCoHeadDeptModel qry = new qrySysCoHeadDeptModel()
        {
            deptid = deptid
        };
        PageResult<SysCoheadDeptViewModel> res = _repository.QueryCoHeadDept(qry);
        if (res.Data.Count > 0)
        {
            return res.Data[0];
        }
        else
        {
            return null;
        }
    }
}
