﻿using Elegal.Flow.Api.Services.Application;
using Elegal.Interface.Api.Common.Control.Filter;
using Elegal.Interface.Api.Common.Control.Web;
using Elegal.Interface.Api.Common.Model.Enum;
using Elegal.Interface.Api.Common.Model.ParaModel.FlowApi;
using Elegal.Interface.Api.Common.Model.ResultModel;
using Elegal.Interface.Api.Common.Model.ResultModel.FlowApi.FlowStep;
using Elegal.Interface.Api.Common.Model.ViewModel.FlowApi;
using Elegal.Interface.ApiData.Service.FuncService;
using Elegal.Interface.ApiData.Service.Model.DbModel;
using Elegal.Interface.ApiData.Service.Model.QueryCondition;
using Elegal.Orm.Utils;
using Microsoft.AspNetCore.Mvc;

namespace Elegal.Flow.Api.Controllers.Application
{
    /// <summary>
    /// 合约管理作业(合约申请)
    /// </summary>
    [Route("[controller]")]
    [ApiController]
    public class ContractOriginalArchiveController : BaseController
    {
        /// <summary>
        /// 基本资料查询
        /// </summary>
        /// <param name="applyNumber">申请单号</param>
        /// <returns></returns>
        [HttpGet("GetBaseInformation")]
        public async Task<ApiResultModelByObject> GetBaseInformation(string applyNumber)
        {
            ApiResultModelByObject apiResult = new ApiResultModelByObject()
            {
                listData = ContractOriginalArchiveService.GetBaseInformation(applyNumber),
                rtnSuccess = true,
            };
            InitLogRecord(LogRecord => { 
                LogRecord.Detail = $"申请单号：{applyNumber}";
            });
            return await Task.FromResult(apiResult);
        }
        /// <summary>
        /// 提交归档资讯
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost("SetOriginalArchive")]
        public async Task<ApiResultModelByObject> SetOriginalArchive([FromBody] ContractValidityModel model)
        {
            #region 申請單已結案，攔截操作(場景：前端多開分頁點擊結案)
            var formApplication = FormApplicationDataService.FindByKey(model.ApplyNumber);
            if (formApplication == null || formApplication.ApplicationState.Equals("E"))
                throw new Exception(ActionFilter.GetMultilingualValue("custom:messageContent:dataChurn"));
            #endregion

            if (model.PaperBasicDatas.GroupBy(b => b.PaperCode).Count() < model.PaperBasicDatas.Count)
                return await Task.FromResult(new ApiResultModelByObject()
                {
                    rtnSuccess = false,
                    messageType = MessageTypeUtils.Warning.ToString(),
                    messageContent = [ActionFilter.GetMultilingualValue("custom:messageContent:repeatPaper")],
                });
            return await Task.FromResult(ContractOriginalArchiveService.SetOriginalArchive(model));
        }
        /// <summary>
        /// 保存特殊加签设定
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost("AddSpecialSignature")]
        public async Task<ApiResultModelByObject> AddSpecialSignature(SpecialSignatureModel model)
        {
            #region 申請單已結案，攔截操作(場景：前端多開分頁點擊結案) SIT548
            var formApplication = FormApplicationDataService.FindByKey(model.ApplyNumber);
            if (formApplication == null || formApplication.ApplicationState.Equals("E"))
                throw new Exception(ActionFilter.GetMultilingualValue("custom:messageContent:dataChurn"));
            #endregion
            return await Task.FromResult(ContractOriginalArchiveService.AddSpecialSignature(model));
        }
        /// <summary>
        /// 删除特殊加签人
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost("DeleteSpecialSignature")]
        public async Task<ApiResultModel> DeleteSpecialSignature(DeleteSpecialSignatureModel model)
        {
            #region 申請單已結案，攔截操作(場景：前端多開分頁點擊結案) SIT546
            var formApplication = FormApplicationDataService.FindByKey(model.ApplyNumber);
            if (formApplication == null || formApplication.ApplicationState.Equals("E"))
                throw new Exception(ActionFilter.GetMultilingualValue("custom:messageContent:dataChurn"));
            #endregion

            #region 校驗數據異動(加簽人已被刪除)
            //判斷方式：所刪除的數據量大於DB實際存在的數據量  
            var data_in = FlowStepSignerInviteeDataService.Query(new FlowStepSignerInviteeQueryCondition()
            {
                SearchItemGroup = new Orm.Dtos.SearchItemGroup()
                {
                    Items = new List<Orm.Dtos.SearchItem>()
                    {
                        new Orm.Dtos.SearchItem()
                        {
                            Compare = Orm.Dtos.CompareOperator.ARRAYIN,
                            Field = "rowid",
                            Values = model.Rowids.Select(s=>s.ToString()).ToArray(),
                        }
                    }
                }
            });
            if (data_in.Count < model.Rowids.Count)
                return new ApiResultModel()
                {
                    messageTitle = ActionFilter.GetMultilingualValue("custom:messageTitle:dataNotexist"),
                    messageContent = [ActionFilter.GetMultilingualValue("custom:messageContent:dataChurn")],
                    messageType = MessageTypeUtils.Warning.ToString()
                };
            #endregion
            return await Task.FromResult(ContractOriginalArchiveService.DeleteSpecialSignature(model));
        }
        /// <summary>
        /// 特殊加签人下拉选项
        /// <param name="applyNumber">申请单号</param>
        /// </summary>
        /// <returns></returns>
        [HttpGet("GetDeleteSpecialSignature")]
        public async Task<ApiResultModelByObject> GetDeleteSpecialSignature(string applyNumber)
        {
            #region SIT546
            List<FlowStepSignerInvitee> lsitData = FlowStepSignerInviteeDataService.Query(new FlowStepSignerInviteeQueryCondition() { InviteeType = 2, ApplyNumber = applyNumber });
            if (lsitData.Count == 0) throw new Exception(ActionFilter.GetMultilingualValue("custom:messageContent:dataChurn"));
            #endregion

            #region 添加user離職時間 CR 385
            List<FlowStepSignerInviteeModel> newListData = new List<FlowStepSignerInviteeModel>();
            foreach (FlowStepSignerInvitee fssi in lsitData)
            {
                FlowStepSignerInviteeModel fssim = new FlowStepSignerInviteeModel();
                PsSubEeLglVwA ee = PsSubEeLglVwADataService.FindByKey(fssi.InviteeEmplid);
                CommonUtil.TypeCopy(fssi, fssim);
                fssim.InviteeEmplid = $"{ee.NameA}/{ee.Name}({ee.Emplid})";
                fssim.invitee_termination = ee?.Termination;
                newListData.Add(fssim);
            }
            #endregion

            ApiResultModelByObject apiResult = new ApiResultModelByObject()
            {
                listData = newListData,
                rtnSuccess = true,
            };
            return await Task.FromResult(apiResult);
        }
        /// <summary>
        /// 保存效期確認
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost("SaveDate")]
        public async Task<ApiResultModelByObject> SaveDate([FromBody] ContractValidityDate model)
        {
            #region 申請單已結案，攔截操作(場景：前端多開分頁點擊結案) SIT548
            var formApplication = FormApplicationDataService.FindByKey(model.ApplyNumber);
            if (formApplication == null || formApplication.ApplicationState.Equals("E"))
                throw new Exception(ActionFilter.GetMultilingualValue("custom:messageContent:dataChurn"));
            #endregion

            return await Task.FromResult(ContractOriginalArchiveService.SaveDate(model));
        }
    }
}
