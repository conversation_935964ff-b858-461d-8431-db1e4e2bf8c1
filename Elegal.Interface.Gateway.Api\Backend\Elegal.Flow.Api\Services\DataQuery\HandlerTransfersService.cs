﻿using Elegal.Flow.Api.Repository.DataQuery;
using Elegal.Flow.Common.Repository.FlowStep;
using Elegal.Flow.Common.Repository.FormApply;
using Elegal.Flow.Common.Services;
using Elegal.Interface.Api.Common.Control.Filter;
using Elegal.Interface.Api.Common.FuncHelper;
using Elegal.Interface.Api.Common.FuncService;
using Elegal.Interface.Api.Common.Model.DBModel.FlowStep;
using Elegal.Interface.Api.Common.Model.Enum;
using Elegal.Interface.Api.Common.Model.ParaModel.FlowApi;
using Elegal.Interface.Api.Common.Model.ResultModel;
using Elegal.Interface.Api.Common.Model.ResultModel.FlowApi;
using Elegal.Interface.Api.Common.Model.ViewModel.FlowApi;
using Elegal.Interface.Api.Common.Repository;
using Elegal.Interface.ApiData.Service.FuncService;
using Elegal.Interface.ApiData.Service.Model.Condition;
using Elegal.Interface.ApiData.Service.Model.DbModel;
using Elegal.Interface.ApiData.Service.Model.QueryCondition;
using Elegal.Orm;
using Newtonsoft.Json;
using System.Text;

namespace Elegal.Flow.Api.Services.DataQuery
{
    /// <summary>
    /// 经办人转单
    /// </summary>
    public static class HandlerTransfersService
    {
        private static readonly HandlerTransfersRepository _repository = new();
        private static readonly FormApplyRepository formApplyRepository = new FormApplyRepository();
        private static readonly FlowStepRepository _flowRepository = new FlowStepRepository();
        private static ApplicationPermissionRepository _applyPermissionRepository = new ApplicationPermissionRepository();
        private static HandlerTransfersByPicRepository _handlerTransfersByPicRepository = new HandlerTransfersByPicRepository();

        private static int GetApplySequence(string apply_number, int step_id)
        {
            List<flow_step_history> historyList = _flowRepository.GetFlowHistory(apply_number, step_id);
            if (historyList == null || historyList.Count() == 0)
                return 1;
            else
                return historyList.Count() + 1;
        }

        /// <summary>
        /// 列表数据
        /// </summary>
        /// <returns></returns>
        public static IEnumerable<HandlerTransferResultModel> ListData(TransferSearchModel model)
        {
            var user = MvcContext.UserInfo;
            IEnumerable<HandlerTransferResultModel> enumerable = _handlerTransfersByPicRepository.ListData(model).OrderByDescending(x => x.operate_time); ;
            List<HandlerTransferResultModel> handlerTransferResultModels = enumerable.ToList();
            var result = handlerTransferResultModels;
            foreach (var item in result) { item.time_zone = user.time_zone; }
            return result;
        }

        /// <summary>
        /// 檢視
        /// </summary>
        /// <returns></returns>
        public static HandlerTransferResultModel GetDataByApply(string transfer_pic_number)
        {
            IEnumerable<HandlerTransferResultModel> enumerable = _handlerTransfersByPicRepository.ListData(new TransferSearchModel() { transfer_pic_number = transfer_pic_number }).OrderByDescending(x => x.operate_time); ;
            HandlerTransferResultModel handlerTransferResultModels = enumerable.FirstOrDefault();
            List<ApplyDetail> applyDetails = _repository.GetDetailData(handlerTransferResultModels.transfer_id).ToList();
            handlerTransferResultModels.applyDetail = applyDetails;
            var result = handlerTransferResultModels;
            return handlerTransferResultModels;
        }

        /// <summary>
        /// 根据人员查询案件清单
        /// </summary>
        /// <param name="emplid">工号</param>
        /// <param name="deptid">部門</param>
        /// <returns></returns>
        public static IEnumerable<ApplyDetail> CaseData(string emplid, string deptid, string transfer_pic_number)
        {
            var user = MvcContext.UserInfo;
            var result = _repository.CaseData(emplid, deptid, transfer_pic_number);
            return result;
        }

        /// <summary>
        /// 插入經辦人轉單
        /// </summary>
        /// <param name="model">新增数据</param>
        /// <returns></returns>
        public static ApiResultModelByObject Submit(ModifyHandlerTransferModel model)
        {
            ApiResultModelByObject apiResult = new();

            if (model.handover_emp.Emplid.Equals(model.apply_pic_emp.Emplid))
            {
                apiResult.messageContent = new List<string> { string.Format(ActionFilter.GetMultilingualValue("custom:messageContent:HanderNotSamePic")) };
                apiResult.messageType = MessageTypeUtils.Warning.ToString();
                apiResult.rtnSuccess = false;
                return apiResult;
            }

            List<string> list = _repository.CheckApplyNumber(model.apply_number_list).ToList();
            if (list.Count > 0)
            {
                apiResult.messageTitle = ActionFilter.GetMultilingualValue("custom:messageTitle:createFail");
                apiResult.messageContent = new List<string> { string.Format(ActionFilter.GetMultilingualValue("custom:messageContent:ApplyInProcess")), string.Join(",", list) };
                apiResult.messageType = MessageTypeUtils.Warning.ToString();
                apiResult.rtnSuccess = false;
                return apiResult;
            }

            //Issue：274 獲取申請單序號改為使用存儲過程
            model.transfer_pic_number = SysApplicationSequenceService.GetApplicationNumber("transfer_pic", "T").ToString();
            bool sendMail = false;
            DbAccess.PerformInTransaction(context =>
            {
                int ModifyCount = _repository.Submit(model, GetApplySequence(model.apply_number, 1301), PsSubEeLglVwADataService.FindByKey(MvcContext.UserInfo.current_emp), context);
                sendMail = true;
            });
            if (sendMail) {
                SendMail(model, new List<MailTypeUtils> { MailTypeUtils.TP });
                apiResult.rtnSuccess = true;
                InitTransferLog(model, null, $"{ActionFilter.GetMultilingualValue("HandlerTransfersManagement_Submit", true)}");
            } 
            return apiResult;
        }

        private static void SendMail(ModifyHandlerTransferModel model, List<MailTypeUtils> list)
        {
            string encodeUrl = string.Empty;
            if (model.queryModel != null)
            {
                model.queryModel.transfer_pic_number = model.transfer_pic_number;
                string Key = AppSettingHelper.GetValue("MinIO:Key");
                string IV = AppSettingHelper.GetValue("MinIO:Iv");
                string encodeQueryUrl = AesService.Encrypt(JsonConvert.SerializeObject(model.queryModel), Key, IV);
                encodeUrl = AppSettingHelper.GetValue("BaseUrl") + "?query=" + Uri.EscapeDataString(encodeQueryUrl);
                _repository.MergeApplicationUrl(model.transfer_pic_number, encodeUrl);
                SendMailService.SendTransferMail(model.transfer_pic_number, list);
            }
            else
            {
                SendMailService.SendTransferMail(model.transfer_pic_number, list);
            }
        }

        /// <summary>
        /// 自動轉單
        /// </summary>
        /// <param name="model">修改数据</param>
        /// <returns></returns>
        public static void AutoTransfer(ModifyHandlerTransferModel model, TransferPicMain transferPicMain, PsSubEeLglVwA psSubEeLglVwA, IDbContext? dbContext = null)
        {
            List<string> list = _repository.GetDetailData(transferPicMain.TransferId?.ToString()).ToList().Select(e => e.apply_number).ToList();
            //待刪除的加簽表Rowid
            List<int?> delFlowStepSignerInvitees = new List<int?>();
            //待修改的加簽表數據
            List<FlowStepSignerInvitee> updateFlowStepSignerInvitees = new List<FlowStepSignerInvitee>();

            foreach (var item in list)
            {
                //查詢加簽表數據
                List<FlowStepSignerInvitee> flowStepSignerInvitees = FlowStepSignerInviteeDataService.Query(new FlowStepSignerInviteeQueryCondition()
                {
                    ApplyNumber = item,
                    InviteeType = 2,
                    SpInviteLevel = 1
                });
                //存在特殊單據時修改動作
                if (flowStepSignerInvitees.Count > 1)
                {
                    flowStepSignerInvitees.Remove(flowStepSignerInvitees[0]);
                    delFlowStepSignerInvitees.Add(flowStepSignerInvitees[0].Rowid);
                }
                if (flowStepSignerInvitees.Count > 0)
                {
                    updateFlowStepSignerInvitees.Add(new FlowStepSignerInvitee()
                    {
                        InviteeDeptid = psSubEeLglVwA.Deptid,
                        InviteeEmplid = psSubEeLglVwA.Emplid,
                        InviteeType = 2,
                        SpInviteLevel = 1,
                        Rowid = flowStepSignerInvitees[0].Rowid
                    });
                }
            }

            #region 自動轉單動作
            delFlowStepSignerInvitees.ForEach(e => {
                dbContext.DeleteByKey<FlowStepSignerInvitee, int?>(e);
            });

            dbContext.BatchUpdate<FlowStepSignerInvitee>(updateFlowStepSignerInvitees.ToArray());
            #endregion

            foreach (var item in list)
            {
                //修改C類申請單現任聯絡人(無論有沒有特殊加簽都需要修改的)
                dbContext.Update(new FormApplication()
                {
                    IncumbentEmplid = transferPicMain.HandoverEmplid,
                    IncumbentDeptid = psSubEeLglVwA.Deptid,
                    ApplyNumber = item
                });
                dbContext.Update(new LiteratureApplication()
                {
                    IncumbentEmplid = transferPicMain.HandoverEmplid,
                    IncumbentDeptid = psSubEeLglVwA.Deptid,
                    ApplyNumber = item
                });
            }
        }

        /// <summary>
        /// 獲取歷史經辦人部門
        /// </summary>
        /// <param name="emplid">工號</param>
        /// <returns></returns>
        internal static IEnumerable<string> GetFormerDept(string emplid)
        {
            //排除空的經辦人部門
            IEnumerable<string> enumerable = _repository.GetFormerDept(emplid);
            if (enumerable.Any())
                enumerable = enumerable.ToList().Where(s => !string.IsNullOrEmpty(s));
            return enumerable;
        }

        /// <summary>
        /// Approve
        /// </summary>
        /// <param name="model">審批/提交</param>
        /// <returns></returns>
        internal static ApiResultModelByObject Approve(ModifyHandlerTransferModel model)
        {
            ApiResultModelByObject apiResult = new();
            TransferLogInfo logInfoOld = null;
            TransferPicMain transferPicMain = TransferPicMainDataService.FindByKey(model.transfer_pic_number);

            string statusDetail = string.Empty;
            SysParameters sysParameters = SysParametersDataService.Find(new SysParametersCondition()
            {
                ParaCode = "transApplyStatus",
                LangType = "ZH-TW",
                FuncCode = transferPicMain.TransApplicationStatus
            });
            if (sysParameters != null) statusDetail = sysParameters.FunName ?? "";

            PsSubEeLglVwA fillEmp = PsSubEeLglVwADataService.FindByKey(transferPicMain.FillEmplid);
            PsSubOgLglVwA psSubOgLglVwA = PsSubOgLglVwADataService.FindByKey(model.handover_deptid);

            string ManagerDept = psSubOgLglVwA != null ? PsSubEeLglVwADataService.FindByKey(psSubOgLglVwA.ManagerId).Deptid : "";
            string agent_empid = string.Empty;
            FlowStepSigner flowStep = FlowStepSignerDataService.Find(new FlowStepSignerQueryCondition
            {
                ApplyNumber = model.transfer_pic_number,
            });
            if (flowStep != null)
                agent_empid = _applyPermissionRepository.GetAgenByEmp(flowStep.SignerEmplid, flowStep.SignerDeptid);

            switch (transferPicMain.TransApplicationStatus)
            {
                //撤回之後提交
                case "00":
                    logInfoOld = _repository.QueryLogInfo(model.transfer_pic_number);
                    if (MvcContext.UserInfo.current_emp.Equals(transferPicMain.FillEmplid) || (MvcContext.UserInfo.current_emp.Equals(agent_empid)))
                    {

                        string pic_emplid = model.apply_pic_emp.Emplid;
                        string handover_emplid = model.handover_emp.Emplid;
                        if (!string.IsNullOrEmpty(pic_emplid) && pic_emplid.Equals(handover_emplid))
                        {
                            apiResult.messageContent = new List<string> { string.Format(ActionFilter.GetMultilingualValue("custom:messageContent:HanderNotSamePic")) };
                            apiResult.messageType = MessageTypeUtils.Warning.ToString();
                            apiResult.rtnSuccess = false;
                            return apiResult;
                        }

                        //校驗待轉單數據狀態是否正常
                        List<string> list = _repository.CheckApplyNumber(model.apply_number_list, model.transfer_pic_number).ToList();
                        if (list.Count > 0)
                        {
                            apiResult.messageTitle = ActionFilter.GetMultilingualValue("custom:messageTitle:createFail");
                            apiResult.messageContent = new List<string> { string.Format(ActionFilter.GetMultilingualValue("custom:messageContent:ApplyInProcess")), string.Join(",", list) };
                            apiResult.messageType = MessageTypeUtils.Warning.ToString();
                            apiResult.rtnSuccess = false;
                            return apiResult;
                        }

                        //如果當前登錄人等於填單人則實際簽核人為填單人否則為代理人
                        PsSubEeLglVwA actualSignerEmp = new PsSubEeLglVwA();
                        if (fillEmp != null && MvcContext.UserInfo.current_emp.Equals(fillEmp.Emplid))
                        {
                            actualSignerEmp = fillEmp;
                        }
                        else
                        {
                            actualSignerEmp = PsSubEeLglVwADataService.FindByKey(agent_empid);
                        }
                        bool sendMail = false;
                        DbAccess.PerformInTransaction(context =>
                        {
                            //主表数据更新
                            context.Update(new TransferPicMain()
                            {
                                TransApplicationStatus = "01",
                                ApprovedStatus = 1,
                                ApproveEmplid = psSubOgLglVwA.ManagerId,
                                ApproveDeptid = ManagerDept,
                                ModifyUser = MvcContext.UserInfo.current_emp,
                                ModifyTime = DateTime.UtcNow,
                                TransferPicNumber = model.transfer_pic_number,
                                SignOptions = model.sign_options,
                                TransferRemarks = model.transfer_remarks,
                                //這裡的handover_deptid是轉單部門
                                TransferDeptid = model.handover_deptid,
                                HandoverEmplid = model.handover_emp.Emplid
                            });
                            //子表数据清除
                            context.SqlExecute($"delete from transfer_pic_history where transfer_id = ${transferPicMain.TransferId};");
                            //新增子表数据
                            context.BatchCreateSql(model.apply_number_list.Select(s => new TransferPicHistory()
                            {
                                TransferId = transferPicMain.TransferId,
                                ApplyNumber = s,
                                CreateTime = DateTime.UtcNow,
                                CreateUser = MvcContext.UserInfo.current_emp,
                            }).ToArray());
                            //簽核表修改
                            _repository.UpdateFlowStepSigner(new FlowStepSigner()
                            {
                                StepId = 1302,
                                SignerTime = DateTime.UtcNow,
                                SignerEmplid = psSubOgLglVwA.ManagerId,
                                SignerDeptid = ManagerDept,
                                ApplyNumber = model.transfer_pic_number,
                                IsReject = 0
                            }, context);
                            //曆程表新增數據
                            if (fillEmp != null)
                                context.Create(new FlowStepHistory()
                                {
                                    FlowId = 13,
                                    ApplyNumber = model.transfer_pic_number,
                                    StepId = 1301,
                                    ApplySequence = GetApplySequence(model.transfer_pic_number, 1301),
                                    StepAction = 0,
                                    StepOpinion = model.sign_options,
                                    ActualSignerEmplid = actualSignerEmp.Emplid,
                                    ActualSignerDeptid = actualSignerEmp.Deptid,
                                    ActualSignerName = actualSignerEmp.Name,
                                    ActualSignerNameA = actualSignerEmp.Name,
                                    ShouldSignerDeptid = fillEmp.Deptid,
                                    ShouldSignerEmplid = fillEmp.Emplid,
                                    ShouldSignerName = fillEmp.Name,
                                    ShouldSignerNameA = fillEmp.Name,
                                    CreateTime = DateTime.UtcNow
                                });
                            sendMail = true;
                        });
                        if (sendMail) {
                            SendMail(model, new List<MailTypeUtils> { MailTypeUtils.TP });
                            InitTransferLog(model, statusDetail, $"{ActionFilter.GetMultilingualValue("HandlerTransfersManagement_approve", true)}", logInfoOld);
                        } 
                    }
                    else
                    {
                        apiResult.messageTitle = ActionFilter.GetMultilingualValue("custom:messageTitle:dataNotexist");
                        apiResult.messageContent = new List<string> { string.Format(ActionFilter.GetMultilingualValue("custom:messageContent:dataChurn")) };
                        apiResult.messageType = MessageTypeUtils.Warning.ToString();
                        apiResult.rtnSuccess = false;
                        return apiResult;
                    }
                    break;
                //主管審批
                case "01":
                    if (MvcContext.UserInfo.current_emp.Equals(transferPicMain.ApproveEmplid) || (MvcContext.UserInfo.current_emp.Equals(agent_empid)))
                    {
                        bool sendMail = ManagerApprove(model, !string.IsNullOrEmpty(agent_empid) ? agent_empid : null);
                        if (sendMail) {
                            SendMail(model, new List<MailTypeUtils> { MailTypeUtils.TP });
                            InitTransferLog(model, statusDetail, $"{ActionFilter.GetMultilingualValue("HandlerTransfersManagement_approve", true)}");
                        } 
                    }
                    else
                    {
                        apiResult.messageTitle = ActionFilter.GetMultilingualValue("custom:messageTitle:dataNotexist");
                        apiResult.messageContent = new List<string> { string.Format(ActionFilter.GetMultilingualValue("custom:messageContent:dataChurn")) };
                        apiResult.messageType = MessageTypeUtils.Warning.ToString();
                        apiResult.rtnSuccess = false;
                        return apiResult;
                    }
                    break;
                //交接人審批
                case "02":
                    if (MvcContext.UserInfo.current_emp.Equals(transferPicMain.ApproveEmplid) || (MvcContext.UserInfo.current_emp.Equals(agent_empid)))
                    {
                        bool sendMail = HandlerApprove(model, !string.IsNullOrEmpty(agent_empid) ? agent_empid : null);
                        if (sendMail) {
                            SendMail(model, new List<MailTypeUtils> { MailTypeUtils.TPA });
                            InitTransferLog(model, statusDetail, $"{ActionFilter.GetMultilingualValue("HandlerTransfersManagement_approve", true)}");
                        } 
                    }
                    else
                    {
                        apiResult.messageTitle = ActionFilter.GetMultilingualValue("custom:messageTitle:dataNotexist");
                        apiResult.messageContent = new List<string> { string.Format(ActionFilter.GetMultilingualValue("custom:messageContent:dataChurn")) };
                        apiResult.messageType = MessageTypeUtils.Warning.ToString();
                        apiResult.rtnSuccess = false;
                        return apiResult;
                    }
                    break;
                //重新提交
                case "03":
                    logInfoOld = _repository.QueryLogInfo(model.transfer_pic_number);
                    if (MvcContext.UserInfo.current_emp.Equals(transferPicMain.ApproveEmplid) || (MvcContext.UserInfo.current_emp.Equals(agent_empid)))
                    {
                        string pic_emplid = model.apply_pic_emp.Emplid;
                        string handover_emplid = model.handover_emp.Emplid;
                        if (!string.IsNullOrEmpty(pic_emplid) && pic_emplid.Equals(handover_emplid))
                        {
                            apiResult.messageContent = new List<string> { string.Format(ActionFilter.GetMultilingualValue("custom:messageContent:HanderNotSamePic")) };
                            apiResult.messageType = MessageTypeUtils.Warning.ToString();
                            apiResult.rtnSuccess = false;
                            return apiResult;
                        }

                        //校驗待轉單數據狀態是否正常
                        List<string> list = _repository.CheckApplyNumber(model.apply_number_list, model.transfer_pic_number).ToList();
                        if (list.Count > 0)
                        {
                            apiResult.messageTitle = ActionFilter.GetMultilingualValue("custom:messageTitle:createFail");
                            apiResult.messageContent = new List<string> { string.Format(ActionFilter.GetMultilingualValue("custom:messageContent:ApplyInProcess")), string.Join(",", list) };
                            apiResult.messageType = MessageTypeUtils.Warning.ToString();
                            apiResult.rtnSuccess = false;
                            return apiResult;
                        }

                        //如果當前登錄人等於填單人則實際簽核人為填單人否則為代理人
                        PsSubEeLglVwA actualSignerEmp = new PsSubEeLglVwA();
                        if (fillEmp != null && MvcContext.UserInfo.current_emp.Equals(fillEmp.Emplid))
                        {
                            actualSignerEmp = fillEmp;
                        }
                        else
                        {
                            actualSignerEmp = PsSubEeLglVwADataService.FindByKey(agent_empid);
                        }
                        bool sendMail = false;
                        DbAccess.PerformInTransaction(context =>
                        {
                            //主表数据更新
                            context.Update(new TransferPicMain()
                            {
                                TransApplicationStatus = "01",
                                ApprovedStatus = 1,
                                ApproveEmplid = psSubOgLglVwA.ManagerId,
                                ApproveDeptid = ManagerDept,
                                ModifyUser = MvcContext.UserInfo.current_emp,
                                ModifyTime = DateTime.UtcNow,
                                TransferPicNumber = model.transfer_pic_number,
                                SignOptions = model.sign_options,
                                TransferRemarks = model.transfer_remarks,
                                //這裡的handover_deptid是轉單部門
                                TransferDeptid = model.handover_deptid,
                                HandoverEmplid = model.handover_emp.Emplid
                            });
                            //子表数据清除
                            context.SqlExecute($"delete from transfer_pic_history where transfer_id = ${transferPicMain.TransferId};");
                            //新增子表数据
                            context.BatchCreateSql(model.apply_number_list.Select(s => new TransferPicHistory()
                            {
                                TransferId = transferPicMain.TransferId,
                                ApplyNumber = s,
                                CreateTime = DateTime.UtcNow,
                                CreateUser = MvcContext.UserInfo.current_emp,
                            }).ToArray());

                            //簽核表修改
                            _repository.UpdateFlowStepSigner(new FlowStepSigner()
                            {
                                StepId = 1302,
                                SignerTime = DateTime.UtcNow,
                                SignerEmplid = psSubOgLglVwA.ManagerId,
                                //這裡的handover_deptid是轉單部門
                                SignerDeptid = model.handover_deptid,
                                ApplyNumber = model.transfer_pic_number,
                                IsReject = 0
                            }, context);
                            //曆程表新增數據
                            if (fillEmp != null)
                                context.Create(new FlowStepHistory()
                                {
                                    FlowId = 13,
                                    ApplyNumber = model.transfer_pic_number,
                                    StepId = 1301,
                                    ApplySequence = GetApplySequence(model.transfer_pic_number, 1301),
                                    StepAction = 0,
                                    StepOpinion = model.sign_options,
                                    ActualSignerEmplid = actualSignerEmp.Emplid,
                                    ActualSignerDeptid = actualSignerEmp.Deptid,
                                    ActualSignerName = actualSignerEmp.Name,
                                    ActualSignerNameA = actualSignerEmp.Name,
                                    ShouldSignerDeptid = fillEmp.Deptid,
                                    ShouldSignerEmplid = fillEmp.Emplid,
                                    ShouldSignerName = fillEmp.Name,
                                    ShouldSignerNameA = fillEmp.Name,
                                    CreateTime = DateTime.UtcNow
                                });
                            sendMail = true;
                        });
                        if (sendMail) {
                            SendMail(model, new List<MailTypeUtils> { MailTypeUtils.TP });
                            InitTransferLog(model, statusDetail, $"{ActionFilter.GetMultilingualValue("HandlerTransfersManagement_approve", true)}", logInfoOld);
                        }
                    }
                    else
                    {
                        apiResult.messageTitle = ActionFilter.GetMultilingualValue("custom:messageTitle:dataNotexist");
                        apiResult.messageContent = new List<string> { string.Format(ActionFilter.GetMultilingualValue("custom:messageContent:dataChurn")) };
                        apiResult.messageType = MessageTypeUtils.Warning.ToString();
                        apiResult.rtnSuccess = false;
                        return apiResult;
                    }
                    break;

                default:
                    apiResult.messageTitle = ActionFilter.GetMultilingualValue("custom:messageTitle:dataNotexist");
                    apiResult.messageContent = new List<string> { string.Format(ActionFilter.GetMultilingualValue("custom:messageContent:dataChurn")) };
                    apiResult.messageType = MessageTypeUtils.Warning.ToString();
                    apiResult.rtnSuccess = false;
                    return apiResult;
            }
            apiResult.rtnSuccess = true;
            return apiResult;
        }

        /// <summary>
        /// ManagerApprove
        /// </summary>
        /// <param name="model">主管審批</param>
        /// <returns></returns>
        internal static bool ManagerApprove(ModifyHandlerTransferModel model, string agentEmplid)
        {
            TransferPicMain transferPicMain = TransferPicMainDataService.FindByKey(model.transfer_pic_number);
            PsSubEeLglVwA handoverEmp = PsSubEeLglVwADataService.FindByKey(transferPicMain.HandoverEmplid);
            PsSubEeLglVwA approveEmp = PsSubEeLglVwADataService.FindByKey(transferPicMain.ApproveEmplid);
            PsSubEeLglVwA actualSignerEmp = new PsSubEeLglVwA();
            if (approveEmp != null && MvcContext.UserInfo.current_emp.Equals(approveEmp.Emplid))
            {
                actualSignerEmp = approveEmp;
            }
            else
            {
                actualSignerEmp = PsSubEeLglVwADataService.FindByKey(agentEmplid);
            }
            bool sendMail = false;
            //主管審批
            if (handoverEmp != null)
                DbAccess.PerformInTransaction(context =>
                {
                    //簽核表修改
                    _repository.UpdateFlowStepSigner(new FlowStepSigner()
                    {
                        StepId = 1303,
                        SignerTime = DateTime.UtcNow,
                        SignerEmplid = handoverEmp.Emplid,
                        SignerDeptid = handoverEmp.Deptid,
                        ApplyNumber = model.transfer_pic_number,
                        IsReject = 0
                    }, context);


                    if (actualSignerEmp != null)
                        //曆程表新增數據
                        context.Create(new FlowStepHistory()
                        {
                            FlowId = 13,
                            ApplyNumber = model.transfer_pic_number,
                            StepId = 1302,
                            ApplySequence = GetApplySequence(model.transfer_pic_number, 1302),
                            StepAction = 2,
                            StepOpinion = model.sign_options,
                            ActualSignerEmplid = actualSignerEmp.Emplid,
                            ActualSignerDeptid = actualSignerEmp.Deptid,
                            ActualSignerName = actualSignerEmp.Name,
                            ActualSignerNameA = actualSignerEmp.Name,
                            ShouldSignerDeptid = approveEmp.Deptid,
                            ShouldSignerEmplid = approveEmp.Emplid,
                            ShouldSignerName = approveEmp.Name,
                            ShouldSignerNameA = approveEmp.Name,
                            CreateTime = DateTime.UtcNow
                        });
                    context.Update(new TransferPicMain()
                    {
                        TransApplicationStatus = "02",
                        ApprovedStatus = 1,
                        ApproveEmplid = transferPicMain.HandoverEmplid,
                        ModifyUser = MvcContext.UserInfo.current_emp,
                        ApproveDeptid = handoverEmp.Deptid,
                        ModifyTime = DateTime.UtcNow,
                        TransferPicNumber = model.transfer_pic_number,
                        SignOptions = model.sign_options
                    });
                    sendMail = true;
                });
            return sendMail;
        }

        /// <summary>
        /// HandlerApprove
        /// </summary>
        /// <param name="model">經辦人審批</param>
        /// <returns></returns>
        internal static bool HandlerApprove(ModifyHandlerTransferModel model, string agentEmplid)
        {
            TransferPicMain transferPicMain = TransferPicMainDataService.FindByKey(model.transfer_pic_number);
            PsSubEeLglVwA handerEE = PsSubEeLglVwADataService.FindByKey(transferPicMain.HandoverEmplid);
            bool sendMail = false;
            if (transferPicMain != null)
            {
                PsSubEeLglVwA approveEmp = PsSubEeLglVwADataService.FindByKey(transferPicMain.ApproveEmplid);
                PsSubEeLglVwA actualSignerEmp = new PsSubEeLglVwA();
                if (approveEmp != null && MvcContext.UserInfo.current_emp.Equals(approveEmp.Emplid))
                {
                    actualSignerEmp = approveEmp;
                }
                else
                {
                    actualSignerEmp = PsSubEeLglVwADataService.FindByKey(agentEmplid);
                }
                DbAccess.PerformInTransaction(context =>
                {
                    //簽核表修改
                    _repository.UpdateFlowStepSigner(new FlowStepSigner()
                    {
                        StepId = 1304,
                        SignerTime = DateTime.UtcNow,
                        SignerEmplid = "",
                        SignerDeptid = "",
                        ApplyNumber = model.transfer_pic_number,
                        IsReject = 0
                    }, context);

                    if (actualSignerEmp != null)
                        //曆程表新增數據
                        context.Create(new FlowStepHistory()
                        {
                            FlowId = 13,
                            ApplyNumber = model.transfer_pic_number,
                            StepId = 1303,
                            ApplySequence = GetApplySequence(model.transfer_pic_number, 1301),
                            StepAction = 2,
                            StepOpinion = model.sign_options,
                            ActualSignerEmplid = actualSignerEmp.Emplid,
                            ActualSignerDeptid = actualSignerEmp.Deptid,
                            ActualSignerName = actualSignerEmp.Name,
                            ActualSignerNameA = actualSignerEmp.Name,
                            ShouldSignerDeptid = approveEmp.Deptid,
                            ShouldSignerEmplid = approveEmp.Emplid,
                            ShouldSignerName = approveEmp.Name,
                            ShouldSignerNameA = approveEmp.Name,
                            CreateTime = DateTime.UtcNow
                        });
                    //經辦人審批
                    context.Update(new TransferPicMain()
                    {
                        TransApplicationStatus = "04",
                        ApprovedStatus = 1,
                        ApproveEmplid = "",
                        ApproveDeptid = "",
                        CompleteDate = DateTime.UtcNow,
                        ModifyUser = MvcContext.UserInfo.current_emp,
                        ModifyTime = DateTime.UtcNow,
                        TransferPicNumber = model.transfer_pic_number,
                        SignOptions = model.sign_options
                    });
                    //自動轉單
                    AutoTransfer(model, transferPicMain, handerEE, context);
                    sendMail = true;
                });
            }
            return sendMail;
        }

        /// <summary>
        /// 駁回
        /// </summary>
        /// <param name="model">駁回</param>
        /// <returns></returns>
        internal static ApiResultModelByObject Reject(ModifyHandlerTransferModel model)
        {
            ApiResultModelByObject apiResult = new();
            TransferPicMain transferPicMain = TransferPicMainDataService.FindByKey(model.transfer_pic_number);

            SysParameters sysParameters = SysParametersDataService.Find(new SysParametersCondition()
            {
                ParaCode = "transApplyStatus",
                LangType = "ZH-TW",
                FuncCode = transferPicMain.TransApplicationStatus
            });

            string statusDetail = string.Empty;
            if (sysParameters != null) statusDetail = sysParameters.FunName ?? "";

            string agent_empid = string.Empty;
            FlowStepSigner flowStep = FlowStepSignerDataService.Find(new FlowStepSignerQueryCondition
            {
                ApplyNumber = model.transfer_pic_number,
            });
            if (flowStep != null)
                agent_empid = _applyPermissionRepository.GetAgenByEmp(flowStep.SignerEmplid, flowStep.SignerDeptid);
            if (transferPicMain != null)
            {
                PsSubEeLglVwA fillEmp = PsSubEeLglVwADataService.FindByKey(transferPicMain.FillEmplid);
                PsSubEeLglVwA approveEmp = PsSubEeLglVwADataService.FindByKey(transferPicMain.ApproveEmplid);
                SysEmail sysEmail = new SysEmail();
                IEnumerable<TransferEmpInfoModel> enumerable = _repository.QuaryTransferMailInfo(model.transfer_pic_number);
                TransferEmpInfoModel? transferEmpInfoModel = enumerable.FirstOrDefault();
                List<string> mailList = new List<string>();

                if (MvcContext.UserInfo.current_emp.Equals(transferPicMain.ApproveEmplid) || (MvcContext.UserInfo.current_emp.Equals(agent_empid)))
                {
                    PsSubEeLglVwA actualSignerEmp = new PsSubEeLglVwA();
                    if (approveEmp != null && MvcContext.UserInfo.current_emp.Equals(approveEmp.Emplid))
                    {
                        actualSignerEmp = approveEmp;
                    }
                    else
                    {
                        actualSignerEmp = PsSubEeLglVwADataService.FindByKey(agent_empid);
                    }
                    int stepId;
                    switch (transferPicMain.TransApplicationStatus)
                    {
                        case "01":
                            stepId = 1302;
                            break;
                        case "02":
                            stepId = 1303;
                            break;
                        default:
                            stepId = 0;
                            break;
                    }

                    bool sendMail = false;
                    DbAccess.PerformInTransaction(context =>
                    {
                        //簽核表修改
                        _repository.UpdateFlowStepSigner(new FlowStepSigner()
                        {
                            StepId = 1301,
                            SignerTime = DateTime.UtcNow,
                            SignerEmplid = transferPicMain.FillEmplid,
                            SignerDeptid = transferPicMain.TransferDeptid,
                            ApplyNumber = model.transfer_pic_number,
                            IsReject = 1
                        }, context);

                        if (fillEmp != null && actualSignerEmp != null)
                            //曆程表新增數據
                            context.Create(new FlowStepHistory()
                            {
                                FlowId = 13,
                                ApplyNumber = model.transfer_pic_number,
                                StepId = stepId,
                                ApplySequence = GetApplySequence(model.transfer_pic_number, stepId),
                                StepAction = 3,
                                StepOpinion = model.sign_options,
                                ActualSignerEmplid = actualSignerEmp.Emplid,
                                ActualSignerDeptid = actualSignerEmp.Deptid,
                                ActualSignerName = actualSignerEmp.Name,
                                ActualSignerNameA = actualSignerEmp.Name,
                                ShouldSignerDeptid = approveEmp.Deptid,
                                ShouldSignerEmplid = approveEmp.Emplid,
                                ShouldSignerName = approveEmp.Name,
                                ShouldSignerNameA = approveEmp.Name,
                                CreateTime = DateTime.UtcNow
                            });
                        context.Update(new TransferPicMain()
                        {
                            TransApplicationStatus = "03",
                            ApprovedStatus = 2,
                            ApproveDeptid = transferPicMain.FillDeptid,
                            ApproveEmplid = transferPicMain.FillEmplid,
                            ModifyUser = MvcContext.UserInfo.current_emp,
                            ModifyTime = DateTime.UtcNow,
                            TransferPicNumber = model.transfer_pic_number,
                            SignOptions = model.sign_options,
                            IsNew = 1
                        });
                        sendMail = true;
                    });
                    //經辦人部門主管審批
                    if (sendMail) {
                        SendMail(model, new List<MailTypeUtils> { MailTypeUtils.TPR });
                        apiResult.rtnSuccess = true;
                        InitTransferLog(model, statusDetail, $"{ActionFilter.GetMultilingualValue("HandlerTransfersManagement_reject", true)}");
                    }
                }
                else
                {
                    apiResult.messageTitle = ActionFilter.GetMultilingualValue("custom:messageTitle:dataNotexist");
                    apiResult.messageContent = new List<string> { string.Format(ActionFilter.GetMultilingualValue("custom:messageContent:dataChurn")) };
                    apiResult.messageType = MessageTypeUtils.Warning.ToString();
                    apiResult.rtnSuccess = false;
                    return apiResult;
                }
                
                return apiResult;
            }
            return apiResult;
        }

        private static void InitTransferLog(ModifyHandlerTransferModel model, string status, string action, TransferLogInfo infoOld = null)
        {
            TransferLogInfo logInfo = _repository.QueryLogInfo(model.transfer_pic_number);
            ActionFilter.InitLogRecord(model, log =>
            {
                StringBuilder stringBuilder = new();
                if (!string.IsNullOrEmpty(logInfo.apply_list))
                    stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("HandlerTransfersManagement_applyNumber", true)}：{logInfo.apply_list}");
                if (!string.IsNullOrEmpty(logInfo.pic))
                    stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("HandlerTransfersManagement_applyIncumbentEmp", true)}：{logInfo.pic})");
                if (!string.IsNullOrEmpty(logInfo.handover))
                    stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("HandlerTransfersManagement_handoverEmp", true)}：{logInfo.handover})");
                if (!string.IsNullOrEmpty(logInfo.transfer_deptid))
                    stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("HandlerTransfersManagement_handoverDeptid", true)}：{logInfo.transfer_deptid}");
                if (!string.IsNullOrEmpty(action))
                    stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("HandlerTransfersManagement_action", true)}：{action}");
                if (!string.IsNullOrEmpty(logInfo.transfer_remarks))
                    stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("HandlerTransfersManagement_transferRemarks", true)}：{logInfo.transfer_remarks}");
                if (!string.IsNullOrEmpty(status))
                    stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("HandlerTransfersManagement_status", true)}：{status}");
                if (!string.IsNullOrEmpty(model.sign_options))
                    stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("HandlerTransfersManagement_signOptions", true)}：{model.sign_options}");

                if (infoOld != null) {
                    StringBuilder stringBuilderOld = new StringBuilder();
                    if (!string.IsNullOrEmpty(infoOld.apply_list))
                        stringBuilderOld.AppendLine($"{ActionFilter.GetMultilingualValue("HandlerTransfersManagement_applyNumber", true)}：{infoOld.apply_list}");
                    if (!string.IsNullOrEmpty(infoOld.pic))
                        stringBuilderOld.AppendLine($"{ActionFilter.GetMultilingualValue("HandlerTransfersManagement_applyIncumbentEmp", true)}：{infoOld.pic})");
                    if (!string.IsNullOrEmpty(infoOld.handover))
                        stringBuilderOld.AppendLine($"{ActionFilter.GetMultilingualValue("HandlerTransfersManagement_handoverEmp", true)}：{infoOld.handover})");
                    if (!string.IsNullOrEmpty(infoOld.transfer_deptid))
                        stringBuilderOld.AppendLine($"{ActionFilter.GetMultilingualValue("HandlerTransfersManagement_handoverDeptid", true)}：{infoOld.transfer_deptid}");
                    if (!string.IsNullOrEmpty(infoOld.transfer_remarks))
                        stringBuilderOld.AppendLine($"{ActionFilter.GetMultilingualValue("HandlerTransfersManagement_transferRemarks", true)}：{infoOld.transfer_remarks}");
                    if (!string.IsNullOrEmpty(model.sign_options))
                        stringBuilderOld.AppendLine($"{ActionFilter.GetMultilingualValue("HandlerTransfersManagement_signOptions", true)}：{model.sign_options}");
                    log.DetailFormer = stringBuilderOld.ToString();
                }
                log.Detail = stringBuilder.ToString();
            });
        }

        /// <summary>
        /// 作廢
        /// </summary>
        /// <param name="model">作廢</param>
        /// <returns></returns>
        internal static ApiResultModelByObject Cancellation(ModifyHandlerTransferModel model)
        {
            ApiResultModelByObject apiResult = new();
            TransferPicMain transferPicMain = TransferPicMainDataService.FindByKey(model.transfer_pic_number);

            SysParameters sysParameters = SysParametersDataService.Find(new SysParametersCondition()
            {
                ParaCode = "transApplyStatus",
                LangType = "ZH-TW",
                FuncCode = transferPicMain.TransApplicationStatus
            });

            string statusDetail = string.Empty;
            if (sysParameters != null) statusDetail = sysParameters.FunName ?? "";

            if (transferPicMain != null)
            {
                PsSubEeLglVwA fillEmp = PsSubEeLglVwADataService.FindByKey(transferPicMain.FillEmplid);
                string agent_empid = string.Empty;
                FlowStepSigner flowStep = FlowStepSignerDataService.Find(new FlowStepSignerQueryCondition
                {
                    ApplyNumber = model.transfer_pic_number,
                });
                if (flowStep != null)
                    agent_empid = _applyPermissionRepository.GetAgenByEmp(flowStep.SignerEmplid, flowStep.SignerDeptid);

                PsSubEeLglVwA actualSignerEmp = new PsSubEeLglVwA();
                if (fillEmp != null && MvcContext.UserInfo.current_emp.Equals(fillEmp.Emplid))
                {
                    actualSignerEmp = fillEmp;
                }
                else
                {
                    actualSignerEmp = PsSubEeLglVwADataService.FindByKey(agent_empid);
                }

                SysEmail sysEmail = new SysEmail();
                IEnumerable<TransferEmpInfoModel> enumerable = _repository.QuaryTransferMailInfo(model.transfer_pic_number);
                TransferEmpInfoModel? transferEmpInfoModel = enumerable.FirstOrDefault();
                List<string> EReceivermailList = new List<string>();
                List<string> mailList = new List<string>();
                //能作廢的申請單狀態只有00已撤回，03已駁回
                if (!"00,03".Contains(transferPicMain.TransApplicationStatus))
                {
                    apiResult.messageTitle = ActionFilter.GetMultilingualValue("custom:messageTitle:dataNotexist");
                    apiResult.messageContent = new List<string> { string.Format(ActionFilter.GetMultilingualValue("custom:messageContent:dataChurn")) };
                    apiResult.messageType = MessageTypeUtils.Warning.ToString();
                    apiResult.rtnSuccess = false;
                    return apiResult;
                }

                if (MvcContext.UserInfo.current_emp.Equals(transferPicMain.FillEmplid) || (MvcContext.UserInfo.current_emp.Equals(agent_empid)))
                {
                    bool sendMail = false;
                    DbAccess.PerformInTransaction(context =>
                    {
                        //簽核表修改
                        _repository.UpdateFlowStepSigner(new FlowStepSigner()
                        {
                            StepId = 1301,
                            SignerTime = DateTime.UtcNow,
                            SignerEmplid = "",
                            SignerDeptid = "",
                            ApplyNumber = model.transfer_pic_number,
                            IsReject = 0,
                        }, context);

                        if (fillEmp != null)
                            //曆程表新增數據
                            context.Create(new FlowStepHistory()
                            {
                                FlowId = 13,
                                ApplyNumber = model.transfer_pic_number,
                                StepId = 1301,
                                ApplySequence = GetApplySequence(model.transfer_pic_number, 1301),
                                StepAction = 6,
                                StepOpinion = model.sign_options,
                                ActualSignerEmplid = actualSignerEmp.Emplid,
                                ActualSignerDeptid = actualSignerEmp.Deptid,
                                ActualSignerName = actualSignerEmp.Name,
                                ActualSignerNameA = actualSignerEmp.Name,
                                ShouldSignerDeptid = fillEmp.Deptid,
                                ShouldSignerEmplid = fillEmp.Emplid,
                                ShouldSignerName = fillEmp.Name,
                                ShouldSignerNameA = fillEmp.Name,
                                CreateTime = DateTime.UtcNow
                            });
                        _repository.Cancellation(model, context);
                        apiResult.rtnSuccess = true;
                        sendMail = true;
                    });
                    if (sendMail)
                    {
                        SendMail(model, new List<MailTypeUtils> { MailTypeUtils.TPV });
                        InitTransferLog(model, statusDetail, $"{ActionFilter.GetMultilingualValue("HandlerTransfersManagement_cancellation", true)}");
                    }
                }
                return apiResult;
            }
            else
            {
                apiResult.messageTitle = ActionFilter.GetMultilingualValue("custom:messageTitle:dataNotexist");
                apiResult.messageContent = new List<string> { string.Format(ActionFilter.GetMultilingualValue("custom:messageContent:dataChurn")) };
                apiResult.messageType = MessageTypeUtils.Warning.ToString();
                apiResult.rtnSuccess = false;
                return apiResult;
            }
        }


        /// <summary>
        /// 撤回
        /// </summary>
        /// <param name="model">撤回</param>
        /// <returns></returns>
        internal static ApiResultModelByObject WithDraw(ModifyHandlerTransferModel model)
        {
            ApiResultModelByObject apiResult = new();
            TransferPicMain transferPicMain = TransferPicMainDataService.FindByKey(model.transfer_pic_number);

            SysParameters sysParameters = SysParametersDataService.Find(new SysParametersCondition()
            {
                ParaCode = "transApplyStatus",
                LangType = "ZH-TW",
                FuncCode = transferPicMain.TransApplicationStatus
            });

            string statusDetail = string.Empty;
            if (sysParameters != null) statusDetail = sysParameters.FunName ?? "";

            //流程中的數據才允許撤回
            if (!"01,02".Contains(transferPicMain.TransApplicationStatus))
            {
                apiResult.messageTitle = ActionFilter.GetMultilingualValue("custom:messageTitle:dataNotexist");
                apiResult.messageContent = new List<string> { string.Format(ActionFilter.GetMultilingualValue("custom:messageContent:dataChurn")) };
                apiResult.messageType = MessageTypeUtils.Warning.ToString();
                apiResult.rtnSuccess = false;
                return apiResult;
            }

            if (transferPicMain != null)
            {
                PsSubEeLglVwA fillEmp = PsSubEeLglVwADataService.FindByKey(transferPicMain.FillEmplid);
                string agent_empid = string.Empty;
                FlowStepSigner flowStep = FlowStepSignerDataService.Find(new FlowStepSignerQueryCondition
                {
                    ApplyNumber = model.transfer_pic_number,
                });
                if (flowStep != null)
                    agent_empid = _applyPermissionRepository.GetAgenByEmp(flowStep.SignerEmplid, flowStep.SignerDeptid);
                SysEmail sysEmail = new SysEmail();
                IEnumerable<TransferEmpInfoModel> enumerable = _repository.QuaryTransferMailInfo(model.transfer_pic_number);
                TransferEmpInfoModel? transferEmpInfoModel = enumerable.FirstOrDefault();
                List<string> EReceivermailList = new List<string>();
                List<string> mailList = new List<string>();
                if (MvcContext.UserInfo.current_emp.Equals(transferPicMain.FillEmplid))
                {
                    int stepId;
                    switch (transferPicMain.TransApplicationStatus)
                    {
                        case "01":
                            stepId = 1302;
                            break;
                        case "02":
                            stepId = 1303;
                            break;
                        default:
                            stepId = 0;
                            break;
                    }
                    bool sendMail = false;
                    DbAccess.PerformInTransaction(context =>
                    {
                        //簽核表修改
                        _repository.UpdateFlowStepSigner(new FlowStepSigner()
                        {
                            StepId = 1301,
                            SignerTime = DateTime.UtcNow,
                            SignerEmplid = transferPicMain.FillEmplid,
                            SignerDeptid = transferPicMain.TransferDeptid,
                            ApplyNumber = model.transfer_pic_number,
                            IsReject = 1
                        }, context);

                        if (fillEmp != null)
                            //曆程表新增數據
                            context.Create(new FlowStepHistory()
                            {
                                FlowId = 13,
                                ApplyNumber = model.transfer_pic_number,
                                StepId = stepId,
                                ApplySequence = GetApplySequence(model.transfer_pic_number, stepId),
                                StepAction = 6,
                                StepOpinion = model.sign_options,
                                ActualSignerEmplid = fillEmp.Emplid,
                                ActualSignerDeptid = fillEmp.Deptid,
                                ActualSignerName = fillEmp.Name,
                                ActualSignerNameA = fillEmp.Name,
                                ShouldSignerDeptid = fillEmp.Deptid,
                                ShouldSignerEmplid = fillEmp.Emplid,
                                ShouldSignerName = fillEmp.Name,
                                ShouldSignerNameA = fillEmp.Name,
                                CreateTime = DateTime.UtcNow
                            });
                        context.Update(new TransferPicMain()
                        {
                            TransApplicationStatus = "00",
                            ApprovedStatus = 1,
                            ApproveDeptid = transferPicMain.FillDeptid,
                            ApproveEmplid = transferPicMain.FillEmplid,
                            ModifyUser = MvcContext.UserInfo.current_emp,
                            ModifyTime = DateTime.UtcNow,
                            TransferPicNumber = model.transfer_pic_number,
                            SignOptions = model.sign_options,
                            IsNew = 1
                        });
                        sendMail = true;
                    });
                    if (sendMail) {
                        SendMail(model, new List<MailTypeUtils> { MailTypeUtils.TPW });
                        InitTransferLog(model, statusDetail, $"{ActionFilter.GetMultilingualValue("HandlerTransfersManagement_withdrawn", true)}");
                    }
                        
                }
                else
                {
                    apiResult.messageTitle = ActionFilter.GetMultilingualValue("custom:messageTitle:dataNotexist");
                    apiResult.messageContent = new List<string> { string.Format(ActionFilter.GetMultilingualValue("custom:messageContent:dataChurn")) };
                    apiResult.messageType = MessageTypeUtils.Warning.ToString();
                    apiResult.rtnSuccess = false;
                    return apiResult;
                }
                apiResult.rtnSuccess = true;
                return apiResult;
            }
            return apiResult;
        }

        /// <summary>
        /// 查詢子列表
        /// </summary>
        /// <param name="transfer_id">transfer_id</param>
        /// <returns></returns>
        internal static IEnumerable<ApplyDetail> ChildListData(string transfer_id)
        {
            return _repository.GetDetailData(transfer_id);
        }

        internal static object QueryExportList(List<string> transfer_number_list)
        {
            List<HandlerTransferExportModel> handlerTransferExportModels = _repository.QueryExportList(transfer_number_list).ToList();
            List<HandlerTransferExportModel> list = new();
            foreach (HandlerTransferExportModel resultModel in handlerTransferExportModels)
            {
                resultModel.confiden_level = string.IsNullOrEmpty(resultModel.confiden_level) ? "02" : resultModel.confiden_level;
                //極機密案件且當前案件狀態為已核准狀態需要進行隱碼處理
                if (resultModel.confiden_level.ToUpper() == "01".ToUpper() && (resultModel.application_state.ToUpper() != "I".ToUpper() && resultModel.application_state.ToUpper() != "T".ToUpper()))
                {
                    HandlerTransferExportModel data = HiddenValueConvertHelper.ConvertToHiddenBySingle<HandlerTransferExportModel>(resultModel);
                    try { data.other_party = string.Join("， ", JsonConvert.DeserializeObject<List<string>>(data.other_party) ?? []); }
                    catch (Exception) { data.other_party = string.Join("， ", data.other_party.Split("/")); }
                    list.Add(data);
                }
                else
                {
                    try { resultModel.other_party = string.Join("， ", JsonConvert.DeserializeObject<List<string>>(resultModel.other_party) ?? []); }
                    catch (Exception) { resultModel.other_party = string.Join("， ", resultModel.other_party.Split("/")); }
                    list.Add(resultModel);
                }
            }
            return list;
        }
    }
}
