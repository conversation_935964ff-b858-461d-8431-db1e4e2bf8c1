﻿using Elegal.Flow.Api.Repository.Paper;
using Elegal.Flow.Api.Services.LendAppliction;
using Elegal.Flow.Common.Services;
using Elegal.Interface.Api.Common.Control.Filter;
using Elegal.Interface.Api.Common.FuncHelper;
using Elegal.Interface.Api.Common.FuncService;
using Elegal.Interface.Api.Common.Model.DBModel;
using Elegal.Interface.Api.Common.Model.Enum;
using Elegal.Interface.Api.Common.Model.ParaModel.FlowApi;
using Elegal.Interface.Api.Common.Model.ResultModel.FlowApi;
using Elegal.Interface.Api.Common.Model.ResultModel.FlowApi.LendAppliction;
using Elegal.Interface.Api.Common.Model.ViewModel.FlowApi;
using Elegal.Interface.ApiData.Service.FuncService;
using Elegal.Interface.ApiData.Service.Model.DbModel;
using Elegal.Orm;
using Elegal.Orm.Dtos;

namespace Elegal.Flow.Api.Services.Paper
{
    /// <summary>
    /// 紙本借出申請
    /// </summary>
    public class PaperLendingApplicationService
    {
        private static readonly PaperLendingApplicationRepository _repository = new();
        private static readonly PaperBasicDataRepository _basicRepository = new();

        #region 查詢借出資料
        /// <summary>
        /// 查詢借出資料
        /// </summary>
        public static PageResult<PaperLendingApplicationViewModel> QueryPaperLendingApplication(qryPaperLendingApplication qry)
        {
            PageResult<PaperLendingApplicationViewModel> res = _repository.QueryPaperLendingApplication(qry);
            List<PaperLendingApplicationViewModel> retrieve_res = res.Data.Where(x => x.lend_status == "02" && !string.IsNullOrEmpty(x.retrieve_number)).ToList();

            foreach (PaperLendingApplicationViewModel ret in retrieve_res)
            {
                List<paper_basic_data> retrieve_paper_List = _repository.GetRetrievePaper(ret.retrieve_number);
                if (retrieve_paper_List == null || retrieve_paper_List.Count == 0) continue;

                //借出明細
                List<paper_lending_detail> details = _repository.GetPaperLendingDetail(ret.lend_id);
                //未借出明細
                List<paper_lending_detail> un_details = _repository.GetPaperUnlendingDetail(ret.lend_id);

                foreach (paper_basic_data basic in retrieve_paper_List)
                {
                    //借出單未包含最新的紙本
                    if (!details.Select(x => x.paper_code).Contains(basic.paper_code))
                    {
                        //有新的紙本
                        if (!un_details.Select(x => x.paper_code).Contains(basic.paper_code))
                        {
                            ret.is_new_paper = 1;
                            break;
                        }
                        //有新的"已入庫紙本"
                        if (basic.paper_entry_status == "05")
                        {
                            ret.is_new_paper = 1;
                            break;
                        }
                    }
                }
            }

            return res;
        }
        #endregion

        #region 借出明細
        /// <summary>
        /// 借出明細
        /// </summary>
        public static List<PaperLendingApplicationDetailViewModel> GetPaperLendingDetails(int lend_id)
        {
            var d = _repository.GetPaperLendingDetails(lend_id, MvcContext.UserInfo.logging_locale);
            d.ForEach(o =>
            {
                if (!string.IsNullOrEmpty(o.contract_number)) o.contract_number = o.contract_number?.Replace("\u001f", "");
            });
            return d;
        }
        #endregion

        #region 調閱申請單列表
        /// <summary>
        /// 調閱申請單列表
        /// </summary>
        public static List<string> GetOtherApplicationList(string retrieve_number)
        {
            List<OtherApplicationDetail> retrieve_List = _repository.GetOtherApplicationDetail(retrieve_number);
            if (retrieve_List == null || retrieve_List.Count == 0) return null;

            return retrieve_List.Select(x => x.retrieve_number).Distinct().ToList();
        }
        #endregion

        #region 調閱申請單資訊(新增-需求資訊)
        /// <summary>
        /// 調閱申請單資訊(新增-需求資訊) -> 20250326PRD更版
        /// </summary>
        public static List<OtherApplicationDetailViewModel> GetOtherApplicationDetail(string retrieve_number)
        {
            List<OtherApplicationDetail> retrieve_List = _repository.GetOtherApplicationDetail(retrieve_number);
            if (retrieve_List == null || retrieve_List.Count == 0) return new List<OtherApplicationDetailViewModel>();

            List<string> retrieveNumberList = retrieve_List.Select(x => x.retrieve_number).ToList();

            List<OtherApplicationDetailViewModel> res = new List<OtherApplicationDetailViewModel>();
            foreach (string retrieveNumber in retrieveNumberList)
            {
                if (res.Count(x => x.retrieve_number == retrieveNumber) == 0)
                {
                    var firstItem = retrieve_List.Where(x => x.retrieve_number == retrieveNumber).FirstOrDefault();
                    var empList = retrieve_List.Where(x => x.retrieve_number == retrieveNumber)
                                  .Select(x => new OtherApplicationEmpDetailViewModel()
                                  {
                                      ename = x.ename,
                                      empid = x.empid,
                                      depid = x.depid,
                                      company = x.company,
                                      sort_order = x.sort_order
                                  })
                                  .GroupBy(x => x.empid)
                                  .Select(g => g.First())
                                  .OrderBy(x => x.sort_order)
                                  .ToList();

                    // 如果 empList 中沒有 empid，則將 empList 設為空
                    if (empList.All(e => string.IsNullOrEmpty(e.empid)))
                    {
                        empList = new List<OtherApplicationEmpDetailViewModel>();
                    }

                    res.Add(new OtherApplicationDetailViewModel()
                    {
                        retrieve_number = firstItem?.retrieve_number,
                        discloseperson = firstItem?.discloseperson,
                        otherperson = firstItem?.otherperson,
                        other = firstItem?.other,
                        auth_date_start = firstItem?.auth_date_start,
                        auth_date_end = firstItem?.auth_date_end,
                        contractList = retrieve_List.Where(x => x.retrieve_number == retrieveNumber)
                                            .Select(x => new OtherApplicationContractDetailViewModel()
                                            {
                                                applynumber = x.applynumber,
                                                contract_number = x.contract_number == null ? string.Empty : x.contract_number.Replace("\u001f", ""),
                                                contract_name = x.contract_name,
                                                paper_code = retrieve_List.Where(y => y.applynumber == x.applynumber).Select(z => z.paper_code).Distinct().ToList()
                                            })
                                            .GroupBy(x => x.applynumber)
                                            .Select(g => g.First())
                                            .ToList(),
                        empList = empList,
                        retrieve_handler = firstItem?.retrieve_handler,
                        applycause1 = firstItem?.applycause1,
                        applyopentime = firstItem?.applyopentime
                    });
                }
            }
            return res;
        }
        #endregion

        #region 借出明細查詢(新增-借出明細)
        /// <summary>
        /// 借出明細查詢(新增-借出明細)
        /// </summary>
        public static List<GetLendingDetailViewMode> GetLendingDetail(string? contract_number, string? paper_code, int? detail_limit = null)
        {
            if (string.IsNullOrEmpty(contract_number) && string.IsNullOrEmpty(paper_code))
            {
                throw new Exception(ActionFilter.GetMultilingualValue("custom:messageTitle:systemError"));
            }
            if (!string.IsNullOrEmpty(contract_number) && !string.IsNullOrEmpty(paper_code))
            {
                throw new Exception(ActionFilter.GetMultilingualValue("custom:messageTitle:systemError"));
            }
            return _repository.GetLendingDetail(contract_number, paper_code, detail_limit);
        }
        #endregion

        #region 借出明細查詢(新增-借出明細)(批次查詢)
        /// <summary>
        /// 借出明細查詢(新增-借出明細)(批次查詢)
        /// </summary>
        public static List<GetLendingDetailViewMode> BatchGetLendingDetail(string? contract_number, string? paper_code)
        {
            if (string.IsNullOrEmpty(contract_number) && string.IsNullOrEmpty(paper_code))
            {
                throw new Exception(ActionFilter.GetMultilingualValue("custom:messageTitle:systemError"));
            }
            if (!string.IsNullOrEmpty(contract_number) && !string.IsNullOrEmpty(paper_code))
            {
                throw new Exception(ActionFilter.GetMultilingualValue("custom:messageTitle:systemError"));
            }

            List<string> contract_number_list = new List<string>();
            List<string> paper_code_list = new List<string>();
            if (!string.IsNullOrEmpty(contract_number))
            {
                contract_number_list = contract_number.Split(',').ToList();
            }
            if (!string.IsNullOrEmpty(paper_code))
            {
                paper_code_list = paper_code.Split(',').ToList();
            }
            return _repository.BatchGetLendingDetail(contract_number_list, paper_code_list);
        }
        #endregion

        #region 利用合約編號查詢對應的紙本列表
        /// <summary>
        /// 利用合約編號查詢對應的紙本列表
        /// </summary>
        public static List<GetLendContractViewModel> GetContractNumber(string contract_number, int? detail_limit = null)
        {
            List<GetLendContractViewModel> res = new List<GetLendContractViewModel>();

            List<GetLendingDetailViewMode> dataList = _repository.GetLendingDetail(contract_number, "", detail_limit);
            if (dataList == null || dataList.Count == 0) return res;

            List<string> contractNumberList = dataList.Select(x => x.contract_number).Distinct().ToList();

            foreach (string contractNumber in contractNumberList)
            {
                if (res.Count(x => x.contract_number == contractNumber) == 0)
                {
                    res.Add(new GetLendContractViewModel()
                    {
                        contract_number = contractNumber,
                        paper_code = dataList.Where(x => x.contract_number == contractNumber).Select(x => x.paper_code).ToList()
                    });
                }
            }
            return res;
        }
        #endregion

        #region 借出單詳細資訊(回顯用)
        /// <summary>
        /// 借出單詳細資訊(回顯用)
        /// </summary>
        /// <param name="lend_id"></param>
        /// <returns></returns>
        public static LoanDetailsView GetPaperLendingApplication(int lend_id)
        {
            LendingApplicationViewModel res = new LendingApplicationViewModel();
            LoanDetailsView obj = LendSearchForUserService.GetLoanDetailsView(lend_id, MvcContext.UserInfo);

            if (obj != null)
            {
                paper_lending_demand demand = _repository.GetPaperLendingDemand(lend_id);
                res.lend_id = lend_id;
                res.PersonnelInfo = obj.PersonnelInfo;
                if (demand != null) res.demand_id = demand.demand_id;
                res.DemandInfo = obj.DemandInfo;
                res.PickUpInfo = obj.PickUpInfo;
                res.LendDetails = obj.LendDetails;
                res.UnlendingDetails = obj.UnlendingDetails;
                if (!string.IsNullOrEmpty(obj.DemandInfo.RetrieveNumber))
                    res.retrieve_detail = GetOtherApplicationDetail(obj.DemandInfo.RetrieveNumber).FirstOrDefault();
            }

            return res;
        }
        #endregion

        #region 新增借出申請(事務處理)

        #region 主方法
        /// <summary>
        /// 主方法
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public static PaperLendingResult SendLending(insPaperLendingApplication data)
        {
            //判斷送件類型
            SendingType sendingType = 0;
            if (data.lend_id != null && data.lend_id != 0)
            {
                paper_lending_application old = _repository.GetPaperLendingApplicationByLendID(data.lend_id.Value);
                if (old == null)
                    throw new Exception(ActionFilter.GetMultilingualValue("custom:messageTitle:dataNotexist"));

                //非暫存及待取件狀態不可修改借出單
                if (old.lend_status != "01" && old.lend_status != "02")
                    throw new Exception(ActionFilter.GetMultilingualValue("custom:messageContent:dataChurn"));

                //暫存-暫存
                if (data.lend_status == "01" && old.lend_status == "01") sendingType = SendingType.temp_temp;
                //暫存-送單
                if (data.lend_status == "02" && old.lend_status == "01") sendingType = SendingType.temp_lend;
                //送單-送單
                if (data.lend_status == "02" && old.lend_status == "02") sendingType = SendingType.lend_lend;

                if (data.lend_status == "01" && old.lend_status == "02")
                    throw new Exception(ActionFilter.GetMultilingualValue("custom:messageTitle:doNotUpdate"));
            }
            else
            {
                //新增暫存單
                if (data.lend_status == "01") sendingType = SendingType.new_temp;
                //新增借出單
                if (data.lend_status == "02") sendingType = SendingType.new_lend;
            }

            //檢查暫存單是否衝突(依據其他申請單號)
            //針對多開分頁
            if (data.lend_status == "01")
            {
                var temp = GetTempLoanDetailsView(data.retrieve_number);
                if (temp != null)
                {
                    if (data.lend_id != null && data.lend_id != 0)
                    {
                        //更新暫存單
                        //需檢查其他申請單號
                        var demand = _repository.GetPaperLendingDemand(data.lend_id.Value);
                        if (demand != null && demand.retrieve_number != data.retrieve_number)
                            throw new Exception(ActionFilter.GetMultilingualValue("custom:messageTitle:dataNotexist"));
                    }
                    else
                    {
                        //新增暫存單
                        throw new Exception(ActionFilter.GetMultilingualValue("custom:messageContent:TempApplicationExist"));
                    }
                }
            }

            //檢查借出明細(借出單)
            if ((data.details == null || data.details.Count == 0) && data.lend_status == "02")
                throw new Exception(ActionFilter.GetMultilingualValue("custom:messageTitle:doNotUpdate"));

            //如果有新的紙本或紙本被其他借出申請單借走，需提醒（“紙本資料有異動，請再次確認”）後回到提交前頁面
            if (!string.IsNullOrEmpty(data.retrieve_number) && data.lend_status == "02")
            {
                List<paper_basic_data> retrieve_paper_List = _repository.GetRetrievePaper(data.retrieve_number);
                if (retrieve_paper_List == null || retrieve_paper_List.Count == 0)
                    throw new Exception(ActionFilter.GetMultilingualValue("custom:messageTitle:dataNotexist"));

                foreach (paper_basic_data basic in retrieve_paper_List)
                {
                    if (basic.paper_entry_status != "05") continue;
                    //借出單未包含最新的紙本，修改時不檢查 20240327 Eric
                    if (sendingType != SendingType.lend_lend && !data.details.Select(x => x.paper_code).Contains(basic.paper_code))
                        throw new Exception(ActionFilter.GetMultilingualValue("custom:messageContent:ApplicationChanged"));
                }
            }

            //檢查紙本
            List<string> errorList = new List<string>();
            if (data.details != null && data.details.Count > 0)
            {
                foreach (var detail in data.details)
                {
                    if (detail.paper_basic_id == null || detail.paper_basic_id == 0)
                        throw new Exception(ActionFilter.GetMultilingualValue("custom:messageTitle:systemError"));

                    paper_basic_data basic = _basicRepository.GetPaperBasicDataByBasicID(detail.paper_basic_id);
                    if (basic == null)
                        throw new Exception(ActionFilter.GetMultilingualValue("custom:messageContent:dataChurn") + ": " + detail.paper_code);

                    //紙本非"已入庫"
                    if (sendingType == SendingType.new_lend || sendingType == SendingType.temp_lend)
                    {
                        if (basic.paper_entry_status != "05") errorList.Add(basic.paper_code);
                    }
                    if (sendingType == SendingType.lend_lend)
                    {
                        //新增的細項才需檢查
                        if (detail.detail_id == null || detail.detail_id == 0)
                        {
                            if (basic.paper_entry_status != "05") errorList.Add(basic.paper_code);
                        }
                    }
                }

                if (errorList.Count > 0)
                    throw new Exception(ActionFilter.GetMultilingualValue("custom:messageContent:LendingError") + string.Join(", ", errorList));
            }

            if (data.lend_id == null || data.lend_id == 0)
            {
                return InsertPaperLendingApplication(data);
            }
            else
            {
                return UpdatePaperLendingApplication(data);
            }
        }
        #endregion

        #region 新增數據
        /// <summary>
        /// 新增數據
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        private static PaperLendingResult InsertPaperLendingApplication(insPaperLendingApplication data)
        {
            paper_lending_application main = new paper_lending_application();

            #region 獲取參數
            string? fill_bu = _repository.GetUserBuBg(0, data.lend_fill_emplid);
            string? fill_bg = _repository.GetUserBuBg(1, data.lend_fill_emplid);
            string? handler_bu = _repository.GetUserBuBg(0, data.lend_handler_emplid);
            string? handler_bg = _repository.GetUserBuBg(1, data.lend_handler_emplid);
            #endregion

            //事務處理
            DbAccess.PerformInTransaction(context =>
            {
                #region 申請單號
                string lend_number = string.Empty;
                //將申請單定義為暫存單 -> 使用登陸者工號
                if (data.lend_status == "01")
                {
                    lend_number = MvcContext.UserInfo.current_emp;
                    main.temp_lend_number = lend_number;
                }
                else
                {
                    lend_number = SysApplicationSequenceService.GetApplicationNumberToTransaction("lend_number", "L".ToUpper(), context);
                    main.lend_number = lend_number;
                }
                #endregion

                #region 定義相關資訊
                main.lend_fill_emplid = data.lend_fill_emplid;
                main.lend_fill_deptid = data.lend_fill_deptid;
                main.lend_handler_emplid = data.lend_handler_emplid;
                main.lend_handler_deptid = data.lend_handler_deptid;
                main.lend_fill_bu = string.IsNullOrEmpty(fill_bu) ? data.lend_fill_deptid.Substring(0, 2) : fill_bu;
                main.lend_fill_bg = string.IsNullOrEmpty(fill_bg) ? data.lend_fill_deptid.Substring(2, 2) : fill_bg;
                main.lend_handler_bu = string.IsNullOrEmpty(handler_bu) ? data.lend_handler_deptid.Substring(0, 2) : handler_bu;
                main.lend_handler_bg = string.IsNullOrEmpty(handler_bg) ? data.lend_handler_deptid.Substring(2, 2) : handler_bg;
                main.lend_status = data.lend_status;
                main.void_reason = data.void_reason;
                main.create_user = MvcContext.UserInfo.current_emp;
                main.create_time = DateTime.UtcNow;
                main.application_time = DateTime.UtcNow;
                #endregion

                #region 新增主表資訊
                if (!_repository.InsertPaperLendingApplicationToTransaction(main, context))
                    throw new Exception(ActionFilter.GetMultilingualValue("custom:messageTitle:createFail"));
                //如為暫存單，需撈最新一筆資料(系統中可能有其他筆暫存單(有 / 無其他申請單號))
                if (data.lend_status == "01")
                {
                    main = _repository.GetNewTempPaperLendingApplicationToTransaction(lend_number, "INSERT", context);
                }
                else
                {
                    main = _repository.GetPaperLendingApplicationToTransaction(lend_number, context);
                }
                if (main == null || main.lend_id == null || main.lend_id == 0)
                    throw new Exception(ActionFilter.GetMultilingualValue("custom:messageTitle:createFail"));
                #endregion

                #region 定義需求資訊
                paper_lending_demand demand = new paper_lending_demand();
                demand.paper_lend_id = main.lend_id;
                demand.retrieve_number = data.retrieve_number;
                demand.borrow_days = data.borrow_days;
                demand.demand_reason = data.demand_reason;
                demand.retrieve_reason = data.retrieve_reason;
                demand.create_user = MvcContext.UserInfo.current_emp;
                demand.create_time = DateTime.UtcNow;

                if (!_repository.InsertPaperLendingDemandToTransaction(demand, context))
                    throw new Exception(ActionFilter.GetMultilingualValue("custom:messageTitle:createFail"));
                demand = _repository.GetPaperLendingDemandToTransaction(main.lend_id, context);
                if (demand == null || demand.demand_id == null || demand.demand_id == 0)
                    throw new Exception(ActionFilter.GetMultilingualValue("custom:messageTitle:createFail"));
                #endregion

                #region 借出明細清單
                if (data.details != null && data.details.Count() > 0)
                {
                    foreach (var item in data.details)
                    {
                        PaperBasicApplicationViewModel basic = _basicRepository.GetPaperBasicDataByBasicIDToTransaction(item.paper_basic_id.ToString(), context);
                        if (basic == null) throw new Exception(ActionFilter.GetMultilingualValue("custom:messageContent:dataChurn"));

                        paper_lending_detail detail = new paper_lending_detail();
                        detail.paper_lend_id = main.lend_id;
                        detail.paper_demand_id = demand.demand_id;
                        detail.paper_basic_id = item.paper_basic_id;
                        detail.contract_number = basic.contract_number;
                        detail.contract_name = basic.contract_name;
                        detail.paper_code = basic.paper_code;
                        detail.paper_name = basic.paper_name;
                        detail.is_return = 0;
                        detail.should_return_time = item.should_return_time;
                        detail.actual_return_time = item.actual_return_time;
                        detail.receive_status = item.receive_status;
                        detail.pickup_status = item.pickup_status;
                        detail.pickup_time = item.pickup_time;
                        detail.consignment_number = item.consignment_number;
                        detail.pickup_emplid = item.pickup_emplid;
                        detail.actual_pickup_time = item.actual_pickup_time;
                        detail.overdue_day = item.overdue_day;
                        detail.actual_borrow_days = item.actual_borrow_days;
                        detail.loan_due_date = item.loan_due_date;
                        detail.create_user = MvcContext.UserInfo.current_emp;
                        detail.create_time = DateTime.UtcNow;
                        detail.is_pickup_lend = null;

                        if (!_repository.InsertPaperLendingDetailToTransaction(detail, context))
                            throw new Exception(ActionFilter.GetMultilingualValue("custom:messageTitle:createFail"));

                        //送件時才需要更改紙本狀態與新增紙本歷程
                        if (data.lend_status == "02")
                        {
                            if (!_basicRepository.UpdatePaperBasicDataStatusToTransaction(detail.paper_basic_id, "01", context))
                                throw new Exception(ActionFilter.GetMultilingualValue("custom:messageTitle:createFail"));

                            paper_history_data history = new paper_history_data();
                            history.paper_basic_id = detail.paper_basic_id;
                            history.paper_entry_status = "01";
                            history.course_emplid = data.lend_handler_emplid;
                            history.paper_remarks = data.demand_reason + " " + data.retrieve_reason;
                            history.borrow_applynumber = lend_number;
                            history.create_user = MvcContext.UserInfo.current_emp;
                            history.create_time = DateTime.UtcNow;

                            if (!_basicRepository.InsertPaperHistoryDataToTransaction(history, context))
                                throw new Exception(ActionFilter.GetMultilingualValue("custom:messageTitle:createFail"));
                        }
                    }
                }
                #endregion

                //插入未借出紙本明細
                InsertPaperUnlendingPaper(main.lend_id, demand.demand_id, main.lend_number, data.unlend_details, context);
            });

            if (data.lend_status == "02") SendLendingNotificationMail(main.lend_id);

            PaperLendingResult res = new PaperLendingResult();
            res.lend_id = main.lend_id;
            res.lend_number = main.lend_number;

            return res;
        }
        #endregion

        #region 修改數據
        /// <summary>
        /// 修改數據
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        private static PaperLendingResult UpdatePaperLendingApplication(insPaperLendingApplication data)
        {
            paper_lending_application old_application = _repository.GetPaperLendingApplicationByLendID(data.lend_id.Value);
            if (old_application == null)
                throw new Exception(ActionFilter.GetMultilingualValue("custom:messageTitle:dataNotexist"));

            paper_lending_application main = new paper_lending_application();
            //0:暫存單 1:送件單 2:原本為送件單，則維持原本的單號
            int lend_flag = 0;

            #region 獲取參數
            string? fill_bu = _repository.GetUserBuBg(0, data.lend_fill_emplid);
            string? fill_bg = _repository.GetUserBuBg(1, data.lend_fill_emplid);
            string? handler_bu = _repository.GetUserBuBg(0, data.lend_handler_emplid);
            string? handler_bg = _repository.GetUserBuBg(1, data.lend_handler_emplid);
            #endregion

            //事務處理
            DbAccess.PerformInTransaction(context =>
            {
                #region 申請單號
                //將申請單定義為暫存單 -> 使用登陸者工號
                string lend_number = MvcContext.UserInfo.current_emp;
                //暫存單的單號為使用者的工號
                if (data.lend_status == "01")
                {
                    main.temp_lend_number = lend_number;
                }
                else
                {
                    //原本為暫存單，送件時才產生單號
                    if (old_application.lend_status == "01")
                    {
                        lend_number = SysApplicationSequenceService.GetApplicationNumberToTransaction("lend_number", "L".ToUpper(), context);
                        _repository.UpdateLendNumberToTransaction(data.lend_id.Value, lend_number, context);
                        lend_flag = 1;
                    }
                    else
                    {
                        //原本為送件單，則維持原本的單號
                        lend_number = old_application.lend_number;
                        main.lend_number = lend_number;
                        lend_flag = 2;
                    }
                }
                #endregion

                #region 定義相關資訊
                main.lend_id = data.lend_id.Value;
                main.lend_fill_emplid = data.lend_fill_emplid;
                main.lend_fill_deptid = data.lend_fill_deptid;
                main.lend_handler_emplid = data.lend_handler_emplid;
                main.lend_handler_deptid = data.lend_handler_deptid;
                main.lend_fill_bu = string.IsNullOrEmpty(fill_bu) ? data.lend_fill_deptid.Substring(0, 2) : fill_bu;
                main.lend_fill_bg = string.IsNullOrEmpty(fill_bg) ? data.lend_fill_deptid.Substring(2, 2) : fill_bg;
                main.lend_handler_bu = string.IsNullOrEmpty(handler_bu) ? data.lend_handler_deptid.Substring(0, 2) : handler_bu;
                main.lend_handler_bg = string.IsNullOrEmpty(handler_bg) ? data.lend_handler_deptid.Substring(2, 2) : handler_bg;
                main.lend_status = data.lend_status;
                main.void_reason = data.void_reason;
                main.modify_user = MvcContext.UserInfo.current_emp;
                main.modify_time = DateTime.UtcNow;
                #endregion

                #region 處理主表資訊
                if (!_repository.UpdatePaperLendingApplicationToTransaction(main, context))
                    throw new Exception(ActionFilter.GetMultilingualValue("custom:messageTitle:updateFail"));

                //暫存單改為送件單時，需更新申請日期時間
                if (lend_flag == 1) _repository.UpdatePaperLendingApplicationTimeToTransaction(main.lend_id, context);

                if (data.lend_status == "01")
                {
                    //依照modift_time撈最新一筆
                    main = _repository.GetNewTempPaperLendingApplicationToTransaction(lend_number, "UPDATE", context);
                }
                else
                {
                    //依照借出單號
                    main = _repository.GetPaperLendingApplicationToTransaction(lend_number, context);
                }
                if (main == null || main.lend_id == null || main.lend_id == 0)
                    throw new Exception(ActionFilter.GetMultilingualValue("custom:messageTitle:updateFail"));
                #endregion

                #region 處理需求資訊
                paper_lending_demand demand = new paper_lending_demand();
                if (data.demand_id == null || data.demand_id == 0)
                {
                    demand.paper_lend_id = main.lend_id;
                    demand.retrieve_number = data.retrieve_number;
                    demand.borrow_days = data.borrow_days;
                    demand.demand_reason = data.demand_reason;
                    demand.retrieve_reason = data.retrieve_reason;
                    demand.create_user = MvcContext.UserInfo.current_emp;
                    demand.create_time = DateTime.UtcNow;

                    if (!_repository.InsertPaperLendingDemandToTransaction(demand, context))
                        throw new Exception(ActionFilter.GetMultilingualValue("custom:messageTitle:updateFail"));
                }
                else
                {
                    demand.paper_lend_id = main.lend_id;
                    demand.demand_id = data.demand_id.Value;
                    demand.retrieve_number = data.retrieve_number;
                    demand.borrow_days = data.borrow_days;
                    demand.demand_reason = data.demand_reason;
                    demand.retrieve_reason = data.retrieve_reason;
                    demand.modify_user = MvcContext.UserInfo.current_emp;
                    demand.modify_time = DateTime.UtcNow;

                    if (!_repository.UpdatePaperLendingDemandToTransaction(demand, context))
                        throw new Exception(ActionFilter.GetMultilingualValue("custom:messageTitle:updateFail"));
                }
                demand = _repository.GetPaperLendingDemandToTransaction(main.lend_id, context);
                if (demand == null || demand.demand_id == null || demand.demand_id == 0)
                    throw new Exception(ActionFilter.GetMultilingualValue("custom:messageTitle:updateFail"));
                #endregion

                #region 借出明細
                //借出明細先delete再insert
                List<paper_lending_detail> detail_List = _repository.GetPaperLendingDetailToTransaction(main.lend_id, context);
                if (detail_List != null && detail_List.Count > 0)
                {
                    foreach (paper_lending_detail oldDetail in detail_List)
                    {
                        if (data.details != null && data.details.Count != 0 && data.details.Count(x => x.detail_id == oldDetail.detail_id) == 0)
                        {
                            if (main.lend_status == "02")
                            {
                                //將舊明細紙本改為"已入庫"
                                if (!_basicRepository.UpdatePaperBasicDataStatusToTransaction(oldDetail.paper_basic_id, "05", context))
                                    throw new Exception(ActionFilter.GetMultilingualValue("custom:messageTitle:updateFail"));
                            }
                            //刪除舊明細
                            if (!_repository.DeletePaperLendingDetailToTransaction(oldDetail.paper_lend_id, oldDetail.paper_demand_id, oldDetail.detail_id, context))
                                throw new Exception(ActionFilter.GetMultilingualValue("custom:messageTitle:updateFail"));
                        }
                    }
                }
                #endregion

                #region 插入紙本歷程
                if (data.details != null && data.details.Count() > 0)
                {
                    foreach (var item in data.details)
                    {
                        paper_lending_detail? old = null;
                        if (item.detail_id != null && item.detail_id != 0)
                        {
                            old = _repository.GetPaperLendingDetailToTransaction(main.lend_id, demand.demand_id, item.detail_id.Value, context);
                        }

                        PaperBasicApplicationViewModel basic = _basicRepository.GetPaperBasicDataByBasicIDToTransaction(item.paper_basic_id.ToString(), context);
                        if (basic == null) throw new Exception(ActionFilter.GetMultilingualValue("custom:messageContent:dataChurn"));

                        paper_lending_detail detail = new paper_lending_detail();
                        if (old == null)
                        {
                            detail.paper_lend_id = main.lend_id;
                            detail.paper_demand_id = demand.demand_id;
                            detail.paper_basic_id = item.paper_basic_id;
                            detail.contract_number = basic.contract_number;
                            detail.contract_name = basic.contract_name;
                            detail.paper_code = basic.paper_code;
                            detail.paper_name = basic.paper_name;
                            detail.is_return = item.is_return;
                            detail.should_return_time = item.should_return_time;
                            detail.actual_return_time = item.actual_return_time;
                            detail.receive_status = item.receive_status;
                            detail.pickup_status = item.pickup_status;
                            detail.pickup_time = item.pickup_time;
                            detail.consignment_number = item.consignment_number;
                            detail.pickup_emplid = item.pickup_emplid;
                            detail.actual_pickup_time = item.actual_pickup_time;
                            detail.overdue_day = item.overdue_day;
                            detail.actual_borrow_days = item.actual_borrow_days;
                            detail.loan_due_date = item.loan_due_date;
                            detail.create_user = MvcContext.UserInfo.current_emp;
                            detail.create_time = DateTime.UtcNow;
                            detail.is_pickup_lend = null;

                            if (!_repository.InsertPaperLendingDetailToTransaction(detail, context)) throw new Exception(ActionFilter.GetMultilingualValue("custom:messageTitle:updateFail"));
                        }
                        else
                        {
                            detail.detail_id = item.detail_id.Value;
                            detail.paper_lend_id = main.lend_id;
                            detail.paper_demand_id = demand.demand_id;
                            detail.paper_basic_id = item.paper_basic_id;
                            detail.contract_number = basic.contract_number;
                            detail.contract_name = basic.contract_name;
                            detail.paper_code = basic.paper_code;
                            detail.paper_name = basic.paper_name;
                            detail.is_return = item.is_return;
                            detail.should_return_time = item.should_return_time;
                            detail.actual_return_time = item.actual_return_time;
                            detail.receive_status = item.receive_status;
                            detail.pickup_status = item.pickup_status;
                            detail.pickup_time = item.pickup_time;
                            detail.consignment_number = item.consignment_number;
                            detail.pickup_emplid = item.pickup_emplid;
                            detail.actual_pickup_time = item.actual_pickup_time;
                            detail.overdue_day = item.overdue_day;
                            detail.actual_borrow_days = item.actual_borrow_days;
                            detail.loan_due_date = item.loan_due_date;
                            detail.modify_user = MvcContext.UserInfo.current_emp;
                            detail.modify_time = DateTime.UtcNow;
                            detail.is_pickup_lend = null;

                            if (!_repository.UpdatePaperLendingDetailToTransaction(detail, context))
                                throw new Exception(ActionFilter.GetMultilingualValue("custom:messageTitle:updateFail"));
                        }

                        //送件時才需要更改紙本狀態與新增紙本歷程
                        if (data.lend_status == "02")
                        {
                            if (!_basicRepository.UpdatePaperBasicDataStatusToTransaction(detail.paper_basic_id, "01", context))
                                throw new Exception(ActionFilter.GetMultilingualValue("custom:messageTitle:updateFail"));

                            paper_history_data history = new paper_history_data();
                            history.paper_basic_id = detail.paper_basic_id;
                            history.paper_entry_status = "01";
                            history.course_emplid = data.lend_handler_emplid;
                            history.paper_remarks = data.demand_reason + " " + data.retrieve_reason;
                            history.borrow_applynumber = lend_number;
                            history.create_user = MvcContext.UserInfo.current_emp;
                            history.create_time = DateTime.UtcNow;

                            if (!_basicRepository.InsertPaperHistoryDataToTransaction(history, context))
                                throw new Exception(ActionFilter.GetMultilingualValue("custom:messageTitle:updateFail"));
                        }
                    }
                }
                #endregion

                InsertPaperUnlendingPaper(main.lend_id, demand.demand_id, main.lend_number, data.unlend_details, context);
            });

            //暫存單改借出單
            if (lend_flag == 1)
            {
                SendLendingNotificationMail(main.lend_id);
            }
            //修改借出單
            if (lend_flag == 2)
            {
                SendModifyNotification(main.lend_id);
            }

            PaperLendingResult res = new PaperLendingResult();
            res.lend_id = main.lend_id;
            res.lend_number = main.lend_number;

            return res;
        }
        #endregion

        #region 更新未出借明細
        /// <summary>
        /// 更新未出借明細
        /// </summary>
        /// <param name="lend_id"></param>
        /// <param name="demand_id"></param>
        /// <param name="lend_number"></param>
        /// <param name="un_details"></param>
        /// <param name="context"></param>
        /// <exception cref="Exception"></exception>
        private static void InsertPaperUnlendingPaper(int lend_id, int demand_id, string lend_number, List<insPaperLendingDetail>? un_details, IDbContext context)
        {
            _repository.DeletePaperUnLendingDetailToTransaction(lend_id, context);

            if (un_details == null || un_details.Count() == 0) return;

            foreach (var item in un_details)
            {
                PaperBasicApplicationViewModel basic = _basicRepository.GetPaperBasicDataByBasicIDToTransaction(item.paper_basic_id.ToString(), context);
                if (basic == null) throw new Exception(ActionFilter.GetMultilingualValue("custom:messageContent:dataChurn"));

                paper_unlending_detail detail = new paper_unlending_detail();
                detail.paper_lend_id = lend_id;
                detail.paper_demand_id = demand_id;
                detail.paper_basic_id = item.paper_basic_id;
                detail.contract_number = basic.contract_number;
                detail.contract_name = basic.contract_name;
                detail.paper_code = basic.paper_code;
                detail.paper_name = basic.paper_name;
                detail.paper_entry_status = basic.paper_entry_status;
                detail.lend_number = lend_number;
                detail.create_user = MvcContext.UserInfo.current_emp;
                detail.create_time = DateTime.UtcNow;

                _repository.InsertPaperUnlendingDetailToTransaction(detail, context);
            }
        }
        #endregion

        #endregion

        #region 顯示填單人資訊
        /// <summary>
        /// 顯示填單人資訊
        /// </summary>
        /// <param name="lend_id"></param>
        /// <returns></returns>
        public static LendFillEmpInfoViewMode GetLendFillEmpInfo(int lend_id)
        {
            return _repository.GetLendFillEmpInfo(lend_id);
        }
        #endregion

        #region 顯示借出明細
        /// <summary>
        /// 顯示借出明細
        /// </summary>
        /// <param name="lend_id"></param>
        /// <returns></returns>
        public static List<LendingPaperInfo> GetLendingPaperInfo(int lend_id)
        {
            return _repository.GetLendingPaperInfo(lend_id);
        }
        #endregion

        #region 舊卡號判斷
        public static UserEntityModel GetUserByBadgeId(int lend_id, string? retrieve_number, string? badge_id, string? empid)
        {
            if (!string.IsNullOrEmpty(badge_id))
            {
                try
                {
                    // 取出中間的部分
                    string middleBadgeId = badge_id.Substring(3, 7);

                    // 將中間部分轉換為10進制
                    int converted_badge_id = Convert.ToInt32(middleBadgeId, 16);

                    // 將轉換後的數字補0到10碼
                    string formattedBadgeId = converted_badge_id.ToString("D10");

                    // 使用新的邏輯查找 empid
                    empid = _repository.GetUserByBadgeId(formattedBadgeId);
                    if (string.IsNullOrEmpty(empid))
                    {
                        // 如果未找到，使用舊邏輯再撈一次
                        int oldConvertedBadgeId = Convert.ToInt32(badge_id, 16);
                        empid = _repository.GetUserByBadgeId(oldConvertedBadgeId.ToString());
                        if (string.IsNullOrEmpty(empid))
                        {
                            empid = _repository.GetUserByBadgeId(oldConvertedBadgeId.ToString("D9"));
                        }
                    }
                }
                catch
                {
                    throw new Exception(ActionFilter.GetMultilingualValue("custom:messageContent:authLoginError_EmplidMissed"));
                }
            }

            if (string.IsNullOrEmpty(empid)) throw new Exception(ActionFilter.GetMultilingualValue("custom:messageContent:authLoginError_EmplidMissed"));

            UserEntityModel res = UserInfoService.GetUserList("", empid).FirstOrDefault();

            if (res == null) throw new Exception(ActionFilter.GetMultilingualValue("custom:messageContent:authLoginError_EmplidMissed"));

            //取得其他申請單號
            paper_lending_demand demand = _repository.GetPaperLendingDemand(lend_id);
            if (demand == null) throw new Exception(ActionFilter.GetMultilingualValue("custom:messageContent:dataChurn"));

            retrieve_number = demand.retrieve_number;

            if (!string.IsNullOrEmpty(retrieve_number))
            {
                if (!_repository.IsFillOrhandlerEmp(lend_id, empid) && !_repository.IsRetrieveEmp(retrieve_number, empid))
                    return null;
            }
            else
            {
                if (!_repository.IsFillOrhandlerEmp(lend_id, empid))
                    return null;
            }

            return res;
        }
        #endregion

        #region 依照卡號撈借出人
        /// <summary>
        /// 依照卡號撈借出人
        /// </summary>
        /// <param name="lend_id"></param>
        /// <param name="retrieve_number"></param>
        /// <param name="badge_id"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public static UserEntityModel_Badge GetUserByBadgeId_new(int lend_id, string? retrieve_number, string badge_id)
        {
            if (string.IsNullOrEmpty(badge_id)) throw new Exception(ActionFilter.GetMultilingualValue("custom:messageContent:dataChurn"));

            string empid = string.Empty;
            UserEntityModel_Badge res = new UserEntityModel_Badge();
            try
            {
                //先將badge_id視為工號查找
                PsSubEeLglVwA ee = PsSubEeLglVwADataService.FindByKey(badge_id);
                if (ee != null)
                {
                    empid = ee.Emplid;
                    res.logging_type = 0;
                    goto matching;
                }

                // 使用新的邏輯查找 empid
                // 卡號紀錄在employeeinfo_jobs_g1的badge_id欄位
                // 針對新卡機
                empid = _repository.GetUserByBadgeId(badge_id);
                if (!string.IsNullOrEmpty(empid))
                {
                    res.logging_type = 1;
                    goto matching;
                }

                #region 舊卡機(掃出來為16進位)
                // 取出中間的部分
                string middleBadgeId = badge_id.Substring(3, 7);

                // 將中間部分轉換為10進制
                int converted_badge_id = Convert.ToInt32(middleBadgeId, 16);

                // 將轉換後的數字補0到10碼
                string formattedBadgeId = converted_badge_id.ToString("D10");

                // 使用新的邏輯查找 empid
                empid = _repository.GetUserByBadgeId(formattedBadgeId);
                if (string.IsNullOrEmpty(empid))
                {
                    // 如果未找到，使用舊邏輯再撈一次
                    int oldConvertedBadgeId = Convert.ToInt32(badge_id, 16);
                    empid = _repository.GetUserByBadgeId(oldConvertedBadgeId.ToString());
                    if (string.IsNullOrEmpty(empid))
                    {
                        empid = _repository.GetUserByBadgeId(oldConvertedBadgeId.ToString("D9"));
                    }
                }
                res.logging_type = 1;
                #endregion
            }
            catch
            {
                throw new Exception(ActionFilter.GetMultilingualValue("custom:messageContent:authLoginError_EmplidMissed"));
            }

            if (string.IsNullOrEmpty(empid)) throw new Exception(ActionFilter.GetMultilingualValue("custom:messageContent:authLoginError_EmplidMissed"));

            matching:

            UserEntityModel empinfo = UserInfoService.GetUserList("", empid).FirstOrDefault();
            if (empinfo == null) throw new Exception(ActionFilter.GetMultilingualValue("custom:messageContent:authLoginError_EmplidMissed"));

            //檢查人員是否離職
            if (empinfo.IsTermination) throw new Exception(ActionFilter.GetMultilingualValue("custom:messageContent:authLoginError_EmplidTermination"));

            //取得其他申請單號
            paper_lending_demand demand = _repository.GetPaperLendingDemand(lend_id);
            if (demand == null) throw new Exception(ActionFilter.GetMultilingualValue("custom:messageContent:dataChurn"));

            retrieve_number = demand.retrieve_number;

            if (!string.IsNullOrEmpty(retrieve_number))
            {
                //有其他申請單號
                //是否為經辦人/填單人或內部同仁
                if (!_repository.IsFillOrhandlerEmp(lend_id, empid) && !_repository.IsRetrieveEmp(retrieve_number, empid))
                    return null;
            }
            else
            {
                //無其他申請單號
                //是否為經辦人/填單人
                if (!_repository.IsFillOrhandlerEmp(lend_id, empid))
                    return null;
            }

            res.Deptid = empinfo.Deptid;
            res.Emplid = empinfo.Emplid;
            res.Name = empinfo.Name;
            res.NameA = empinfo.NameA;
            res.Company = empinfo.Company;
            res.CompanyShort = empinfo.CompanyShort;
            res.Descrshort = empinfo.Descrshort;
            res.EmailAddress = empinfo.EmailAddress;
            res.PrefixDialCodeA = empinfo.PrefixDialCodeA;
            res.PhoneA = empinfo.PhoneA;
            res.Bg = empinfo.Bg;
            res.Bu = empinfo.Bu;
            res.Termination = empinfo.Termination;

            return res;
        }
        #endregion

        #region 取件(事務處理)
        /// <summary>
        /// 取件
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public static bool PickLending(pickLendingPara data)
        {
            paper_lending_application old = _repository.GetPaperLendingApplicationByLendID(data.lend_id);
            if (old == null)
                throw new Exception(ActionFilter.GetMultilingualValue("custom:messageContent:dataChurn"));
            if (old.lend_status != "02")
                throw new Exception(ActionFilter.GetMultilingualValue("custom:messageContent:dataChurn"));

            List<paper_lending_detail> detail_list = _repository.GetPaperLendingDetail(data.lend_id);
            if (detail_list == null || detail_list.Count == 0)
                throw new Exception(ActionFilter.GetMultilingualValue("custom:messageTitle:fileNotexist"));

            bool result = false;
            DbAccess.PerformInTransaction(context =>
            {
                foreach (paper_lending_detail detail in detail_list)
                {
                    //勾選取件的
                    if (data.detail_id_List.Contains(detail.detail_id))
                    {
                        detail.pickup_status = data.pickup_status;
                        detail.pickup_time = data.pickup_time;
                        detail.should_return_time = data.should_return_time;
                        detail.actual_pickup_time = data.actual_pickup_time;
                        detail.consignment_number = data.consignment_number;
                        detail.pickup_emplid = data.pickup_emplid;
                        detail.loan_due_date = data.loan_due_date;
                        detail.modify_user = MvcContext.UserInfo.current_emp;
                        detail.modify_time = DateTime.UtcNow;
                        detail.is_pickup_lend = 1;

                        if (!_repository.UpdatePaperLendingDetailToTransaction(detail, context))
                            throw new Exception(ActionFilter.GetMultilingualValue("custom:messageTitle:updateFail"));

                        //修改為借出中
                        if (!_basicRepository.UpdatePaperBasicDataStatusToTransaction(detail.paper_basic_id, "03", context))
                            throw new Exception(ActionFilter.GetMultilingualValue("custom:messageTitle:updateFail"));

                        paper_history_data history = new paper_history_data();
                        history.paper_basic_id = detail.paper_basic_id;
                        history.paper_entry_status = "03";
                        history.borrow_applynumber = old.lend_number;
                        if (string.IsNullOrEmpty(data.pickup_emplid))
                        {
                            history.course_emplid = MvcContext.UserInfo.current_emp;
                        }
                        else
                        {
                            history.course_emplid = data.pickup_emplid;
                        }
                        history.create_user = MvcContext.UserInfo.current_emp;
                        history.create_time = DateTime.UtcNow;

                        if (!_basicRepository.InsertPaperHistoryDataToTransaction(history, context))
                            throw new Exception(ActionFilter.GetMultilingualValue("custom:messageTitle:createFail"));
                    }
                    //無勾選取件的
                    else
                    {
                        detail.modify_user = MvcContext.UserInfo.current_emp;
                        detail.modify_time = DateTime.UtcNow;
                        detail.is_pickup_lend = 0;

                        //修改為待入庫
                        if (!_basicRepository.UpdatePaperBasicDataStatusToTransaction(detail.paper_basic_id, "04", context))
                            throw new Exception(ActionFilter.GetMultilingualValue("custom:messageTitle:updateFail"));

                        paper_history_data history = new paper_history_data();
                        history.paper_basic_id = detail.paper_basic_id;
                        history.paper_entry_status = "04";
                        history.borrow_applynumber = old.lend_number;
                        if (string.IsNullOrEmpty(data.pickup_emplid))
                        {
                            history.course_emplid = MvcContext.UserInfo.current_emp;
                        }
                        else
                        {
                            history.course_emplid = data.pickup_emplid;
                        }
                        history.create_user = MvcContext.UserInfo.current_emp;
                        history.create_time = DateTime.UtcNow;

                        if (!_basicRepository.InsertPaperHistoryDataToTransaction(history, context))
                            throw new Exception(ActionFilter.GetMultilingualValue("custom:messageTitle:createFail"));
                    }
                }

                //修改申請單為出借中
                if (!_repository.UpdateLendStatusToTransaction(data.lend_id, "03", context))
                    throw new Exception(ActionFilter.GetMultilingualValue("custom:messageTitle:updateFail"));

                result = true;
            });

            return result;
        }
        #endregion

        #region 刪除借出單(事務處理)
        /// <summary>
        /// 刪除借出單
        /// </summary>
        /// <param name="lend_id"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public static bool DeleteLending(int lend_id)
        {
            paper_lending_application data = _repository.GetPaperLendingApplicationByLendID(lend_id);
            if (data == null) throw new Exception(ActionFilter.GetMultilingualValue("custom:messageContent:dataChurn"));

            if (data.lend_status != "01")
                throw new Exception(ActionFilter.GetMultilingualValue("custom:messageTitle:doNotDelete"));

            DbAccess.PerformInTransaction(context =>
            {
                _repository.DeletePaperLendingApplicationToTransaction(lend_id, context);
                _repository.DeletePaperLendingDemandToTransaction(lend_id, context);
                _repository.DeletePaperLendingDetailToTransaction(lend_id, context);
            });

            return true;
        }
        #endregion

        #region 作廢申請單(事務處理)
        /// <summary>
        /// 作廢申請單
        /// </summary>
        /// <param name="lend_id"></param>
        /// <param name="void_reason"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public static bool VoidLending(int lend_id, string void_reason)
        {
            paper_lending_application data = _repository.GetPaperLendingApplicationByLendID(lend_id);
            if (data == null) throw new Exception(ActionFilter.GetMultilingualValue("custom:messageContent:dataChurn"));

            //暫存單不可作廢
            if (data.lend_status == "01")
                throw new Exception(ActionFilter.GetMultilingualValue("custom:messageTitle:doNotUpdate"));
            //除了待取件的單號，其他狀態不可作廢
            if (data.lend_status != "02")
                throw new Exception(ActionFilter.GetMultilingualValue("custom:messageContent:dataChurn"));

            paper_lending_demand deamnd = _repository.GetPaperLendingDemand(lend_id);
            if (deamnd == null)
                throw new Exception(ActionFilter.GetMultilingualValue("custom:messageContent:dataChurn"));

            List<paper_lending_detail> details = _repository.GetPaperLendingDetail(lend_id);
            if (details == null || details.Count == 0)
                throw new Exception(ActionFilter.GetMultilingualValue("custom:messageContent:dataChurn"));

            string lend_fill_bu = _repository.GetUserBuBg(0, data.lend_fill_emplid);
            string lend_fill_bg = _repository.GetUserBuBg(1, data.lend_fill_emplid);
            string lend_handler_bu = _repository.GetUserBuBg(0, data.lend_handler_emplid);
            string lend_handler_bg = _repository.GetUserBuBg(1, data.lend_handler_emplid);

            DbAccess.PerformInTransaction(context =>
            {
                //修改主表為作廢
                _repository.VoidLendingToTransaction(lend_id, void_reason, context);

                foreach (paper_lending_detail detail in details)
                {
                    //紙本狀態改為待入庫
                    if (!_basicRepository.UpdatePaperBasicDataStatusToTransaction(detail.paper_basic_id, "05", context))
                        throw new Exception(ActionFilter.GetMultilingualValue("custom:messageContent:updateFail"));

                    paper_history_data data_history = new paper_history_data();
                    data_history.paper_basic_id = detail.paper_basic_id;
                    data_history.paper_entry_status = "05";
                    data_history.course_emplid = MvcContext.UserInfo.current_emp;
                    data_history.paper_remarks = void_reason;
                    data_history.borrow_applynumber = data.lend_number;
                    data_history.create_user = MvcContext.UserInfo.current_emp;
                    data_history.create_time = DateTime.UtcNow;

                    if (!_basicRepository.InsertPaperHistoryDataToTransaction(data_history, context))
                        throw new Exception(ActionFilter.GetMultilingualValue("custom:messageContent:createFail"));

                    #region 新增借出歷史明細(已結案和已作廢)
                    paper_lending_history history = new paper_lending_history();
                    history.lend_number = data.lend_number;
                    history.lend_fill_emplid = data.lend_fill_emplid;
                    history.lend_fill_deptid = data.lend_fill_deptid;
                    history.lend_fill_bu = lend_fill_bu;
                    history.lend_fill_bg = lend_fill_bg;
                    history.lend_handler_emplid = data.lend_handler_emplid;
                    history.lend_handler_deptid = data.lend_handler_deptid;
                    history.lend_handler_bu = lend_handler_bu;
                    history.lend_handler_bg = lend_handler_bg;
                    history.application_time = data.application_time;
                    history.lend_status = "05";
                    history.void_reason = void_reason;
                    history.retrieve_number = deamnd.retrieve_number;
                    history.borrow_days = deamnd.borrow_days;
                    history.demand_reason = deamnd.demand_reason;
                    history.retrieve_reason = deamnd.retrieve_reason;
                    history.paper_basic_id = detail.paper_basic_id;
                    history.paper_code = detail.paper_code;
                    history.paper_name = detail.paper_name;
                    history.contract_number = detail.contract_number;
                    history.contract_name = detail.contract_name;
                    history.is_return = detail.is_return;
                    history.should_return_time = detail.should_return_time;
                    history.actual_return_time = detail.actual_return_time;
                    history.pickup_status = detail.pickup_status;
                    history.pickup_time = detail.pickup_time;
                    history.consignment_number = detail.consignment_number;
                    history.pickup_emplid = detail.pickup_emplid;
                    history.actual_pickup_time = detail.actual_pickup_time;
                    history.overdue_day = detail.overdue_day;
                    history.actual_borrow_days = detail.actual_borrow_days;
                    history.loan_due_date = detail.loan_due_date;
                    history.create_user = MvcContext.UserInfo.current_emp;
                    history.create_time = DateTime.UtcNow;
                    history.is_pickup_lend = detail.is_pickup_lend;
                    history.lend_return_status = detail.lend_return_status;
                    if (!_repository.InsertPaperLendingHistoryToTransaction(history, context))
                        throw new Exception(ActionFilter.GetMultilingualValue("custom:messageContent:createFail"));
                    #endregion
                }
            });

            //作廢郵件
            SendVoidLendingMail(lend_id);

            return true;
        }
        #endregion

        #region 取件紙本資料(記log用)
        /// <summary>
        /// 取件紙本資料(記log用)
        /// </summary>
        /// <param name="lend_id"></param>
        /// <param name="detail_id_list"></param>
        /// <returns></returns>
        public static List<string> GetPickPaperCode(int lend_id, List<int> detail_id_list)
        {
            List<string> res = new List<string>();
            List<paper_lending_detail> detail_list = _repository.GetPaperLendingDetail(lend_id);
            if (detail_list == null || detail_list.Count == 0) return res;

            foreach (paper_lending_detail detail in detail_list)
            {
                if (detail_id_list.Contains(detail.detail_id))
                {
                    res.Add(detail.paper_code);
                }
            }
            return res;
        }
        #endregion

        #region 查詢當前使用者是否已有暫存單
        /// <summary>
        /// 查詢當前使用者是否已有暫存單(無其他申請單號)
        /// 查詢其他申請單號是否有對應暫存借出單(有其他申請單號)
        /// </summary>
        /// <param name="retrieve_number"></param>
        /// <returns></returns>
        public static LoanDetailsView GetTempLoanDetailsView(string? retrieve_number = null)
        {
            List<int> res;
            if (string.IsNullOrEmpty(retrieve_number))
            {
                res = _repository.GetPersonTempApplication(MvcContext.UserInfo.current_emp);
            }
            else
            {
                res = _repository.GetOtherTempApplication(retrieve_number);
            }
            if (res == null || res.Count == 0) return null;
            return GetPaperLendingApplication(res.FirstOrDefault());
        }
        #endregion

        #region 借出提醒郵件
        /// <summary>
        /// 借出提醒郵件
        /// TO： 揭露人員
        /// CC： 填單人、填單人代理人、經辦人、經辦人代理人
        /// </summary>
        /// <param name="lend_id"></param>
        private static void SendLendingNotificationMail(int lend_id)
        {
            paper_lending_application form = _repository.GetPaperLendingApplicationByLendID(lend_id);
            if (form != null)
                SendMailService.SendLendingMail(form.lend_number, new List<MailTypeUtils> { MailTypeUtils.LN });
            return;

            #region 舊邏輯
            //try
            //{
            //    #region 發送結案郵件
            //    SysEmail se = new SysEmail();
            //    //獲取郵件模板(借出申請歸還結案通知)
            //    SysEmailContent sysEmailContent = DbAccess.FirstOrDefault<SysEmailContent>(f => f.EType.Equals(MailTypeUtils.LN.ToString()));
            //    //不為空繼續作業
            //    if (sysEmailContent != null)
            //    {
            //        //1.沒有揭露人員的時候發給經辦人，2.如果揭露人員和經辦人都沒有郵箱（正常情況下不會發生），就發給elegal總郵箱，3.CC不變
            //        //根據 lendid 獲取需要發送揭露人員(多個人員)
            //        List<LendClosedMailModel> listReceiver = _repository.GetLendExposeData(lend_id, "02");
            //        //如果揭露人員無數據時，則將郵件發送給經辦人
            //        if (listReceiver.Count() <= 0) { listReceiver = _repository.GetLendHandleData(lend_id, "02"); }
            //        //循環遍歷發送人員
            //        if (listReceiver.Count() > 0)
            //        {
            //            //根據 lendid 獲取 借出單號，申請日期，其他申請單號，經辦人(單一數據)
            //            paper_lending_application application = _repository.GetPaperLendingApplicationByLendID(lend_id);
            //            paper_lending_demand demand = _repository.GetPaperLendingDemand(lend_id);
            //            paper_lending_detail details = _repository.GetPaperLendingDetail(lend_id).FirstOrDefault();

            //            string should_return_time = details.should_return_time == null ? "" : Convert.ToDateTime(details.should_return_time).ToString("yyyy/MM/dd");

            //            PsSubEeLglVwA handler_ee = PsSubEeLglVwADataService.FindByKey(application.lend_handler_emplid);
            //            PsSubEeLglVwA fill_ee = PsSubEeLglVwADataService.FindByKey(application.lend_fill_emplid);
            //            //根據 lendid 獲取 填單人、填單人代理人、經辦人郵箱、Legal總信箱(單一數據)
            //            string cc = _repository.GetLendCc(lend_id, "02");
            //            string legalEMail = AppSettingHelper.Configuration["legalEMail"] ?? "";
            //            //根據 lendid 獲取 借出單號對應的數據集合(需要拼接)
            //            //表格模板
            //            string tableTemp = @"<tr><td>{0}</td><td>{1}</td><td style=""text-align: left;"">{2}</td><td>{3}</td></tr>";

            //            //中文模板數據
            //            List<string> tableContentC = new List<string>();
            //            //英文模板數據
            //            List<string> tableContentE = new List<string>();
            //            List<PaperLendingApplicationDetailViewModel> listDetial_C = _repository.GetPaperLendingDetails(lend_id, "ZH-TW");
            //            List<PaperLendingApplicationDetailViewModel> listDetial_E = _repository.GetPaperLendingDetails(lend_id, "EN-US");
            //            foreach (PaperLendingApplicationDetailViewModel lcl in listDetial_C)
            //            {
            //                if (!string.IsNullOrEmpty(lcl.returnStatusName)) lcl.returnStatusName = lcl.returnStatusName.Replace(",", ",<br/>");
            //                tableContentC.Add(string.Format(tableTemp, lcl.contract_number, lcl.paper_code, lcl.paper_name, lcl.returnStatusName));
            //            }
            //            foreach (PaperLendingApplicationDetailViewModel lcl in listDetial_E)
            //            {
            //                if (!string.IsNullOrEmpty(lcl.returnStatusName)) lcl.returnStatusName = lcl.returnStatusName.Replace(",", ",<br/>");
            //                tableContentE.Add(string.Format(tableTemp, lcl.contract_number, lcl.paper_code, lcl.paper_name, lcl.returnStatusName));
            //            }

            //            string receiver = "";
            //            if (listReceiver.Where(s => !string.IsNullOrEmpty(s.oEmail) && s.oEmail.ToLower() != "NULL".ToLower()).ToList().Count > 0)
            //            {
            //                receiver = string.Join(";", listReceiver.Where(s => !string.IsNullOrEmpty(s.oEmail) && s.oEmail.ToLower() != "NULL".ToLower()).Select(s => s.oEmail));
            //            }
            //            else
            //            {
            //                receiver = legalEMail;
            //            }
            //            string name = string.Join("、", listReceiver.Select(s => $"{s.oCname}({s.oEname})"));
            //            se = new SysEmail();
            //            //郵件發送人員
            //            se.EReceiver = receiver;
            //            //郵件內容
            //            se.EContent = string.Format(sysEmailContent.EContent,
            //                name,
            //                application.lend_number, application.application_time.ToString("yyyy/MM/dd"), demand.retrieve_number, handler_ee.Name, handler_ee.NameA, demand.borrow_days.ToString(), should_return_time,
            //                string.Join("\r\n", tableContentC.Select(s => s)), string.Join("\r\n", tableContentE.Select(s => s)), fill_ee.NameA, fill_ee.PhoneA);

            //            #region 默認參數
            //            //郵件標題
            //            se.ESubject = string.Format(sysEmailContent.ESubject, application.lend_number);
            //            //拼接郵件抄送人員
            //            se.ECc = cc;
            //            se.ESendtime = DateTime.UtcNow;
            //            se.ESendnum = 0;
            //            se.EIssend = (int)YesOrNoUtils.No;
            //            se.EType = sysEmailContent.EType;
            //            #endregion

            //            //發送郵件
            //            SysEmailDataService.Create(se);
            //        }
            //    }
            //    #endregion
            //}
            //catch
            //{
            //    return;
            //} 
            #endregion
        }
        #endregion

        #region 借出修改提醒郵件
        /// <summary>
        /// 借出修改提醒郵件
        /// TO： 揭露人員
        /// CC： 填單人、填單人代理人、經辦人、經辦人代理人
        /// </summary>
        /// <param name="lend_id"></param>
        private static void SendModifyNotification(int lend_id)
        {
            paper_lending_application form = _repository.GetPaperLendingApplicationByLendID(lend_id);
            if (form != null)
                SendMailService.SendLendingMail(form.lend_number, new List<MailTypeUtils> { MailTypeUtils.LM });
            return;

            try
            {
                #region 發送提醒郵件
                SysEmail se = new SysEmail();
                //獲取郵件模板(借出申請歸還結案通知)
                SysEmailContent sysEmailContent = DbAccess.FirstOrDefault<SysEmailContent>(f => f.EType.Equals(MailTypeUtils.LM.ToString()));
                //不為空繼續作業
                if (sysEmailContent != null)
                {
                    //根據 lendid 獲取需要發送揭露人員(多個人員)
                    List<LendClosedMailModel> listReceiver = _repository.GetLendExposeData(lend_id, "02");
                    //如果揭露人員無數據時，則將郵件發送給經辦人
                    if (listReceiver.Count() <= 0) { listReceiver = _repository.GetLendHandleData(lend_id, "02"); }
                    //循環遍歷發送人員
                    if (listReceiver.Count() > 0)
                    {
                        //根據 lendid 獲取 借出單號，申請日期，其他申請單號，經辦人(單一數據)
                        paper_lending_application application = _repository.GetPaperLendingApplicationByLendID(lend_id);
                        paper_lending_demand demand = _repository.GetPaperLendingDemand(lend_id);
                        paper_lending_detail details = _repository.GetPaperLendingDetail(lend_id).FirstOrDefault();

                        string should_return_time = details.should_return_time == null ? "" : Convert.ToDateTime(details.should_return_time).ToString("yyyy/MM/dd");

                        PsSubEeLglVwA handler_ee = PsSubEeLglVwADataService.FindByKey(application.lend_handler_emplid);
                        PsSubEeLglVwA fill_ee = PsSubEeLglVwADataService.FindByKey(application.lend_fill_emplid);
                        //根據 lendid 獲取 填單人、填單人代理人、經辦人郵箱、Legal總信箱(單一數據)
                        string cc = _repository.GetLendCc(lend_id, "02");
                        string legalEMail = AppSettingHelper.Configuration["legalEMail"] ?? "";
                        //根據 lendid 獲取 借出單號對應的數據集合(需要拼接)
                        //表格模板
                        string tableTemp = @"<tr><td>{0}</td><td>{1}</td><td style=""text-align: left;"">{2}</td><td>{3}</td></tr>";

                        //中文模板數據
                        List<string> tableContentC = new List<string>();
                        //英文模板數據
                        List<string> tableContentE = new List<string>();
                        List<PaperLendingApplicationDetailViewModel> listDetial_C = _repository.GetPaperLendingDetails(lend_id, "ZH-TW");
                        List<PaperLendingApplicationDetailViewModel> listDetial_E = _repository.GetPaperLendingDetails(lend_id, "EN-US");
                        foreach (PaperLendingApplicationDetailViewModel lcl in listDetial_C)
                        {
                            if (!string.IsNullOrEmpty(lcl.returnStatusName)) lcl.returnStatusName = lcl.returnStatusName.Replace(",", ",<br/>");
                            tableContentC.Add(string.Format(tableTemp, lcl.contract_number, lcl.paper_code, lcl.paper_name, lcl.returnStatusName));
                        }
                        foreach (PaperLendingApplicationDetailViewModel lcl in listDetial_E)
                        {
                            if (!string.IsNullOrEmpty(lcl.returnStatusName)) lcl.returnStatusName = lcl.returnStatusName.Replace(",", ",<br/>");
                            tableContentE.Add(string.Format(tableTemp, lcl.contract_number, lcl.paper_code, lcl.paper_name, lcl.returnStatusName));
                        }

                        string receiver = "";
                        if (listReceiver.Where(s => !string.IsNullOrEmpty(s.oEmail) && s.oEmail.ToLower() != "NULL".ToLower()).ToList().Count > 0)
                        {
                            receiver = string.Join(";", listReceiver.Where(s => !string.IsNullOrEmpty(s.oEmail) && s.oEmail.ToLower() != "NULL".ToLower()).Select(s => s.oEmail));
                        }
                        else
                        {
                            receiver = legalEMail;
                        }
                        string name = string.Join("、", listReceiver.Select(s => $"{s.oCname}({s.oEname})"));

                        //根據揭露人員拼接郵件信息，並發送
                        se = new SysEmail();
                        //郵件發送人員
                        se.EReceiver = receiver;
                        //郵件內容
                        se.EContent = string.Format(sysEmailContent.EContent,
                            name,
                            application.lend_number, application.application_time.ToString("yyyy/MM/dd"), demand.retrieve_number, handler_ee.Name, handler_ee.NameA, demand.borrow_days.ToString(), should_return_time,
                            string.Join("\r\n", tableContentC.Select(s => s)), string.Join("\r\n", tableContentE.Select(s => s)), fill_ee.NameA, fill_ee.PhoneA);

                        #region 默認參數
                        //郵件標題
                        se.ESubject = string.Format(sysEmailContent.ESubject, application.lend_number);
                        //拼接郵件抄送人員
                        se.ECc = cc;
                        se.ESendtime = DateTime.UtcNow;
                        se.ESendnum = 0;
                        se.EIssend = (int)YesOrNoUtils.No;
                        se.EType = sysEmailContent.EType;
                        #endregion

                        //發送郵件
                        SysEmailDataService.Create(se);
                    }
                }
                #endregion
            }
            catch
            {
                return;
            }
        }
        #endregion

        #region 作廢提醒郵件
        /// <summary>
        /// 作廢提醒郵件
        /// TO： 揭露人員
        /// CC： 填單人、填單人代理人、經辦人、經辦人代理人
        /// </summary>
        /// <param name="lend_id"></param>
        private static void SendVoidLendingMail(int lend_id)
        {
            paper_lending_application form = _repository.GetPaperLendingApplicationByLendID(lend_id);
            if (form != null)
                SendMailService.SendLendingMail(form.lend_number, new List<MailTypeUtils> { MailTypeUtils.LD });
            return;

            try
            {
                #region 發送結案郵件
                SysEmail se = new SysEmail();
                //獲取郵件模板(借出申請歸還結案通知)
                SysEmailContent sysEmailContent = DbAccess.FirstOrDefault<SysEmailContent>(f => f.EType.Equals(MailTypeUtils.LD.ToString()));
                //不為空繼續作業
                if (sysEmailContent != null)
                {
                    //根據 lendid 獲取需要發送揭露人員(多個人員)
                    List<LendClosedMailModel> listReceiver = _repository.GetLendExposeData(lend_id, "05");
                    //如果揭露人員無數據時，則將郵件發送給經辦人
                    if (listReceiver.Count() <= 0) { listReceiver = _repository.GetLendHandleData(lend_id, "05"); }
                    //循環遍歷發送人員
                    if (listReceiver.Count() > 0)
                    {
                        //根據 lendid 獲取 借出單號，申請日期，其他申請單號，經辦人(單一數據)
                        paper_lending_application application = _repository.GetPaperLendingApplicationByLendID(lend_id);
                        paper_lending_demand demand = _repository.GetPaperLendingDemand(lend_id);
                        paper_lending_detail details = _repository.GetPaperLendingDetail(lend_id).FirstOrDefault();

                        string should_return_time = details.should_return_time == null ? "" : Convert.ToDateTime(details.should_return_time).ToString("yyyy/MM/dd");

                        PsSubEeLglVwA handler_ee = PsSubEeLglVwADataService.FindByKey(application.lend_handler_emplid);
                        PsSubEeLglVwA fill_ee = PsSubEeLglVwADataService.FindByKey(application.lend_fill_emplid);
                        //根據 lendid 獲取 填單人、填單人代理人、經辦人郵箱、Legal總信箱(單一數據)
                        string cc = _repository.GetLendCc(lend_id, "05");
                        string legalEMail = AppSettingHelper.Configuration["legalEMail"] ?? "";
                        //根據 lendid 獲取 借出單號對應的數據集合(需要拼接)
                        //表格模板
                        string tableTemp = @"<tr><td>{0}</td><td>{1}</td><td style=""text-align: left;"">{2}</td><td>{3}</td></tr>";
                        //中文模板數據
                        List<string> tableContentC = new List<string>();
                        //英文模板數據
                        List<string> tableContentE = new List<string>();
                        List<PaperLendingApplicationDetailViewModel> listDetial_C = _repository.GetPaperLendingDetails(lend_id, "ZH-TW");
                        List<PaperLendingApplicationDetailViewModel> listDetial_E = _repository.GetPaperLendingDetails(lend_id, "EN-US");
                        foreach (PaperLendingApplicationDetailViewModel lcl in listDetial_C)
                        {
                            if (!string.IsNullOrEmpty(lcl.returnStatusName)) lcl.returnStatusName = lcl.returnStatusName.Replace(",", ",<br/>");
                            tableContentC.Add(string.Format(tableTemp, lcl.contract_number, lcl.paper_code, lcl.paper_name, lcl.returnStatusName));
                        }
                        foreach (PaperLendingApplicationDetailViewModel lcl in listDetial_E)
                        {
                            if (!string.IsNullOrEmpty(lcl.returnStatusName)) lcl.returnStatusName = lcl.returnStatusName.Replace(",", ",<br/>");
                            tableContentE.Add(string.Format(tableTemp, lcl.contract_number, lcl.paper_code, lcl.paper_name, lcl.returnStatusName));
                        }
                        //根據揭露人員拼接郵件信息，並發送
                        string receiver = "";
                        if (listReceiver.Where(s => !string.IsNullOrEmpty(s.oEmail) && s.oEmail.ToLower() != "NULL".ToLower()).ToList().Count > 0)
                        {
                            receiver = string.Join(";", listReceiver.Where(s => !string.IsNullOrEmpty(s.oEmail) && s.oEmail.ToLower() != "NULL".ToLower()).Select(s => s.oEmail));
                        }
                        else
                        {
                            receiver = legalEMail;
                        }
                        string name = string.Join("、", listReceiver.Select(s => $"{s.oCname}({s.oEname})"));
                        //根據揭露人員拼接郵件信息，並發送
                        se = new SysEmail();
                        //郵件發送人員
                        se.EReceiver = receiver;
                        //郵件內容
                        se.EContent = string.Format(sysEmailContent.EContent,
                            name,
                            application.lend_number, application.application_time.ToString("yyyy/MM/dd"), demand.retrieve_number, handler_ee.Name, handler_ee.NameA, demand.borrow_days.ToString(), should_return_time,
                            string.Join("\r\n", tableContentC.Select(s => s)), string.Join("\r\n", tableContentE.Select(s => s)), application.void_reason);

                        #region 默認參數
                        //郵件標題
                        se.ESubject = string.Format(sysEmailContent.ESubject, application.lend_number);
                        //拼接郵件抄送人員
                        se.ECc = cc;
                        se.ESendtime = DateTime.UtcNow;
                        se.ESendnum = 0;
                        se.EIssend = (int)YesOrNoUtils.No;
                        se.EType = sysEmailContent.EType;
                        #endregion

                        //發送郵件
                        SysEmailDataService.Create(se);
                    }
                }
                #endregion
            }
            catch
            {
                return;
            }
        }
        #endregion

        #region 查詢借出申請明細
        /// <summary>
        /// 查詢借出申請明細
        /// </summary>
        /// <param name="lend_id"></param>
        /// <returns></returns>
        public static paper_lending_application GetPaperLendingApplicationByLendID(int lend_id)
        {
            return _repository.GetPaperLendingApplicationByLendID(lend_id);
        }
        #endregion

        #region 查詢有新的紙本
        /// <summary>
        /// 查詢有新的紙本
        /// </summary>
        /// <param name="lend_id"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public static NewOtherApplicationPaperList GetNewOtherApplicationPaperList(int lend_id)
        {
            NewOtherApplicationPaperList res = new NewOtherApplicationPaperList();

            paper_lending_application appli = _repository.GetPaperLendingApplicationByLendID(lend_id);
            if (appli == null) throw new Exception(ActionFilter.GetMultilingualValue("custom:messageContent:dataChurn"));

            paper_lending_demand demand = _repository.GetPaperLendingDemand(lend_id);
            if (demand == null) throw new Exception(ActionFilter.GetMultilingualValue("custom:messageContent:dataChurn"));
            if (string.IsNullOrEmpty(demand.retrieve_number)) return null;

            List<paper_lending_detail> details = _repository.GetPaperLendingDetail(lend_id);
            if (details == null) throw new Exception(ActionFilter.GetMultilingualValue("custom:messageContent:dataChurn"));

            List<paper_lending_detail> un_details = _repository.GetPaperUnlendingDetail(lend_id);

            List<paper_basic_data> retrieve_paper_List = _repository.GetRetrievePaper(demand.retrieve_number);
            if (retrieve_paper_List == null || retrieve_paper_List.Count == 0) throw new Exception(ActionFilter.GetMultilingualValue("custom:messageContent:dataNotexist"));

            List<string> received_paper = new List<string>();
            List<string> not_received_paper = new List<string>();

            foreach (paper_basic_data basic in retrieve_paper_List)
            {
                //借出明細及非借出明細皆無此紙本
                if (!details.Select(x => x.paper_code).Contains(basic.paper_code) && !un_details.Select(x => x.paper_code).Contains(basic.paper_code))
                {
                    if (basic.paper_entry_status == "05")
                        received_paper.Add(basic.paper_code);
                    else
                        not_received_paper.Add(basic.paper_code);
                }
                else
                {  //借出明細沒有此紙本，但其為"已入庫"
                    if (!details.Select(x => x.paper_code).Contains(basic.paper_code) && basic.paper_entry_status == "05")
                        received_paper.Add(basic.paper_code);
                }
            }

            if (received_paper != null && received_paper.Count > 0)
                res.received_paper_code = _repository.BatchGetLendingDetail(null, received_paper);
            if (not_received_paper != null && not_received_paper.Count > 0)
                res.not_received_paper_code = _repository.BatchGetLendingDetail(null, not_received_paper);

            return res;
        }
        #endregion
    }
}
