﻿using Elegal.Flow.Api.Repository.Paper;
using Elegal.Interface.Api.Common.Control.Filter;
using Elegal.Interface.Api.Common.FuncService;
using Elegal.Interface.Api.Common.Model.DBModel;
using Elegal.Interface.Api.Common.Model.ParaModel.FlowApi;
using Elegal.Interface.Api.Common.Model.ResultModel.FlowApi;
using Elegal.Interface.Api.Common.Model.ViewModel.FlowApi;
using Elegal.Orm;
using Elegal.Orm.Dtos;

namespace Elegal.Flow.Api.Services.Paper
{
    /// <summary>
    /// 紙本批次作業
    /// </summary>
    public static class PaperBatchWorkService
    {
        private static readonly PaperBatchWorkRepository _repository = new();

        #region 查詢存放位置清單
        /// <summary>
        /// 查詢存放位置清單
        /// </summary>
        /// <returns></returns>
        public static IEnumerable<string> GetBatchPositionList()
        {
            return _repository.GetBatchPositionList();
        }
        #endregion

        #region 查詢紙本批次資料
        /// <summary>
        /// 查詢紙本批次資料
        /// </summary>
        /// <param name="qry"></param>
        /// <returns></returns>
        public static PageResult<PaperBatchDataViewModel> QueryPaperBatchData(qryPaperBatchData qry)
        {
            //if (qry.start_time.HasValue)
            //{
            //    qry.start_time = qry.start_time.Value.Date.AddHours(0).AddMinutes(0).AddSeconds(0);
            //}
            //if (qry.end_time.HasValue)
            //{
            //    qry.end_time = qry.end_time.Value.Date.AddHours(23).AddMinutes(59).AddSeconds(59);
            //}
            return _repository.QueryPaperBatchData(qry);
        }
        #endregion

        #region 根據掃碼/輸入查詢對應的合約編號與紙本名稱
        /// <summary>
        /// 根據掃碼/輸入查詢對應的合約編號與紙本名稱
        /// </summary>
        /// <param name="paper_code"></param>
        /// <returns></returns>
        public static List<PaperBatchDataViewModel> GetPaperBatchDataList(string paper_code)
        {
            return _repository.GetPaperBatchDataList(paper_code);
        }
        #endregion

        #region 新增批次資料(事務處理)
        /// <summary>
        /// 新增批次資料
        /// </summary>
        /// <param name="batchData"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public static PaperBasicWorkResult InsertPaperBatchData(insertPaperBatchData batchData)
        {
            #region 根據用戶批次獲取的紙本資料查詢需要新增的數據
            List<PaperBasicApplicationViewModel> basic_data = _repository.GetPaperBasicData(batchData.basic_id_list);
            if (basic_data == null || basic_data.Count == 0) throw new Exception(ActionFilter.GetMultilingualValue("custom:messageContent:getPaperDataFail"));
            #endregion

            #region 數據過濾
            //按照 basic_id_list 的順序對 basic_data 進行排序
            basic_data = basic_data.OrderBy(basic => batchData.basic_id_list.IndexOf(basic.basic_id)).ToList();
            //定義需要修改的數據
            List<PaperBasicApplicationViewModel> update_list = new List<PaperBasicApplicationViewModel>();
            List<string> errorList = new List<string>();
            //過濾掉預約中(01)、出借中(03)或者已銷毀(06)的數據
            update_list = basic_data.Where(x => x.paper_entry_status != "01" && x.paper_entry_status != "03" && x.paper_entry_status != "06").ToList();
            errorList = basic_data.Where(x => x.paper_entry_status == "01" || x.paper_entry_status == "03" || x.paper_entry_status == "06").Select(x => x.paper_code).ToList();
            //如果過濾後的數據為空，則提示用戶
            if (update_list == null || update_list.Count == 0) throw new Exception(ActionFilter.GetMultilingualValue("custom:messageContent:batchPaperWork_LostOrDestroyed"));
            #endregion

            #region 日期賦值
            DateTime? batch_destory_time = batchData.batch_entry_status == "06" ? DateTime.UtcNow : null;
            DateTime? batch_lost_time = batchData.batch_entry_status == "07" ? DateTime.UtcNow : null;

            if (batchData.batch_entry_status == "06" && batchData.batch_destory_time != null)
                batch_destory_time = batchData.batch_destory_time;
            if (batchData.batch_entry_status == "07" && batchData.batch_lost_time != null)
                batch_lost_time = batchData.batch_lost_time;
            #endregion

            //定義需要的參數以便後續作業
            List<string> success_list = new List<string>();

            //使用事務方法進行數據處理
            DbAccess.PerformInTransaction(context =>
            {
                List<paper_batch_work> batch_list = new List<paper_batch_work>();
                List<paper_basic_data> batch_update_list = new List<paper_basic_data>();
                List<paper_history_data> batch_history = new List<paper_history_data>();

                //批次號：P+西元年+5碼流水號(次年重新計算)
                string batch_number = SysApplicationSequenceService.GetApplicationNumberToTransaction("paper_batch_number", "P".ToUpper(), context);

                #region 數據處理
                foreach (PaperBasicApplicationViewModel basic in update_list)
                {
                    #region 新增批次數據
                    paper_batch_work batch = new paper_batch_work();
                    batch.batch_number = batch_number;
                    batch.paper_basic_id = basic.basic_id;
                    batch.paper_code = basic.paper_code;
                    batch.paper_name = basic.paper_name;
                    batch.contract_number = basic.contract_number;
                    batch.contract_name = basic.contract_name;
                    batch.batch_position = batchData.batch_position;
                    batch.paper_position = basic.paper_position;
                    batch.batch_entry_status = batchData.batch_entry_status;
                    batch.paper_entry_status = basic.paper_entry_status;
                    batch.batch_destory_time = batch_destory_time;
                    batch.batch_destory_reason = batchData.batch_destory_reason;
                    batch.destroy_time = null;
                    batch.batch_lost_time = batch_lost_time;
                    batch.batch_lost_reason = batchData.batch_lost_reason;
                    batch.lost_time = null;
                    batch.batch_remarks = batchData.batch_remarks;
                    batch.create_user = MvcContext.UserInfo.current_emp;
                    batch.create_time = DateTime.UtcNow;
                    batch_list.Add(batch);
                    #endregion

                    #region 修改基本資料的數據
                    paper_basic_data batch_update = new paper_basic_data();
                    batch_update.basic_id = basic.basic_id;
                    batch_update.paper_position = batchData.batch_position;
                    batch_update.paper_entry_status = batchData.batch_entry_status;
                    batch_update.destroy_time = batch_destory_time;
                    batch_update.lost_time = batch_lost_time;
                    batch_update.paper_remarks = basic.paper_remarks + " " + batchData.batch_remarks;
                    batch_update.modify_user = MvcContext.UserInfo.current_emp;
                    batch_update.modify_time = DateTime.UtcNow;
                    batch_update.destroy_reason = batchData.batch_destory_reason;
                    batch_update.lost_reason = batchData.batch_lost_reason;
                    batch_update_list.Add(batch_update);
                    #endregion

                    #region 新增歷程數據
                    paper_history_data history = new paper_history_data();
                    history.paper_basic_id = basic.basic_id;
                    history.paper_entry_status = batchData.batch_entry_status;
                    history.course_emplid = MvcContext.UserInfo.current_emp;
                    history.create_user = MvcContext.UserInfo.current_emp;
                    history.create_time = DateTime.UtcNow;
                    if (batch_destory_time != null)
                    {
                        history.paper_remarks = "銷毀日期: " + batch_destory_time.Value.ToString("yyyy/MM/dd");
                        if (!string.IsNullOrEmpty(batchData.batch_destory_reason)) history.paper_remarks += " ; 銷毀原因: " + batchData.batch_destory_reason;
                    }
                    else if (batch_lost_time != null)
                    {
                        history.paper_remarks = "遺失日期: " + batch_lost_time.Value.ToString("yyyy/MM/dd");
                        if (!string.IsNullOrEmpty(batchData.batch_lost_reason)) history.paper_remarks += " ; 遺失原因: " + batchData.batch_lost_reason;
                    }
                    else
                    {
                        history.paper_remarks = basic.paper_remarks + " " + batchData.batch_remarks;
                    }
                    batch_history.Add(history);
                    #endregion

                    success_list.Add(basic.paper_code);
                }
                #endregion

                //新增批次數據
                if (!_repository.InsertPaperBatchWorkToTransaction(batch_list, context))
                    throw new Exception(ActionFilter.GetMultilingualValue("custom:messageTitle:createFail"));
                //修改基本資料
                if (!_repository.BatchUpdatePaperBasicDataToTransaction(batch_update_list, context))
                    throw new Exception(ActionFilter.GetMultilingualValue("custom:messageTitle:updateFail"));
                //新增成功後，新增歷程數據(更改狀態為已遺失或已銷毀)
                if (batch_history != null && batch_history.Count > 0)
                {
                    if (!_repository.BatchInsertPaperHistoryDataToTransaction(batch_history, context))
                        throw new Exception(ActionFilter.GetMultilingualValue("custom:messageTitle:createFail"));
                }
            });

            //返回數據結果
            PaperBasicWorkResult res = new PaperBasicWorkResult();
            res.success_list = success_list;
            res.fail_list = errorList;
            return res;
        }
        #endregion
    }
}