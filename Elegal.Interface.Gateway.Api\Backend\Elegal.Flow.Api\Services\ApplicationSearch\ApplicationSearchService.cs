﻿using Elegal.Flow.Api.Repository;
using Elegal.Interface.Api.Common.Control.Filter;
using Elegal.Interface.Api.Common.FuncHelper;
using Elegal.Interface.Api.Common.FuncService;
using Elegal.Interface.Api.Common.Model;
using Elegal.Interface.Api.Common.Model.ParaModel.FlowApi.ApplicationSearch;
using Elegal.Interface.Api.Common.Model.ViewModel.FlowApi;
using Elegal.Interface.Api.Common.Model.ViewModel.FlowApi.ApplicationSearch;
using Elegal.Interface.Api.Common.Model.ViewModel.PermissionApi;
using Elegal.Interface.Api.Common.Model.ViewModel.PermissionApi.EmpManage;
using Elegal.Interface.Api.Common.Repository;
using Elegal.Interface.ApiData.Service.FuncService;
using Elegal.Interface.ApiData.Service.Model.DbModel;
using NPOI.SS.UserModel;
using NPOI.XSSF.UserModel;


namespace Elegal.Flow.Api.Services.ApplicationSearch
{
    /// <summary>
    /// 資料查詢 -> 申請單查詢
    /// </summary>
    public static class ApplicationSearchService
    {
        private static ApplicationSearchRepository _applicationSearchRepository = new ApplicationSearchRepository();
        private static ApplicationSearchCommonRepository _applicationSearchCommonRepository = new ApplicationSearchCommonRepository();
        private static SysParametersRepository _sysParametersRepository = new SysParametersRepository();
        private static ApplicationPermissionRepository _applyPermissionRepository = new ApplicationPermissionRepository();

        #region 根據案件狀態查詢不同的主體
        /// <summary>
        /// 根據案件狀態查詢不同的主體
        /// </summary>
        /// <param name="caseStatus">案件狀態</param>
        /// <param name="menuCode">菜單欄</param>
        /// <param name="empID">當前登陸者工號</param>
        /// <param name="userRole">當前登陸者的角色信息</param>
        /// <returns></returns>
        public static List<FnpEntityModel> GetFnpEntityByCaseStatus(int caseStatus, int menuCode, string empID, List<int> userRole)
        {
            List<FnpEntityModel> listEntity = new List<FnpEntityModel>();
            //根據人員工號獲取當前人員的單據角色
            List<int> applyRole = _applyPermissionRepository.GetFixedRolesByEmplID(empID, string.Empty).Where(s => s < 0).ToList();
            insertLog("根據人員工號獲取當前人員的單據角色");
            //根據角色+菜單欄信息驗證角色是否存在全部主體類型
            userRole.AddRange(applyRole);
            List<ContractViewModel> contractViews = new List<ContractViewModel>();
            #region 資料檢視權限進行中案件主體查詢特殊處理
            //小綜合查詢進行中案件需要進行特殊處理，這個狀態只能為小綜合查詢使用，後期如查進行中，請確認好要求後看看是否可以複用
            if (caseStatus == 0)
            {
                contractViews = _applicationSearchCommonRepository.GetSelectTypeByContractView(userRole, menuCode, 38);
                insertLog("資料檢視權限進行中案件主體查詢特殊處理 case 0");
            }
            else
            {
                contractViews = _applicationSearchCommonRepository.GetSelectTypeByContractView(userRole, menuCode);
                insertLog("資料檢視權限進行中案件主體查詢特殊處理 not 0");
            }
            #endregion
            //是否存在全部主體權限
            caseStatus = contractViews.Any(s => s.selectType == 0) ? -1 : caseStatus;
            //根據檢查後的數據進行處理
            switch (caseStatus)
            {
                case -1://根據用戶角色發現存在全部主體權限角色
                    listEntity = _applicationSearchCommonRepository.GetAllEntity();
                    insertLog("根據用戶角色發現存在全部主體權限角色");
                    break;
                case 0://簽核中案件
                    listEntity = _applicationSearchCommonRepository.GetSignerEntity(empID, contractViews);
                    insertLog("簽核中案件");
                    break;
                case 1://歷史案件
                    listEntity = _applicationSearchCommonRepository.GetOldCaseEntity(empID, contractViews);
                    insertLog("歷史案件");
                    break;
                case 2://歷史案件(大綜合查詢使用)
                    listEntity = _applicationSearchCommonRepository.GetOldCaseEntityBySynthesis(empID, contractViews);
                    insertLog("歷史案件(大綜合查詢使用)");
                    break;
                case 3://其他申請查詢中ACD類型申請單主體
                    listEntity = _applicationSearchCommonRepository.GetFnpEntityByOtherACD(empID);
                    insertLog("其他申請查詢中ACD類型申請單主體");
                    break;
                case 4://其他申請查詢中EF類型申請單主體
                    listEntity = _applicationSearchCommonRepository.GetFnpEntityByOtherEF(empID);
                    insertLog("其他申請查詢中EF類型申請單主體");
                    break;
            }

            return listEntity;
        }
        #endregion

        #region 獲取我方主體(ACD)
        /// <summary>
        /// 獲取我方主體(ACD)
        /// </summary>
        /// <returns></returns>
        public static List<FnpEntityModel> GetFnpEntityByOtherACD()
        {
            return _applicationSearchCommonRepository.GetFnpEntityByOtherACD(MvcContext.UserInfo.current_emp);
        }
        #endregion

        #region 獲取檢視/特殊主體權限(E/F)
        /// <summary>
        /// 獲取檢視/特殊主體權限(E/F)
        /// </summary>
        /// <returns></returns>
        public static List<FnpEntityModel> GetFnpEntityByOtherEF()
        {
            return _applicationSearchCommonRepository.GetFnpEntityByOtherEF(MvcContext.UserInfo.current_emp);
        }
        #endregion

        #region 獲取關聯合約編號
        /// <summary>
        /// 獲取關聯合約編號
        /// </summary>
        /// <returns></returns>
        public static List<string> GetGroupContractNumber()
        {
            return _applicationSearchRepository.GetGroupContractNumber();
        }
        #endregion

        #region  獲取目前關卡，關卡並集(合約申請，資料建檔，關企建檔)
        /// <summary>
        ///  獲取目前關卡，關卡並集(合約申請，資料建檔，關企建檔)
        /// </summary>
        /// <param name="langType">語係</param>
        /// <returns></returns>
        public static List<DropDownListModel> GetFlowStepByParaCode(string langType = "ZH-TW")
        {
            return _sysParametersRepository.GetFlowStepByParaCode(new List<string> { "generalContractStep", "capitalContractStep", "hrContractStep", "rdContractStep", "geContractStep" }, langType);
        }
        #endregion

        #region 獲取目前關卡，關卡並集(其他申請)
        /// <summary>
        /// 獲取目前關卡，關卡並集(其他申請)
        /// </summary>
        /// <param name="langType">語係</param>
        /// <returns></returns>
        public static List<DropDownListModel> GetOtherFlowStepByParaCode(string langType = "ZH-TW")
        {
            return _sysParametersRepository.GetFlowStepByParaCode(new List<string> { "otherAContractStep", "otherBContractStep", "otherCContractStep", "otherDContractStep", "otherEContractStep", "otherFContractStep", "otherGContractStep" }, langType);
        }
        #endregion

        #region 獲取目前關卡，關卡並集(所有申請)
        /// <summary>
        /// 獲取目前關卡，關卡並集(其他申請)
        /// </summary>
        /// <param name="langType">語係</param>
        /// <returns></returns>
        public static List<DropDownListModel> GetAllFlowStepByParaCode(string langType = "ZH-TW")
        {
            return _sysParametersRepository.GetFlowStepByParaCode(new List<string> { "generalContractStep", "capitalContractStep", "hrContractStep", "rdContractStep", "geContractStep", "otherAContractStep", "otherBContractStep", "otherCContractStep", "otherDContractStep", "otherEContractStep", "otherFContractStep", "otherGContractStep" }, langType);
        }
        #endregion

        #region 獲取關企建檔掛帳部門資訊
        /// <summary>
        /// 獲取關企建檔掛帳部門資訊
        /// </summary>
        /// <param name="deptid"></param>
        /// <returns></returns>
        public static List<PsSubOgLglVwA> GetAccountDeptID(string deptid)
        {
            return _applicationSearchRepository.GetAccountDeptID(deptid);
        }
        #endregion

        #region 通過SP獲取申請單數據
        /// <summary>
        /// 通過SP獲取申請單數據 ISSUE：468
        /// </summary>
        /// <param name="asp"></param>
        /// <param name="empID"></param>
        /// <param name="userRole"></param>
        /// <param name="langType"></param>
        /// <returns></returns>
        public static (int, List<ApplicationSearchViewModel>) GetApplicationFormInquiry(ApplicationSearchParaModel asp, string empID, List<int> userRole,
            string langType = "ZH-TW")
        {
            List<ApplicationSearchViewModel> listData = new List<ApplicationSearchViewModel>();
            int totalCount = 0;
            //當無主體數據時，不用查詢數據(UAT NO：145)
            //改為使用前端傳遞的參數 ********
            if (asp.defaultEntity.Any())
            {
                //根據主體分別獲取單據主體+角色主體數據 -> Issue：323
                string applyNumberSql = string.Empty;
                //改為使用存儲過程處理，不需要了 ISSUE：468
                //switch (asp.caseStatus)
                //{
                //    case 0:
                //        applyNumberSql = _applicationSearchCommonRepository.GetApplyNumberSignerByEntity(asp.defaultEntity, empID);
                //        break;
                //    default:
                //        applyNumberSql = _applicationSearchCommonRepository.GetApplyNumberByEntity(asp.defaultEntity, empID, userRole, asp.menuCode);
                //        break;
                //}
                insertLog("GetApplicationFormInquiryToSql：" + DateTime.Now);
                var (total, applicationSearchViewModels) = _applicationSearchCommonRepository.GetApplicationFormInquiry(asp, empID, userRole, langType, applyNumberSql);

                insertLog("GetApplicationFormInquiryToHidden：" + DateTime.Now);
                foreach (ApplicationSearchViewModel asv in applicationSearchViewModels)
                {
                    //極機密案件且當前案件狀態不為暫存(T)且不為進行中(I)，需要做隱碼作業
                    if (asv.confiden_level.ToUpper() == "01".ToUpper() && asv.application_state.ToUpper() != "I".ToUpper() && asv.application_state.ToUpper() != "T".ToUpper())
                    {
                        listData.Add(HiddenValueConvertHelper.ConvertToHiddenBySingle(asv));
                    }
                    else { listData.Add(asv); }
                }
                totalCount = total;
            }

            insertLog("GetApplicationFormInquiryToReturn：" + DateTime.Now);
            return (totalCount, listData);
        }
        #endregion

        #region 匯出
        /// <summary>
        /// 匯出
        /// </summary>
        /// <param name="asp">查詢欄位</param>
        /// <param name="empID">當前登陸者工號</param>
        /// <param name="userRole">當前登陸者工號</param>>
        /// <param name="langType">語係</param>
        /// <returns></returns>
        public static byte[] GetApplicationDataExport(ApplicationExportParaModel asp, string empID, List<int> userRole, string langType = "ZH-TW")
        {
            insertLog("GetApplicationDataExport_Export_Begin");

            //小綜合查詢匯出改為使用存儲過程匯出結果  ISSUE：468 by SpringJiang 20250708
            var (total, applicationSearchViewModels) = GetApplicationFormInquiry(asp, empID, userRole, langType);
            //获取时区，仅仅获取一次，仅仅调用一次当前线程的 （Accessor.HttpContext会从线程池拿到当前请求的httpcontext；不适合放在一个超多数据的循环里）
            var timeZone = MvcContext.UserInfo.time_zone;

            string yes = ActionFilter.GetMultilingualValue("ApplicationSearch_Export:yes", true);//異動時間
            string no = ActionFilter.GetMultilingualValue("ApplicationSearch_Export:no", true);//異動時間
            string signType1 = ActionFilter.GetMultilingualValue("ApplicationSearch_Export:signType1", true);//異動時間
            insertLog("GetApplicationDataExport_seach_end");
            //创建工作表
            IWorkbook workbook = new XSSFWorkbook();
            ISheet sheet = workbook.CreateSheet("Sheet1");

            //定义一个字体
            IFont font = workbook.CreateFont().configFont(f =>
            {
                f.FontHeightInPoints = 12;
                f.FontName = "Calibri";
            });
            //定义一个样式
            ICellStyle defaultstyle = sheet.Workbook.CreateCellStyle().SetDefaultTitleBorder().AddFont(font).SetwrapTextOn().VerticalCenter();

            //設置表頭
            sheet.SetHeader(asp.ExportColumns.Select(e => e.header).ToList(), style =>
            {
                var font = sheet.Workbook.CreateFont();
                font.IsBold = true; // 设置加粗
                style.AddFont(font).SetDefaultTitleBorder();
            });

            int columnLength = asp.ExportColumns.Count;
            //設置列寬
            sheet.SetColumnAutoWidth(0, columnLength);

            //写入数据
            sheet.WriteData(applicationSearchViewModels, (item, row) =>
            {
                for (int i = 0; i < columnLength; i++)
                {
                    var column = asp.ExportColumns[i];

                    if (column.field == "currentSigner")
                    {
                        var data1 = item.current_signer.EmptyDefault(string.Empty).Split(",").Where(e => !string.IsNullOrEmpty(e))
                            .Select(e => e + (item.is_invitee == 1 ? signType1 : string.Empty));
                        var data2 = item.current_signer_over.EmptyDefault(string.Empty).EmptyDefault(string.Empty).Split(",").Where(e => !string.IsNullOrEmpty(e))
                            .Select(e => e + (item.is_invitee == 1 ? signType1 : string.Empty));
                        item["currentSigner", timeZone] = string.Join("\r\n", data1.Concat(data2));
                    }
                    if (column.field == "hasStampDuty")
                    {
                        item["hasStampDuty", timeZone] = item.has_stamp_duty == "1" ? yes : item.has_stamp_duty == "0" ? no : item.has_stamp_duty;
                    }
                    row.CreateElegalDefaultCell(i, defaultstyle).SetCellValue(item[column.field, timeZone]);
                }

            });
            insertLog("GetApplicationDataExport_Export_End");
            return workbook.ToBytes();
        }
        #endregion

        #region 根據查詢參數獲取案件申請單集合
        /// <summary>
        /// 根據查詢參數獲取案件申請單集合
        /// </summary>
        /// <param name="asp">查詢欄位</param>
        /// <param name="empID">當前登陸者工號</param>
        /// <param name="userRole">當前登陸者工號</param>
        /// <param name="totalCount">總筆數</param>
        /// <param name="langType">語係</param>
        /// <returns></returns>
        public static List<ApplicationSearchViewModel> GetApplicationData(ApplicationSearchParaModel asp, string empID, List<int> userRole, out int totalCount, string langType = "ZH-TW")
        {
            List<ApplicationSearchViewModel> listData = new List<ApplicationSearchViewModel>();
            totalCount = 0;
            insertLog("strat" + DateTime.Now.ToString());

            //獲取主體集合
            //List<FnpEntityModel> listEntity = GetFnpEntityByCaseStatus(asp.caseStatus, asp.menuCode, empID, userRole);

            //當無主體數據時，不用查詢數據(UAT NO：145)
            //改為使用前端傳遞的參數 ********
            if (asp.defaultEntity.Any())
            {
                //根據主體分別獲取單據主體+角色主體數據 -> Issue：323
                string applyNumberSql = string.Empty;
                switch (asp.caseStatus)
                {
                    case 0:
                        applyNumberSql = _applicationSearchCommonRepository.GetApplyNumberSignerByEntity(asp.defaultEntity, empID);
                        insertLog("獲取當前登錄人員在對應主體下的簽核中案件：", $@"listEntity:{string.Join(',', asp.defaultEntity)},
                                empID:{empID},
                                userRole:{string.Join(',', userRole.Where(s => s > 0).ToList())}");
                        break;
                    default:
                        applyNumberSql = _applicationSearchCommonRepository.GetApplyNumberByEntity(asp.defaultEntity, empID, userRole, asp.menuCode);
                        insertLog(@"根據主體獲取申請單號數據：", $@"listEntity:{string.Join(',', asp.defaultEntity)},
                                empID:{empID},
                                userRole:{string.Join(',', userRole.Where(s => s > 0).ToList())}");
                        break;
                }

                List<ApplicationSearchViewModel> applicationSearchViewModels = _applicationSearchCommonRepository.GetApplicationData(asp, out totalCount, langType, applyNumberSql);
                insertLog("GetApplicationData");
                foreach (ApplicationSearchViewModel asv in applicationSearchViewModels)
                {
                    //極機密案件且當前案件狀態不為暫存(T)且不為進行中(I)，需要做隱碼作業
                    if (asv.confiden_level.ToUpper() == "01".ToUpper() && asv.application_state.ToUpper() != "I".ToUpper() && asv.application_state.ToUpper() != "T".ToUpper())
                    {
                        listData.Add(HiddenValueConvertHelper.ConvertToHiddenBySingle(asv));
                    }
                    else { listData.Add(asv); }
                }
            }
            insertLog("END" + DateTime.Now.ToString());
            return listData;
        }


        private static void insertLog(string step, string log = null)
        {
            UploadLogDataService.Create(new UploadLog()
            {
                UploadKey = "history query",
                UploadType = "history query",
                UploadStep = step,
                CreateUser = MvcContext.UserInfo.current_emp,
                LogDetail = log
            });
        }
        #endregion

        #region 根據查詢參數獲取其他申請單集合
        /// <summary>
        /// 根據查詢參數獲取其他申請單集合
        /// </summary>
        /// <param name="asp">查詢欄位</param>
        /// <param name="empID">當前登陸者工號</param>
        /// <param name="userRole">當前登陸者工號</param>
        /// <param name="totalCount">總筆數</param>
        /// <param name="langType">語係</param>
        /// <returns></returns>
        public static List<OtherApplicationSearchViewModel> GetOtherApplicationData(OtherApplicationSearchParaModel asp, string empID, List<int> userRole, out int totalCount, string langType = "ZH-TW")
        {
            List<OtherApplicationSearchViewModel> listResult = new List<OtherApplicationSearchViewModel>();
            totalCount = 0;

            #region 根據查詢結果分組顯示數據
            List<OtherApplicationSearchResultModel> listData = _applicationSearchCommonRepository.GetOtherApplicationData(asp, MvcContext.UserInfo.current_emp, out totalCount, langType);
            if (listData.Any())
            {
                //Issue：215 其他申請單顯示界面也需要隱碼
                listData = HiddenValueConvertHelper.ConvertToHiddenByList<OtherApplicationSearchResultModel>(listData, "od");
                OtherApplicationSearchViewModel oasv = new OtherApplicationSearchViewModel();
                //根據申請單號將集合分組
                var groupApplication = listData.GroupBy(ga => ga.apply_number);
                foreach (var ga in groupApplication)
                {
                    oasv = new OtherApplicationSearchViewModel();
                    List<OtherApplicationSearchResultModel> listGroupData = listData.Where(lga => lga.apply_number.ToLower() == ga.Key.ToLower()).ToList();
                    if (listGroupData.Any())
                    {
                        #region 默認欄位
                        oasv.apply_number = listGroupData[0].apply_number;
                        oasv.apply_time = listGroupData[0].apply_time;
                        oasv.pic_deptid = listGroupData[0].pic_deptid;
                        oasv.pic_emplid = listGroupData[0].pic_emplid;
                        oasv.pic_cname = listGroupData[0].pic_cname;
                        oasv.pic_ename = listGroupData[0].pic_ename;
                        oasv.form_type = listGroupData[0].form_type;
                        oasv.form_type_pfn = listGroupData[0].form_type_pfn;
                        oasv.application_state = listGroupData[0].application_state;
                        oasv.application_state_pfn = listGroupData[0].application_state_pfn;
                        #region 循環承辦法務人員
                        foreach (OtherApplicationSearchResultModel oasvm in listGroupData)
                        {
                            if (!string.IsNullOrEmpty(oasvm.legal_emplid))
                            {
                                oasv.legal_emplid.Add(new EmpManagementModel
                                {
                                    name_a = oasvm.legal_ename,
                                    name = oasvm.legal_cname,
                                    emplid = oasvm.legal_emplid
                                });
                            }
                        }
                        //去除重複數據
                        oasv.legal_emplid = new List<EmpManagementModel>(new HashSet<EmpManagementModel>(oasv.legal_emplid));
                        #endregion
                        oasv.current_levels = listGroupData[0].current_levels;
                        oasv.current_levels_pfn = listGroupData[0].current_levels_pfn;
                        oasv.current_signer = listGroupData[0].current_signer;
                        oasv.current_signer_over = listGroupData[0].current_signer_over;
                        oasv.is_invitee = listGroupData[0].is_invitee;
                        #endregion

                        #region 其他欄位
                        oasv.dept_type = listGroupData[0].dept_type;
                        oasv.dept_type_pfn = listGroupData[0].dept_type_pfn;
                        #region 循環必要揭露人員(內部同仁)
                        foreach (OtherApplicationSearchResultModel oasvm in listGroupData)
                        {
                            if (!string.IsNullOrEmpty(oasvm.expose_emplid))
                            {
                                oasv.expose_emplid.Add(new EmpManagementModel
                                {
                                    name_a = oasvm.expose_ename,
                                    name = oasvm.expose_cname,
                                    emplid = oasvm.expose_emplid
                                });
                            }
                        }
                        //去除重複數據
                        oasv.expose_emplid = new List<EmpManagementModel>(new HashSet<EmpManagementModel>(oasv.expose_emplid));
                        #endregion
                        oasv.expose_other_person = string.Join(", ", listGroupData.Where(s => !string.IsNullOrEmpty(s.expose_other_person)).Select(eo => eo.expose_other_person).Distinct().ToList());
                        oasv.retrieve_apply_number = string.Join(", ", listGroupData.Where(s => !string.IsNullOrEmpty(s.retrieve_apply_number)).Select(eo => eo.retrieve_apply_number).Distinct().ToList());
                        oasv.estimate_apply_number = string.Join(", ", listGroupData.Where(s => !string.IsNullOrEmpty(s.estimate_apply_number)).Select(eo => eo.estimate_apply_number).Distinct().ToList());
                        oasv.voided_apply_number = listGroupData[0].voided_apply_number;
                        oasv.seal_apply_number = listGroupData[0].seal_apply_number;
                        #region 我方主體
                        foreach (OtherApplicationSearchResultModel fem in listGroupData)
                        {
                            if (!string.IsNullOrEmpty(fem.entity_id))
                            {
                                oasv.entity_id.Add(new FnpEntityModel
                                {
                                    EntityId = fem.entity_id,
                                    EntityNamec = fem.entity_namec,
                                    EntityNamee = fem.entity_namee,
                                    Entity = fem.entity
                                });
                            }
                        }
                        //去除重複數據
                        oasv.entity_id = new List<FnpEntityModel>(new HashSet<FnpEntityModel>(oasv.entity_id));
                        #endregion
                        #region 他方
                        foreach (OtherApplicationSearchResultModel op in listGroupData)
                        {
                            if (!string.IsNullOrEmpty(op.other_party))
                            {
                                oasv.other_party.Add(op.other_party);
                            }
                        }
                        //去除重複數據
                        oasv.other_party = oasv.other_party.Distinct().ToList();
                        #endregion
                        #region 合約編號
                        foreach (OtherApplicationSearchResultModel op in listGroupData)
                        {
                            if (!string.IsNullOrEmpty(op.contract_number))
                            {
                                oasv.contract_number.Add(op.contract_number);
                            }
                        }
                        //去除重複數據
                        oasv.contract_number = oasv.contract_number.Distinct().ToList();
                        #endregion
                        #region 申請主體
                        foreach (OtherApplicationSearchResultModel fem in listGroupData)
                        {
                            if (!string.IsNullOrEmpty(fem.history_entity_id))
                            {
                                oasv.history_entity_id.Add(new FnpEntityModel
                                {
                                    EntityId = fem.history_entity_id,
                                    EntityNamec = fem.h_entity_namec,
                                    EntityNamee = fem.h_entity_namee,
                                    Entity = fem.h_entity
                                });
                            }
                        }
                        //去除重複數據
                        oasv.history_entity_id = new List<FnpEntityModel>(new HashSet<FnpEntityModel>(oasv.history_entity_id));
                        #endregion
                        #region 特殊主體權限
                        foreach (OtherApplicationSearchResultModel fem in listGroupData)
                        {
                            if (!string.IsNullOrEmpty(fem.special_entity_id))
                            {
                                oasv.special_entity_id.Add(new FnpEntityModel
                                {
                                    EntityId = fem.special_entity_id,
                                    EntityNamec = fem.s_entity_namec,
                                    EntityNamee = fem.s_entity_namee,
                                    Entity = fem.s_entity
                                });
                            }
                        }
                        //去除重複數據
                        oasv.special_entity_id = new List<FnpEntityModel>(new HashSet<FnpEntityModel>(oasv.special_entity_id));
                        #endregion
                        oasv.doc_type = listGroupData[0].doc_type;
                        oasv.doc_type_pfn = listGroupData[0].doc_type_pfn;
                        oasv.actual_date_start = listGroupData[0].actual_date_start;
                        oasv.actual_date_end = listGroupData[0].actual_date_end;
                        oasv.closed_date = listGroupData[0].closed_date;
                        #endregion
                    }
                    listResult.Add(oasv);
                }
            }
            #endregion

            return listResult;
        }
        #endregion
    }
}
