﻿using Elegal.Flow.Api.Repository.Individual;
using Elegal.Interface.Api.Common.Control.Filter;
using Elegal.Interface.Api.Common.Model.ResultModel;
using Elegal.Interface.Api.Common.Model.ResultModel.PermissionApi;
using Elegal.Interface.Api.Common.Model.ViewModel.FlowApi;
using Elegal.Interface.ApiData.Service.FuncService;
using Elegal.Interface.ApiData.Service.Model.DbModel;
using Elegal.Interface.ApiData.Service.Model.QueryCondition;
using Elegal.Orm;
using Elegal.Orm.Utils;
using iText.StyledXmlParser.Jsoup.Nodes;
using Minio.DataModel;
using Newtonsoft.Json;
using NPOI.SS.Formula.Functions;
using NPOI.Util;
using System;
using System.Linq;
using System.Security.Cryptography;
using System.Text;

namespace Elegal.Flow.Api.Services.Individual
{
    /// <summary>
    /// 首页区块管理
    /// </summary>
    public static class SysHomeBlockService
    {
        /// <summary>
        /// 默认布局
        /// </summary>
        public static readonly List<SysHomePositionModel> SysHomePositionModelList =
        [
            new(){ component = "HomeItemPaperComponent", id= "paperCase", x = 0, y = 0, w = 4, h = 2, minw = 0, minh = 0, title = "", code = "01", show = true, group = 1},
            new(){ component = "HomeItemPaperComponent", id= "applyCases", x = 0, y = 2, w = 4, h = 2, minw = 0, minh = 0, title = "", code = "02", show = true, group = 1},
            new(){ component = "HomeItemCardComponent", id= "remindCases", x = 0, y = 4, w = 4, h = 1, minw = 0, minh = 0, title = "", code = "03", show = true, group = 1},
            new(){ component = "HomeItemNewsComponent", id= "news", x = 4, y = 0, w = 11, h = 5, minw = 0, minh = 0, title = "", code = "04", show = true, group = 2},
            new(){ component = "HomeItemNewsComponent", id= "host", x = 0, y = 5, w = 15, h = 5, minw = 0, minh = 0, title = "", code = "05", show = true, group = 2},
        ];
        private static readonly SysHomeBlockRepository _repositort = new SysHomeBlockRepository();
        /// <summary>
        /// 获取区块信息
        /// </summary>
        /// <returns></returns>
        public static IEnumerable<SysHomeBlockModel> GetList()
        {
            return _repositort.GetList();
        }
        /// <summary>
        /// 保存区块信息
        /// </summary>
        /// <param name="list"></param>
        /// <returns></returns>
        public static bool Save(List<SysHomeBlockModel> list)
        {
            var oldBlocks = SysHomeBlockDataService.Query(new SysHomeBlockQueryCondition()).OrderBy(b => b.SortOrder);
            DbAccess.PerformInTransaction(content =>
            {
                content.BatchUpdate(list.Select(s =>
                {
                    s.ModifyTime = DateTime.UtcNow;
                    s.ModifyUser = MvcContext.UserInfo.current_emp;
                    return CommonUtil.Map<SysHomeBlockModel, SysHomeBlock>(s);
                }).ToArray());
                //状态变更时清除布局信息
                if (oldBlocks.Where(s => list.FirstOrDefault(f => f.Rowid.Equals(s.Rowid) && !f.Closeable.Equals(s.Closeable)) != null).Count() > 0)
                {
                    content.SqlExecute("truncate table sys_home_position");
                    content.SqlExecute("truncate table sys_home_block_status");
                    content.SqlExecute("update sys_timezone set home_remind = 1");
                }
                List<SysHomePositionModel> result = [];
                var group = list.GroupBy(g => g.GroupIndex).OrderBy(b => b.Key);
                #region 分组1
                var group1 = group.First(f => f.Key.Equals(1)).ToList();
                var oldGroup1 = SysHomePositionModelList.Where(w => w.group.Equals(1)).ToList();
                for (int i = 0; i < group1.Count; i++)
                {
                    var newBlock = oldGroup1.First(f => f.code.Equals(group1[i].Code));
                    newBlock.y = result.Sum(s => s.h);
                    result.Add(newBlock);
                }
                #endregion
                #region 分组2
                var group2 = group.First(f => f.Key.Equals(2)).ToList();
                var oldGroup2 = SysHomePositionModelList.Where(w => w.group.Equals(2)).ToList();
                result.AddRange(group2.Select((block, index) =>
                {
                    var old = oldGroup2[index];
                    var newBlock = CommonUtil.Map<SysHomePositionModel, SysHomePositionModel>(oldGroup2.First(f => f.code.Equals(block.Code)));
                    newBlock.x = old.x;
                    newBlock.y = old.y;
                    newBlock.w = old.w;
                    newBlock.h = old.h;
                    newBlock.show = true;
                    return newBlock;
                }).Where(w => w.show));
                #endregion
                SysHomePosition sysHomePosition = new SysHomePosition()
                {
                    Emplid = "sys.admin",
                    HomePosition = JsonConvert.SerializeObject(result),
                    CreateTime = DateTime.UtcNow,
                    CreateUser = "sys.admin",
                    HomeLock = 1
                };
                if (content.Exists<SysHomePosition>(new SysHomePosition() { Emplid = "sys.admin" })) content.Update(sysHomePosition);
                else content.Create(sysHomePosition);
            });
            var newBlocks = SysHomeBlockDataService.Query(new SysHomeBlockQueryCondition()).OrderBy(b => b.SortOrder);
            ActionFilter.InitLogRecord((log) =>
            {
                bool changeSeq = !oldBlocks.Select(s => { return new KeyValuePair<int?, string>(s.SortOrder, s.Code); }).SequenceEqual(newBlocks.Select(s => { return new KeyValuePair<int?, string>(s.SortOrder, s.Code); }));
                if (changeSeq)
                {
                    log.DetailFormer = string.Join(CommonUtil.GetDivisionLine() + "\r\n",
                        oldBlocks.Select(s =>
                        {
                            StringBuilder stringBuilder = new StringBuilder();
                            stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("HomeBlock_code", true)}：{s.Code}");
                            stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("HomeBlock_closeable", true)}：{ActionFilter.GetMultilingualValue($"commonWord:status_{s.Closeable}")}");
                            stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("HomeBlock_cTitle", true)}：{s.CTitle}");
                            stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("HomeBlock_eTitle", true)}：{s.ETitle}");
                            stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("HomeBlock_mark", true)}：{s.Remark}");
                            return stringBuilder.ToString();
                        }));
                    log.Detail = string.Join(CommonUtil.GetDivisionLine() + "\r\n",
                        newBlocks.Select(s =>
                        {
                            StringBuilder stringBuilder = new StringBuilder();
                            stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("HomeBlock_code", true)}：{s.Code}");
                            stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("HomeBlock_closeable", true)}：{ActionFilter.GetMultilingualValue($"commonWord:status_{s.Closeable}")}");
                            stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("HomeBlock_cTitle", true)}：{s.CTitle}");
                            stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("HomeBlock_eTitle", true)}：{s.ETitle}");
                            stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("HomeBlock_mark", true)}：{s.Remark}");
                            return stringBuilder.ToString();
                        }));
                }
                else
                {
                    var source = oldBlocks.Select(s => { return new HomeBlockCompareModel() { Code = s.Code, Closeable = s.Closeable, CTitle = s.CTitle, ETitle = s.ETitle, Remark = s.Remark }; }).ToList();
                    var target = newBlocks.Select(s => { return new HomeBlockCompareModel() { Code = s.Code, Closeable = s.Closeable, CTitle = s.CTitle, ETitle = s.ETitle, Remark = s.Remark }; }).ToList();
                    log.DetailFormer = string.Join(CommonUtil.GetDivisionLine() + "\r\n",
                        source.Except(target, new SaveBlockComparer()).Select(s =>
                        {
                            StringBuilder stringBuilder = new StringBuilder();
                            stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("HomeBlock_code", true)}：{s.Code}");
                            stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("HomeBlock_closeable", true)}：{ActionFilter.GetMultilingualValue($"commonWord:status_{s.Closeable}")}");
                            stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("HomeBlock_cTitle", true)}：{s.CTitle}");
                            stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("HomeBlock_eTitle", true)}：{s.ETitle}");
                            stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("HomeBlock_mark", true)}：{s.Remark}");
                            return stringBuilder.ToString();
                        }));
                    log.Detail = string.Join(CommonUtil.GetDivisionLine() + "\r\n",
                        target.Except(source, new SaveBlockComparer()).Select(s =>
                        {
                            StringBuilder stringBuilder = new StringBuilder();
                            stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("HomeBlock_code", true)}：{s.Code}");
                            stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("HomeBlock_closeable", true)}：{ActionFilter.GetMultilingualValue($"commonWord:status_{s.Closeable}")}");
                            stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("HomeBlock_cTitle", true)}：{s.CTitle}");
                            stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("HomeBlock_eTitle", true)}：{s.ETitle}");
                            stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("HomeBlock_mark", true)}：{s.Remark}");
                            return stringBuilder.ToString();
                        }));
                }
            });
            return true;
        }
        /// <summary>
        /// 获取个人区块信息
        /// </summary>
        /// <returns></returns>
        internal static List<SysHomeBlockStatusModel> GetBlockInfo()
        {
            var user = MvcContext.UserInfo;
            List<SysHomeBlock> sysHomeBlocks = SysHomeBlockDataService.Query(new SysHomeBlockQueryCondition() { Closeable = 1, OrderBys = [new() { Field = "sort_order", }] });
            var list = SysHomeBlockStatusDataService.Query(new SysHomeBlockStatusQueryCondition() { Emplid = user.current_emp });
            if (list.Count != 0) return list.Where(w => sysHomeBlocks.FirstOrDefault(f => f.Rowid.Equals(w.BlockId)) != null).Select(s =>
            {
                SysHomeBlock block = sysHomeBlocks.First(f => f.Rowid.Equals(s.BlockId));
                SysHomeBlockStatusModel model = CommonUtil.Map<SysHomeBlockStatus, SysHomeBlockStatusModel>(s);
                model.Title = user.logging_locale.Equals("ZH-TW") ? block.CTitle : block.ETitle;
                PsSubEeLglVwA psSubEeLglVwA = PsSubEeLglVwADataService.FindByKey(s.ModifyUser ?? s.CreateUser);
                model.SortOrder = block.SortOrder;
                model.operate_cuser = psSubEeLglVwA.Name;
                model.operate_euser = psSubEeLglVwA.NameA;
                model.operate_time = s.ModifyTime ?? s.CreateTime;
                return model;
            }).OrderBy(b => b.SortOrder).ToList();
            return sysHomeBlocks.Select(s =>
            {
                return new SysHomeBlockStatusModel()
                {
                    BlockId = s.Rowid,
                    Status = 1,
                    Title = user.logging_locale.Equals("ZH-TW") ? s.CTitle : s.ETitle
                };
            }).ToList();
        }
        /// <summary>
        /// 新增保存个人区块显示信息
        /// </summary>
        /// <param name="list"></param>
        /// <exception cref="NotImplementedException"></exception>
        internal static ApiResultModelByObject SaveAdd(List<SysHomeBlockStatusModel> list)
        {
            var block = SysHomeBlockDataService.Query(new() { Closeable = 1 });
            var user = MvcContext.UserInfo;
            DbAccess.PerformInTransaction(content =>
            {
                content.BatchCreate(list.Select(s =>
                {
                    return new SysHomeBlockStatus()
                    {
                        Emplid = user.current_emp,
                        BlockId = s.BlockId,
                        Status = s.Open ? 1 : 0,
                        CreateUser = user.current_emp,
                        CreateTime = DateTime.UtcNow
                    };
                }).ToArray());
                content.DeleteByKey<SysHomePosition, string>(user.current_emp);
            });
            ActionFilter.InitLogRecord((log) =>
            {
                log.Detail = string.Join(CommonUtil.GetDivisionLine() + "\r\n",
                    list.Select(s =>
                    {
                        StringBuilder stringBuilder = new StringBuilder();
                        stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("HomeBlock_title", true)}：{s.Title}");
                        stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("HomeBlock_homeOpen", true)}：{ActionFilter.GetMultilingualValue($"commonWord:status_{s.Status}")}");
                        return stringBuilder.ToString();
                    }));
                log.DetailFormer = string.Join(CommonUtil.GetDivisionLine() + "\r\n",
                    block.Select(s =>
                    {
                        StringBuilder stringBuilder = new StringBuilder();
                        stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("HomeBlock_title", true)}：{s.CTitle}");
                        stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("HomeBlock_homeOpen", true)}：{ActionFilter.GetMultilingualValue($"commonWord:status_1")}");
                        return stringBuilder.ToString();
                    }));
            });
            return new ApiResultModelByObject() { listData = true, rtnSuccess = true };
        }
        /// <summary>
        /// 编辑保存个人区块显示信息
        /// </summary>
        /// <param name="list"></param>
        /// <exception cref="NotImplementedException"></exception>
        internal static ApiResultModelByObject SaveEdit(List<SysHomeBlockStatusModel> list)
        {
            var user = MvcContext.UserInfo;
            var oldData = SysHomeBlockStatusDataService.Query(new SysHomeBlockStatusQueryCondition() { Emplid = user.current_emp });
            var blocks = GetList();
            DbAccess.PerformInTransaction(content =>
            {
                content.BatchUpdate(list.Select(s =>
                {
                    return new SysHomeBlockStatus()
                    {
                        Rowid = s.Rowid,
                        Status = s.Open ? 1 : 0,
                        ModifyUser = user.current_emp,
                        ModifyTime = DateTime.UtcNow
                    };
                }).ToArray());
                content.DeleteByKey<SysHomePosition, string>(user.current_emp);
            });
            var newData = SysHomeBlockStatusDataService.Query(new SysHomeBlockStatusQueryCondition() { Emplid = user.current_emp });
            ActionFilter.InitLogRecord((log) =>
             {
                 var source = oldData.Select(s => { return new KeyValuePair<int, int>(Convert.ToInt32(s.BlockId), Convert.ToInt32(s.Status)); }).ToList();
                 var target = newData.Select(s => { return new KeyValuePair<int, int>(Convert.ToInt32(s.BlockId), Convert.ToInt32(s.Status)); }).ToList();
                 log.DetailFormer = string.Join(CommonUtil.GetDivisionLine()+"\r\n",
                     source.Except(target, new EditBlockComparer()).Select(s =>
                     {
                         StringBuilder stringBuilder = new StringBuilder();
                         stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("HomeBlock_title", true)}：{blocks.First(f => f.Rowid.Equals(s.Key))?.CTitle}");
                         stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("HomeBlock_homeOpen", true)}：{ActionFilter.GetMultilingualValue($"commonWord:status_{s.Value}")}");
                         return stringBuilder.ToString();
                     }));
                 log.Detail = string.Join(CommonUtil.GetDivisionLine() + "\r\n",
                     target.Except(source, new EditBlockComparer()).Select(s =>
                     {
                         StringBuilder stringBuilder = new StringBuilder();
                         stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("HomeBlock_title", true)}：{blocks.First(f => f.Rowid.Equals(s.Key))?.CTitle}");
                         stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("HomeBlock_homeOpen", true)}：{ActionFilter.GetMultilingualValue($"commonWord:status_{s.Value}")}");
                         return stringBuilder.ToString();
                     }));
             });
            return new ApiResultModelByObject() { listData = true, rtnSuccess = true };
        }
        /// <summary>
        /// 完成引导页
        /// </summary>
        /// <returns></returns>
        internal static bool GuidePageCompleted()
        {
            return SysTimezoneDataService.Update(new SysTimezone() { Emplid = MvcContext.UserInfo.current_emp, Guided = 1 });
        }
    }
    #region
    /// <summary>
    /// 
    /// </summary>
    public class SaveBlockComparer : IEqualityComparer<HomeBlockCompareModel>
    {
        /// <summary>
        /// 
        /// </summary>
        /// <param name="x"></param>
        /// <param name="y"></param>
        /// <returns></returns>
        public bool Equals(HomeBlockCompareModel x, HomeBlockCompareModel y)
        {
            if (x == null || y == null)
                return false;

            return x.Closeable == y.Closeable &&
                   x.CTitle == y.CTitle &&
                   x.ETitle == y.ETitle &&
                   x.Remark == y.Remark;
        }
        /// <summary>
        /// 
        /// </summary>
        /// <param name="obj"></param>
        /// <returns></returns>
        /// <exception cref="ArgumentNullException"></exception>
        public int GetHashCode(HomeBlockCompareModel obj)
        {
            if (obj == null)
                throw new ArgumentNullException(nameof(obj));
            unchecked
            {
                int hash = 17;
                hash = hash * 23 + obj.Closeable?.GetHashCode() ?? 0;
                hash = hash * 23 + obj.CTitle?.GetHashCode() ?? 0;
                hash = hash * 23 + obj.ETitle?.GetHashCode() ?? 0;
                hash = hash * 23 + obj.Remark?.GetHashCode() ?? 0;
                return hash;
            }
        }
    }
    /// <summary>
    /// 
    /// </summary>
    public class EditBlockComparer : IEqualityComparer<KeyValuePair<int, int>>
    {
        /// <summary>
        /// 
        /// </summary>
        /// <param name="x"></param>
        /// <param name="y"></param>
        /// <returns></returns>
        public bool Equals(KeyValuePair<int, int> x, KeyValuePair<int, int> y)
        {
            return x.Key == y.Key &&
                   x.Value == y.Value;
        }
        /// <summary>
        /// 
        /// </summary>
        /// <param name="obj"></param>
        /// <returns></returns>
        /// <exception cref="ArgumentNullException"></exception>
        public int GetHashCode(KeyValuePair<int, int> obj)
        {
            unchecked
            {
                int hash = 17;
                hash = hash * 23 + obj.Key.GetHashCode();
                hash = hash * 23 + obj.Value.GetHashCode();
                return hash;
            }
        }
    }
    #endregion
}
