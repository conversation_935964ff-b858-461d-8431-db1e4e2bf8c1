﻿using Elegal.Flow.Api.Repository;
using Elegal.Interface.Api.Common.Control.Filter;
using Elegal.Interface.Api.Common.FuncHelper;
using Elegal.Interface.Api.Common.Model.ParaModel.FlowApi;
using Elegal.Interface.Api.Common.Model.ViewModel.FlowApi;
using Elegal.Interface.Api.Common.Repository;
using Elegal.Orm.Dtos;
using Elegal.Interface.Api.Common.FuncService;
using NPOI.SS.UserModel;
using NPOI.XSSF.UserModel;
using NUnit.Framework.Internal.Execution;

namespace Elegal.Flow.Api.Services
{
    /// <summary>
    /// 特殊主體設定 -> 人員設定
    /// </summary>
    public static class FnpEntityUserService
    {
        private static readonly FnpEntityUserRepository _repository = new FnpEntityUserRepository();
        private static readonly FnpEntityRepository _commonRepository = new FnpEntityRepository();

        public static PageResult<FnpEntityUserModel> Query(qryFnpEntityUser qry)
        {
            PageResult<FnpEntityUserModel> res = _repository.QueryUser(qry);

            if (res.Data != null && res.Data.Count > 0)
            {
                foreach (var item in res.Data)
                {
                    //託管部門主體
                    item.hosted_entity = _commonRepository.GetCustodyDeptEntity(item.emplid);

                    if (item.operate_time != null)
                        item.local_operate_time = TimeZoneInfoConvertHelper.ConvertDateByTimeZoneByUtc(item.operate_time, MvcContext.UserInfo.time_zone);
                }
            }

            return res;
        }

        public static byte[] ExportUserQuery(qryFnpEntityUser qry)
        {
            qry.page = new PageParam { PageIndex = 1, PageSize = 25 };

            PageResult<FnpEntityUserModel> res = _repository.QueryUser(qry);

            if (res.Data != null && res.Data.Count > 0)
            {
                foreach (var item in res.Data)
                {
                    //託管部門主體
                    item.hosted_entity = _commonRepository.GetCustodyDeptEntity(item.emplid);

                    if (item.operate_time != null)
                        item.local_operate_time = TimeZoneInfoConvertHelper.ConvertDateByTimeZoneByUtc(item.operate_time, MvcContext.UserInfo.time_zone);
                }
            }
            //创建工作表
            IWorkbook workbook = new XSSFWorkbook();
            ISheet sheet = workbook.CreateSheet("Sheet1");
            //设置表头
            List<string> heards = new List<string>()
            {
                ActionFilter.GetMultilingualValue("export:export_fnpEntityUser:employeeId"),
                ActionFilter.GetMultilingualValue("export:export_fnpEntityUser:name"),
                ActionFilter.GetMultilingualValue("export:export_fnpEntityUser:employeeStatus"),
                ActionFilter.GetMultilingualValue("export:export_fnpEntityUser:companyCodeDept"),
                ActionFilter.GetMultilingualValue("export:export_fnpEntityUser:deptEntity"),
                ActionFilter.GetMultilingualValue("export:export_fnpEntityUser:custodianCompany"),
                ActionFilter.GetMultilingualValue("export:export_fnpEntityUser:custodyDeptEntity"),
                ActionFilter.GetMultilingualValue("export:export_fnpEntityUser:specialEntitys"),
                ActionFilter.GetMultilingualValue("export:export_fnpEntityUser:updateTime")
            };

            sheet.SetHeader(heards);

            //設置列寬
            sheet.SetColumnAutoWidth(0, 8);


            ICellStyle defaultstyle = sheet.Workbook.CreateCellStyle().SetDefaultBorder().SetwrapTextOn();
            //寫入數據
            sheet.WriteData(res.Data, (data, row) =>
            {
                //因主職部門須放在第一行，故主副職部門需分開給

                //先處理主職部門
                //公司欄位格式為 公司/部門
                List<string> companyList = new List<string>();
                List<string> entityList = new List<string>();
                companyList.Add(data.ee_company + "/" + data.ee_deptid);
                entityList.Add(data.ee_entity);

                //處理副職部門
                if (!string.IsNullOrEmpty(data.cj_dept))
                {
                    //分號(;)作為多筆副職部門分隔
                    List<string> cj_deptList = data.cj_dept.Split(";").ToList();
                    foreach (var item in cj_deptList)
                    {
                        //垂直線(|)作為 公司/部門 與 副部門主體分隔
                        if (item.Contains("|"))
                        {
                            var temp = item.Split("|").ToList();
                            companyList.Add(temp[0]);
                            entityList.Add(temp[1]);
                        }
                    }
                }

                  row.CreateElegalDefaultCell(0, defaultstyle).SetCellValue(data.emplid);
                  row.CreateElegalDefaultCell(1, defaultstyle).SetCellValue(data.name_a + "(" + data.name + ")");
                  row.CreateElegalDefaultCell(2, defaultstyle).SetCellValue(data.emp_status == 1 ? ActionFilter.GetMultilingualValue("commonWord:incumbent_1") : ActionFilter.GetMultilingualValue("commonWord:incumbent_0"));
                  row.CreateElegalDefaultCell(3, defaultstyle).SetCellValue(string.Join("\n", companyList));
                  row.CreateElegalDefaultCell(4, defaultstyle).SetCellValue(string.Join("\n", entityList));
                  row.CreateElegalDefaultCell(5, defaultstyle).SetCellValue(data.ee_company);
                  row.CreateElegalDefaultCell(6, defaultstyle).SetCellValue(data.hosted_entity);
                  row.CreateElegalDefaultCell(7, defaultstyle).SetCellValue(data.user_entity + "\n" + data.dept_entity);
                row.CreateElegalDefaultCell(8, defaultstyle).SetCellValue(data.local_operate_time != null ? data.local_operate_time.ToString("yyyy/MM/dd HH:mm") + "/" + data.operate_euser : string.Empty);

            });

            return workbook.ToBytes();


            
            /*
            ExcelPackage.LicenseContext = LicenseContext.NonCommercial; // 設置LicenseContext屬性

            if (res.Data != null)
            {
                using (var package = new ExcelPackage())
                {
                    var worksheet = package.Workbook.Worksheets.Add("Sheet1");

                    // 添加Header
                    var headerRange = worksheet.Cells["A1:I1"];
                    headerRange.Style.Font.Bold = true; // 粗體
                    headerRange.Style.Border.Top.Style = ExcelBorderStyle.Medium;
                    headerRange.Style.Border.Bottom.Style = ExcelBorderStyle.Medium;
                    headerRange.Style.Border.Left.Style = ExcelBorderStyle.Medium;
                    headerRange.Style.Border.Right.Style = ExcelBorderStyle.Medium;
                    headerRange.Style.Font.Size = 12;
                    headerRange.Style.VerticalAlignment = ExcelVerticalAlignment.Center;

                    // 添加Header
                    worksheet.Cells["A1"].Value = ActionFilter.GetMultilingualValue("export:export_fnpEntityUser:employeeId");
                    worksheet.Cells["B1"].Value = ActionFilter.GetMultilingualValue("export:export_fnpEntityUser:name");
                    worksheet.Cells["C1"].Value = ActionFilter.GetMultilingualValue("export:export_fnpEntityUser:employeeStatus");
                    worksheet.Cells["D1"].Value = ActionFilter.GetMultilingualValue("export:export_fnpEntityUser:companyCodeDept");
                    worksheet.Cells["E1"].Value = ActionFilter.GetMultilingualValue("export:export_fnpEntityUser:deptEntity");
                    worksheet.Cells["F1"].Value = ActionFilter.GetMultilingualValue("export:export_fnpEntityUser:custodianCompany");
                    worksheet.Cells["G1"].Value = ActionFilter.GetMultilingualValue("export:export_fnpEntityUser:custodyDeptEntity");
                    worksheet.Cells["H1"].Value = ActionFilter.GetMultilingualValue("export:export_fnpEntityUser:specialEntitys");
                    worksheet.Cells["I1"].Value = ActionFilter.GetMultilingualValue("export:export_fnpEntityUser:updateTime");

                    var row = 2;
                    foreach (FnpEntityUserModel data in res.Data)
                    {
                        var dataRange = worksheet.Cells[$"A{row}:I{row}"];
                        // 邊框
                        dataRange.Style.Border.Top.Style = ExcelBorderStyle.Medium;
                        dataRange.Style.Border.Bottom.Style = ExcelBorderStyle.Medium;
                        dataRange.Style.Border.Left.Style = ExcelBorderStyle.Medium;
                        dataRange.Style.Border.Right.Style = ExcelBorderStyle.Medium;
                        dataRange.Style.Font.Size = 12;
                        dataRange.Style.VerticalAlignment = ExcelVerticalAlignment.Center;

                        //因主職部門須放在第一行，故主副職部門需分開給

                        //先處理主職部門
                        //公司欄位格式為 公司/部門
                        List<string> companyList = new List<string>();
                        List<string> entityList = new List<string>();
                        companyList.Add(data.ee_company + "/" + data.ee_deptid);
                        entityList.Add(data.ee_entity);

                        //處理副職部門
                        if (!string.IsNullOrEmpty(data.cj_dept))
                        {
                            //分號(;)作為多筆副職部門分隔
                            List<string> cj_deptList = data.cj_dept.Split(";").ToList();
                            foreach (var item in cj_deptList)
                            {
                                //垂直線(|)作為 公司/部門 與 副部門主體分隔
                                if (item.Contains("|"))
                                {
                                    var temp = item.Split("|").ToList();
                                    companyList.Add(temp[0]);
                                    entityList.Add(temp[1]);
                                }
                            }
                        }

                        worksheet.Cells[$"A{row}"].Value = data.emplid;
                        worksheet.Cells[$"B{row}"].Value = data.name_a + "(" + data.name + ")";
                        worksheet.Cells[$"C{row}"].Value = data.emp_status == 1 ? ActionFilter.GetMultilingualValue("commonWord:incumbent_1") : ActionFilter.GetMultilingualValue("commonWord:incumbent_0");
                        worksheet.Cells[$"D{row}"].Value = string.Join("\n", companyList);
                        worksheet.Cells[$"E{row}"].Value = string.Join("\n", entityList);
                        worksheet.Cells[$"F{row}"].Value = data.ee_company;
                        worksheet.Cells[$"G{row}"].Value = data.hosted_entity;
                        worksheet.Cells[$"H{row}"].Value = data.user_entity + "\n" + data.dept_entity;
                        worksheet.Cells[$"I{row}"].Value = data.local_operate_time != null ? data.local_operate_time.ToString("yyyy/MM/dd HH:mm") + "/" + data.operate_euser : string.Empty;

                        row++;
                    }

                    worksheet.Columns.AutoFit();

                    worksheet.Columns.Style.WrapText = true;

                    return package.GetAsByteArray();
                }
            }

            return null;
            */
        }

        /// <summary>
        /// 修改畫面回顯
        /// </summary>
        public static List<FnpEntityUserDetailModel> QueryDetails(string? emplid)
        {
            if (string.IsNullOrEmpty(emplid))
                throw new Exception(ActionFilter.GetMultilingualValue("custom:messageTitle:systemError"));

            List<FnpEntityUserDetailModel> res = new List<FnpEntityUserDetailModel>();

            if (!string.IsNullOrEmpty(emplid))
                res = _repository.GetUserDetail(emplid);

            if (res != null && res.Count > 0)
            {
                foreach (var item in res)
                {
                    if (item.operate_time != null)
                        item.local_operate_time = TimeZoneInfoConvertHelper.ConvertDateByTimeZoneByUtc(item.operate_time, MvcContext.UserInfo.time_zone);
                }
            }

            return res;
        }

        /// <summary>
        /// 儲存變更
        /// </summary>
        public static bool UpdateFnpEntityUser(string emplid, List<string> entity_id_list)
        {
            if (!_repository.UpdateFnpEntityUser(emplid, entity_id_list))
                throw new Exception(ActionFilter.GetMultilingualValue("custom:messageTitle:createFail"));
            if (!_repository.UpdateFnpEntityUserSpecial(emplid, entity_id_list))
                throw new Exception(ActionFilter.GetMultilingualValue("custom:messageTitle:createFail"));

            return true;
        }

        public static List<string> DeleteFnpEntityUserByEntityId(string entity_id)
        {
            if (string.IsNullOrEmpty(entity_id))
                throw new Exception(ActionFilter.GetMultilingualValue("custom:messageTitle:systemError"));
            return _repository.DeleteFnpEntityUser(entity_id);
        }
    }
}
