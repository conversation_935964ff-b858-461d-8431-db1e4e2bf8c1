{
  "error": {
    "400": "",
    "401": "Unauthorized. Please log in.",
    "403": "Access Denied",
    "404": "Error in requesting resource",
    "405": "HTTP method not allowed",
    "408": "Request timeout",
    "500": "Internal server error",
    "501": "Service not implemented",
    "502": "Gateway error",
    "503": "Service Unavailable",
    "504": "Gateway timeout",
    "505": "HTTP version not supported",
    "601": "Email sending failed",
    "602": "IT Test account"
  },
  "commonWord": {
    "information": "Notice",
    "add": "add",
    "all": "all",
    "addedAgentDepartment": "The following department's proxy have been set.s",
    "existingAgentDepartment": "The following department has been authorized to other personnel.",
    "repeat": "duplicate data",
    "incumbent_1": "Incumbency",
    "incumbent_0": "Dimission",
    "fileName": "FileName",
    "DepartmentCode": "Department",
    "DepartmentAndCompany": "Company code of Dept.",
    "retrieve": "Retrieve",
    "count": "Total {0} records",
    "status_1": "啟用",
    "status_0": "Disenable",
    "newCase": "New Application",
    "oldCase": "Old Application",
    "isReturn": "Returned",
    "isReject": "Rejected",
    "isInvitee": "Inviting",
    "isAcknowledge": "Acknowledged"
  },
  "custom": {
    "messageTitle": {
      "fileExist": "A file for this purpose already exists",
      "uploadSuccess": "upload successful",
      "systemError": "System Error",
      "doNotUpdate": "modification not allowed",
      "doNotDelete": "deletion not allowed",
      "createSuccess": "addition successful",
      "createFail": "addition failed",
      "updateFail": "Modification failed",
      "dataExists": "Data already exists  ",
      "dataNotexist": "Data does not exist",
      "dataUsed": "Data is in use",
      "fileNotexist": "Document does not exist",
      "enableFail": "entity active failed",
      "fileUploadFail": "Document upload failed",
      "fileDeleteFail": "Document deletion failed",
      "fileDownloadFail": "Document download failed",
      "selectData": "Please select the data.",
      "editFail": "Update Fail",
      "toPdfFail": "File conversion failed",
      "approveFail": "Approve Fail",
      "contractNumberError": "Unable to generate contract_number",
      "previewFail": "Preview Fail",
      "noAuthorizedRecipient": "Non authorized recipient",
      "exportError": "Export Abnormal"
    },
    "messageContent": {
      "repeatUpload": "File [{0}] already exists, please re-upload!",
      "fileUploadFiltered": "Document upload completed. The system has filtered out the following empty documents.",
      "fileUpload": "File uploaded",
      "validateError": "Parameter validation conversion error, please contact the system administrator (check the contact information on the homepage - handling window).",
      "notFindByDB": "File [{0}] data does not exist",
      "notToPdf": "File [{0}] does not support online reading; please check if it is a compressed file or password-protected",
      "notToPdfAndWatermark": "File [{0}] does not support downloading watermarked versions",
      "selectDownloadFiles": "Please select the file to download",
      "selectDeleteFiles": "Please select the file to delete",
      "fileSizeMax": "The total size of all files exceeds 80MB",
      "repeatFiles": "The following documents are duplicate uploads, do you want to choose to overwrite them?",
      "dataExists": "duplicate data : {0}",
      "searchreQuired": "Query criteria is required : {0}",
      "repeatAll": "Failed to add item, the selected data already exists",
      "noTemplateFile": "You are attempting to download a file that does not exist.",
      "repeatMessage": "The following data already exists, please re-enter.",
      "dataChurn": "This data may have changed. It is recommended to check the data update time or reload the page to obtain the latest status.",
      "dataUsed": "This data is in use and cannot be deleted.",
      "AgentsExist": "This department has been authorized to other personnel, dual authorization is not allowed.",
      "mailContentEmpty": "The email template is missing, please contact the management personnel!",
      "approverLevelNotEdit": "The entity has been deactivated, and the approval level cannot be modified",
      "roleNameNotEdit": "此角色的'角色名稱'不可修改",
      "roleNotAdd": "該角色名稱不允許新增",
      "levelRange": "簽核層級範圍[-1,9]",
      "endLessStart": "起始簽核層級不可小於最高簽核層級",
      "notZero": "簽核層級不可為0",
      "noRecordDaysSetting": "未找到系統記錄保留天數配置信息",
      "mailFail": "Email sending failed",
      "mailSuccess": "Email sent successfully",
      "publicRoleNameExists": "公版角色名稱：{0}",
      "systemError": "Error Message：{0}",
      "RoleNameExists": "角色名稱已存在，請重新輸入。",
      "createSuccess": "已成功新增項目，系統已自動過濾已存在資料",
      "modifySuccess": "已成功修改項目，系統已自動過濾異動資料",
      "keepOne": "管理員角色至少保留一個人員",
      "keepOneContract": "以下合約理員角色至少保留一個人員,{0}",
      "roleDeleted": "角色已被刪除",
      "nullArea": "區域不允許為空",
      "empidIsEmpty": "工號為空",
      "keepOneApproverManagement": "同一主體下的總部法務行政必須至少有一人",
      "entityDeleted": "主體已被刪除",
      "contractDeleted": "合約性質已被刪除",
      "contractViewSelectType2": "自訂主體，請選擇主體數據",
      "approverManagementDeleted": "關卡已被刪除",
      "directorsSupervisorsDeleted": "董監事職稱已被刪除",
      "sealCustodianDeleted": "用印類型已被刪除",
      "accountTypeDeleted": "類別已被刪除",
      "doNotDeleteRole": "內置角色不允許刪除",
      "doNotUpdateRole": "內置角色不允許修改名稱",
      "StartTimeLargerThanEndTime": "結束時間應該大於開始時間",
      "AgentExist": "This department has already been authorized to another person and cannot be authorized again!",
      "uploadMessageTitle": "Document upload error",
      "uploadMessageContent": "Document upload error, please try again",
      "downloadMessageTitle": "Document download error",
      "downloadMessageContent": "Document download error, please try again",
      "bulletinsNewSubjectLength": "The announcement subject must contain less than 200 characters",
      "bulletinsTabCannotModify": "The seal custody window (01) cannot be modified.",
      "bulletinsTabColumnLength": "The field length is limited to 15 characters in Chinese or 30 characters in English.",
      "globalContentMaxLength": "The character length should not exceed {0} characters",
      "webSuspendStartDate": "Please enter the start date!",
      "webSuspendStartTime": "Please enter the start time!",
      "webSuspendCloseMessage": "Please enter the closing prompt language!",
      "webSuspendGlobalTitle": "Please enter a global prompt title!",
      "webSuspendEndDateTime": "Please enter the full end date and end time.",
      "entityEnableMessage": "請先設置總部法務行政人員",
      "contractManagerMessage": "請設置合約管理員至少為一人",
      "areaNameExist": "區域名稱已存在，請重新輸入",
      "areaUsed": "所選區域已有設定主體資訊，請確認！",
      "shutdownContentMaxLength": "關站提示語不應超過 {0} 字",
      "shutdownNowDateLargerStartDate": "關站開始時間不能小於當前時間！",
      "shutdownHasValue": "還有待公告/待執行設定，請等待執行完成後再新增作業！",
      "cancelSuspendStatus": "該筆關站數據狀態已變動，請刷新頁面！",
      "keepOneApprover": "主體：{0} 在總部法務行政必須至少有一人",
      "sameUnderAgent": "項目：{0} 承辦人和代理人不能為同一個人",
      "sameDirectorsSupervisors": "職稱：{0} ；主體：{1} 存在相同數據",
      "sameSealCustodianManage": "用印種類：{0} ；主體：{1} 存在相同數據",
      "sameUnderItem": "項目：{0} 有相同數據",
      "areaDataNotExists": "區域數據不存在",
      "entityNotInNews": "公告中不存在該主體數據",
      "paraRequired": "請檢查是否有選擇查詢類型以及輸入關鍵字！",
      "paraMaxLength": "參數中/英文不得超過 {0} 字！",
      "nullPara": "參數中/英文名稱必填！",
      "nullParaValue": "請填寫參數值！",
      "paraValueMaxLength": "參數值中/英文不得超過 {0} 字！",
      "emptyParaValue": "參數值中/英文名稱必填！",
      "sameParaValue": "參數值：{0} 已存在！",
      "bulletinsNewTime": "公告起迄開始、結束時間需大於或等於公告日期",
      "nikeNameFileError": "Please verify whether the document is empty or contains the specific field (Code Name). If unsure, please refer to the sample document!",
      "nikeMissPara": "Necessary parameters are missing, please contact the developer!",
      "nikeMaxWord": "Nickname: {0} exceeds the maximum length of {1} characters, please modify!",
      "empRole": "角色：{0} 可能已經發生變動，建議重新載入頁面，以獲取該人員最新角色信息",
      "empApprove": "關卡：{0} 可能已經發生變動，建議重新載入頁面，以獲取該人員最新關卡信息",
      "empUndertak": "項目：{0} 可能已經發生變動，建議重新載入頁面，以獲取該人員最新承辦窗口信息",
      "empSpecialEntity": "主體：{0} 可能已經發生變動，建議重新載入頁面，以獲取該人員最新主體信息",
      //Issue：309
      "deleteApproveType": "關卡：{0}，存在未完成單號 {1}，請確認是否仍需刪除。",
      "deleteApproveEntityID": "關卡：{0}；主體：{1}，存在未完成單號 {2}，請全部完成後再進行刪除作業！",
      "empDirectorsSupervisors": "職稱：{0} ；主體：{1} 可能已經發生變動，建議重新載入頁面，以獲取該人員最新職稱信息",
      "empSealCustodianManage": "用印種類：{0} ；主體：{1} 可能已經發生變動，建議重新載入頁面，以獲取該人員最新用印種類信息",
      "authLoginError_AuthCodeMissed": "User: {0} has not set the pass code.",
      "authLoginError_AuthCodeError": "incorrect authorization code",
      "authLoginError_AuthCodeDisable": "authorization code not activated",
      "scheduleJobNameExist": "自動化批次排程任務名重複",
      "AutoTimeEarly": "設置時間不能早於07:20",
      "AutoWeeklyJob": "每週作業的作業運行間隔無效",
      "AutoMonthlyJob": "每月作業的作業運行間隔無效",
      "AutoInvaildType": "作業運行類型無效",
      "authLoginError_EmplidMissed": "取得人事資料失敗",
      "authLoginError_EmplidTermination": "人員已離職",
      "actualCompanyHavaBeenBound": "存在實際公司代碼已經綁定[{0}]主體",
      "RemoveCompanyCode": "將移除當前主體的授權公司代碼({0})",
      "RemoveSpecialEntityUser": "移除特殊主體人員設定({0})",
      "RemoveSpecialEntityDept": "移除特殊主體部門設定({0})",
      "RemoveSpecialEntityApply": "特殊主體關聯申請單({0})",
      //SIT Issue:485
      "nofeSendMail": "主體郵件通知被停用",
      //UAT Issue:165  Start
      "entityIsUsedOtherEntity": "並將移除以下主體的授權公司代碼({0})，共{1}筆",
      "entityIsUsedOnlyOtherEntity": "以下賦予啟用主體公司代碼僅有({0})將無法移除，共{1}筆，請確認",
      //UAT Issue:165  End
      "getPaperDataFail": "The current form number does not exist in the database table, please verify before proceeding.",
      "paperLending": "The current form number is on loan and cannot be modified.",
      "batchPaperWork_LostOrDestroyed": "Form numbers that are reserved, on loan, or already destroyed cannot be modified.",
      "batchPaperWork_LostOrDestroyed_ApplyNumber": "The following numbers are reserved, on loan, or already destroyed and cannot be modified: {0}",
      "OldPaperData_repeatData": "Successfully added {0} hard copy records. The system has automatically filtered out existing records: {1}.",
      "OldPaperData_repeat": "The following hard copies already exist in the system and cannot be added: {0}",
      "LendingError": "The following hard copies are not in a stored status and cannot be loaned out:",
      "Inbound_Status": "Warehousing Status : ",
      "Storage_Location": "Location : ",
      "lendReturnCheck": "The return data may have changed. It is recommended to reload the page to obtain the latest data.",
      "paperDeletedError": "Hard copies with existing loan applications cannot be deleted.",
      "paperModifiedError": "Hard copies with existing loan applications cannot have their hard copy numbers modified.",
      "TempApplicationExist": "temporary form already exists",
      "ApplicationChanged": "There are changes in the hard copy data, please confirm again.",
      "AuthCodeNull": "Pass code cannot be empty.",
      "AuthCodeError": "Pass code length must be 8-20 characters and include a combination of at least three of the following: uppercase letters, lowercase letters, numbers, special symbols",
      //SIT：604 -> 更改提示信息
      "LoginError": "Reminder: HR data update is in progress, please try again later.",
      "RemoveFnpEntityUser": "已移除當前主體的特殊主體設定-人員設定:",
      "RemoveFnpEntityDept": "已移除當前主體的特殊主體設定-部門設定:",
      "SelectData": "Please select the data and proceed with the operation",
      "Form_FileNoExist": "Please upload at least one contract and attachment before submitting.",
      "FileNoExist": "Please upload at least one attachment before submitting.",
      "FileNoExist_References": "Please upload at least one reference material before submitting.",
      "FormEnd_Validate_Date": "Please fill in the confirmation effective date/confirmation signing date/confirmation expiration date.",
      "FormEnd_Validate_ArchiveStatus": "Please fill in the archiving status.",
      "FormEnd_Validate_File": "Please upload at least one document to the archive area.",
      "FormEnd_Validate_Paper": "Please create a hard copy list.",
      "FormEnd_Validate_ApplyNumber": "Please verify if the application number exists.",
      "FormEnd_Validate_ContractNumber": "Before performing this operation, please complete the [Contract Number] field in 'Contract Management Operation.'",
      "FormEnd_Validate_Same_ContractNumber": "The [Contract Number] field in 'Contract Management Operation' contains the same information.",
      "RefNumberError": "找不到REF主約對應的主體合約編號",
      "BeforeEntityConsistency": "The previous entity abbreviation cannot be the same as the current entity abbreviation.",
      "StepSignerNull": "The [{0}] stage has no personnel assigned. Please contact the system administrator (check the handling window on the homepage - contact information).",
      //Issue：367 -> 簽核人員提示信息修改
      "SignerIsNull": "The [{0}] stage has no personnel assigned. Please contact the system administrator (check the handling window on the homepage - contact information).",
      "StepSignerError": "Approval stage error",
      "McpSigning": "Approved by MCP",
      "Bypass_CEO": "Due to {0}, it needs to be forwarded to the President's stage",
      "Bypass_President": "Due to {0}, it needs to be forwarded to the Chairman / Representative's stage",
      "EntityDifferent": "The entity abbreviation does not match the main contract number.",
      "EntityExistApplication": "The entity has existing applications, {0}.",
      "NoApproveAuth": "No approval permissions",
      "ApplyInProcess": "Application form is in process",
      "OtherFormTypeError": "Other application form type error",
      "OtherInProcess": "For case {0}, there is an ongoing application {1} with form {2}. Please confirm again.",
      "TypeBNoData": "指定的條件查無資料，請重新選擇",
      "StartDate": "start date",
      "EndDate": "end date",
      "StartEndDate": "start and end dates",
      "ActualDateError": "The actual open period ({0}) is earlier than the current date; the system automatically changes it to the closing date.",
      "AddPaperApplicationError": "The application form has been confirmed as having no hard copy, addition failed.",
      "NoToPdfWatermark": "The selected file cannot have a watermark added.",
      "repeatPaper": "Duplicate hard copies, please re-enter.",
      "SigntoryNotChange": "The approval personnel did not submit modifications",
      "SigntoryCurrentSignerLessOne": "There are fewer than one person pending review currently",
      "StepStartNotAddSinger": "In the applicant/PIC phase, invitee cannot be invited.",
      "noAuthorizedRecipient": "This person is not an authorized recipient.",
      "readError": "Failed to read information, please try the operation again or contact the administrator.",
      "builtinRoleNotUpdate": "Built-in roles do not allow name modifications",
      "CeoOrPresidentStepNull": "Initially, stage {0} had no personnel assigned. The system now confirms that {1} has been assigned to this stage. Are you sure you want to approve up to stage {0}?",
      //Issue：92
      "CeoOrPresidentStepNullByNoPass": "Stage {0} initially had no personnel assigned.",
      "HanderNotSamePic": "Assignee、PIC/New PIC  cannot be the same person",
      "EntityExits": "The company abbreviation is duplicated, please re-enter!",
      "UnderTakeWindowAgentExits": "代理人：{0} {1}；承辦窗口聯絡人：{2} {3}不能是同一人",
      "UnderTakeWindowDataExits": "項目：{0}；承辦窗口聯絡人：{1} {2}",
      //Issue：500
      "InvateNotSign": "The invitiee has not approved yet!",
      //Issue：200
      "supplementary_reason": "Due to a return that meets the co-signing rules, additional supervisors have been added for awareness of this case: {0}",
      "hasInviteeSigner": "There is still a list of additional signatories. Please go to [invite] to confirm whether to invite.",
      //在線預覽相關
      "againLater": "file conversion failed, please try again later.",
      //附件下載
      "fileNull": "Document ({0}) does not exist.",
      //Issue：381
      "MCPApprovalInProgress": "MCP approval in progress",
      //SIT：638
      "InviterIsNull": "Please select the person to add the signature"
    }
  },
  "export": {
    "lendId": "Loan application number",
    "accessNumber": "Other application number",
    "filler": "Applicant",
    "handler": "Person-in-charge",
    "applyDate": "Application Date",
    "applyStatus": "Application Status",
    "contractNum": "Contract number",
    "paperId": "hard Copy number",
    "paperName": "hard CopyName",
    "inboundStatus": "Warehousing Status",
    "copiesStatus": "hard Copy Status",
    "saveLocation": "Location",
    "maxBorrowDay": "Days of Max Lending",
    "shouldReturnDate": "Due Date",
    "returnDate": "return date",
    "pickUp": "Picked up or not",
    "returnStatus": "Return Status",
    "overdueDays": "Overdue Days",
    "returnPaperStatus": "current return condition",
    "hasPickUp": "Picked up",
    "noPickUp": "not picked up",
    "hasReturn": "Returned",
    "noReturn": "Not Returned",
    "export_fnpEntityUser": {
      "employeeId": "員工工號",
      "name": "員工姓名",
      "employeeStatus": "任職狀態",
      "companyCodeDept": "部門公司別/部門代碼",
      "deptEntity": "部門所屬主體",
      "custodianCompany": "託管人員公司別",
      "custodyDeptEntity": "託管部門主體",
      "specialEntitys": "特殊主體",
      "updateTime": "更新時間"
    },
    "export_fnpEntityDept": {
      "deptid": "部門代號",
      "descr": "部門名稱",
      "ogCompany": "部門公司別",
      "ogEntity": "部門所屬主體",
      "hostedCompany": "託管人員公司別",
      "hostedEntity": "託管部門主體",
      "specialEntitys": "特殊主體",
      "updateTime": "更新時間"
    },
    "export_ExportPaperBasicData": {
      "paper_code": "Hard copy number.",
      "contract_number": "Contract Number",
      "paper_name": "Hard copy name.",
      "paper_entry_status_name": "Warehousing Status",
      "paper_position": "Location",
      "paper_return_status_name": "Return Status",
      "my_entity": "Legal Entity ID",
      "party_a": "Counter Party",
      "paper_type_name": "Hard Copy Type",
      "confiden_level_name": "Classification level"
    }
  },
  "ByPassReason": {
    "Evacuation": "The current stage approver will sign at President/Chairman / Representative stage, therefore the system will bypass this stage.",
    "Signed": "The current stage approver has already signed in a previous stage, therefore the system will bypass this stage.",
    "EqualAgent": "The current stage approver is the same as the Applicant / PIC, therefore the system will bypass this stage.",
    "EqualPresident": "The current stage approver will sign at Chairman / Representative stage, therefore the system will bypass this stage.",
    "Same": "Due to the same stage personnel at {0}, it is forwarded to {1}.",
    "Assign": "According to the approval authority table, it needs to be sent to {0}",
    "None": "The system determined that approval is not required for the President > Chairman / Representative stage.",
    "SiteGM": "Since it is not the affiliated entity and lacks a SiteGM, it needs to be forwarded to {0}.",
    "NoneCeo": "According to the approval authority table, it needs to be sent to the President. However, since the President is not set as the main stage personnel, it is sent to the Chairman / Representative.",
    //Issue：283
    "NoneCeoByPresident": "According to the approval authority table, it needs to be sent to the President > Chairman / Representative. However, since the President is not set as the main stage personnel, it is sent to the Chairman / Representative."
  },
  //項目中其他需要多語言清單匯總
  "otherLang": {
    //檔案附件
    "fileLang": {
      "ArchivePurposes1": "Contracts and Attachments",
      "ArchivePurposes2": "References",
      "ArchivePurposes3": "archive"
    },
    //紙本進度追蹤
    "paperTrackingBathJob": {
      "paperTracking01": "Notify the PIC to provide the hard copy to the legal admin.",
      "paperTracking02": "Notify Legal PIC to pick up  the hard copy.",
      "paperTracking05": "Notify the PIC that hard copies has been sent for signing/stamping .",
      "paperTracking07": "Notify the PIC to pick up the hard copy from the legal admin.",
      "paperTracking09": "Notify the PIC that the hard copy has been shipped.",
      "paperTracking00": "No email notification is required."
    }
  },
  //XML文檔多語言匯總
  "XmlLang": {
    //XML頭部
    "xmlTitles": {
      "applicationType": "Process Category",
      "stepName": "Current Step",
      "normalSigner": "Assigned Approver",
      "inviteSigner": "invitee",
      "applyInfo": "Applicants Information",
      "financeInfo": "Financial Information",
      "undertakeInfo": "HQ Legal PIC Information",
      "undfinanceInfo": "Local Finance Information",
      "acknowledgeInfo": "AcknowledgeType",
      "legalAdminInfo": "HQ Legal Admin Information",
      "originalSignerInfo": "Authorize to Signing Representative",
      "inviterInfo": "Inviter Information",
      "flowHistoryInfo": "Approval History",
      "inviteeInfo": "Designated Invitee List",
      "otherACDCaseInfo": "Case",
      "otherEEntityInfo": "Apply Legal Entity",
      "otherFEntityInfo": "Exceptional Entity permissions",
      "otherInterPersonInfo": "List of disclosure members - internal colleagues",
      "otherOuterPersonInfo": "Other"
    },
    //XML區塊
    "xmlBlock": {
      "applyNumber": "Application No.",
      "picEmplid": "Person-In-Charge",
      "picDeptid": "Person-In-Charge Dept.",
      "myEntity": "Legal Entity",
      "otherParty": "Counter Party (including abbreviation)",
      "isSchool": "The other party is a school, research, or academic institution",
      "contractName": "Contract Name",
      "contractBgRemark": "Contract Abstract",
      "confidenLevel": "Classification level",
      "havingMoney": "Actual Amount",
      "exchangeAmount": "Amount(NTD)(Reference exchange rate:{0})",
      "accountType": "Account Receivable/Payable",
      "accountDeptid": "Charge Department",
      "isPrForm": "Approved PMCS - PR Application",
      "prNo": "PR No.",
      "prDeptid": "PR Charge Department",
      "prType": "PR Type",
      "prRemark": "PR Remark / Purchase Reason",
      "contractType": "Contract category",
      "fileCategory": "Contract category",
      "contractObj": "Subject Matter",
      "isBg": "Does the rights and obligations at the time of signing relate to other known BGs",
      "bgDeptid": "BG Dept.",
      "undertakeRemark": "HQ Legal PIC Remark",
      "hasStampDuty": "Stamp Duty",
      "taxCurrency": "Tax currency",
      "taxType": "Tax",
      "taxRate": "Rate",
      "estimateTaxAmount": "Estimated tax amount",
      "undfinanceRemark": "Local Finance Remark",
      "acknowledgeType": "Determination result of Acknowledge type",
      "originaStatus": "Method of authorization",
      "finalSignLevel": "Final Approver Assignment",
      "byPassReason": "Evacuate to determine the reason",
      "originalRemark": "Recommended reason",
      "docType": "Contract/Document Access Type",
      "hasDownload": "Exceptional entity download permissions",
      "applyReason": "Reason for Apply",
      "applyDate": "Apply Date",
      "otherBSearchPara": "list query criteria",
      "otherBSearchCount": "Number of query criteria",
      "otherBExportPara": "list export required fields",
      "otherBSearchList": "list query results",
      "oldPaperStatus": "Original hard copy status."
    },
    //XML表格
    "xmlTable": {
      //簽核歷程
      "flowHistoryTable": {
        "stepName": "Approval Stage name",
        "shouldSignerName": "Assigned Approver",
        "signerName": "Actual Approver",
        "signerResult": "Approval result",
        "signerTime": "Approval Time",
        "signerComment": "Comments"
      },
      //加簽人員資訊
      "inviterTable": {
        "inviterEmplid": "Employee ID",
        "inviterEName": "English Name",
        "inviterDeptCode": "Dept.",
        "inviterRemark": "Reason for the invitation."
      },
      //其他申請案件清單列表
      "otherACDCaseTable": {
        "serialNumber": "No.",
        "applyNumber": "Application No.",
        "myEntity": "Legal Entity",
        "otherParty": "Counter Party",
        "contractName": "Contract Name",
        "picDeptid": "Department",
        "contractNumber": "Contract Number",
        "confidenLevel": "Classification level"
      },
      //其他申請主體清單列表
      "otherEFEntityTable": {
        "serialNumber": "No.",
        "entity": "Entity Code",
        "entityName": "Legal Entity Name"
      },
      //其他申請必要揭露人員列表
      "otherInterPersonTable": {
        "empNo": "Employee ID",
        "empEName": "English Name",
        "deptCode": "Dept.",
        "deptCompany": "Company Code"
      }
    },
    //XML其他
    "xmlOther": {
      "Yes": "Yes",
      "No": "No",
      "otherTitle": "Other Applications",
      "formTitle": "Contract Application",
      "inviteeEmp": "Inviteer",
      "entityIsLegal": "(Initiating the approval process for the legal representative.)",
      "oldData": "Old Data",
      "originSuperTypeReturn": "Since this contract needs to be signed by multiple designated persons, if you need to authorize other signatories, please return to legal admin.",
      "originalComment": "If 'Other' is selected as the authorized signing representative, be sure to enter the 'Name' before submitting the application form.",
      "originalType": "Authorize to Signing Representative",
      "other": "Other",
      "commentOther": "For the complete Access to Application List, please check in eLegal.",
      "invitee": "Inviteed",
      "acknowledge": "Acknowledged",
      "picWithdraw": "Withdrew",
      "inviteMessage": "To add personnel for approval, specify the person in the 'Invitee' field and click 'Invite' before submitting the application  form."
    },
    //XML附件處理
    "xmlArchivePurposes": {
      "1": "Contracts and Attachments",
      "2": "References",
      "3": "Archive",
      "4": "Attachment"
    }
  },
  "ApplicationPageType": {
    //"0": "任意頁面",
    "0": "未结案",
    "1": "待處理案件 -- 當關案件",
    "2": "待處理案件 -- 代理案件",
    "3": "待處理案件 -- 被加簽案件",
    "4": "未结案",
    "5": "O類申請單調閱案件檢視"
  }
}