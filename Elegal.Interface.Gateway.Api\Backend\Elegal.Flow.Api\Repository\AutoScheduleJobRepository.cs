using Elegal.Interface.Api.Common.FuncHelper;
using Elegal.Interface.Api.Common.Model.ParaModel;
using Elegal.Interface.Api.Common.Repository;
using Microsoft.IdentityModel.Tokens;
using System.Text;

namespace Elegal.Flow.Api.Repository
{
    public class AutoScheduleJobRepository : BaseRepository
    {
        /// <summary>
        /// 實例化
        /// </summary>
        public AutoScheduleJobRepository() : base() { }

        /// <summary>
        /// 查詢所有自動化排程
        /// </summary>
        public List<SysAutoJobModel> QueryScheduleJobInfo(string logging_locale, string time_zone)
        {
            string sql = @"SELECT rowid,
                                job_name,
                                job_url,
                                job_mail_address,
                                job_run_type,
                                job_run_interval,
                                job_run_time,
                                job_status,
                                job_descr,
                                job_run_program,
                                is_modify,
                                (select top 1 STATE from LOG_JOB where JOB_ID = saj.job_run_program ORDER by LOG_ID DESC) as last_run_status,
                                (select top 1 END_DATE from LOG_JOB where JOB_ID = saj.job_run_program ORDER by LOG_ID DESC) as last_run_time,
                                (SELECT fun_name FROM sys_parameters WHERE para_code = 'jobRunType' AND lang_type = @logging_locale AND func_code = CONVERT(NVARCHAR,job_run_type)) AS job_run_type_name,
                                ISNULL(modify_time,create_time) AS operate_time,
                                ee.name AS operate_cuser,
                                ee.name_a AS operate_euser
                                FROM sys_auto_job AS saj
                                LEFT JOIN (SELECT emplid,name,name_a FROM ps_sub_ee_lgl_vw_a) AS ee ON ee.emplid = ISNULL(modify_user,create_user)";
            return this.NpgsqlSearchByList<SysAutoJobModel>(sql, new { logging_locale = logging_locale }, "time_zone", time_zone);
        }

        /// <summary>
        /// 根據key值查詢自動化排程
        /// </summary>
        public SysAutoJobModel QueryScheduleJobInfoByName(SysAutoJobModel sysAutoJobModel, string logging_locale, string time_zone)
        {
            StringBuilder sql = new StringBuilder();
            sql.Append(@"SELECT saj.*,
                                ee.name AS operate_cuser,
                                ee.name_a AS operate_euser from
                                (
                                    SELECT rowid,
                                    job_name,
                                    job_url,
                                    job_mail_address,
                                    job_run_type,
                                    job_run_interval,
                                    job_run_time,
                                    job_status,
                                    job_descr,
                                    job_run_program,
                                    is_modify,
                                    (select top 1 STATE from LOG_JOB where JOB_ID = job_run_program ORDER by LOG_ID DESC) as last_run_status,
                                    (select top 1 END_DATE from LOG_JOB where JOB_ID = job_run_program ORDER by LOG_ID DESC) as last_run_time,
                                    (SELECT fun_name FROM sys_parameters WHERE para_code = 'jobRunType' AND lang_type = @logging_locale AND func_code = CONVERT(NVARCHAR,job_run_type)) AS job_run_type_name,
                                    modify_user,
                                    create_user,
                                    ISNULL(modify_time,create_time) AS operate_time FROM sys_auto_job where 1=1 ");
            if (sysAutoJobModel.job_run_program != null && !string.IsNullOrEmpty(sysAutoJobModel.job_run_program))
                sql.Append(@" AND job_run_program = @job_run_program");
            if (sysAutoJobModel.rowid > 0)
                sql.Append(@" AND rowid = @rowid");
            sql.Append(@") saj
                                LEFT JOIN (SELECT emplid,name,name_a FROM ps_sub_ee_lgl_vw_a) AS ee ON ee.emplid = ISNULL(saj.modify_user,saj.create_user)");
            return this.NpgsqlSearchBySingle<SysAutoJobModel>(sql.ToString(), new { job_run_program = sysAutoJobModel.job_run_program, rowid = sysAutoJobModel.rowid, logging_locale = logging_locale }, "time_zone", time_zone);
        }



        /// <summary>
        /// 根據key值修改自動化排程
        /// </summary>
        public int UpdateScheduleJobInfoByName(SysAutoJobModel sysAutoJobModel)
        {
            StringBuilder sql = new();
            sql.Append(@"UPDATE sys_auto_job SET ");
            if (sysAutoJobModel.job_mail_address != null && !string.IsNullOrEmpty(sysAutoJobModel.job_mail_address))
                sql.Append(@"job_mail_address =@job_mail_address,");
            if (sysAutoJobModel.job_name != null && !string.IsNullOrEmpty(sysAutoJobModel.job_name))
                sql.Append(@"job_name =@job_name,");
            if (sysAutoJobModel.job_run_type > 0)
                sql.Append(@"job_run_type =@job_run_type,");
            if (sysAutoJobModel.job_run_interval != null && !string.IsNullOrEmpty(sysAutoJobModel.job_run_interval))
                sql.Append(@"job_run_interval =@job_run_interval,");
            if (sysAutoJobModel.job_run_time != null && !string.IsNullOrEmpty(sysAutoJobModel.job_run_time))
                sql.Append(@"job_run_time =@job_run_time,");
            if (sysAutoJobModel.job_status != null && !string.IsNullOrEmpty(sysAutoJobModel.job_status))
                sql.Append(@"job_status =@job_status,");
            if (sysAutoJobModel.job_descr != null && !string.IsNullOrEmpty(sysAutoJobModel.job_descr))
                sql.Append(@"job_descr =@job_descr,");
            sql.Append(@"create_user=@create_user, create_time=getutcdate(), modify_user=@modify_user, modify_time=getutcdate()
                 where job_run_program = @job_run_program");
            return this.ExecuteCommand(sql.ToString(), new { job_name = sysAutoJobModel.job_name, job_url = sysAutoJobModel.job_url, job_mail_address = sysAutoJobModel.job_mail_address, job_run_type = sysAutoJobModel.job_run_type, job_run_interval = sysAutoJobModel.job_run_interval, job_run_time = sysAutoJobModel.job_run_time, job_status = sysAutoJobModel.job_status, create_user = sysAutoJobModel.opearte_emp, modify_user = sysAutoJobModel.opearte_emp, job_run_program = sysAutoJobModel.job_run_program, job_descr = sysAutoJobModel.job_descr });
        }

        /// <summary>
        /// 同步更新排程執行時間
        /// </summary>
        public int UpdateJobTaskByName(string job_run_program, string cron, long timestamp, string job_status)
        {
            if (!String.IsNullOrEmpty(AppSettingHelper.Configuration["TaskDbConnection"]))
            {
                string? dataBase = AppSettingHelper.Configuration["TaskDbConnection"];
                string sql = @$"UPDATE {dataBase}.dbo.QRTZ_CRON_TRIGGERS
                            SET CRON_EXPRESSION=@cron
                            WHERE TRIGGER_NAME=@job_run_program

                            UPDATE {dataBase}.dbo.QRTZ_TRIGGERS
                            SET NEXT_FIRE_TIME=@timestamp 
                                ,TRIGGER_STATE=@job_status
                            WHERE TRIGGER_NAME=@job_run_program";
                return this.ExecuteCommand(sql, new { job_run_program = job_run_program, cron = cron, timestamp = timestamp, job_status = job_status });
            }
            return 0;
        }

        public int Verify(string job_name, long rowid)
        {
            string sql = @"SELECT COUNT(rowid) FROM sys_auto_job WHERE job_name = CONCAT(N'', @@job_name) AND rowid <> @rowid AND job_status = 1 AND is_modify = 1";
            return Convert.ToInt32(this.NpgsqlSearchBySingleValue(sql, new { job_name = job_name, rowid = rowid }));
        }

        public bool VerifyScheduleJobRepetition(SysAutoJobModel sysAutoJobModel)
        {
            string sql = @"select count(1) from sys_auto_job where job_name = @job_name and rowid != @rowid";
            return this.NpgsqlSearchBySingleValue(sql, new { job_name = sysAutoJobModel.job_name, rowid = sysAutoJobModel.rowid }).Equals(0);
        }
    }
}
