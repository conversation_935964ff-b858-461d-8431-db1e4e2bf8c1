﻿using Elegal.Flow.Api.Repository;
using Elegal.Interface.Api.Common.Control.Filter;
using Elegal.Interface.Api.Common.FuncService;
using Elegal.Interface.Api.Common.Model.Enum;
using Elegal.Interface.Api.Common.Model.ResultModel;
using Elegal.Interface.Api.Common.Model.ResultModel.FlowApi;
using Elegal.Interface.Api.Common.Model.ViewModel.FlowApi;
using Elegal.Interface.ApiData.Service.FuncService;
using Elegal.Interface.ApiData.Service.Model.Condition;
using Elegal.Interface.ApiData.Service.Model.DbModel;
using Elegal.Orm.Dtos;
using Elegal.Orm.Utils;
using Microsoft.AspNetCore.Mvc;
using System.Text;

namespace Elegal.Flow.Api.Services.Process
{
    /// <summary>
    /// 關企主體管理
    /// </summary>
    public static class AffiliateCompanyService
    {
        private static readonly AffiliateCompanyRepository _repository = new();

        /// <summary>
        /// 關企公司列表查詢
        /// </summary>
        /// <param name="condition">查詢參數</param>
        /// <returns></returns>
        public static IEnumerable<AffiliateCompanyViewModel> GetDataList(AffiliateCompanySearchModel condition)
        {
            IEnumerable<AffiliateCompanyViewModel> enumerable = _repository.GetDataList(condition);
            ActionFilter.InitLogRecord(condition, log =>
            {
                StringBuilder stringBuilder = new();
                if (!string.IsNullOrEmpty(condition.Name))
                    stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("affCompany_affCompanyAbbName", true)}：{condition.Name}");
                log.Detail = stringBuilder.ToString();
            });
            return enumerable;
        }
        /// <summary>
        /// 新增關企主體
        /// </summary>
        /// <param name="affiliateCompany">數據模型</param>
        /// <returns></returns>
        internal static ApiResultModelByObject Insert([FromBody] AffiliateCompanyModel affiliateCompany)
        {
            ApiResultModelByObject apiResult = new();

            //校驗是否存在重複關企公司簡稱
            AffiliateCompany model = AffiliateCompanyDataService.Find(new AffiliateCompanyCondition()
            {
                AffCompanyAbb = affiliateCompany.affCompanyAbb
            });
            if (model != null && !string.IsNullOrEmpty(model.AffCompanyAbb))
            {
                apiResult.messageContent.Add(ActionFilter.GetMultilingualValue("custom:messageTitle:dataExists"));
                apiResult.messageType = MessageTypeUtils.Warning.ToString();
                return apiResult;
            }

            string code = SysApplicationSequenceService.GetApplicationNumber("affiliate_company", null);

            string entityId = string.Empty;
            if (affiliateCompany.affGroupEntity != null)
                entityId = affiliateCompany.affGroupEntity?.EntityId;

            // 新增關企公司
            apiResult.rtnSuccess = AffiliateCompanyDataService.Create(new AffiliateCompany()
            {
                AffCompanyCode = code,
                AffCompanyAbb = affiliateCompany.affCompanyAbb,
                AffCompanyCname = affiliateCompany.affCompanyCname,
                AffCompanyEname = affiliateCompany.affCompanyEname,
                AffGroupEntity = entityId,
                AffReamrk = affiliateCompany.affReamrk,
                AffStatus = affiliateCompany.affStatus,
                CreateUser = MvcContext.UserInfo.current_emp,
                CreateTime = DateTime.UtcNow
            });
            ActionFilter.InitLogRecord(affiliateCompany, log =>
            {
                StringBuilder stringBuilder = new();
                if (!string.IsNullOrEmpty(affiliateCompany.affCompanyAbb))
                    stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("affCompany_affCompanyAbb", true)}：{affiliateCompany.affCompanyAbb}");
                if (!string.IsNullOrEmpty(affiliateCompany.affCompanyCname))
                    stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("affCompany_affCompanyCname", true)}：{affiliateCompany.affCompanyCname}");
                if (!string.IsNullOrEmpty(affiliateCompany.affCompanyEname))
                    stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("affCompany_affCompanyEname", true)}：{affiliateCompany.affCompanyEname}");
                if (!string.IsNullOrEmpty(entityId))
                    stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("affCompany_entity", true)}：{BulletinsService.GetEntityByEntityID(entityId.ToString())}");
                if (!string.IsNullOrEmpty(affiliateCompany.affReamrk))
                    stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("affCompany_affReamrk", true)}：{affiliateCompany.affReamrk}");
                if (!string.IsNullOrEmpty(affiliateCompany.affStatus)) {
                    string status = "";
                    switch (affiliateCompany.affStatus)
                    {
                        case "0":
                            status = ActionFilter.GetMultilingualValue("EntityManager_log:disable", true);
                            break;
                        case "1":
                            status = ActionFilter.GetMultilingualValue("EntityManager_log:enable", true);
                            break;
                    }
                    stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("affCompany_affStatus", true)}：{status}");
                }
                log.Detail = stringBuilder.ToString();
            });
            return apiResult;
        }

        /// <summary>
        /// 刪除關企公司
        /// </summary>
        /// <param name="affCompanyCode">公司代碼</param>
        /// <returns></returns>
        internal static ApiResultModelByObject Delete(string affCompanyCode)
        {
            ApiResultModelByObject apiResult = new();

            //校驗主體下是否有申請單
            List<string> vGetAllApplications = _repository.QueryApplyNumber(affCompanyCode).ToList();
            if (vGetAllApplications.Count > 0)
            {
                apiResult.messageTitle = ActionFilter.GetMultilingualValue("custom:messageTitle:doNotDelete");
                apiResult.messageContent = new List<string> { string.Format(ActionFilter.GetMultilingualValue("custom:messageContent:EntityExistApplication"), string.Join(",", vGetAllApplications)) };
                apiResult.messageType = MessageTypeUtils.Warning.ToString();
                return apiResult;
            }

            //校驗是否存在關企公司
            AffiliateCompany model = AffiliateCompanyDataService.FindByKey(affCompanyCode);
            if (model == null || string.IsNullOrEmpty(model.AffCompanyAbb))
            {
                apiResult.messageTitle = ActionFilter.GetMultilingualValue("custom:messageTitle:dataNotexist");
                apiResult.messageContent = new List<string> { string.Format(ActionFilter.GetMultilingualValue("custom:messageContent:dataChurn")) };
                apiResult.messageType = MessageTypeUtils.Warning.ToString();
                return apiResult;
            }

            ActionFilter.InitLogRecord(affCompanyCode, log =>
            {
                StringBuilder stringBuilder = new();
                if (!string.IsNullOrEmpty(affCompanyCode))
                {
                    stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("affCompany_affCompanyAbb", true)}：{model.AffCompanyAbb}");
                    stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("affCompany_affCompanyCname", true)}：{model.AffCompanyCname}");
                    stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("affCompany_affCompanyEname", true)}：{model.AffCompanyEname}");
                    stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("affCompany_entity", true)}：{BulletinsService.GetEntityByEntityID(model.AffGroupEntity.ToString())}");
                    stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("affCompany_affReamrk", true)}：{model.AffReamrk}");
                    if (!string.IsNullOrEmpty(model.AffStatus))
                    {
                        string status = "";
                        switch (model.AffStatus)
                        {
                            case "0":
                                status = ActionFilter.GetMultilingualValue("EntityManager_log:disable", true);
                                break;
                            case "1":
                                status = ActionFilter.GetMultilingualValue("EntityManager_log:enable", true);
                                break;
                        }
                        stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("affCompany_affStatus", true)}：{status}");
                    }
                    //stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("affCompany_affReamrk", true)}: {model.AffStatus==1?\"启用\":\"停用\"}");
                }

                log.Detail = stringBuilder.ToString();
            });

            // 刪除關企公司
            apiResult.rtnSuccess = AffiliateCompanyDataService.DeleteByKey(affCompanyCode);
            return apiResult;
        }
        /// <summary>
        /// 獲取單筆關卡主體數據
        /// </summary>
        /// <param name="affCompanyCode">公司代碼</param>
        /// <returns></returns>
        internal static ApiResultModelByObject Find(string affCompanyCode)
        {
            //數據是否存在
            AffiliateCompany model = AffiliateCompanyDataService.FindByKey(affCompanyCode);
            if (model == null) return new()
            {
                messageTitle = ActionFilter.GetMultilingualValue("custom:messageTitle:dataNotexist"),
                messageContent = [string.Format(ActionFilter.GetMultilingualValue("custom:messageContent:dataChurn"))],
                messageType = MessageTypeUtils.Warning.ToString()
            };

            ActionFilter.InitLogRecord(affCompanyCode, log =>
            {
                StringBuilder stringBuilder = new();
                if (!string.IsNullOrEmpty(affCompanyCode))
                    stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("affCompany_affCompanyAbb", true)}：{model.AffCompanyAbb}");
                log.Detail = stringBuilder.ToString();
            });
            // 返回關企公司
            return new()
            {
                listData = AffiliateCompanyDataService.FindByKey(affCompanyCode),
                rtnSuccess = true
            };
        }

        /// <summary>
        /// 修改關企公司
        /// </summary>
        /// <returns></returns>
        internal static ApiResultModelByObject Modify(AffiliateCompanyModel affiliateCompany)
        {
            ApiResultModelByObject apiResult = new();

            //校驗是否存在關企公司
            AffiliateCompany model = AffiliateCompanyDataService.FindByKey(affiliateCompany.affCompanyCode);
            if (model == null || string.IsNullOrEmpty(model.AffCompanyAbb))
            {
                apiResult.messageContent.Add(ActionFilter.GetMultilingualValue("custom:messageTitle:dataNotexist"));
                apiResult.messageContent = new List<string> { ActionFilter.GetMultilingualValue("custom:messageContent:dataChurn") };
                apiResult.messageType = MessageTypeUtils.Warning.ToString();
                return apiResult;
            }

            //校驗是否存在重複關企公司簡稱
            AffiliateCompany affiliate = AffiliateCompanyDataService.Find(new AffiliateCompanyCondition()
            {
                AffCompanyAbb = affiliateCompany.affCompanyAbb,
            });
            if (affiliate != null && !string.IsNullOrEmpty(affiliate.AffCompanyAbb) && !affiliateCompany.affCompanyCode.Equals(affiliate.AffCompanyCode))
            {
                apiResult.messageContent.Add(ActionFilter.GetMultilingualValue("custom:messageTitle:dataExists"));
                apiResult.messageType = MessageTypeUtils.Warning.ToString();
                return apiResult;
            }

            string entityId = string.Empty;
            if (affiliateCompany.affGroupEntity != null)
                entityId = affiliateCompany.affGroupEntity?.EntityId;

            // 修改關企公司
            apiResult.rtnSuccess = AffiliateCompanyDataService.Update(new AffiliateCompany()
            {
                AffCompanyAbb = affiliateCompany.affCompanyAbb,
                AffCompanyCname = affiliateCompany.affCompanyCname,
                AffCompanyEname = affiliateCompany.affCompanyEname,
                AffCompanyCode = affiliateCompany.affCompanyCode,
                AffGroupEntity = entityId,
                AffReamrk = affiliateCompany.affReamrk,
                AffStatus = affiliateCompany.affStatus,
                ModifyUser = MvcContext.UserInfo.current_emp,
                ModifyTime = DateTime.UtcNow
            });
            ActionFilter.InitLogRecord(affiliateCompany, log =>
            {
                StringBuilder stringBuilder = new();
                if (!string.IsNullOrEmpty(affiliateCompany.affCompanyAbb))
                    stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("affCompany_affCompanyAbb", true)}：{affiliateCompany.affCompanyAbb}");
                if (!string.IsNullOrEmpty(affiliateCompany.affCompanyCname))
                    stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("affCompany_affCompanyCname", true)}：{affiliateCompany.affCompanyCname}");
                if (!string.IsNullOrEmpty(affiliateCompany.affCompanyEname))
                    stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("affCompany_affCompanyEname", true)}：{affiliateCompany.affCompanyEname}");
                if (!string.IsNullOrEmpty(entityId))
                    stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("affCompany_entity", true)}：{BulletinsService.GetEntityByEntityID(entityId.ToString())}");
                if (!string.IsNullOrEmpty(affiliateCompany.affReamrk))
                    stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("affCompany_affReamrk", true)}：{affiliateCompany.affReamrk}");

                if (!string.IsNullOrEmpty(affiliateCompany.affStatus))
                {
                    string status = "";
                    switch (affiliateCompany.affStatus)
                    {
                        case "0":
                            status = ActionFilter.GetMultilingualValue("EntityManager_log:disable", true);
                            break;
                        case "1":
                            status = ActionFilter.GetMultilingualValue("EntityManager_log:enable", true);
                            break;
                    }
                    stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("affCompany_affStatus", true)}：{status}");
                }


                StringBuilder stringFormer = new StringBuilder();
                if (!string.IsNullOrEmpty(model.AffCompanyAbb))
                    stringFormer.AppendLine($"{ActionFilter.GetMultilingualValue("affCompany_affCompanyAbb", true)}：{model.AffCompanyAbb}");
                if (!string.IsNullOrEmpty(model.AffCompanyCname))
                    stringFormer.AppendLine($"{ActionFilter.GetMultilingualValue("affCompany_affCompanyCname", true)}：{model.AffCompanyCname}");
                if (!string.IsNullOrEmpty(model.AffCompanyEname))
                    stringFormer.AppendLine($"{ActionFilter.GetMultilingualValue("affCompany_affCompanyEname", true)}：{model.AffCompanyEname}");
                if (!string.IsNullOrEmpty(model.AffGroupEntity))
                    stringFormer.AppendLine($"{ActionFilter.GetMultilingualValue("affCompany_entity", true)}：{BulletinsService.GetEntityByEntityID(model.AffGroupEntity.ToString())}");
                if (!string.IsNullOrEmpty(model.AffReamrk))
                    stringFormer.AppendLine($"{ActionFilter.GetMultilingualValue("affCompany_affReamrk", true)}：{model.AffReamrk}");
                if (!string.IsNullOrEmpty(model.AffStatus))
                {
                    string status = "";
                    switch (model.AffStatus)
                    {
                        case "0":
                            status = ActionFilter.GetMultilingualValue("EntityManager_log:disable", true);
                            break;
                        case "1":
                            status = ActionFilter.GetMultilingualValue("EntityManager_log:enable", true);
                            break;
                    }
                    stringFormer.AppendLine($"{ActionFilter.GetMultilingualValue("affCompany_affStatus", true)}：{status}");
                }
                log.Detail = stringBuilder.ToString();
                log.DetailFormer = stringFormer.ToString();
            });
            return apiResult;
        }
    }
}
