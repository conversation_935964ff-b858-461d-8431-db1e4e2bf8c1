﻿using Elegal.Flow.Api.Services;
using Elegal.Flow.Common.Services;
using Elegal.Flow.Common.Services.OtherApply;
using Elegal.Interface.Api.Common.Control.Filter;
using Elegal.Interface.Api.Common.Control.Web;
using Elegal.Interface.Api.Common.FuncHelper;
using Elegal.Interface.Api.Common.GetLogCommon;
using Elegal.Interface.Api.Common.Model.DBModel;
using Elegal.Interface.Api.Common.Model.ParaModel.FlowApi;
using Elegal.Interface.Api.Common.Model.ParaModel.FlowApi.FormInfoModify;
using Elegal.Interface.Api.Common.Model.ResultModel;
using Elegal.Interface.Api.Common.Model.ViewModel.FlowApi;
using Elegal.Interface.Api.Common.Model.ViewModel.FlowApi.Application;
using Elegal.Interface.ApiData.Service.FuncService;
using Elegal.Interface.ApiData.Service.Model.QueryCondition;
using Elegal.Orm.Dtos;
using Microsoft.AspNetCore.Mvc;
using Minio.DataModel;
using System.Text;

namespace Elegal.Flow.Api.Controllers
{
    /// <summary>
    /// 表單修改
    /// </summary>
    [Route("[controller]")]
    [ApiController]
    public class FormInfoModifyController : BaseController
    {
        #region 表單修改 -> 查詢
        /// <summary>
        /// 表單修改 -> 查詢
        /// </summary>
        /// <param name="asp"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("FormInfoModify_Search")]
        public async Task<ApiResultModelByObject> FormInfoModify_Search([FromBody] FormInfoModifyBySearch asp)
        {
            ApiResultModelByObject apiResult = new ApiResultModelByObject();
            int totalCount = 0;
            List<FormInfoModifyViewModel> listData = FormInfoModifyService.FormInfoModifySearch(asp, MvcContext.UserInfo.current_roles, MvcContext.UserInfo.current_emp, out totalCount, MvcContext.UserInfo.logging_locale);
            apiResult.listData = listData;
            apiResult.totalCount = totalCount;
            apiResult.rtnSuccess = true;
            #region 記錄日誌
            InitLogRecord(asp, log =>
            {
                //獲取下拉框選項
                SysParametersQueryCondition sysParametersQueryCondition = new SysParametersQueryCondition()
                {
                    LangType = MvcContext.UserInfo.logging_locale,
                    //ParaCode = new string[] { "applicationState" },
                    SearchItemGroup = new SearchItemGroup()
                    {
                        Items = new List<SearchItem>() { new SearchItem() { Field = "ParaCode", Values = new string[] { "applicationState", "applicationType" }, Compare = CompareOperator.ARRAYIN } }
                    },
                    OrderBys = new List<Orm.Dtos.OrderByParam>()
                    {
                        new Orm.Dtos.OrderByParam(){Field="SortOrder",Order=Orm.Dtos.OrderBy.ASC}
                    }
                };
                var listData = SysParametersDataService.Query(sysParametersQueryCondition);
                var entityData = FormInfoModifyService.FormInfoModifyGetEntity(711, MvcContext.UserInfo.current_roles, MvcContext.UserInfo.current_emp).DistinctBy(x => x.EntityId).Select(x => new {
                    x.EntityId,
                    x.Entity
                });
                StringBuilder stringBuilder = new StringBuilder();
                stringBuilder.GetLogString("FormInfoModify_applytype", listData.Where(x => x.ParaCode == "applicationType" && asp.apply_type.Contains(x.FuncCode)).Select(x => x.FunName).ToList())
                             .GetLogString(asp.apply_number, "FormInfoModify_applynumber")
                             .GetLogString(asp.apply_time_start.ConvertDateByTimeZoneByUtc(MvcContext.UserInfo.time_zone)?.ToString("yyyy/MM/dd") ?? string.Empty, "FormInfoModify_applytimestart")
                             .GetLogString(asp.apply_time_end.ConvertDateByTimeZoneByUtc(MvcContext.UserInfo.time_zone)?.ToString("yyyy/MM/dd") ?? string.Empty, "FormInfoModify_applytimeend")
                             .GetLogString(asp.contract_number, "FormInfoModify_contractnumber")
                             .GetLogString("FormInfoModify_entityid", GetLogCommon.GetEntity(asp.entity_id))
                             .GetLogString(asp.other_party, "FormInfoModify_otherparty")
                             .GetLogString(asp.contract_name, "FormInfoModify_contractname")
                             .GetLogString(GetLogCommon.GetUserName(asp.emp_data), "FormInfoModify_empdata")
                             .GetLogString(asp.dept_id, "FormInfoModify_deptid")
                             .GetLogString("FormInfoModify_applicationState", listData.Where(x => x.ParaCode == "applicationState" && asp.applicationState.Contains(x.FuncCode)).Select(x => x.FunName).ToList())
                             ;
                log.Detail = stringBuilder.ToString();
            });
            #endregion

            return await Task.FromResult<ApiResultModelByObject>(apiResult);
        }
        #endregion

        #region 表單修改 -> 主體查詢
        /// <summary>
        /// 表單修改 -> 主體查詢
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("FormInfoModify_GetEntity")]
        public async Task<ApiResultModelByObject> FormInfoModify_GetEntity(int menuCode)
        {
            ApiResultModelByObject apiResult = new ApiResultModelByObject();

            apiResult.listData = FormInfoModifyService.FormInfoModifyGetEntity(menuCode, MvcContext.UserInfo.current_roles, MvcContext.UserInfo.current_emp);
            apiResult.rtnSuccess = true;

            return await Task.FromResult<ApiResultModelByObject>(apiResult);
        }
        #endregion

        #region 表單修改 -> 合約申請

        #region 申請單資訊
        /// <summary>
        /// 表單修改 -> 合約申請
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("FormInfoModify_Contract")]
        public async Task<ApiResultModel> FormInfoModify_Contract([FromBody] FormInfoModify_Contract data)
        {
            ApiResultModel apiResult = new();
            apiResult.rtnSuccess = FormInfoModifyService.FormInfoModify_Contract(data);
            #region 記錄日誌
            InitLogRecord(data, log =>
            {
                //如有異動紀錄則新增
                if (data.sysChangeRecordParaModel != null)
                {
                    StringBuilder stringBuilder1 = new StringBuilder();
                    StringBuilder stringBuilder2 = new StringBuilder();
                    //獲取下拉框選項
                    SysParametersQueryCondition sysParametersQueryCondition = new SysParametersQueryCondition()
                    {
                        LangType = MvcContext.UserInfo.logging_locale,
                        //ParaCode = new string[] { "applicationState" },
                        SearchItemGroup = new SearchItemGroup()
                        {
                            Items = new List<SearchItem>() { new SearchItem() { Field = "ParaCode", Values = new string[] { "YN", "suggestStatus", "originalWriteType", "sealStatus", "sealType", "confidentialStatus", "confidentStatus", "formType_C", "otherPartySetting", "amountStatus", "currency", "accountType", "payType", "taxType", "beType", "contractType", "fileCategory", "targetType", "egType", "dateOptions", "caseNature", "effDateOptions", "originaStatus", "finalSignLevel" }, Compare = CompareOperator.ARRAYIN } }
                        },
                        OrderBys = new List<Orm.Dtos.OrderByParam>()
                        {
                            new Orm.Dtos.OrderByParam(){Field="SortOrder",Order=Orm.Dtos.OrderBy.ASC}
                        }
                    };
                    var listData = SysParametersDataService.Query(sysParametersQueryCondition);
                    var entitylist = FnpEntityService.Query(new FnpEntityQueryModel());
                    stringBuilder1.GetLogString(data.formApplication.apply_number, "PublicLogCodeFile_applyApplication_applyNumber");
                    stringBuilder2.GetLogString(data.formApplication.apply_number, "PublicLogCodeFile_applyApplication_applyNumber");
                    //var yyy = data.sysChangeRecordParaModel.details.Where(x => x.change_code == "expected_date").Select(x => x.after_value).FirstOrDefault().Replace("T", " ").Replace("Z", "");
                    //var xxx = DateTime.Parse(data.sysChangeRecordParaModel.details.Where(x => x.change_code == "expected_date").Select(x => x.after_value).FirstOrDefault().Replace("T", " ").Replace("Z", "")).ConvertDateByTimeZoneByUtc(MvcContext.UserInfo.time_zone);
                    foreach (SysChangeRecordDetail item in data.sysChangeRecordParaModel.details)
                    {
                        List<string> afterValueList, beforeValueList;
                        //修改主体时日志
                        if (item.change_code == "entity_id")
                        {
                            afterValueList = entitylist.Where(x => x.EntityId == item.after_value).Select(x => "(" + x.Entity + ")" + x.EntityNamec + " " + x.EntityNamee).ToList();
                            beforeValueList = entitylist.Where(x => x.EntityId == item.before_value).Select(x => "(" + x.Entity + ")" + x.EntityNamec + " " + x.EntityNamee).ToList();
                        }
                        else
                        {
                                afterValueList = listData.Where(x => x.ParaCode == item.change_code_source && item.after_value == x.FuncCode).Select(x => x.FunName).ToList();
                                beforeValueList = listData.Where(x => x.ParaCode == item.change_code_source && item.before_value == x.FuncCode).Select(x => x.FunName).ToList();
                        }
                        if (afterValueList == null || afterValueList.Count == 0)
                        {
                            if (item.change_code == "other_party")
                            {
                                if(!string.IsNullOrEmpty(item.after_value)) stringBuilder1.GetLogString(item.after_value.Replace("[", "").Replace("]", "").Replace("\"", ""), item.change_code_cname);
                                else stringBuilder1.GetLogString("-", item.change_code_cname);
                            }
                            else if (item.change_code == "expected_date")
                            {
                                if(!string.IsNullOrEmpty(item.after_value))stringBuilder1.GetLogString(DateTime.Parse(item.after_value.Replace("T", " ").Replace("Z", "")).ConvertDateByTimeZoneByUtc(MvcContext.UserInfo.time_zone).ToString("yyyy/MM/dd") ?? string.Empty, item.change_code_cname);
                                else stringBuilder1.GetLogString("-", item.change_code_cname);
                            }
                            else
                            {
                                stringBuilder1.GetLogString(item.after_value, item.change_code_cname);
                            }
                        }
                        else
                        {
                            stringBuilder1.GetLogString(afterValueList, item.change_code_cname);

                        }
                        if (beforeValueList == null || beforeValueList.Count == 0)
                        {
                            if (item.change_code == "other_party")
                            {
                                if (!string.IsNullOrEmpty(item.before_value)) stringBuilder2.GetLogString(item.before_value.Replace("[", "").Replace("]", "").Replace("\"", ""), item.change_code_cname);
                                else stringBuilder2.GetLogString("-", item.change_code_cname);
                            }
                            else if (item.change_code == "expected_date")
                            {
                                if (!string.IsNullOrEmpty(item.before_value)) stringBuilder2.GetLogString(DateTime.Parse(item.before_value.Replace("T", " ").Replace("Z", "")).ConvertDateByTimeZoneByUtc(MvcContext.UserInfo.time_zone).ToString("yyyy/MM/dd") ?? string.Empty, item.change_code_cname);
                                else stringBuilder2.GetLogString("-", item.change_code_cname);
                            }
                            else
                            {
                                stringBuilder2.GetLogString(item.before_value, item.change_code_cname);
                            }
                        }
                        else
                        {
                            stringBuilder2.GetLogString(beforeValueList, item.change_code_cname);

                        }
                    }
                    log.Detail = stringBuilder1.ToString();
                    log.DetailFormer = stringBuilder2.ToString();
                }

            });
            #endregion
            return await Task.FromResult(apiResult);
        }
        #endregion

        #region 合約申請歸檔資訊
        /// <summary>
        /// 合約申請歸檔資訊
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("ArchiveInfoModify_Contract")]
        public async Task<ApiResultModel> ArchiveInfoModify_Contract([FromBody] ContractValidityModel data)
        {
            ApiResultModel apiResult = new();
            apiResult.rtnSuccess = FormInfoModifyService.ArchiveInfoModify_Contract(data);
            return await Task.FromResult(apiResult);
        }
        #endregion

        #endregion

        #region 表單修改 -> 其他申請
        /// <summary>
        /// 表單修改 -> 其他申請
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("FormInfoModify_Other")]
        public async Task<ApiResultModel> FormInfoModify_Other([FromBody] FormInfoModify_Other data)
        {
            ApiResultModel apiResult = new();
            apiResult.rtnSuccess = FormInfoModifyService.FormInfoModify_Other(data);
            #region 記錄日誌
            InitLogRecord(data, log =>
            {
                //如有異動紀錄則新增
                if (data.sysChangeRecordParaModel != null)
                {
                    StringBuilder stringBuilder1 = new StringBuilder();
                    StringBuilder stringBuilder2 = new StringBuilder();
                    stringBuilder1.GetLogString(data.otherApplicationAdmin.apply_number, "PublicLogCodeFile_otherApplication_applyNumber");
                    stringBuilder2.GetLogString(data.otherApplicationAdmin.apply_number, "PublicLogCodeFile_otherApplication_applyNumber");
                    List<p_role> proleList = OtherApplyService.GetOtherTypeRole(data.otherApplicationAdmin.apply_number);
                    foreach (SysChangeRecordDetail item in data.sysChangeRecordParaModel.details)
                    {
                        switch (item.change_code)
                        {
                            case "has_top_manager":
                                stringBuilder1.GetLogString(item.after_value == "1" ? $"{ActionFilter.GetMultilingualValue("PublicLogCodeFile_PublicStr_yes", true)}" : $"{ActionFilter.GetMultilingualValue("PublicLogCodeFile_PublicStr_no", true)}", item.change_code_cname);
                                stringBuilder2.GetLogString(item.before_value == "1" ? $"{ActionFilter.GetMultilingualValue("PublicLogCodeFile_PublicStr_yes", true)}" : $"{ActionFilter.GetMultilingualValue("PublicLogCodeFile_PublicStr_no", true)}", item.change_code_cname);
                                break;
                            case "actual_date_start":
                                if (!string.IsNullOrEmpty(item.after_value)) stringBuilder1.GetLogString(DateTime.Parse(item.after_value.Replace("T"," ").Replace("Z","")).ConvertDateByTimeZoneByUtc(MvcContext.UserInfo.time_zone).ToString("yyyy/MM/dd") ?? string.Empty, item.change_code_cname);
                                else stringBuilder1.GetLogString("-", item.change_code_cname);
                                if (!string.IsNullOrEmpty(item.before_value)) stringBuilder2.GetLogString(DateTime.Parse(item.before_value.Replace("T", " ").Replace("Z", "")).ConvertDateByTimeZoneByUtc(MvcContext.UserInfo.time_zone).ToString("yyyy/MM/dd") ?? string.Empty, item.change_code_cname);
                                else stringBuilder2.GetLogString("-", item.change_code_cname);
                                break;
                            case "actual_date_end":
                                if (!string.IsNullOrEmpty(item.after_value)) stringBuilder1.GetLogString(DateTime.Parse(item.after_value.Replace("T", " ").Replace("Z", "")).ConvertDateByTimeZoneByUtc(MvcContext.UserInfo.time_zone).ToString("yyyy/MM/dd") ?? string.Empty, item.change_code_cname);
                                else stringBuilder1.GetLogString("-", item.change_code_cname);
                                if (!string.IsNullOrEmpty(item.before_value)) stringBuilder2.GetLogString(DateTime.Parse(item.before_value.Replace("T", " ").Replace("Z", "")).ConvertDateByTimeZoneByUtc(MvcContext.UserInfo.time_zone).ToString("yyyy/MM/dd") ?? string.Empty, item.change_code_cname);
                                else stringBuilder2.GetLogString("-", item.change_code_cname);
                                break;
                            case "auth_role_id":
                                stringBuilder1.GetLogString(proleList.Where(x => x.r_id.ToString() == item.after_value).Select(x => x.r_name).ToList(), item.change_code_cname);
                                stringBuilder2.GetLogString(proleList.Where(x => x.r_id.ToString() == item.before_value).Select(x => x.r_name).ToList(), item.change_code_cname);
                                break;
                            default:
                                stringBuilder1.GetLogString(item.after_value, item.change_code_cname);
                                stringBuilder2.GetLogString(item.before_value, item.change_code_cname);
                                break;
                        }

                    }
                    log.Detail = stringBuilder1.ToString();
                    log.DetailFormer = stringBuilder2.ToString();
                }
                //StringBuilder stringBuilder = new StringBuilder();
                //stringBuilder.GetLogString(data.otherApplicationAdmin.apply_number, "PublicLogCodeFile_otherApplication_applyNumber")
                //             .GetLogString(data.otherApplicationAdmin.has_top_manager==1? $"{ActionFilter.GetMultilingualValue("PublicLogCodeFile_PublicStr_yes", true)}" : $"{ActionFilter.GetMultilingualValue("PublicLogCodeFile_PublicStr_no", true)}", "PublicLogCodeFile_otherApplication_hasTopManager")
                //             .GetLogString(data.otherApplicationAdmin.admin_reason, "PublicLogCodeFile_otherApplication_adminReason")
                //             .GetLogString(data.otherApplicationAdmin.actual_date_start.ConvertDateByTimeZoneByUtc(MvcContext.UserInfo.time_zone)?.ToString("yyyy/MM/dd") ?? string.Empty, "PublicLogCodeFile_otherApplication_actualDateStart")
                //             .GetLogString(data.otherApplicationAdmin.actual_date_end.ConvertDateByTimeZoneByUtc(MvcContext.UserInfo.time_zone)?.ToString("yyyy/MM/dd") ?? string.Empty, "PublicLogCodeFile_otherApplication_actualDateEnd")
                //             .GetLogString(data.otherApplicationAdmin.auth_role_id, "PublicLogCodeFile_otherApplication_authRoleId")
                //             ;
                //log.Detail = stringBuilder.ToString();

            });
            #endregion
            return await Task.FromResult(apiResult);
        }
        #endregion

        #region 表單修改 -> 關企建檔

        #region 申請單資訊
        /// <summary>
        /// 申請單資訊
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("FormInfoModify_GuanEnterprise")]
        public async Task<ApiResultModel> FormInfoModify_GuanEnterprise([FromBody] FormInfoModifyByGuanEnterprise data)
        {
            ApiResultModel apiResult = new ApiResultModel();
            apiResult.rtnSuccess = FormInfoModifyService.FormInfoModifyByGuanEnterprise(data);
            #region 記錄日誌
            InitLogRecord(data, log =>
            {
                //如有異動紀錄則新增
                if (data.sysChangeRecordParaModel != null)
                {
                    //獲取下拉框選項
                    SysParametersQueryCondition sysParametersQueryCondition = new SysParametersQueryCondition()
                    {
                        LangType = MvcContext.UserInfo.logging_locale,
                        //ParaCode = new string[] { "applicationState" },
                        SearchItemGroup = new SearchItemGroup()
                        {
                            Items = new List<SearchItem>() { new SearchItem() { Field = "ParaCode", Values = new string[] { "YN", "suggestStatus", "originalWriteType", "sealStatus", "sealType", "confidentialStatus", "confidentStatus", "formType_C", "otherPartySetting", "amountStatus", "currency", "accountType", "payType", "taxType", "beType", "contractType", "fileCategory", "targetType", "egType", "dateOptions", "caseNature", "effDateOptions", "originaStatus", "finalSignLevel" }, Compare = CompareOperator.ARRAYIN } }
                        },
                        OrderBys = new List<Orm.Dtos.OrderByParam>()
                        {
                            new Orm.Dtos.OrderByParam(){Field="SortOrder",Order=Orm.Dtos.OrderBy.ASC}
                        }
                    };
                    var listData = SysParametersDataService.Query(sysParametersQueryCondition);
                    var entitylist = FnpEntityService.Query(new FnpEntityQueryModel());
                    StringBuilder stringBuilder1 = new StringBuilder();
                    StringBuilder stringBuilder2 = new StringBuilder();
                    stringBuilder1.GetLogString(data.enterpriseApplication.apply_number, "PublicLogCodeFile_applyApplication_applyNumber");
                    stringBuilder2.GetLogString(data.enterpriseApplication.apply_number, "PublicLogCodeFile_applyApplication_applyNumber");
                    //stringBuilder1.GetLogString(data.sysChangeRecordParaModel., "申請單號");
                    //stringBuilder2.GetLogString(data.apply_number, "申請單號");
                    foreach (SysChangeRecordDetail item in data.sysChangeRecordParaModel.details)
                    {
                        List<string> afterValueList, beforeValueList;
                        //修改主体时日志
                        if (item.change_code == "entity_id")
                        {
                            afterValueList = entitylist.Where(x => x.EntityId == item.after_value).Select(x => "(" + x.Entity + ")" + x.EntityNamec + " " + x.EntityNamee).ToList();
                            beforeValueList = entitylist.Where(x => x.EntityId == item.before_value).Select(x => "(" + x.Entity + ")" + x.EntityNamec + " " + x.EntityNamee).ToList();
                        }
                        else
                        {
                            afterValueList = listData.Where(x => x.ParaCode == item.change_code_source && item.after_value == x.FuncCode).Select(x => x.FunName).ToList();
                            beforeValueList = listData.Where(x => x.ParaCode == item.change_code_source && item.before_value == x.FuncCode).Select(x => x.FunName).ToList();
                        }
                        if (afterValueList == null || afterValueList.Count == 0)
                        {
                            if (item.change_code == "other_party")
                            {
                                if(!string.IsNullOrEmpty(item.after_value)) stringBuilder1.GetLogString(item.after_value.Replace("[", "").Replace("]", "").Replace("\"", ""), item.change_code_cname);
                                else stringBuilder1.GetLogString("-", item.change_code_cname);
                            }
                            else
                            {
                                stringBuilder1.GetLogString(item.after_value, item.change_code_cname);
                            }
                        }
                        else
                        {
                            stringBuilder1.GetLogString(afterValueList, item.change_code_cname);

                        }
                        if (beforeValueList == null || beforeValueList.Count == 0)
                        {
                            if (item.change_code == "other_party")
                            {
                                if (!string.IsNullOrEmpty(item.before_value)) stringBuilder2.GetLogString(item.before_value.Replace("[", "").Replace("]", "").Replace("\"", ""), item.change_code_cname);
                                else stringBuilder2.GetLogString("-", item.change_code_cname);
                            }
                            else
                            {
                                stringBuilder2.GetLogString(item.before_value, item.change_code_cname);
                            }                     
                        }
                        else
                        {
                            stringBuilder2.GetLogString(beforeValueList, item.change_code_cname);

                        }
                    }
                    log.Detail = stringBuilder1.ToString();
                    log.DetailFormer = stringBuilder2.ToString();
                }

            });
            #endregion
            return await Task.FromResult(apiResult);
        }
        #endregion

        #region 關企建檔歸檔資訊
        /// <summary>
        /// 關企建檔歸檔資訊
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("ArchiveInfoModify_GuanEnterprise")]
        public async Task<ApiResultModel> ArchiveInfoModify_GuanEnterprise([FromBody] EnterpriseContractViewModel data)
        {
            ApiResultModel apiResult = new();
            apiResult.rtnSuccess = FormInfoModifyService.ArchiveInfoModify_GuanEnterprise(data);
            return await Task.FromResult(apiResult);
        }
        #endregion

        #endregion

        #region 表單修改 -> 資料建檔

        #region 申請單資訊
        /// <summary>
        /// 申請單資訊
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("FormInfoModify_Literature")]
        public async Task<ApiResultModel> FormInfoModify_Literature([FromBody] FormInfoModifyByLiterature data)
        {
            ApiResultModel apiResult = new ApiResultModel();
            apiResult.rtnSuccess = FormInfoModifyService.FormInfoModifyByLiterature(data);
            #region 記錄日誌
            InitLogRecord(data, log =>
            {
                //如有異動紀錄則新增
                if (data.sysChangeRecordParaModel != null)
                {
                    //獲取下拉框選項
                    SysParametersQueryCondition sysParametersQueryCondition = new SysParametersQueryCondition()
                    {
                        LangType = MvcContext.UserInfo.logging_locale,
                        //ParaCode = new string[] { "applicationState" },
                        SearchItemGroup = new SearchItemGroup()
                        {
                            Items = new List<SearchItem>() { new SearchItem() { Field = "ParaCode", Values = new string[] { "YN", "suggestStatus", "originalWriteType", "sealStatus", "sealType", "confidentialStatus", "confidentStatus", "formType_C", "otherPartySetting", "amountStatus", "currency", "accountType", "payType", "taxType", "beType", "contractType", "fileCategory", "targetType", "egType", "dateOptions", "caseNature", "effDateOptions", "originaStatus", "finalSignLevel" }, Compare = CompareOperator.ARRAYIN } }
                        },
                        OrderBys = new List<Orm.Dtos.OrderByParam>()
                        {
                            new Orm.Dtos.OrderByParam(){Field="SortOrder",Order=Orm.Dtos.OrderBy.ASC}
                        }
                    };
                    var listData = SysParametersDataService.Query(sysParametersQueryCondition);
                    var entitylist = FnpEntityService.Query(new FnpEntityQueryModel());
                    StringBuilder stringBuilder1 = new StringBuilder();
                    StringBuilder stringBuilder2 = new StringBuilder();
                    stringBuilder1.GetLogString(data.literatureApplication.apply_number, "PublicLogCodeFile_applyApplication_applyNumber");
                    stringBuilder2.GetLogString(data.literatureApplication.apply_number, "PublicLogCodeFile_applyApplication_applyNumber");
                    //stringBuilder1.GetLogString(data.sysChangeRecordParaModel., "申請單號");
                    //stringBuilder2.GetLogString(data.apply_number, "申請單號");
                    foreach (SysChangeRecordDetail item in data.sysChangeRecordParaModel.details)
                    {
                        List<string> afterValueList, beforeValueList;
                        //修改主体时日志
                        if (item.change_code == "entity_id")
                        {
                            afterValueList = entitylist.Where(x => x.EntityId == item.after_value).Select(x => "(" + x.Entity + ")" + x.EntityNamec + " " + x.EntityNamee).ToList();
                            beforeValueList = entitylist.Where(x => x.EntityId == item.before_value).Select(x => "(" + x.Entity + ")" + x.EntityNamec + " " + x.EntityNamee).ToList();
                        }
                        else
                        {
                            afterValueList = listData.Where(x => x.ParaCode == item.change_code_source && item.after_value == x.FuncCode).Select(x => x.FunName).ToList();
                            beforeValueList = listData.Where(x => x.ParaCode == item.change_code_source && item.before_value == x.FuncCode).Select(x => x.FunName).ToList();
                        }
                        if (afterValueList == null || afterValueList.Count == 0)
                        {
                            if (item.change_code == "other_party")
                            {
                                if (!string.IsNullOrEmpty(item.after_value)) stringBuilder1.GetLogString(item.after_value.Replace("[", "").Replace("]", "").Replace("\"", ""), item.change_code_cname);
                                else stringBuilder1.GetLogString("-", item.change_code_cname);
                            }
                            else
                            {
                                stringBuilder1.GetLogString(item.after_value, item.change_code_cname);
                            }
                        }
                        else
                        {
                            stringBuilder1.GetLogString(afterValueList, item.change_code_cname);

                        }
                        if (beforeValueList == null || beforeValueList.Count == 0)
                        {
                            if (item.change_code == "other_party")
                            {
                                if (!string.IsNullOrEmpty(item.before_value)) stringBuilder2.GetLogString(item.before_value.Replace("[", "").Replace("]", "").Replace("\"", ""), item.change_code_cname);
                                else stringBuilder2.GetLogString("-", item.change_code_cname);
                            }
                            else
                            {
                                stringBuilder2.GetLogString(item.before_value, item.change_code_cname);
                            }
                        }
                        else
                        {
                            stringBuilder2.GetLogString(beforeValueList, item.change_code_cname);

                        }
                    }
                    log.Detail = stringBuilder1.ToString();
                    log.DetailFormer = stringBuilder2.ToString();
                }

            });
            #endregion
            return await Task.FromResult(apiResult);
        }
        #endregion

        #region 資料建檔歸檔資訊
        /// <summary>
        /// 資料建檔歸檔資訊
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("ArchiveInfoModify_Literature")]
        public async Task<ApiResultModel> ArchiveInfoModify_Literature([FromBody] DocumentationContractViewModel data)
        {
            ApiResultModel apiResult = new();
            apiResult.rtnSuccess = FormInfoModifyService.ArchiveInfoModify_Literature(data);
            return await Task.FromResult(apiResult);
        }
        #endregion

        #endregion
    }
}
