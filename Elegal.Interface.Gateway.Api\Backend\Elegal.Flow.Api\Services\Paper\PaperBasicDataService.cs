﻿using Elegal.Flow.Api.Repository.Paper;
using Elegal.Interface.Api.Common.Control.Filter;
using Elegal.Interface.Api.Common.FuncHelper;
using Elegal.Interface.Api.Common.FuncService;
using Elegal.Interface.Api.Common.Model.DBModel;
using Elegal.Interface.Api.Common.Model.ParaModel.FlowApi;
using Elegal.Interface.Api.Common.Model.ResultModel.FlowApi;
using Elegal.Interface.Api.Common.Model.ViewModel.FlowApi;
using Elegal.Interface.ApiData.Service.FuncService;
using Elegal.Interface.ApiData.Service.Model.DbModel;
using Elegal.Interface.ApiData.Service.Model.QueryCondition;
using Elegal.Orm;
using Elegal.Orm.Dtos;
using Newtonsoft.Json;
using NPOI.SS.UserModel;
using NPOI.XSSF.UserModel;

namespace Elegal.Flow.Api.Services.Paper
{
    /// <summary>
    /// 紙本基本資料服務層
    /// 處理紙本文件的基本資料維護，包含新增、修改、查詢及匯出等功能
    /// </summary>
    public static class PaperBasicDataService
    {
        private static readonly PaperBasicDataRepository _repository = new();
        private static readonly PaperOldDataRepository _oldDataRepository = new();
        private static readonly PaperLendingApplicationRepository _lendingRepository = new();

        #region 查詢存放位置清單
        /// <summary>
        /// 查詢存放位置清單
        /// </summary>
        /// <returns>所有紙本存放位置列表</returns>
        public static IEnumerable<string> GetPaperPositionList()
        {
            return _repository.GetPaperPositionList();
        }
        #endregion

        #region 紙本基本資料查詢
        /// <summary>
        /// 紙本基本資料查詢
        /// </summary>
        /// <param name="qry">查詢條件</param>
        /// <returns>分頁的紙本資料列表</returns>
        public static PageResult<PaperBasicDataViewModel> QueryPaperBasicData(qryPaperBasicData qry)
        {
            return _repository.QueryPaperBasicData_New(qry);
        }
        #endregion

        #region 紙本基本資料匯出Excel
        /// <summary>
        /// 紙本基本資料匯出Excel
        /// </summary>
        /// <param name="qry">查詢條件</param>
        /// <returns>Excel檔案的byte陣列</returns>
        /// <remarks>
        /// 匯出欄位包含：
        /// - 紙本編號
        /// - 合約編號
        /// - 紙本名稱
        /// - 入庫狀態
        /// - 存放位置
        /// - 紙本現狀
        /// - 我方主體
        /// - 他方主體
        /// - 紙本類型
        /// - 機密等級
        /// </remarks>
        public static byte[] ExportPaperBasicData(qryPaperBasicData qry)
        {

            qry.page = new PageParam() { PageSize = 999999, PageIndex = 1 };
            List<PaperBasicDataViewModel> resData = _repository.QueryPaperBasicData_New(qry).Data;


            //创建工作表
            IWorkbook workbook = new XSSFWorkbook();
            ISheet sheet = workbook.CreateSheet("Sheet1");

            //设置表头
            List<string> heards = new List<string>()
            {
                ActionFilter.GetMultilingualValue("export:export_ExportPaperBasicData:paper_code"),
                ActionFilter.GetMultilingualValue("export:export_ExportPaperBasicData:contract_number"),
                ActionFilter.GetMultilingualValue("export:export_ExportPaperBasicData:paper_name"),
                ActionFilter.GetMultilingualValue("export:export_ExportPaperBasicData:paper_entry_status_name"),
                ActionFilter.GetMultilingualValue("export:export_ExportPaperBasicData:paper_position"),
                ActionFilter.GetMultilingualValue("export:export_ExportPaperBasicData:paper_return_status_name"),
                ActionFilter.GetMultilingualValue("export:export_ExportPaperBasicData:my_entity"),
                ActionFilter.GetMultilingualValue("export:export_ExportPaperBasicData:party_a"),
                ActionFilter.GetMultilingualValue("export:export_ExportPaperBasicData:paper_type_name"),
                ActionFilter.GetMultilingualValue("export:export_ExportPaperBasicData:confiden_level_name")
            };
            sheet.SetHeader(heards);

            //设置列宽
            sheet.SetColumnListWidth(new List<int>() { 25 * 256, 25 * 256, 60 * 256, 15 * 256, 25 * 256, 20 * 256, 25 * 256, 25 * 256, 20 * 256, 15 * 256 });


            ICellStyle defaultstyle = sheet.Workbook.CreateCellStyle().SetDefaultBorder();
            ICellStyle wrapTextStyle = sheet.Workbook.CreateCellStyle().SetDefaultBorder().SetwrapTextOn();

            //写入数据
            sheet.WriteData(resData, (item, row) =>
            {
                row.CreateElegalDefaultCell(0, defaultstyle).SetCellValue(item.paper_code);
                row.CreateElegalDefaultCell(1, defaultstyle).SetCellValue(item.contract_number);
                row.CreateElegalDefaultCell(2, defaultstyle).SetCellValue(item.paper_name);
                row.CreateElegalDefaultCell(3, defaultstyle).SetCellValue(item.paper_entry_status_name);
                row.CreateElegalDefaultCell(4, defaultstyle).SetCellValue(item.paper_position);
                row.CreateElegalDefaultCell(5, defaultstyle).SetCellValue(item.paper_return_status_name);
                row.CreateElegalDefaultCell(6, defaultstyle).SetCellValue(item.my_entity);
                row.CreateElegalDefaultCell(7, defaultstyle).SetCellValue(ConvertPartyAString(item.party_a));
                row.CreateElegalDefaultCell(8, wrapTextStyle).SetCellValue(item.paper_type_name);
                row.CreateElegalDefaultCell(9, defaultstyle).SetCellValue(item.confiden_level_name);
            });
            return workbook.ToBytes();
        }
        #endregion

        #region 處理他方主體字串格式轉換
        /// <summary>
        /// 處理他方主體字串格式轉換
        /// </summary>
        /// <param name="party_a">原始他方主體字串</param>
        /// <returns>格式化後的他方主體字串</returns>
        /// <remarks>
        /// 處理兩種格式：
        /// 1. JSON格式的字串列表
        /// 2. 以"/"分隔的字串
        /// </remarks>
        private static string ConvertPartyAString(string party_a)
        {
            if (string.IsNullOrEmpty(party_a)) return string.Empty;

            List<string> items = new List<string>();
            try
            {
                // 反序列化 JSON 字串為 List<string>
                items = JsonConvert.DeserializeObject<List<string>>(party_a);
            }
            catch
            {
                items = party_a.Split("/").ToList();
            }

            return string.Join("\r\n ", items);
        }
        #endregion

        #region 新增紙本基本資料(事務處理)
        /// <summary>
        /// 新增紙本基本資料
        /// </summary>
        /// <param name="data">紙本基本資料</param>
        /// <returns>新增是否成功</returns>
        /// <remarks>
        /// 處理流程：
        /// 1. 檢查紙本編號是否重複
        /// 2. 驗證並處理申請單資料
        /// 3. 新增紙本基本資料
        /// 4. 記錄紙本歷程
        /// </remarks>
        /// <exception cref="Exception"></exception>
        public static bool AddPaperApplicationData(paper_basic_data data)
        {
            paper_basic_data basic_Data = _repository.GetPaperBasicData(data.paper_code);
            if (basic_Data != null)
                throw new Exception(string.Format(ActionFilter.GetMultilingualValue("custom:messageContent:OldPaperData_repeat"), data.paper_code));

            DbAccess.PerformInTransaction(context =>
            {
                #region 主表資訊
                //首先驗證 paper_application_data 是否存在單號，如果存在則不需要插入，並且顯示頁面上需要數據
                PaperApplicationDataViewModel main = new PaperApplicationDataViewModel();
                paper_application_data applicationData = null;
                main = _repository.GetPaperApplicationDataToTransaction(data.apply_number, context);
                if (main != null)
                    applicationData = _repository.GetPaperApplicationDataByApplicationIdToTransaction(main.application_id, context);

                V_GetUnConfirmedApplication oldData = _repository.GetAllOldApplicationToTransaction(data.apply_number, context);
                //如果 1 跟 2 都不存在數據，給出提示 當前單號不存在於資料表中，請確認後再操作！
                if (main == null && oldData == null)
                    throw new Exception(ActionFilter.GetMultilingualValue("custom:messageContent:getPaperDataFail"));
                //如為2.0歸檔來的且歸檔時選擇無紙本，則不允許新增紙本
                if (oldData != null && oldData.is_old_system == 0)
                {
                    if (applicationData != null && applicationData.having_paper == 0)
                        throw new Exception(ActionFilter.GetMultilingualValue("custom:messageContent:AddPaperApplicationError"));
                }
                //如果 paper_application_data 不存在單號，則需要 查看 V_GetUnConfirmedApplication 中是否存在
                if (main == null)
                {
                    //插入舊數據
                    paper_application_data newData = new paper_application_data();
                    newData.apply_number = oldData.apply_number;
                    newData.my_entity_id = oldData.entity_id;
                    newData.party_a = oldData.party_a;
                    newData.pic_emplid = oldData.emplid;
                    newData.pic_deptid = oldData.deptid;
                    newData.contract_number = oldData.contract_number;
                    newData.contract_name = oldData.contract_name;
                    newData.having_paper = 1;
                    newData.is_old_application = oldData.is_old_system;
                    newData.application_status = "02";
                    newData.create_user = MvcContext.UserInfo.current_emp;
                    newData.create_time = DateTime.UtcNow;

                    if (!_repository.InsertPaperApplicationDataToTransaction(newData, context)) throw new Exception(ActionFilter.GetMultilingualValue("custom:messageTitle:createFail"));

                    main = _repository.GetPaperApplicationDataToTransaction(data.apply_number, context);
                }
                //如紙本確認狀態為"未確認"，須更新paper_application_data
                if (main.application_status == "01" && oldData != null)
                {
                    paper_application_data newData = new paper_application_data();
                    newData.apply_number = oldData.apply_number;
                    newData.my_entity_id = oldData.entity_id;
                    newData.party_a = oldData.party_a;
                    newData.pic_emplid = oldData.emplid;
                    newData.pic_deptid = oldData.deptid;
                    newData.having_paper = 1;
                    newData.contract_number = oldData.contract_number;
                    newData.contract_name = oldData.contract_name;
                    newData.modify_user = MvcContext.UserInfo.current_emp;
                    newData.modify_time = DateTime.UtcNow;

                    if (!_repository.UpdatePaperApplicationDataToTransaction(newData, context)) throw new Exception(ActionFilter.GetMultilingualValue("custom:messageTitle:createFail"));
                }
                #endregion

                #region 紙本基本資料(擴展表)

                data.paper_applica_id = main.application_id;
                data.create_user = MvcContext.UserInfo.current_emp;
                data.create_time = DateTime.UtcNow;

                //如果沒塞則紙本現狀預設"05"正常
                if (string.IsNullOrEmpty(data.paper_return_status)) data.paper_return_status = "05";

                if (!_repository.InsertPaperBasicDataToTransaction(data, context)) throw new Exception(ActionFilter.GetMultilingualValue("custom:messageTitle:createFail"));

                #endregion

                #region 插入歷程數據
                paper_basic_data basic = _repository.GetPaperBasicDataToTransaction(data.paper_code, context);
                if (basic == null) throw new Exception(ActionFilter.GetMultilingualValue("custom:messageTitle:createFail"));

                paper_history_data history = new paper_history_data();
                history.paper_basic_id = basic.basic_id.Value;
                history.paper_entry_status = basic.paper_entry_status;
                history.course_emplid = MvcContext.UserInfo.current_emp;
                history.create_user = MvcContext.UserInfo.current_emp;
                history.create_time = DateTime.UtcNow;

                if (data.destroy_time != null)
                {
                    history.paper_remarks = "銷毀日期: " + data.destroy_time.Value.ToString("yyyy/MM/dd");
                    if (!string.IsNullOrEmpty(data.destroy_reason)) history.paper_remarks += " ; 銷毀原因: " + data.destroy_reason;
                }
                else if (data.lost_time != null)
                {
                    history.paper_remarks = "遺失日期: " + data.lost_time.Value.ToString("yyyy/MM/dd");
                    if (!string.IsNullOrEmpty(data.lost_reason)) history.paper_remarks += " ; 遺失原因: " + data.lost_reason;
                }
                else
                {
                    history.paper_remarks = basic.paper_remarks;
                }

                if (!_repository.InsertPaperHistoryDataToTransaction(history, context)) throw new Exception(ActionFilter.GetMultilingualValue("custom:messageTitle:createFail"));

                #endregion
            });

            return true;
        }
        #endregion

        #region 修改紙本基本資料(事務處理)
        /// <summary>
        /// 修改紙本基本資料
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public static bool UpdatePaperBasicData(paper_basic_data data)
        {
            //檢查該basic_id是否存在
            PaperBasicApplicationViewModel basic_Data = _repository.GetPaperBasicDataByBasicID(data.basic_id.ToString());
            if (basic_Data == null)
                throw new Exception(ActionFilter.GetMultilingualValue("custom:messageContent:dataChurn"));

            //如果該basic_id的paper_code跟修改後的paper_code不一致，則需要檢查paper_code是否重複
            if (basic_Data.paper_code != data.paper_code)
            {
                //紙本沒有借出申請單時，才可以修改紙本編號(只要有借出過，都不可更改)
                if (_lendingRepository.CheckPaperLendingDetail(basic_Data.paper_code))
                    throw new Exception(ActionFilter.GetMultilingualValue("custom:messageContent:paperModifiedError"));

                //檢查該paper_code是否重複
                paper_basic_data paper_basic_data = _repository.GetPaperBasicData(data.paper_code);
                if (paper_basic_data != null)
                    throw new Exception(ActionFilter.GetMultilingualValue("custom:messageContent:repeatAll"));
            }

            DbAccess.PerformInTransaction(context =>
            {
                if (data.destroy_time != null)
                    data.destroy_time = TimeZoneInfoConvertHelper.ConvertDateByTimeZoneByLocal(data.destroy_time.Value, MvcContext.UserInfo.time_zone);
                if (data.lost_time != null)
                    data.lost_time = TimeZoneInfoConvertHelper.ConvertDateByTimeZoneByLocal(data.lost_time.Value, MvcContext.UserInfo.time_zone);
                data.modify_time = DateTime.UtcNow;
                data.modify_user = MvcContext.UserInfo.current_emp;
                //插入歷程數據
                paper_history_data history = new paper_history_data();
                history.paper_basic_id = data.basic_id.Value;
                history.paper_entry_status = data.paper_entry_status;
                history.course_emplid = MvcContext.UserInfo.current_emp;
                history.create_user = MvcContext.UserInfo.current_emp;
                history.create_time = DateTime.UtcNow;
                if (data.destroy_time != null)
                {
                    history.paper_remarks = "銷毀日期: " + data.destroy_time.Value.ToString("yyyy/MM/dd");
                    if (!string.IsNullOrEmpty(data.destroy_reason)) history.paper_remarks += " ; 銷毀原因: " + data.destroy_reason;
                }
                else if (data.lost_time != null)
                {
                    history.paper_remarks = "遺失日期: " + data.lost_time.Value.ToString("yyyy/MM/dd");
                    if (!string.IsNullOrEmpty(data.lost_reason)) history.paper_remarks += " ; 遺失原因: " + data.lost_reason;
                }
                else
                {
                    history.paper_remarks = data.paper_remarks;
                }
                //插入紙本歷程
                if (!_repository.InsertPaperHistoryDataToTransaction(history, context))
                    throw new Exception(ActionFilter.GetMultilingualValue("custom:messageTitle:createFail"));
                //更新紙本基本資料
                if (!_repository.UpdatePaperBasicDataToTransaction(data, context))
                    throw new Exception(ActionFilter.GetMultilingualValue("custom:messageTitle:createFail"));
            });

            return true;
        }
        #endregion

        #region 刪除紙本基本資料(事務處理)
        /// <summary>
        /// 刪除紙本基本資料
        /// </summary>
        /// <param name="basic_id"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public static bool DeletePaperBasicData(string basic_id)
        {
            PaperBasicApplicationViewModel basic_Data = _repository.GetPaperBasicDataByBasicID(basic_id);
            if (basic_Data == null)
                throw new Exception(ActionFilter.GetMultilingualValue("custom:messageContent:dataChurn"));

            //修改為只要有出現在借出申請單中，就不可刪除(包含暫存的申請單、作廢的申請單)
            if (_lendingRepository.CheckPaperLendingDetail(basic_Data.paper_code))
                throw new Exception(ActionFilter.GetMultilingualValue("custom:messageContent:paperDeletedError"));

            DbAccess.PerformInTransaction(context =>
            {
                if (!_repository.DeletePaperBasicDataToTransaction(basic_id, context))
                    throw new Exception(ActionFilter.GetMultilingualValue("custom:messageTitle:updateFail"));

                //舊資料紙本確認裡將申請單進行紙本確認後，在紙本基本資料維護裡將這個申請單下的紙本都刪除，在舊資料紙本確認裡這個申請單就應該是未確認狀態
                List<paper_basic_data> basicList = _repository.GetPaperBasicDataListToTransaction(basic_Data.paper_applica_id.Value, context);
                if (basicList.Count == 0)
                {
                    if (!_oldDataRepository.UpdatePaperApplicationStatusToTransaction(basic_Data.paper_applica_id.Value, "01", "", 1, context)) throw new Exception(ActionFilter.GetMultilingualValue("custom:messageTitle:updateFail"));
                }
            });


            return true;
        }
        #endregion

        #region 查詢紙本基本資料(For AutoComplete)
        /// <summary>
        /// 查詢紙本基本資料(For AutoComplete)
        /// </summary>
        public static List<PaperApplicationDataViewModel> GetPaperApplicationDataList(string apply_number)
        {
            List<PaperApplicationDataViewModel> res = _repository.GetPaperApplicationDataList(apply_number);
            return res;
        }
        #endregion

        #region 查詢紙本歷程
        /// <summary>
        /// 查詢紙本歷程
        /// </summary>
        /// <param name="paper_basic_id"></param>
        /// <returns></returns>
        public static List<PaperHistoryDataViewModel> GetPaperHistoryData(string paper_basic_id)
        {
            PaperBasicApplicationViewModel data = _repository.GetPaperBasicDataByBasicID(paper_basic_id);
            if (data == null) throw new Exception(ActionFilter.GetMultilingualValue("custom:messageContent:dataChurn"));

            List<PaperHistoryDataViewModel> res = _repository.GetPaperHistoryData(paper_basic_id);
            foreach (PaperHistoryDataViewModel item in res)
            {
                item.operate_time = item.operate_time.ConvertDateByTimeZoneByUtc(MvcContext.UserInfo.time_zone);
            }
            return res;
        }
        #endregion

        #region 取得紙本基本資料，包含時區轉換處理
        /// <summary>
        /// 取得紙本基本資料，包含時區轉換處理
        /// </summary>
        /// <param name="basic_id">紙本ID</param>
        /// <returns>紙本基本資料視圖模型</returns>
        /// <exception cref="Exception">當資料不存在時拋出異常</exception>
        /// <remarks>
        /// 處理流程：
        /// 1. 查詢紙本基本資料
        /// 2. 進行時區轉換(create_time, destroy_time, lost_time, modify_time)
        /// 3. 清理特殊字元(\u001f)
        /// </remarks>
        public static PaperBasicApplicationViewModel GetPaperBasicDataByBasicID(string basic_id)
        {
            PaperBasicApplicationViewModel res = _repository.GetPaperBasicDataByBasicID(basic_id);
            if (res == null) throw new Exception(ActionFilter.GetMultilingualValue("custom:messageContent:dataChurn"));

            res.create_time = res.create_time.Value.ConvertDateByTimeZoneByUtc(MvcContext.UserInfo.time_zone);
            if (res.destroy_time != null) res.destroy_time = res.destroy_time.Value.ConvertDateByTimeZoneByUtc(MvcContext.UserInfo.time_zone);
            if (res.lost_time != null) res.lost_time = res.lost_time.Value.ConvertDateByTimeZoneByUtc(MvcContext.UserInfo.time_zone);
            if (res.modify_time != null) res.modify_time = res.modify_time.Value.ConvertDateByTimeZoneByUtc(MvcContext.UserInfo.time_zone);

            if (!string.IsNullOrEmpty(res.contract_name)) res.contract_name = res.contract_name.Replace("\u001f", "");
            if (!string.IsNullOrEmpty(res.contract_number)) res.contract_number = res.contract_number.Replace("\u001f", "");

            return res;
        }
        #endregion

        #region 取得紙本基本資料，不進行時區轉換
        /// <summary>
        /// 取得紙本基本資料，不進行時區轉換
        /// </summary>
        /// <param name="basic_id">紙本ID</param>
        /// <returns>紙本基本資料視圖模型</returns>
        /// <exception cref="Exception">當資料不存在時拋出異常</exception>
        public static PaperBasicApplicationViewModel GetPaperBasicData(string basic_id)
        {
            PaperBasicApplicationViewModel res = _repository.GetPaperBasicDataByBasicID(basic_id);
            if (res == null) throw new Exception(ActionFilter.GetMultilingualValue("custom:messageContent:dataChurn"));
            return res;
        }
        #endregion

        #region 取得系統參數對應的顯示文字
        /// <summary>
        /// 取得系統參數對應的顯示文字
        /// </summary>
        /// <param name="type">參數類型代碼</param>
        /// <param name="value">參數值，支援多值(以逗號分隔)</param>
        /// <returns>對應的顯示文字，多值時以逗號分隔</returns>
        /// <remarks>
        /// 處理流程：
        /// 1. 根據當前用戶語系查詢系統參數
        /// 2. 將參數值轉換為對應的顯示文字
        /// 3. 多值時進行合併處理
        /// </remarks>
        public static string GetTypeString(string type, string value)
        {
            SysParametersQueryCondition qry = new SysParametersQueryCondition();
            qry.SearchItemGroup = new SearchItemGroup()
            {
                Items = new List<SearchItem>(),
                Logic = LogicOperator.And
            };
            qry.SearchItemGroup.Items.Add(new SearchItem()
            {
                Field = "para_code",
                Logic = LogicOperator.And,
                Value = type,
                Compare = CompareOperator.EQ
            });
            qry.SearchItemGroup.Items.Add(new SearchItem()
            {
                Field = "lang_type",
                Logic = LogicOperator.And,
                Value = MvcContext.UserInfo.logging_locale,
                Compare = CompareOperator.EQ
            });
            List<SysParameters> res = SysParametersDataService.Query(qry);
            List<string> valStr = value.Split(",").ToList();
            List<string> resStr = new List<string>();
            foreach (string val in valStr)
            {
                SysParameters sysParameters = res.Find(s => s.FuncCode == val);
                if (sysParameters != null) resStr.Add(sysParameters.FunName);
            }

            return string.Join(",", resStr);
        }
        #endregion

        #region 取得主體代碼對應的主體名稱
        /// <summary>
        /// 取得主體代碼對應的主體名稱
        /// </summary>
        /// <param name="value">主體代碼，支援多值(以逗號分隔)</param>
        /// <returns>主體名稱，多值時以逗號分隔</returns>
        /// <remarks>
        /// 處理流程：
        /// 1. 查詢所有主體資料
        /// 2. 將主體代碼轉換為對應的主體名稱
        /// 3. 多值時進行合併處理
        /// </remarks>
        public static string GetEntityString(string value)
        {
            List<string> entityList = value.Split(",").ToList();
            FnpEntityQueryCondition condition = new FnpEntityQueryCondition();
            condition.SearchItemGroup = new SearchItemGroup()
            {
                Items = new List<SearchItem>(),
                Logic = LogicOperator.And
            };
            List<FnpEntity> res = FnpEntityDataService.Query(condition);
            List<string> valStr = value.Split(",").ToList();
            List<string> resStr = new List<string>();
            foreach (string val in valStr)
            {
                FnpEntity fnpEntity = res.Find(s => s.EntityId == val);
                if (fnpEntity != null) resStr.Add(fnpEntity.Entity);
            }

            return string.Join(",", resStr);
        }
        #endregion

        #region 取得下一個紙本編號
        /// <summary>
        /// 取得下一個紙本編號
        /// </summary>
        /// <param name="applyNumber">申請單號</param>
        /// <returns>下一個可用的紙本編號，格式為"XX"，若超過99則返回null</returns>
        /// <remarks>
        /// 處理邏輯：
        /// 1. 檢查申請單是否存在
        /// 2. 檢查是否有現有紙本資料
        /// 3. 計算下一個序號(最大序號+1)
        /// 4. 序號超過99時返回null
        /// </remarks>
        public static string GetNextPaperCode(string applyNumber)
        {
            //沒有申請單號，返回00
            PaperApplicationDataViewModel appli = _repository.GetPaperApplicationData(applyNumber);
            if (appli == null) return "00";

            //沒有紙本資料，返回00
            List<paper_basic_data> basics = _repository.GetPaperBasicDataList(appli.application_id);
            if (basics == null || basics.Count == 0) return "00";

            //取得最大的紙本編號
            paper_basic_data basic = basics.OrderByDescending(b => b.paper_code).First();
            int next_code = Convert.ToInt32(basic.paper_code.Split("-")[1]) + 1;

            if (next_code > 99)
            {
                return null;
            }
            else
            {
                return next_code.ToString().PadLeft(2, '0');
            }
        }
        #endregion
    }
}
