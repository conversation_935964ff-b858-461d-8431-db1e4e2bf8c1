﻿using Elegal.Flow.Api.Repository.Process;
using Elegal.Interface.Api.Common.Control.Filter;
using Elegal.Interface.Api.Common.Model.Enum;
using Elegal.Interface.Api.Common.Model.ParaModel.FlowApi;
using Elegal.Interface.Api.Common.Model.ResultModel;
using Elegal.Interface.Api.Common.Model.ResultModel.FlowApi;
using Elegal.Interface.Api.Common.Model.ViewModel.FlowApi;
using Elegal.Interface.ApiData.Service.FuncService;
using Elegal.Interface.ApiData.Service.Model.DbModel;
using Elegal.Interface.ApiData.Service.Model.QueryCondition;
using Elegal.Interface.ApiData.Service.Model.ViewModel;
using Elegal.Orm;
using Elegal.Orm.Dtos;
using Elegal.Orm.Utils;
using System.Text;

namespace Elegal.Flow.Api.Services.Process
{
    /// <summary>
    /// 董監事管理
    /// </summary>
    public static class DirectorsSupervisorsManagementService
    {
        private static readonly DirectorsSupervisorsManagementRepository _repository = new();

        /// <summary>
        /// 董監事管理頁面查詢
        /// </summary>
        /// <returns></returns>
        public static List<DirectorsSupervisorsResult> QueryDirectorsSupervisors(DirectorsSupervisorsSearchModel DirectorsSupervisors, string logging_locale, string time_zone)
        {
            List<DirectorsSupervisorsResult> DirectorsSupervisorsResults = _repository.QueryDirectorsSupervisors(DirectorsSupervisors, logging_locale, time_zone);
            ActionFilter.InitLogRecord(DirectorsSupervisors, log =>
            {
                StringBuilder stringBuilder = new();
                if (DirectorsSupervisors.title_types != null && DirectorsSupervisors.title_types.Count > 0)
                    stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("DirectorsSupervisorsManagement_titletype", true)}：{string.Join(",", DirectorsSupervisors.title_types.Select(kvp => kvp.Value))}");
                if (!string.IsNullOrEmpty(DirectorsSupervisors.area.Value))
                    stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("DirectorsSupervisorsManagement_area", true)}：{DirectorsSupervisors.area.Value}");
                if (!string.IsNullOrEmpty(DirectorsSupervisors.entity.Value))
                    stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("DirectorsSupervisorsManagement_fnpEntity", true)}：{DirectorsSupervisors.entity.Value}");
                if (!string.IsNullOrEmpty(DirectorsSupervisors.emp_id))
                    stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("DirectorsSupervisorsManagement_name", true)}：{DirectorsSupervisors.emp_id}");
                if (DirectorsSupervisors.filterList.Any())
                {
                    stringBuilder.AppendLine(CommonUtil.GetDivisionLine(ActionFilter.GetMultilingualValue("DirectorsSupervisorsManagement_filter", true)));
                    stringBuilder.AppendLine(string.Join("\r\n", DirectorsSupervisors.filterList.Select(s => $"{ActionFilter.GetMultilingualValue($"DirectorsSupervisorsManagement_export:{Nomenclature.ToCamel(s.Key)}", true)}：{s.Value}")));
                }
                log.Detail = stringBuilder.ToString();
            });
            foreach (var item in DirectorsSupervisorsResults)
            {
                item.Status = item.termination.HasValue ? JobStatusUtils.LeaveJob : JobStatusUtils.OnJob;
            }
            return DirectorsSupervisorsResults;
        }

        /// <summary>
        /// 董監事管理頁面新增数据
        /// </summary>
        /// <param name="model">新增参数</param>
        /// <returns></returns>
        public static ApiResultModelByObject AddData(DirectorsSupervisorsSearchModel model)
        {
            ApiResultModelByObject apiResult = new();
            //判斷職稱類型是否存在
            if (!DbAccess.Exists<SysParameters>(new SysParametersViewModel() { ParaCode = "dsTitle", FuncCode = model.title_types[0].Key, LangType = MvcContext.UserInfo.logging_locale }))
            {
                return new ApiResultModelByObject()
                {
                    messageTitle = ActionFilter.GetMultilingualValue("custom:messageTitle:dataNotexist"),
                    messageContent = new List<string> { $"{model.title_types[0].Value} {ActionFilter.GetMultilingualValue("custom:messageContent:directorsSupervisorsDeleted")}" }
                };
            }

            //判斷區域是否存在
            if (!DbAccess.Exists<SysArea>(new SysArea() { AreaId = model.area.Key }))
            {
                return new ApiResultModelByObject()
                {
                    messageTitle = ActionFilter.GetMultilingualValue("custom:messageTitle:dataNotexist"),
                    messageContent = new List<string> { $"{ActionFilter.GetMultilingualValue("custom:messageContent:nullArea")}：{model.area.Value}" }
                };
            }

            //判斷主體是否存在
            foreach (var entityId in model.entities)
            {
                if (!DbAccess.Exists<FnpEntity>(new FnpEntityViewModel() { EntityId = entityId.Key }))
                {
                    return new ApiResultModelByObject()
                    {
                        messageTitle = ActionFilter.GetMultilingualValue("custom:messageTitle:dataNotexist"),
                        messageContent = new List<string> { $"{ActionFilter.GetMultilingualValue("custom:messageContent:entityDeleted")}：{entityId.Value}" }
                    };
                }
            }

            List<string> repeatData = new();
            List<string> addData = new();
            List<string> logs = new List<string>();
            var users = PsSubEeLglVwADataService.Query(new PsSubEeLglVwAQueryCondition()
            {
                SearchItemGroup = new SearchItemGroup()
                {
                    Logic = LogicOperator.And,
                    Items = new List<SearchItem>()
                    {
                        new SearchItem()
                        {
                            Field="emplid",
                            Value=string.Join(',',model.UserList.Select(s=>s.Emplid)),
                            Compare=CompareOperator.IN
                        }
                    }
                }
            });

            var hasCharEmplid = model.UserList.Where(w => char.IsLetter(w.Emplid[0]));
            var orderEmplid = new List<UserEntityModel>();
            orderEmplid.AddRange(model.UserList.Except(hasCharEmplid).OrderByDescending(o => o.Emplid.Length).OrderByDescending(o => o.Emplid));
            orderEmplid.AddRange(hasCharEmplid.OrderBy(b => b.Emplid));

            foreach (var entity in model.entities.OrderBy(b => b.Value))
            {
                foreach (var user in orderEmplid)
                {
                    var psSubEeLglVwA = users.First(f => f.Emplid.Equals(user.Emplid));
                    string statusName = psSubEeLglVwA.Termination.HasValue ? ActionFilter.GetMultilingualValue("commonWord:incumbent_0") : ActionFilter.GetMultilingualValue("commonWord:incumbent_1");
                    string name = string.IsNullOrEmpty(user.Name) ? "" : $"({user.Name})";
                    StringBuilder detail = new();
                    detail.AppendLine($"{ActionFilter.GetMultilingualValue("DirectorsSupervisorsManagement_fnpEntity", true)}：{entity.Value}");
                    detail.AppendLine($"{ActionFilter.GetMultilingualValue("DirectorsSupervisorsManagement_titletype", true)}：{model.title_types[0].Value}");
                    detail.AppendLine($"{ActionFilter.GetMultilingualValue("DirectorsSupervisorsManagement_deptid", true)}：{user.Deptid}");
                    detail.AppendLine($"{ActionFilter.GetMultilingualValue("DirectorsSupervisorsManagement_empId", true)}：{user.Emplid}");
                    detail.AppendLine($"{ActionFilter.GetMultilingualValue("DirectorsSupervisorsManagement_cName", true)}：{user.Name}");
                    detail.AppendLine($"{ActionFilter.GetMultilingualValue("DirectorsSupervisorsManagement_eName", true)}：{user.NameA}");
                    detail.AppendLine($"{ActionFilter.GetMultilingualValue("DirectorsSupervisorsManagement_prefixPhone", true)}：{(string.IsNullOrEmpty(psSubEeLglVwA.PrefixDialCodeA) ? "0000" : psSubEeLglVwA.PrefixDialCodeA)}+{psSubEeLglVwA.PhoneA}"); 
                    detail.AppendLine($"{ActionFilter.GetMultilingualValue("DirectorsSupervisorsManagement_titleRemark", true)}：{model.title_remark}");

                    string content = $"{ActionFilter.GetMultilingualValue("DirectorsSupervisorsManagement_fnpEntity", true)}：{entity.Value}；{user.NameA}{name}";
                    DirectorsSupervisorsViewModel DirectorsSupervisorsManagementViewModel = new()
                    {
                        EmpId = user.Emplid,
                        EntityId = entity.Key,
                        TitleType = model.title_types[0].Key
                    };
                    if (DbAccess.Exists<DirectorsSupervisors>(DirectorsSupervisorsManagementViewModel))
                    {
                        repeatData.Add($"<p class='rem_3'>{content}</p>");
                    }
                    else
                    {
                        DirectorsSupervisors DirectorsSupervisorsManagement = new DirectorsSupervisors()
                        {
                            EmpId = user.Emplid,
                            EntityId = entity.Key,
                            CreateUser = model.CreateUser,
                            CreateTime = DateTime.UtcNow,
                            TitleType = model.title_types[0].Key,
                            TitleRemark = model.title_remark
                        };
                        DbAccess.Create(DirectorsSupervisorsManagement);
                        logs.Add(detail.ToString());
                        addData.Add(@$"<p class='rem_3'>{content}</p>");
                    }
                }
            }

            if (!addData.Any())
            {
                return new()
                {
                    messageTitle = ActionFilter.GetMultilingualValue("custom:messageTitle:createFail"),
                    messageType = MessageTypeUtils.Warning.ToString(),
                    messageContent = new List<string>() { ActionFilter.GetMultilingualValue("custom:messageContent:repeatAll") }
                };
            }
            apiResult.messageContent.Add(ActionFilter.GetMultilingualValue("custom:messageContent:createSuccess"));
            apiResult.messageContent.Add($"{ActionFilter.GetMultilingualValue("DirectorsSupervisorsManagement_titletype", true)}：{model.title_types[0].Value}");
            if (addData.Any())
                apiResult.messageContent.Add($"{ActionFilter.GetMultilingualValue("commonWord:add")}({addData.Count})：<br />{string.Join("", addData)}");
            if (repeatData.Any())
                apiResult.messageContent.Add($"<div class='red_text'>{ActionFilter.GetMultilingualValue("commonWord:repeat")}({repeatData.Count})：<br />{string.Join("", repeatData)}</div>");
            apiResult.messageTitle = ActionFilter.GetMultilingualValue("custom:messageTitle:createSuccess");
            apiResult.listData = true;
            apiResult.rtnSuccess = true;
            ActionFilter.InitLogRecord<object>(new(), logRecord =>
            {
                logRecord.Detail = string.Join("-------------------------------------\r\n", logs);
            });
            return apiResult;

        }

        /// <summary>
        /// 删除数据
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        internal static ApiResultModelByObject DeleteData(DirectorsSupervisorsResult model)
        {
            DirectorsSupervisors DirectorsSupervisorsManagement = DirectorsSupervisorsDataService.FindByKey(model.rowid);
            //判断删除的数据是否存在
            if (DirectorsSupervisorsManagement == null)
            {
                return new ApiResultModelByObject()
                {
                    messageTitle = ActionFilter.GetMultilingualValue("custom:messageTitle:dataNotexist"),
                    messageContent = new List<string> { ActionFilter.GetMultilingualValue("custom:messageContent:dataChurn") }
                };


            }
            ApiResultModelByObject apiResultModelByObject = new ApiResultModelByObject()
            {
                listData = _repository.DeleteByKey(model.rowid),
                rtnSuccess = true
            };

            ActionFilter.InitLogRecord(model, log =>
            {
                StringBuilder stringBuilder = new();
                stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("DirectorsSupervisorsManagement_fnpEntity", true)}：{model.entity}");
                stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("DirectorsSupervisorsManagement_area", true)}：{model.area_name}");
                stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("DirectorsSupervisorsManagement_titletype", true)}：{model.title_name}");
                stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("DirectorsSupervisorsManagement_empId", true)}：{model.emp_id}");
                stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("DirectorsSupervisorsManagement_cName", true)}：{model.name}");
                stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("DirectorsSupervisorsManagement_eName", true)}：{model.name_a}");
                stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("DirectorsSupervisorsManagement_prefixPhone", true)}：{(string.IsNullOrEmpty(model.prefix_dial_code_a) ? "0000" : model.prefix_dial_code_a)}+{model.phone_a}");
                stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("DirectorsSupervisorsManagement_titleRemark", true)}：{model.title_remark}");
                string empStatus = model.emp_status.Equals(0) ? ActionFilter.GetMultilingualValue("commonWord:incumbent_0") : ActionFilter.GetMultilingualValue("commonWord:incumbent_1");
                stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("DirectorsSupervisorsManagement_statusName", true)}：{empStatus}");
                log.Detail = stringBuilder.ToString();
            });
            return apiResultModelByObject;
        }
    }
}
