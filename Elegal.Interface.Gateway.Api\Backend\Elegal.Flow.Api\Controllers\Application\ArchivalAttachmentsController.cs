﻿using Elegal.Flow.Api.Service;
using Elegal.Interface.Api.Common.Control.Filter;
using Elegal.Interface.Api.Common.Control.Web;
using Elegal.Interface.Api.Common.FuncHelper;
using Elegal.Interface.Api.Common.FuncService;
using Elegal.Interface.Api.Common.Model;
using Elegal.Interface.Api.Common.Model.DBModel;
using Elegal.Interface.Api.Common.Model.Enum;
using Elegal.Interface.Api.Common.Model.ParaModel.FlowApi;
using Elegal.Interface.Api.Common.Model.ParaModel.MinioApi;
using Elegal.Interface.Api.Common.Model.ResultModel;
using Elegal.Interface.Api.Common.Model.ResultModel.FlowApi.FlowStep;
using Elegal.Interface.Api.Common.Model.ResultModel.Mino;
using Elegal.Interface.ApiData.Service.FuncService;
using Elegal.Interface.ApiData.Service.Model.Condition;
using Elegal.Interface.ApiData.Service.Model.DbModel;
using Elegal.Interface.ApiData.Service.Model.QueryCondition;
using Elegal.Interface.ApiData.Service.Model.ViewModel;
using Elegal.Orm;
using Elegal.Orm.Dtos;
using iText.Kernel.Pdf;
using MathNet.Numerics.LinearAlgebra.Factorization;
using Microsoft.AspNetCore.Http.Features;
using Microsoft.AspNetCore.Mvc;
using Minio;
using Minio.DataModel.Args;
using System.IO;
using System.Text;
using GloabSystem = System;
using SysUploadFile = Elegal.Interface.ApiData.Service.Model.DbModel.SysUploadFile;
#nullable disable
namespace Elegal.Flow.Api.Controllers
{
    /// <summary>
    /// 档案附件
    /// </summary>
    [Route("[controller]")]
    [ApiController]
    public class ArchivalAttachmentsController : BaseController
    {
        #region 參數定義
        private readonly MinioClient _minioClient;
        private readonly MinIOConfig _minIOConfig;
        //可轉檔文件後綴名
        private readonly List<string> canTransferSuffix = AppSettingHelper.GetValue("canTransferSuffix").Split(",").ToList();
        #endregion

        #region 構造函數，注入MinioClient、MinIOConfig
        /// <summary>
        /// 構造函數，注入MinioClient、MinIOConfig
        /// </summary>
        /// <param name="minioClient"></param>
        /// <param name="minIOConfig"></param>
        public ArchivalAttachmentsController(MinioClient minioClient, MinIOConfig minIOConfig)
        {
            ArchivalAttachmentsService._minioClient = _minioClient = minioClient;
            ArchivalAttachmentsService._minIOConfig = _minIOConfig = minIOConfig;
        }
        #endregion

        #region 獲取附件信息
        /// <summary>
        /// 獲取附件信息
        /// </summary>
        /// <param name="model">查询参数</param>
        /// <returns></returns>
        [HttpPost]
        [Route("GetBaseInformation")]
        public async Task<ApiResultModelByObject> GetBaseInformation(ArchivalAttachmentsSearchModel model)
        {
            InitLogRecord();
            ApiResultModelByObject apiResult = new ApiResultModelByObject()
            {
                listData = ArchivalAttachmentsService.GetBaseInformation(model.applyNumber, model.applyType),
                rtnSuccess = true,
            };
            return await Task.FromResult(apiResult);
        }
        #endregion

        #region 檢查文件大小(批量上傳，所有文件匯總)
        /// <summary>
        /// 檢查文件大小(批量上傳，所有文件匯總)
        /// </summary>
        /// <param name="option"></param>
        /// <returns></returns>
        [HttpPost("UploadSingleFileCheckSize")]
        public async Task<ApiResultModelByObject> UploadSingleFileCheckSize([FromForm] SysUploadFileParaModel option)
        {
            //20250306新增上傳文件監控，不影響正常流程，可不上PRD
            UploadLogDataService.Create(new UploadLog()
            {
                UploadKey = option.UploadKey,
                UploadType = option.UploadType.ToString(),
                UploadStep = "Start",
                CreateUser = MvcContext.UserInfo.current_emp
            });
            ApiResultModelByObject model = new ApiResultModelByObject() { listData = -1 };
            //上傳的文件
            var uploadFiles = (await Request.ReadFormAsync(new FormOptions { })).Files;
            //申請單下已有的文件(非浮水印、列印合約、參考資料、歸檔)
            var contractFiles = SysUploadFileDataService.Query(new SysUploadFileQueryCondition()
            {
                UploadType = option.UploadType,
                UploadKey = option.UploadKey.ToUpper(),
                IsWatermake = (int)YesOrNoUtils.No,
                SearchItemGroup = new SearchItemGroup()
                {
                    Items = [new SearchItem() { Compare = CompareOperator.ARRAYIN, Field = "archive_purposes", Values = ["1", "2", "3"], Logic = LogicOperator.And }]
                }
            });
            //文件大小总和+上传的文件大小总和不超過80M
            if (contractFiles.Sum(s => s.FileSize) + uploadFiles.Sum(f => f.Length) > 1024 * 1024 * 80)
            {
                model.messageTitle = ActionFilter.GetMultilingualValue("custom:messageTitle:fileUploadFail");
                model.messageContent.Add(ActionFilter.GetMultilingualValue("custom:messageContent:fileSizeMax"));
                model.rtnSuccess = false;
                return model;
            }
            return await Task.FromResult(await ArchivalAttachmentsService.UploadSingleFileCheckSize(uploadFiles, option));
        }
        #endregion

        #region 判斷文件是否重複上傳
        /// <summary>
        /// 判斷文件是否重複上傳
        /// </summary>
        /// <param name="option"></param>
        /// <returns></returns>
        [HttpPost("CheckFileExists")]
        public ApiResultModelByObject CheckFileExists(SysUploadFileCheckModel option)
        {
            ApiResultModelByObject result = new ApiResultModelByObject()
            {
                listData = true,
                rtnSuccess = true,
                messageType = MessageTypeUtils.Success.ToString(),
            };
            var allFiles = SysUploadFileDataService.Query(new SysUploadFileQueryCondition()
            {
                UploadType = option.UploadType,
                UploadKey = option.UploadKey.ToUpper(),
                //ArchivePurposes = option.ArchivePurposes,
                IsWatermake = (int)YesOrNoUtils.No
            }).Where(w => new List<int?>() { 1, 2, 3 }.Contains(w.ArchivePurposes));
            List<string> existsFile = allFiles.Where(w => option.FileNames.FirstOrDefault(f => w.FileName.Equals(f)) != null).Select(s => s.FileName).ToList();
            if (existsFile.Any())
            {
                result.listData = false;
                result.rtnSuccess = false;
                result.messageType = MessageTypeUtils.Warning.ToString();
                //拼接message信息
                string errorMessage = string.Format(ActionFilter.GetMultilingualValue("custom:messageContent:repeatUpload"), string.Join(",", existsFile));
                result.messageContent.Add(errorMessage);
            }
            return result;
        }
        #endregion

        #region 预览pdf转档(SpringJiang已經重新整理邏輯)
        /// <summary>
        /// 预览pdf转档(首次预览保存转档文件)
        /// 如無轉檔文件或轉檔失敗，則會將文件狀態修改為【無轉檔文件】
        /// </summary>
        /// <param name="fileId">文件ID</param>
        /// <returns>通用数据格式(保存pdf文件流)</returns>
        [HttpGet("PreviewPdfStreamSave")]
        public async Task<ApiResultModelByObject> PreviewPdfStreamSave(int fileId)
        {
            ApiResultModelByObject result = new ApiResultModelByObject();
        Start:
            FileContentResult fileContentResult = default;
            //獲取原檔數據
            SysUploadFile sysUploadFile = SysUploadFileDataService.FindByKey(fileId);
            //黨獲取到數據時，進行後續作業
            if (sysUploadFile != null)
            {
                //獲取原檔後綴不帶 [.]
                string suffix = Path.GetExtension(sysUploadFile.FileName).TrimStart('.');
                //驗證原檔文件是否為可轉檔白名單
                if (!canTransferSuffix.Contains(suffix))
                {
                    return new ApiResultModelByObject()
                    {
                        messageType = MessageTypeUtils.Warning.ToString(),
                        messageContent = [string.Format(ActionFilter.GetMultilingualValue("custom:messageContent:notToPdf"), sysUploadFile.FileName)],
                        messageTitle = ActionFilter.GetMultilingualValue("custom:messageTitle:previewFail")
                    };
                }
                //獲取轉檔數據(黨完全沒有做過轉檔或轉檔文件被刪除則無數據)
                SysUploadFile pdfUploadFile = SysUploadFileDataService.Query(new SysUploadFileQueryCondition()
                {
                    OriginalFileId = fileId,
                    SearchItemGroup = new SearchItemGroup()
                    {
                        Items = [
                            new SearchItem() {
                                Compare = CompareOperator.NE, //排除水印文件
                                Value = "101",
                                Field="upload_type"
                            }]
                    }
                }).FirstOrDefault();
                //如果為false表示無轉檔數據，true表示有轉檔數據
                bool hasTranData = pdfUploadFile != null;
                //驗證在minio上是否存在數據(默認存在數據)
                bool hasMinioData = true;
                //有轉檔數據，根據轉檔數據的流進行預覽
                if (hasTranData)
                {
                    #region 如果走到catch，表示存在轉檔數據，但無轉檔文件，需要再次根據原檔進行轉檔
                    try
                    {
                        await _minioClient.StatObjectAsync(new StatObjectArgs().WithBucket(_minIOConfig.Bucket).WithObject(pdfUploadFile.FilePath));
                    }
                    catch (Exception)
                    {
                        hasMinioData = false;
                    }
                    #endregion

                    #region 有轉檔文件，獲取minio上的文件
                    MemoryStream pdfMemory = new MemoryStream();//轉檔文件流
                    try
                    {
                        //確認在minio上面存在文件流
                        if (hasMinioData)
                        {
                            await _minioClient.GetObjectAsync(
                                new GetObjectArgs()
                                .WithBucket(_minIOConfig.Bucket)
                                .WithObject(pdfUploadFile.FilePath)
                                .WithCallbackStream((stream) => { stream.CopyTo(pdfMemory); pdfMemory.Seek(0, SeekOrigin.Begin); }))
                                .ConfigureAwait(false);
                            fileContentResult = File(pdfMemory.ToArray(), "application/json", pdfUploadFile.FileName);
                            return new ApiResultModelByObject() { listData = fileContentResult, rtnSuccess = true };
                        }
                    }
                    catch (Exception)
                    {
                        return new ApiResultModelByObject()
                        {
                            messageType = MessageTypeUtils.Warning.ToString(),
                            messageContent = [ActionFilter.GetMultilingualValue("custom:messageContent:dataChurn")],
                            messageTitle = ActionFilter.GetMultilingualValue("custom:messageTitle:fileNotexist")
                        };
                    }
                    finally
                    {
                        //釋放轉檔資源的文件流
                        pdfMemory.Close();
                    }
                    #endregion
                }

                #region 若無轉檔文件或轉檔文件在資料中存在且minio不存在，則需要根據原始文件在minio添加轉檔文件
                if (!hasTranData || !hasMinioData)
                {
                    //状态判断,若处于转档状态，等待10秒后则回退等待执行
                    if (sysUploadFile.FileStatus.Equals((int)FileStatusUtil.Turning)) { Thread.Sleep(10 * 1000); goto Start; }

                    //定義原檔文件流
                    MemoryStream memoryStream = new MemoryStream();
                    //修改狀態為轉檔中
                    SysUploadFileDataService.Update(new SysUploadFile() { Fileid = sysUploadFile.Fileid, FileStatus = (int)FileStatusUtil.Turning });
                    #region 獲取原檔文件
                    try
                    {
                        await _minioClient.GetObjectAsync(
                            new GetObjectArgs()
                            .WithBucket(_minIOConfig.Bucket)
                            .WithObject(sysUploadFile.FilePath)
                            .WithCallbackStream((stream) => { stream.CopyTo(memoryStream); memoryStream.Seek(0, SeekOrigin.Begin); }))
                            .ConfigureAwait(false);
                    }
                    catch (Exception)
                    {
                        //釋放轉檔資源的文件流
                        memoryStream.Close();
                        SysUploadFileDataService.Update(new SysUploadFile() { Fileid = sysUploadFile.Fileid, FileStatus = (int)FileStatusUtil.None });
                        return new ApiResultModelByObject()
                        {
                            messageType = MessageTypeUtils.Warning.ToString(),
                            messageContent = [ActionFilter.GetMultilingualValue("custom:messageContent:dataChurn")],
                            messageTitle = ActionFilter.GetMultilingualValue("custom:messageTitle:fileNotexist")
                        };
                    }
                    #endregion

                    //表示原檔文件流已經獲取到了，需要進行轉檔作業
                    if (memoryStream != null)
                    {
                        MemoryStream pdfStream = new MemoryStream();
                        try
                        {
                            //轉檔動作
                            pdfStream = (MemoryStream)ToPdfService.ToPdfStream(sysUploadFile.FileType, memoryStream);
                            //轉檔流存在
                            if (pdfStream != null)
                            {
                                #region 轉檔成功后原文件数据更新
                                //更新數據(只修改浮水印狀態+時間+人員+文件狀態)
                                SysUploadFileDataService.Update(new SysUploadFile()
                                {
                                    Fileid = sysUploadFile.Fileid,
                                    HasWatermake = (int)YesOrNoUtils.Yes,
                                    FileStatus = (int)FileStatusUtil.Turned,
                                    ModifyTime = DateTime.UtcNow,
                                    ModifyUser = MvcContext.UserInfo.current_emp
                                });
                                #endregion

                                #region 保存转档文件
                                try
                                {
                                    //拼接pdf路徑
                                    string pdfPath = Path.GetDirectoryName(sysUploadFile.FilePath) + "/" + $"{DateTime.Now:yyyyMMddHHmmss}{Path.GetFileNameWithoutExtension(sysUploadFile.FileName)}.pdf";

                                    #region 在minion上存儲轉檔文件
                                    var putObjectArgs = new PutObjectArgs()
                                                                    .WithBucket(_minIOConfig.Bucket)
                                                                    .WithObject(pdfPath)
                                                                    .WithStreamData(pdfStream)
                                                                    .WithObjectSize(pdfStream.Length)
                                                                    .WithContentType("application/octet-stream");
                                    await _minioClient.PutObjectAsync(putObjectArgs).ConfigureAwait(false);
                                    #endregion

                                    #region 當minio上傳成功后，存儲一筆轉檔數據
                                    sysUploadFile = SysUploadFileDataService.FindByKey(fileId);
                                    sysUploadFile.Fileid = null;
                                    sysUploadFile.FileSize = pdfStream.Length;
                                    sysUploadFile.FileName = $"{Path.GetFileNameWithoutExtension(sysUploadFile.FileName)}.pdf";
                                    sysUploadFile.FileType = "pdf";
                                    sysUploadFile.FilePath = pdfPath;
                                    sysUploadFile.ModifyUser = MvcContext.UserInfo.current_emp;
                                    sysUploadFile.ModifyTime = DateTime.UtcNow;
                                    sysUploadFile.IsWatermake = (int)YesOrNoUtils.Yes;
                                    sysUploadFile.OriginalFileId = fileId;
                                    //新增
                                    SysUploadFileDataService.Create(sysUploadFile);
                                    #endregion

                                    #endregion
                                }
                                //不做動作，可以讓程序直接運行，下次進入轉檔方法時會走無轉檔文件的流程
                                catch { }
                                finally { }

                                //返回轉檔文件結果 -> 文件名不會在頁面顯示，所以可以文件名的不一致
                                fileContentResult = File(pdfStream.ToArray(), "application/json", sysUploadFile.FileName);
                            }
                        }
                        catch (Exception)
                        {
                            //表示 Gotenberg 服務出現問題，無法轉檔
                            SysUploadFileDataService.Update(new SysUploadFile() { Fileid = sysUploadFile.Fileid, FileStatus = (int)FileStatusUtil.None });
                            return new ApiResultModelByObject()
                            {
                                messageType = MessageTypeUtils.Warning.ToString(),
                                messageContent = [string.Format(ActionFilter.GetMultilingualValue("custom:messageContent:notToPdf"), sysUploadFile.FileName)],
                                messageTitle = ActionFilter.GetMultilingualValue("custom:messageTitle:previewFail")
                            };
                        }
                        finally
                        {
                            //釋放資源
                            pdfStream.Close();
                            memoryStream.Close();
                        }
                    }
                }
                #endregion

                //返回結果
                result.listData = fileContentResult;
                result.rtnSuccess = true;
                //記錄日志
                InitLogRecord(log =>
                {
                    log.Detail = $"{ActionFilter.GetMultilingualValue($"ArchivalAttachments_applyNumber", true)}：{sysUploadFile.UploadKey}\r\n{ActionFilter.GetMultilingualValue("ArchivalAttachments_fileName", true)}：${sysUploadFile.FileName}";
                });
            }
            return result;
        }



        /// <summary>
        /// 预览pdf转档,返回即将下载的转档文件信息，由前端来询问并且分片下载
        /// 如無轉檔文件或轉檔失敗，則會將文件狀態修改為【無轉檔文件】
        /// </summary>
        /// <param name="fileId">文件ID</param>
        /// <returns>通用数据格式(保存pdf文件流)</returns>
        [HttpGet("PreviewPdfStreamInfo")]
        public async Task<ApiResultModelByObject> PreviewPdfStreamInfo(int fileId)
        {
            FilePreviewPdfModel fileDown = new FilePreviewPdfModel();
            ApiResultModelByObject result = new ApiResultModelByObject();
            //獲取原檔數據
            SysUploadFile sysUploadFile = SysUploadFileDataService.FindByKey(fileId);

            if (sysUploadFile == null ||
                string.IsNullOrWhiteSpace(sysUploadFile?.FilePath) ||
                !MinioFileExist(sysUploadFile.FilePath))
            { //如果原档不存在，直接return
                return new ApiResultModelByObject()
                {
                    messageType = MessageTypeUtils.Warning.ToString(),
                    messageContent = [ActionFilter.GetMultilingualValue("custom:messageContent:dataChurn")],
                    messageTitle = ActionFilter.GetMultilingualValue("custom:messageTitle:fileNotexist")
                };
            }

            if (sysUploadFile.FileType?.ToUpper() == "PDF") //如果原档就是PDF，直接返回原档
            {
                fileDown = new FilePreviewPdfModel()
                {
                    OriginalFile = sysUploadFile,
                    pdf = sysUploadFile,
                    Status = 200
                };
                InitLogRecord(LogRecord =>
                {
                    StringBuilder stringBuilder = new StringBuilder();
                    var applyNumber = sysUploadFile.UploadKey;
                    
                    var istemp = DbAccess.Database.ExecuteScalar($"select emplid from ps_sub_ee_lgl_vw_a where emplid ='{applyNumber}'")?.ToString() ?? "";
                    if (istemp != null && istemp != "")
                        stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue($"ArchivalAttachments_applyNumber", true)}：-");
                    else
                        stringBuilder.AppendLine(ActionFilter.GetMultilingualValue($"ArchivalAttachments_applyNumber", true) + "：" + sysUploadFile.UploadKey);
                    stringBuilder.AppendLine(ActionFilter.GetMultilingualValue("ArchivalAttachments_fileName", true) + "：" + sysUploadFile.FileName);
                    LogRecord.Detail = stringBuilder.ToString();
                });
                return new ApiResultModelByObject()
                {
                    rtnSuccess = true,
                    listData = fileDown
                };
            }

            //獲取原檔後綴不帶 [.]
            string suffix = Path.GetExtension(sysUploadFile.FileName).TrimStart('.');
            //驗證原檔文件是否為可轉檔白名單
            if (!canTransferSuffix.Contains(suffix)) //如果原档不能转档，则直接提示
            {
                return new ApiResultModelByObject()
                {
                    messageType = MessageTypeUtils.Warning.ToString(),
                    messageContent = [string.Format(ActionFilter.GetMultilingualValue("custom:messageContent:notToPdf"), sysUploadFile.FileName)],
                    messageTitle = ActionFilter.GetMultilingualValue("custom:messageTitle:previewFail")
                };
            }



            //獲取轉檔數據(黨完全沒有做過轉檔或轉檔文件被刪除則無數據)
            SysUploadFile pdfUploadFile = SysUploadFileDataService.Query(new SysUploadFileQueryCondition()
            {
                OriginalFileId = fileId,
                SearchItemGroup = new SearchItemGroup()
                {
                    Items = [
                        new SearchItem() {
                                Compare = CompareOperator.NE, //排除水印文件
                                Value = "101",
                                Field="upload_type"
                            }]
                }
            }).FirstOrDefault();


            if (pdfUploadFile != null)
            {//数据库有转档文件信息 
                if (MinioFileExist(pdfUploadFile.FilePath)) //并且minio有转档文件，这直接返回转档文件
                {  //miio上边有转档文件
                    fileDown = new FilePreviewPdfModel()
                    {
                        OriginalFile = sysUploadFile,
                        pdf = pdfUploadFile,
                        Status = 200

                    };
                    InitLogRecord(LogRecord =>
                    {
                        StringBuilder stringBuilder = new StringBuilder();
                        var applyNumber = sysUploadFile.UploadKey;
                        var istemp = DbAccess.Database.ExecuteScalar($"select emplid from ps_sub_ee_lgl_vw_a where emplid ='{applyNumber}'")?.ToString() ?? "";
                        if (istemp != null && istemp != "")
                            stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue($"ArchivalAttachments_applyNumber", true)}：-");
                        else
                            stringBuilder.AppendLine(ActionFilter.GetMultilingualValue($"ArchivalAttachments_applyNumber", true) + "：" + sysUploadFile.UploadKey);
                        stringBuilder.AppendLine(ActionFilter.GetMultilingualValue("ArchivalAttachments_fileName", true) + "：" + sysUploadFile.FileName);
                        LogRecord.Detail = stringBuilder.ToString();
                    });
                    return new ApiResultModelByObject()
                    {
                        rtnSuccess = true,
                        listData = fileDown
                    };
                }
                else
                { //数据库有转档文件，但是MinIo没得 

                    if (sysUploadFile.FileStatus != (int)FileStatusUtil.Turning  //如果文件不在转档中
                            || (sysUploadFile.ModifyTime.HasValue && DateTime.UtcNow > sysUploadFile.ModifyTime.Value.AddMinutes(5)) //或者 在转档中，但是又超过了 10分钟没提交转档结果，就再次产生转档
                        )
                    {
                        //启动一个转档任务
                        _ = ToPdfFileShiftOnly(sysUploadFile, pdfUploadFile, MvcContext.UserInfo.current_emp);
                    }

                    //不等待转档结果，直接返回
                    fileDown = new FilePreviewPdfModel()
                    {
                        OriginalFile = sysUploadFile,
                        pdf = null,
                        Status = 106 //给前端一个询问转档结果的状态码

                    };
                    InitLogRecord(LogRecord =>
                    {
                        StringBuilder stringBuilder = new StringBuilder();
                        var applyNumber = sysUploadFile.UploadKey;
                        var istemp = DbAccess.Database.ExecuteScalar($"select emplid from ps_sub_ee_lgl_vw_a where emplid ='{applyNumber}'")?.ToString() ?? "";
                        if (istemp != null && istemp != "")
                            stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue($"ArchivalAttachments_applyNumber", true)}：-");
                        else
                            stringBuilder.AppendLine(ActionFilter.GetMultilingualValue($"ArchivalAttachments_applyNumber", true) + "：" + sysUploadFile.UploadKey);
                        stringBuilder.AppendLine(ActionFilter.GetMultilingualValue("ArchivalAttachments_fileName", true) + "：" + sysUploadFile.FileName);
                        LogRecord.Detail = stringBuilder.ToString();
                    });
                    return new ApiResultModelByObject()
                    {
                        rtnSuccess = true,
                        listData = fileDown
                    };
                }
            }
            else
            {  //需要转档

                if (sysUploadFile.FileStatus != (int)FileStatusUtil.Turning  //如果文件不在转档中
                        || (sysUploadFile.ModifyTime.HasValue && DateTime.UtcNow > sysUploadFile.ModifyTime.Value.AddMinutes(5)) //或者 在转当中，但是又超过了 5分钟没提交转档结果，就再次产生转档
                   )
                {
                    //启动一个转档任务
                    _ = ToPdfFileShiftOnly(sysUploadFile, null, MvcContext.UserInfo.current_emp);
                }

                //不等待转档结果，直接返回
                fileDown = new FilePreviewPdfModel()
                {
                    OriginalFile = sysUploadFile,
                    pdf = null,
                    Status = 105 //给前端一个询问转档结果的状态码

                };
                InitLogRecord(LogRecord =>
                {
                    StringBuilder stringBuilder = new StringBuilder();
                    var applyNumber = sysUploadFile.UploadKey;
                    var istemp = DbAccess.Database.ExecuteScalar($"select emplid from ps_sub_ee_lgl_vw_a where emplid ='{applyNumber}'")?.ToString() ?? "";
                    if (istemp != null && istemp != "")
                        stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue($"ArchivalAttachments_applyNumber", true)}：-");
                    else
                        stringBuilder.AppendLine(ActionFilter.GetMultilingualValue($"ArchivalAttachments_applyNumber", true) + "：" + sysUploadFile.UploadKey);
                    stringBuilder.AppendLine(ActionFilter.GetMultilingualValue("ArchivalAttachments_fileName", true) + "：" + sysUploadFile.FileName);
                    LogRecord.Detail = stringBuilder.ToString();
                });
                return new ApiResultModelByObject()
                {
                    rtnSuccess = true,
                    listData = fileDown
                };
            }

        }


        /// <summary>
        /// 前端詢問文件狀態(预览哟，这里转档使用以前的设计，转档成功之后数据库才有转档记录，所以通过原档文件fileId来询问)
        /// </summary>
        /// <param name="fileId"></param>
        /// <returns></returns>
        [HttpGet("InquirePreviewFile")]
        public async Task<ApiResultModelByObject> InquirePreviewFile(int fileId)
        {
            SysUploadFile file = SysUploadFileDataService.FindByKey(fileId);
            //保存即将要下载的文件
            FilePreviewPdfModel fileDown = new FilePreviewPdfModel();
            if (file == null)
            {
                fileDown.Status = 400;
                return new ApiResultModelByObject()
                {
                    rtnSuccess = true,
                    messageType = MessageTypeUtils.Warning.ToString(),
                    messageContent = [ActionFilter.GetMultilingualValue("custom:messageContent:dataChurn")],
                    messageTitle = ActionFilter.GetMultilingualValue("custom:messageTitle:fileNotexist"),
                    listData = fileDown,
                };

            }
            else
            {
                fileDown.OriginalFile = file;
                if (file.FileStatus == (int)FileStatusUtil.Turning)
                { //还在处于转档中
                    fileDown.Status = 105;
                }
                else if (file.FileStatus == (int)FileStatusUtil.Turned)
                { //已经转档
                    fileDown.Status = 200;
                }
                else if (file.FileStatus == (int)FileStatusUtil.None)
                { //无转档文件，或者转档失败
                    fileDown.Status = 501;
                    return new ApiResultModelByObject()
                    {
                        messageType = MessageTypeUtils.Warning.ToString(),
                        messageContent = [string.Format(ActionFilter.GetMultilingualValue("custom:messageContent:againLater"), file.FileName)],
                        messageTitle = ActionFilter.GetMultilingualValue("custom:messageTitle:previewFail"),
                        listData = fileDown,
                        rtnSuccess = true

                    };
                }


                //獲取轉檔數據(黨完全沒有做過轉檔或轉檔文件被刪除則無數據)
                SysUploadFile pdfUploadFile = SysUploadFileDataService.Query(new SysUploadFileQueryCondition()
                {
                    OriginalFileId = fileId,
                    SearchItemGroup = new SearchItemGroup()
                    {
                        Items = [
                            new SearchItem() {
                                Compare = CompareOperator.NE, //排除水印文件
                                Value = "101",
                                Field="upload_type"
                            }]
                    }
                }).FirstOrDefault();

                if (pdfUploadFile != null && MinioFileExist(pdfUploadFile.FilePath))
                {  //如果数据库有转档记录，并且也有转档文件，则直接返回成功的状态码（即使上边出现了，105与 501也返回200）
                    fileDown.Status = 200;
                    fileDown.pdf = pdfUploadFile;

                    //判断原档有转档文件，minio也有转档文件，但是原档文件状态不对
                    if (file.HasWatermake != (int)YesOrNoUtils.Yes || file.FileStatus != (int)FileStatusUtil.Turned)
                    {
                        //(状态修正)
                        //修正一下状态，避免因为异常原因，转档成功后，转档文件也上传到MINIO，但是原档文件状态未被正常更新
                        SysUploadFileDataService.Update(new SysUploadFile()
                        {
                            Fileid = file.Fileid, 
                            HasWatermake = (int)YesOrNoUtils.Yes,
                            FileStatus = (int)FileStatusUtil.Turned
                        });
                        file.FileStatus = (int)FileStatusUtil.Turned;
                    }
                }

                var modifyTimeAdd5 = (file.ModifyTime ?? DateTime.UtcNow.AddMinutes(-6)).AddMinutes(10); //如果修改时间未NULL，则直接视为6分钟之前修改的(不过走到这里不会出现 ModifyTime 为null的情况)

                if (file.FileStatus == (int)FileStatusUtil.Turning  //如果文件还在转档中
                            && DateTime.UtcNow > modifyTimeAdd5)
                { //如果文件状态在轉檔中未成功也未失败，并且超过了10分钟，直接提示前端，失败
                    fileDown.Status = 501;
                    return new ApiResultModelByObject()
                    {
                        messageType = MessageTypeUtils.Warning.ToString(),
                        messageContent = [string.Format(ActionFilter.GetMultilingualValue("custom:messageContent:againLater"), file.FileName)],
                        messageTitle = ActionFilter.GetMultilingualValue("custom:messageTitle:previewFail"),
                        listData = fileDown,
                        rtnSuccess = false,

                    };
                }


            }

            var res = new ApiResultModelByObject()
            {
                rtnSuccess = true,
                listData = fileDown
            };
            return await Task.FromResult(res);
        }

        #endregion

        #region 批量刪除文件，同時刪除轉檔文件
        /// <summary>
        /// 批量刪除文件，同時刪除轉檔文件
        /// </summary>
        /// <param name="fileIds">文件id集合</param>
        /// <param name="applyNumber">申請單號</param>
        /// <param name="menuCode">菜單欄</param>
        /// <returns></returns>
        [HttpPost("RemoveMultipleFile")]
        public async Task<ApiResultModelByObject> RemoveMultipleFile(List<int> fileIds, string applyNumber = "", string menuCode = "")
        {
            if (!fileIds.Any())
            {
                return new ApiResultModelByObject()
                {
                    messageType = MessageTypeUtils.Warning.ToString(),
                    messageContent = new List<string>() { ActionFilter.GetMultilingualValue("custom:messageContent:selectDeleteFiles") },
                    messageTitle = ActionFilter.GetMultilingualValue("custom:messageTitle:fileDeleteFail")
                };
            }
            return await Task.FromResult(await ArchivalAttachmentsService.DeleteMultipleFile(fileIds, applyNumber, menuCode));
        }
        #endregion

        #region 批量下載附件(包含轉檔文件)
        /// <summary>
        /// 批量下載附件(返回即將下載的文件信息)
        /// </summary>
        /// <param name="conditions"></param>
        /// <returns></returns>
        [HttpPost("DownloadFilesWithPdf")]
        public async Task<ApiResultModelByObject> DownloadFilesWithPdf(DownloadFileModel conditions)
        {
            #region 驗證前端是否有勾選下載文件 （参数验证）
            if (conditions.ListDownFile.Count == 0)
            {
                return new ApiResultModelByObject()
                {
                    messageType = MessageTypeUtils.Warning.ToString(),
                    messageTitle = ActionFilter.GetMultilingualValue("custom:messageTitle:selectData")
                };
            }
            #endregion

            #region 一次性获取这批文件原档的数据库记录 （后边逻辑使用，避免后边多次查询数据库）
            List<SysUploadFile> selectFiles = new List<SysUploadFile>();
            //獲取文件，不包含轉檔文件
            if (conditions.DownLoadAll)
            {
                selectFiles = SysUploadFileDataService.Query(new SysUploadFileQueryCondition()
                {
                    UploadKey = conditions.ApplyNumber,
                    IsWatermake = 0
                });
            }
            else
            {
                selectFiles = SysUploadFileDataService.Query(new SysUploadFileQueryCondition()
                {
                    SearchItemGroup = new SearchItemGroup()
                    {
                        Items = [
                            new SearchItem() {
                                Compare = CompareOperator.ARRAYIN,
                                Values = conditions.ListDownFile.Select(s => s.FileId.ToString()), Field = "fileid", Logic = LogicOperator.And
                            }]
                    }
                });
            }
            #endregion

            #region DB中有數據時的邏輯驗證 //(勾选的文件，在数据库中不存在)
            List<string> noFileByDb = conditions.ListDownFile.Where(e => !selectFiles.Any(sf => sf.Fileid?.ToString() == e.FileId)).Select(e => e.FileName).ToList();
            if (noFileByDb.Any()) //如果有勾选的文件，不存在于数据库，则直接给出提示
            {
                return new ApiResultModelByObject()
                {
                    messageType = MessageTypeUtils.Warning.ToString(),
                    messageContent = [ActionFilter.GetMultilingualValue("custom:messageContent:dataChurn")],
                    messageTitle = ActionFilter.GetMultilingualValue("custom:messageTitle:fileDownloadFail")
                };
            }
            #endregion

            //保存即将要下载的文件
            List<FileDownResultModel> downFiles = new List<FileDownResultModel>();

            //一次性获取这批文件里边已生成过水印的文件(水印是基于PDF)
            var shuiyinFiles = SysUploadFileDataService.Query(new SysUploadFileQueryCondition()
            {
                SearchItemGroup = new SearchItemGroup()
                {
                    Items = [
                            new SearchItem() {
                                Compare = CompareOperator.ARRAYIN,
                                Values = selectFiles.Select(s => s.Fileid.ToString()), Field = "OriginalFileId", Logic = LogicOperator.And
                            },
                            new SearchItem{
                                 Compare= CompareOperator.EQ,
                                 Value = "101", Field = "UploadType", Logic = LogicOperator.And
                            },
                             new SearchItem{
                                 Compare= CompareOperator.EQ,
                                 Value = "101", Field = "ArchivePurposes", Logic = LogicOperator.And
                            }
                           ]
                }
            });

            //保存即将要生成水印档案的文件
            List<BeginToWatermarkPdf> beginToPdfsList = new List<BeginToWatermarkPdf>();

            //保存即将新生成的文件记录
            List<SysUploadFile> insertDBFiles = new List<SysUploadFile>();

            //保存异常错误消息
            List<string> errorFileByShift = new List<string>();

            //保存需要更新文件信息的文件
            List<SysUploadFile> updateDBFiles = new List<SysUploadFile>();

            //（是否暫存單，機密等級，合約編號） //賦值（一次查詢出數據）
            (conditions.isTemp, conditions.confidenLevel, conditions.contractNumber) = ArchivalAttachmentsService.GetfileModelApplyInfo(conditions.ApplyNumber, selectFiles.FirstOrDefault()?.UploadType ?? 0);

            foreach (DownloadFileToPdfModel conFileData in conditions.ListDownFile)
            {
                var fileData = selectFiles.FirstOrDefault(e => e.Fileid?.ToString() == conFileData.FileId);
                if (fileData == null) continue; //原档不存在，目前不会出现这种情况  因为在  noFileByDb 的时候已经验证了
                #region 自定義驗證
                //原檔文件是否在可轉檔白名單中
                bool canTransfer = canTransferSuffix.Contains(Path.GetExtension(fileData.FileName).TrimStart('.'));
                //當選中浮水印下載時 -> 給定前端提示，該原檔無法進行轉檔作業
                if (!canTransfer && conFileData.DocumentToPdf) { errorFileByShift.Add(fileData.FileName); }
                #endregion

                #region 原始文件下載 -> 不需要加浮水印，也不需要加合約編號和申請單號
                if (conFileData.Document)
                {
                    downFiles.Add(new FileDownResultModel()
                    {
                        Fileid = Convert.ToInt32(fileData.Fileid),
                        FileName = fileData.FileName,
                        ShowFileName = ArchivalAttachmentsService.GetFileName(fileData.FileName, conditions.confidenLevel),
                        FilePath = fileData.FilePath,
                        FileSize = fileData.FileSize,
                        Status = fileData.FileStatus ?? 0
                    });
                }
                #endregion

                // 轉檔文件下載(前端用戶選擇浮水印+原檔存在+轉檔後綴白名單) -> 需要加上浮水印+合約編號+申請單號
                if (!conFileData.DocumentToPdf || !canTransfer) continue; //如果不下载水印档，或者不在站当白名单，则直接跳过本文件操作

                #region 生成水印档
                //浮水印信息匯總
                Dictionary<string, string> waterData = new Dictionary<string, string>();
                waterData["cl"] = CommonUtilHelper.GetConfidenLevelWatermarkText(conditions.confidenLevel);
                waterData["cn"] = conditions.contractNumber;
                //暫存單 -> 申請單號為空
                string applyNumber = conditions.isTemp ? string.Empty : conditions.ApplyNumber;
                waterData["an"] = applyNumber;


                SysUploadFile syd = shuiyinFiles.FirstOrDefault(e => e.OriginalFileId == fileData.Fileid);
                if (syd != null) //水印档存在，但是水印是实时更新的，所以从新生成水印档
                {
                    int minutes = fileData.FileType.ToUpper() == "PDF" ? 10 : 20; //如果原档就是PDF，仅仅加水印等待10分钟，如果原档不是PDF，则可能包含转档任务，则等待20分钟，

                    //if (syd.FileStatus == 200) 
                    //{  //並且是 200 的狀態碼 //不觸發加水印，直接觸發下載 這個操作需要再水印信息字段被改變的時候刪除水印文件記錄才行
                    //    downFiles.Add(new FileDownResultModel() //返回给前端要下载的文件列表
                    //    {
                    //        Fileid = Convert.ToInt32(syd.Fileid),
                    //        FileName = syd.FileName,
                    //        ShowFileName = ArchivalAttachmentsService.GetPdfFileName(fileData.FileName, conditions.confidenLevel),
                    //        FilePath = syd.FilePath,
                    //        FileSize = syd.FileSize,
                    //        Status = syd.FileStatus ?? 0 // 表示文件正在上传中或者等待上传中，在mio暂时还不存在
                    //    });
                    //}
                    //else 
                    if (syd.FileStatus == 105 //處於加水印任務中
                        && syd.ModifyTime.HasValue
                        && DateTime.UtcNow < syd.ModifyTime.Value.AddMinutes(minutes)
                        )//还在等待时间内，则不用生成加水印任务，直接返回给前端，让前端询问) //处于一个加水印任务中
                    {
                        downFiles.Add(new FileDownResultModel() //返回给前端要下载的文件列表
                        {
                            Fileid = Convert.ToInt32(syd.Fileid),
                            FileName = syd.FileName,
                            ShowFileName = ArchivalAttachmentsService.GetPdfFileName(fileData.FileName, conditions.confidenLevel),
                            FilePath = syd.FilePath,
                            FileSize = 0,
                            Status = 105 // 給前端一個需要詢問的狀態碼
                        });
                    }
                    else //狀態嗎不是 200 
                    //也 不在加水印中，就重新生成一個家水印任務,
                    //或者超过等待时间,一般不会出现一个任务等待10分钟这么久，报错的话状态不会是105，成功的话状态是200；
                    //如果发现操过等待时间，并且状态还是 105，说明未知原因加水印成功后，更新数据库状态 FileStatus 為 200 的代碼未執行；
                    //或者加水印失敗後，進入catch 的代碼 更新 FileStatus 為對應的錯誤狀態碼的代碼因位置原因未執行成功，
                    {
                        syd.FileSize = 0;
                        syd.FileStatus = 105; //表示文件正在上传中，在mio暂时还不存在 （不记录错误次数每次重试）
                        syd.ModifyTime = DateTime.UtcNow;
                        syd.CreateUser = MvcContext.UserInfo.current_emp;
                        updateDBFiles.Add(syd); //生成待修改数据
                                                //保存一个待生成水印档的操作
                        beginToPdfsList.Add(new BeginToWatermarkPdf()
                        {
                            OriginalFile = fileData,
                            WatermarkPdf = syd,
                            waterData = waterData

                        });
                    }

                }
                else
                {
                    syd = new SysUploadFile();
                    syd.UploadKey = fileData.UploadKey + "_Watermark101";
                    syd.Fileid = null;
                    syd.FileSize = 0;
                    syd.FileName = $"{Path.GetFileNameWithoutExtension(fileData.FileName)}.pdf";
                    syd.FileType = "pdf";
                    //使用一个唯一的UUIDB保证PATH不会冲突
                    syd.FilePath = Path.GetDirectoryName(fileData.FilePath) + "/" + $"{Guid.NewGuid().ToString()}{Path.GetFileNameWithoutExtension(fileData.FileName)}.pdf"; ;
                    syd.ModifyUser = MvcContext.UserInfo.current_emp;
                    syd.ModifyTime = DateTime.UtcNow;
                    syd.IsWatermake = (int)YesOrNoUtils.No; //这不是一个转档文件，仅仅表示基于PDF生成的水印文件
                    syd.OriginalFileId = fileData.Fileid;
                    syd.UploadType = 101; // 表示一个水印文件 （仅提供下载用）
                    syd.FileStatus = 105; //表示文件正在上传中或者等待上传中，在mio暂时还不存在
                    syd.ArchivePurposes = 101;//档案用途 101 表示带有浮水印的PDF档案（仅提供下载用）
                    syd.CreateUser = MvcContext.UserInfo.current_emp;
                    syd.CreateTime = DateTime.UtcNow;
                    //把这个水印档保存为数据库记录
                    insertDBFiles.Add(syd);

                    //保存一个待生成水印档的操作
                    beginToPdfsList.Add(new BeginToWatermarkPdf()
                    {
                        OriginalFile = fileData,
                        WatermarkPdf = syd,
                        waterData = waterData

                    });
                }
                #endregion
            }

            #region 持久化
            SysUploadFileDataService.BatchUpdate(updateDBFiles.ToArray());
            SysUploadFileDataService.BatchCreate(insertDBFiles.ToArray());
            #endregion


            //一次性查询出水印文档 （插入数据之后，查出带 Fileid 数据）
            var syFiles = SysUploadFileDataService.Query(new SysUploadFileQueryCondition()
            {
                SearchItemGroup = new SearchItemGroup()
                {
                    Items = [
                        new SearchItem() {
                                Compare = CompareOperator.ARRAYIN,
                                Values = beginToPdfsList.Select(s => s.OriginalFile.Fileid.ToString()), Field = "OriginalFileId", Logic = LogicOperator.And
                            },
                            new SearchItem{
                                 Compare= CompareOperator.EQ,
                                 Value = "101", Field = "UploadType", Logic = LogicOperator.And
                            },
                             new SearchItem{
                                 Compare= CompareOperator.EQ,
                                 Value = "101", Field = "ArchivePurposes", Logic = LogicOperator.And
                            }
                       ]
                }
            });


            foreach (var btpl in beginToPdfsList)
            {
                //执行持久化之后 匹配新的水印文件的带有 Fileid 数据库记录 
                var dbWatermarkPdf = syFiles.FirstOrDefault(e => e.OriginalFileId == btpl.OriginalFile.Fileid);
                btpl.WatermarkPdf = dbWatermarkPdf;
                btpl.WatermarkPdf.CreateUser = MvcContext.UserInfo.current_emp;
                if (dbWatermarkPdf == null || !dbWatermarkPdf.Fileid.HasValue) continue;

                downFiles.Add(new FileDownResultModel() //返回给前端要下载的文件列表
                {
                    Fileid = Convert.ToInt32(dbWatermarkPdf.Fileid),
                    FileName = dbWatermarkPdf.FileName,
                    ShowFileName = ArchivalAttachmentsService.GetPdfFileName(btpl.OriginalFile.FileName, conditions.confidenLevel),
                    FilePath = dbWatermarkPdf.FilePath,
                    FileSize = 0,
                    Status = 105 // 表示文件正在上传中或者等待上传中，在mio暂时还不存在
                });

            }

            //调用转档并且加水印，这里不需要等待异步完成（待前端分片下载的时候轮询询问文件的状态）
            _ = ToPdfFiles(beginToPdfsList, MvcContext.UserInfo.current_emp);

            #region 插入log日誌
            InitLogRecord(log =>
                {
                    StringBuilder stringBuilder = new();
                    if (!conditions.isTemp) stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue($"ArchivalAttachments_applyNumber", true)}：{conditions.ApplyNumber}");
                    else stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue($"ArchivalAttachments_tempApplyNumber", true)}：-");
                    stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue($"ArchivalAttachments_fileName", true)}：{string.Join("，", selectFiles.Select(s => s.FileName))}");
                    log.Detail = stringBuilder.ToString();
                });
            #endregion

            #region 提示信息

            ApiResultModelByObject result = new ApiResultModelByObject();
            result.listData = downFiles;
            result.rtnSuccess = downFiles.Any();
            if (errorFileByShift != null && errorFileByShift.Count > 0)
            {
                result.messageContent.Add(string.Format(ActionFilter.GetMultilingualValue("custom:messageContent:notToPdfAndWatermark"), string.Join(",", errorFileByShift)));
            }
            result.messageType = result.messageContent.Any() ? MessageTypeUtils.Warning.ToString() : MessageTypeUtils.Success.ToString();
            #endregion

            return result;
        }


        /// <summary>
        /// 前端詢問文件狀態
        /// </summary>
        /// <param name="fileIds"></param>
        /// <returns></returns>
        [HttpPost("InquireFileList")]
        public async Task<ApiResultModelByObject> InquireFileListStatus(List<int> fileIds)
        {
            List<SysUploadFile> selectFiles = SysUploadFileDataService.Query(new SysUploadFileQueryCondition()
            {
                SearchItemGroup = new SearchItemGroup() { Items = [new SearchItem() { Compare = CompareOperator.ARRAYIN, Values = fileIds.Select(s => s.ToString()), Field = "fileid", Logic = LogicOperator.And }] }
            });
            //保存即将要下载的文件
            List<FileDownResultModel> downFiles = new List<FileDownResultModel>();

            foreach (var item in selectFiles)
            {
                downFiles.Add(new FileDownResultModel()
                {
                    Fileid = Convert.ToInt32(item.Fileid),
                    FileName = item.FileName,
                    FilePath = item.FilePath,
                    Status = Convert.ToInt32(item.FileStatus ?? 0),
                    FileSize = item.FileSize

                });
            }
            var res = new ApiResultModelByObject()
            {
                rtnSuccess = true,
                listData = downFiles
            };
            return await Task.FromResult(res);
        }
        #endregion

        #region 獲取文件列表(SpringJiang已經重新整理邏輯)
        /// <summary>
        /// 獲取文件列表
        /// </summary>
        /// <param name="condition"></param>
        /// <returns></returns>
        [HttpPost("GetFileList")]
        public async Task<ApiResultModelByObject> GetFileList(SysUploadFileViewModel condition)
        {
            ApiResultModelByObject apiResult = new()
            {
                listData = ArchivalAttachmentsService.GetFileList(condition),
                rtnSuccess = true
            };
            return await Task.FromResult(apiResult);
        }
        #endregion

        #region 修改文件列表信息(SpringJiang已經重新整理邏輯)
        /// <summary>
        /// 修改文件列表信息
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost("Update")]
        public async Task<ApiResultModelByObject> Update(SysUploadFileParaModel model)
        {
            return await Task.FromResult(ArchivalAttachmentsService.Update(model));
        }
        #endregion

        #region 獲取檔案歷程信息
        /// <summary>
        /// 獲取檔案歷程信息
        /// </summary>
        /// <param name="condition">查询参数</param>
        /// <returns></returns>
        [HttpPost("GetArchiveOperationProcess")]
        public async Task<ApiResultModelByObject> GetArchiveOperationProcess(ArchiveOperationProcessCondition condition)
        {
            ApiResultModelByObject apiResult = new ApiResultModelByObject()
            {
                listData = ArchivalAttachmentsService.GetArchiveOperationProcess(condition),
                rtnSuccess = true
            };
            return await Task.FromResult(apiResult);
        }
        #endregion

        #region 生成水印文件
        /// <summary>
        /// 批量生成水印文件
        /// </summary>
        /// <param name="tasks">即将生成水印文件的信息</param>
        /// <param name="currentUser">当前用户</param>
        /// <returns></returns>
        private async Task ToPdfFiles(List<BeginToWatermarkPdf> tasks, string currentUser)
        {
            await Task.Run(() => //使用一个新的任务来执行
           {


               //这一批文件的原档案信息
               List<SysUploadFile> originalFiles = tasks.Select(e => e.OriginalFile).ToList();
               //一次性查询出这一批文件已经转换成的PDF档案 (注意这里仅仅是转档成PDF，不包含水印文件)
               List<SysUploadFile> pdfs = SysUploadFileDataService.Query(new SysUploadFileQueryCondition()
               {
                   SearchItemGroup = new SearchItemGroup()
                   {
                       Items = [
                               new SearchItem() {
                                Compare = CompareOperator.ARRAYIN,
                                Values = originalFiles.Select(s => s.Fileid.ToString()), Field = "OriginalFileId", Logic = LogicOperator.And
                            },
                            new SearchItem{
                                 Compare= CompareOperator.EQ,
                                 Value = "1", Field = "IsWatermake", Logic = LogicOperator.And
                            }
                              ]
                   }
               });



               //循环生成带水印的PDF
               foreach (var item in tasks)
               {
                   var watermarkPdf = item.WatermarkPdf;
                   if (watermarkPdf == null || watermarkPdf.Fileid == null) continue;
                   var pdffile = pdfs.FirstOrDefault(e => e.OriginalFileId == item.OriginalFile.Fileid);
                   //这里不加await 因为不需要等待运行结果在运行下一次循环
                   _ = ToPdfFile(item.OriginalFile, watermarkPdf, pdffile, item.waterData, currentUser);


               }
           });




        }
        /// <summary>
        /// 生成水印文件
        /// </summary>
        /// <param name="originalFile">原档案</param>
        /// <param name="WatermarkPdf">水印文件数据库记录 </param>
        /// <param name="pdf">转档文件数据库记录 </param>
        /// <param name="waterData">waterData 水印信息</param>
        /// <returns></returns>
        private async Task ToPdfFile(SysUploadFile originalFile, SysUploadFile WatermarkPdf, SysUploadFile pdf, Dictionary<string, string> waterData, string currentUser)
        {
            //PDF文件流
            MemoryStream pdfMemory = new MemoryStream();
            //原档
            try
            {


                //在PDF基础上加水印，所以要先找到PDF（如果原档就是PDF那最好，否则查询转档文件）
                if (originalFile.FileType?.ToUpper() == "PDF")
                {
                    //直接下载原档进行加水印的操作
                    if (MinioFileExist(originalFile.FilePath))
                    { //如果原文档在MIO上存在,则直接下载PDF
                        await _minioClient.GetObjectAsync(new GetObjectArgs().
                         WithBucket(_minIOConfig.Bucket).
                         WithObject(originalFile.FilePath).
                         WithCallbackStream((stream) => { stream.CopyTo(pdfMemory); })).
                         ConfigureAwait(false);
                    }
                    else
                    {
                        throw new Exception("400");
                    }

                }
                else //如果原档不是PDF
                {
                    #region 转档成PDF
                    if (pdf != null) //说明有转档过
                    {
                        if (MinioFileExist(pdf.FilePath))
                        {
                            //如果转档文件PDF在minio上存在,这直接下载PDF
                            await _minioClient.GetObjectAsync(new GetObjectArgs().
                             WithBucket(_minIOConfig.Bucket).
                             WithObject(pdf.FilePath).
                             WithCallbackStream((stream) => { stream.CopyTo(pdfMemory); })).
                             ConfigureAwait(false);
                        }
                        else
                        {  //如果数据库又转档记录，但是minio上转档文件又不存在，则再次转档，(比如文件过期或者其他情况)
                           //下载原档案
                            using (var memoryStream = new MemoryStream())
                            {
                                if (MinioFileExist(originalFile.FilePath))
                                {
                                    await _minioClient.GetObjectAsync(new GetObjectArgs().
                                        WithBucket(_minIOConfig.Bucket).
                                        WithObject(originalFile.FilePath).
                                        WithCallbackStream((stream) => { stream.CopyTo(memoryStream); memoryStream.Seek(0, SeekOrigin.Begin); })).
                                        ConfigureAwait(false);
                                }
                                else
                                {
                                    throw new Exception("400");
                                }

                                //轉檔動作 throw new Exception("501"); throw new Exception("410");
                                pdfMemory = (MemoryStream)ToPdfService.ToPdfStreamNotContext(originalFile.FileType, memoryStream);

                                #region 更新PDF档案

                                pdfMemory.Position = 0;
                                pdfMemory.Seek(0, SeekOrigin.Begin);
                                string pdfPath = Path.GetDirectoryName(originalFile.FilePath) + "/" + $"{DateTime.Now:yyyyMMddHHmmss}{Path.GetFileNameWithoutExtension(originalFile.FileName)}.pdf";
                                pdf.FilePath = pdfPath;
                                pdf.CreateUser = currentUser;


                                try
                                {
                                    //上传新的转档文件在MINIO
                                    var putObjectArgs = new PutObjectArgs()
                                                                        .WithBucket(_minIOConfig.Bucket)
                                                                        .WithObject(pdfPath)
                                                                        .WithStreamData(pdfMemory)
                                                                        .WithObjectSize(pdfMemory.Length)
                                                                        .WithContentType("application/octet-stream");
                                    await _minioClient.PutObjectAsync(putObjectArgs).ConfigureAwait(false);
                                    //上傳完成後寫入數據庫
                                    //更新数据库
                                    SysUploadFileDataService.Update(new SysUploadFile()
                                    {
                                        Fileid = pdf.Fileid,
                                        FilePath = pdfPath,
                                        ModifyTime = DateTime.UtcNow,
                                        ModifyUser = currentUser
                                    });

                                    //更新数据库源文件
                                    //SysUploadFile file = SysUploadFileDataService.FindByKey(originalFile.Fileid);
                                    //if (file != null)
                                    //{
                                    //    file.HasWatermake = (int)YesOrNoUtils.Yes;
                                    //    file.FileStatus = (int)FileStatusUtil.Turned;
                                    //    //轉檔完成，轉檔文件還未刪除的情況下更新文件表，只更新原檔的狀態，不更新時間
                                    //    SysUploadFileDataService.Update(file);
                                    //}
                                    //else
                                    //{
                                    //}
                                }
                                catch (Exception)
                                {
                                    throw new Exception("401");
                                }
                                #endregion
                            }


                        }
                    }
                    else
                    {
                        //下载原档案
                        using (var memoryStream = new MemoryStream())
                        {

                            if (MinioFileExist(originalFile.FilePath))
                            {
                                await _minioClient.GetObjectAsync(new GetObjectArgs().
                                    WithBucket(_minIOConfig.Bucket).
                                    WithObject(originalFile.FilePath).
                                    WithCallbackStream((stream) => { stream.CopyTo(memoryStream); memoryStream.Seek(0, SeekOrigin.Begin); })).
                                    ConfigureAwait(false);
                            }
                            else //如果原档案不存在
                            {
                                throw new Exception("400");
                            }

                            //轉檔動作
                            pdfMemory = (MemoryStream)ToPdfService.ToPdfStreamNotContext(originalFile.FileType, memoryStream);


                            pdfMemory.Position = 0;
                            pdfMemory.Seek(0, SeekOrigin.Begin);



                            //拼接pdf路徑
                            string pdfPath = Path.GetDirectoryName(originalFile.FilePath) + "/" + $"{DateTime.Now:yyyyMMddHHmmss}{Path.GetFileNameWithoutExtension(originalFile.FileName)}.pdf";

                            try
                            {
                                //上传新的转档文件在MINIO
                                var putObjectArgs = new PutObjectArgs()
                                                                    .WithBucket(_minIOConfig.Bucket)
                                                                    .WithObject(pdfPath)
                                                                    .WithStreamData(pdfMemory)
                                                                    .WithObjectSize(pdfMemory.Length)
                                                                    .WithContentType("application/octet-stream");

                                await _minioClient.PutObjectAsync(putObjectArgs).ConfigureAwait(false);
                                //上傳完成後寫入數據庫
                                #region 儅minio上傳成功后，存儲一筆轉檔數據
                                SysUploadFile sysUploadFile = SysUploadFileDataService.FindByKey(originalFile.Fileid);
                                sysUploadFile.Fileid = null;
                                sysUploadFile.FileSize = pdfMemory.Length;
                                sysUploadFile.FileName = $"{Path.GetFileNameWithoutExtension(originalFile.FileName)}.pdf";
                                sysUploadFile.FileType = "pdf";
                                sysUploadFile.FilePath = pdfPath;
                                sysUploadFile.ModifyUser = currentUser;
                                sysUploadFile.ModifyTime = DateTime.UtcNow;
                                sysUploadFile.IsWatermake = (int)YesOrNoUtils.Yes;
                                sysUploadFile.OriginalFileId = originalFile.Fileid;
                                sysUploadFile.CreateTime = DateTime.UtcNow;
                                //新增
                                SysUploadFileDataService.Create(sysUploadFile);
                            }
                            catch (Exception)
                            {

                                throw new Exception("401");
                            }

                            #endregion

                        }
                    }
                    #endregion
                }


                #region 根据得到的PDF档案添加水印


                try
                {
                    WatermarkService.AddWatermarkTextToPdf2(pdfMemory, waterData); //加水印

                }
                catch (Exception)
                {

                    throw new Exception("503");
                }

                try
                {
                    //上传新的带水印的文件到Minio
                    var putWatermarkPdfObjectArgs = new PutObjectArgs()
                                                        .WithBucket(_minIOConfig.Bucket)
                                                        .WithObject(WatermarkPdf.FilePath)
                                                        .WithStreamData(pdfMemory)
                                                        .WithObjectSize(pdfMemory.Length)
                                                        .WithContentType("application/octet-stream");
                    // 等待上传完成
                    await _minioClient.PutObjectAsync(putWatermarkPdfObjectArgs).ConfigureAwait(false);

                    //上传完成后更新数据库
                    if (WatermarkPdf.Fileid.HasValue)
                    {

                        //更新水印文件信息
                        SysUploadFileDataService.Update(new SysUploadFile()
                        {
                            Fileid = WatermarkPdf.Fileid,
                            FileSize = pdfMemory.Length,
                            FileStatus = 200, //表示文件上传完成
                            ModifyTime = DateTime.UtcNow,
                            ModifyUser = WatermarkPdf.CreateUser
                        });
                        //更新源文件PDF标识
                        //更新数据库源文件
                        SysUploadFile file = SysUploadFileDataService.FindByKey(originalFile.Fileid);
                        if (file != null)
                        {
                            file.HasWatermake = (int)YesOrNoUtils.Yes;
                            file.FileStatus = (int)FileStatusUtil.Turned;
                            //轉檔完成，轉檔文件還未刪除的情況下更新文件表，只更新原檔的狀態，不更新時間
                            SysUploadFileDataService.Update(file);
                        }
                        else
                        {
                        }
                    }
                }
                catch (Exception)
                {

                    throw new Exception("405");
                }


                #endregion

            }
            catch (Exception ex)
            {
                try //防止在catch里边更新数据库又报错了
                {
                    SysUploadFile file = SysUploadFileDataService.FindByKey(WatermarkPdf.Fileid);


                    int status = 500; //默认为其他错误
                    if (ex.Message == "400")
                    { //原文件不存在
                        status = 400;
                    }
                    else if (ex.Message == "401") //PDF上传到minio报错
                    {
                        status = 401;
                    }
                    else if (ex.Message == "405")
                    { // 上传水印文件到minio报错
                        status = 405;
                    }
                    else if (ex.Message == "503") //PDF档案加水印报错
                    {
                        status = 503;
                    }
                    else if (ex.Message == "501")
                    {  //转档报错
                        status = 501;//失败了
                    }
                    else if (ex.Message == "410")
                    {
                        status = 410;//未配置转档 的GotenbergUrl 
                    }
                    else
                    {
                        status = 500;
                    }

                    // 如果 file.FileStatus = 200 说明被最近其他人点击加水印任务 ，给加水印成功了 这个任务报错也就无所谓了，
                    //一般情況不會出現，因為觸發加水印的時候，狀態是105, 再次點擊 發現狀態是 105並且是 10分鐘內不會再次觸發加水印任務；
                    //除非有一個任務 A 執行了超過 10分鐘還在執行中，狀態嗎 是 105，這個時候，用戶A重新又點擊下載，再次觸發任務B加水印
                    //這個時候A在第11分鐘成功了，那麼B任務失敗了也無所謂
                    if (file != null && file.FileStatus != 200)
                    {
                        SysUploadFileDataService.Update(new SysUploadFile()
                        {
                            Fileid = WatermarkPdf.Fileid,
                            FileSize = 0,
                            FileStatus = status,
                            ModifyTime = DateTime.UtcNow,
                            ModifyUser = WatermarkPdf.CreateUser
                        });
                    }
                    else
                    {  // file.FileStatus = 200  被其他任務轉檔成功了，這裡加水印失败也就無所謂了

                    }

                }
                catch (Exception)
                {

                }

            }
            finally
            { //关闭
                pdfMemory.Close();
            }
        }

        #endregion

        #region 下載文件(SpringJiang已經重新整理邏輯)
        /// <summary>
        /// 下載文件
        /// </summary>
        /// <param name="fileData">資料表中文件(原檔)</param>
        /// <param name="conFileData">入參文件(原檔)</param>
        /// <param name="fileContentResults">結果</param>
        /// <param name="noFileByMinio">原檔文件在minio上無數據</param>
        /// <param name="errorFileByShift">轉檔失敗</param>
        /// <returns></returns>
        private async Task ToPdfFile(SysUploadFile fileData, DownloadFileToPdfModel conFileData, List<FileContentResult> fileContentResults, List<string> noFileByMinio, List<string> errorFileByShift)
        {
            #region 獲取機密等級+是否為暫存單+合約編號+申請單號
            //機密等級 
            string confidenLevel = ArchivalAttachmentsService.GetConfidenLevel(new SysUploadFileViewModel
            {
                UploadType = fileData.UploadType,
                UploadKey = fileData.UploadKey
            });
            //是否為暫存單
            bool isTemp = ArchivalAttachmentsService.GetApplicationStatue(fileData.UploadType, fileData.UploadKey);
            //獲取合約編號
            string contractNumber = ArchivalAttachmentsService.GetContractNumber(fileData.UploadKey);
            //暫存單 -> 申請單號為空
            string applyNumber = isTemp ? string.Empty : fileData.UploadKey;
            #endregion

            #region 自定義驗證
            //是否可下載轉檔文件(默認為true，可下載) -> 當原檔不存在時，不可下載
            bool isHavingProtoFile = true;
            //原檔文件是否在可轉檔白名單中
            bool canTransfer = canTransferSuffix.Contains(Path.GetExtension(fileData.FileName).TrimStart('.'));
            //當選中浮水印下載時 -> 給定前端提示，該原檔無法進行轉檔作業
            if (!canTransfer && conFileData.DocumentToPdf) { errorFileByShift.Add(fileData.FileName); }
            #endregion

            #region 原始文件下載 -> 不需要加浮水印，也不需要加合約編號和申請單號
            if (conFileData.Document)
            {
                //定義原檔文件流
                MemoryStream docMemory = new MemoryStream();
                try
                {
                    await _minioClient.GetObjectAsync(new GetObjectArgs().
                        WithBucket(_minIOConfig.Bucket).
                        WithObject(fileData.FilePath).
                        WithCallbackStream((stream) => { stream.CopyTo(docMemory); })).
                        ConfigureAwait(false);
                    //存儲數據
                    fileContentResults.Add(File(docMemory.ToArray(), "application/json", ArchivalAttachmentsService.GetFileName(fileData.FileName, confidenLevel)));
                }
                catch
                {
                    //表示在minion上無原檔文件，則不可下載轉檔文件
                    isHavingProtoFile = false;
                    noFileByMinio.Add(fileData.FileName);
                }
                finally { docMemory.Close(); }
            }
            #endregion

            #region 轉檔文件下載(前端用戶選擇浮水印+原檔存在+轉檔後綴白名單) -> 需要加上浮水印+合約編號+申請單號
            SysUploadFile pdfUploadFile = null;
            if (conFileData.DocumentToPdf && isHavingProtoFile && canTransfer)
            {
                #region 浮水印信息匯總
                Dictionary<string, string> waterData = new Dictionary<string, string>();
                waterData["cl"] = CommonUtilHelper.GetConfidenLevelWatermarkText(confidenLevel);
                waterData["cn"] = contractNumber;
                waterData["an"] = applyNumber;
                #endregion
                //獲取轉檔數據(黨完全沒有做過轉檔或轉檔文件被刪除則無數據)
                pdfUploadFile = SysUploadFileDataService.Find(new SysUploadFileCondition() { OriginalFileId = fileData.Fileid });
                //如果為false表示無轉檔數據，true表示有轉檔數據
                bool hasTranData = pdfUploadFile != null;
                //驗證在minio上是否存在數據(默認存在數據)
                bool hasMinioData = true;

                #region 有轉檔數據，默認minion上存在轉檔文件數據
                if (hasTranData)
                {
                    MemoryStream pdfMemory = new MemoryStream();
                    #region 如果走到catch，表示存在轉檔數據，但無轉檔文件，需要再次根據原檔進行轉檔
                    try
                    {
                        await _minioClient.GetObjectAsync(new GetObjectArgs().
                            WithBucket(_minIOConfig.Bucket).
                            WithObject(pdfUploadFile.FilePath).
                            WithCallbackStream((stream) => { stream.CopyTo(pdfMemory); })).
                            ConfigureAwait(false);
                        //給文件流添加機密等級+合約編號+申請單號
                        WatermarkService.AddWatermarkTextToPdf(pdfMemory, waterData);
                        //存儲數據
                        fileContentResults.Add(File(pdfMemory.ToArray(), "application/json", ArchivalAttachmentsService.GetPdfFileName(fileData.FileName, confidenLevel)));

                        #region 如果原始檔為轉檔中，則需要將狀態修改
                        if (fileData.FileStatus.Equals((int)FileStatusUtil.Turning))
                        {
                            SysUploadFileDataService.Update(new SysUploadFile()
                            {
                                Fileid = fileData.Fileid,
                                HasWatermake = (int)YesOrNoUtils.Yes,
                                FileStatus = (int)FileStatusUtil.Turned,
                                ModifyTime = DateTime.UtcNow,
                                ModifyUser = MvcContext.UserInfo.current_emp
                            });
                            fileData.FileStatus = (int)FileStatusUtil.Turned;
                        }
                        #endregion
                    }
                    catch
                    {
                        hasMinioData = false;
                    }
                    finally
                    {
                        //釋放轉檔資源的文件流
                        pdfMemory.Close();
                    }
                    #endregion
                }
                #endregion

                #region 若無轉檔文件或轉檔文件在資料中存在且minio不存在，則需要根據原始文件在minio添加轉檔文件
                if (!hasTranData || !hasMinioData)
                {
                    #region 不為轉檔中，需要獲取原文件進行轉檔作業
                    //定義原檔文件流
                    MemoryStream memoryStream = new MemoryStream();
                    //修改狀態為轉檔中
                    SysUploadFileDataService.Update(new SysUploadFile() { Fileid = fileData.Fileid, FileStatus = (int)FileStatusUtil.Turning });
                    #region 獲取原檔文件
                    try
                    {
                        await _minioClient.GetObjectAsync(new GetObjectArgs().
                            WithBucket(_minIOConfig.Bucket).
                            WithObject(fileData.FilePath).
                            WithCallbackStream((stream) => { stream.CopyTo(memoryStream); memoryStream.Seek(0, SeekOrigin.Begin); })).
                            ConfigureAwait(false);

                        #region 表示原檔文件流已經獲取到了，需要進行轉檔作業
                        if (memoryStream != null)
                        {
                            MemoryStream pdfStream = new MemoryStream();
                            try
                            {
                                //轉檔動作
                                pdfStream = (MemoryStream)ToPdfService.ToPdfStream(fileData.FileType, memoryStream);
                                if (pdfStream != null)
                                {
                                    #region 更新原檔數據，將狀態改為轉檔成功
                                    SysUploadFileDataService.Update(new SysUploadFile()
                                    {
                                        Fileid = fileData.Fileid,
                                        HasWatermake = (int)YesOrNoUtils.Yes,
                                        FileStatus = (int)FileStatusUtil.Turned,
                                        ModifyTime = DateTime.UtcNow,
                                        ModifyUser = MvcContext.UserInfo.current_emp
                                    });
                                    #endregion

                                    //拼接pdf路徑
                                    string pdfPath = Path.GetDirectoryName(fileData.FilePath) + "/" + $"{DateTime.Now:yyyyMMddHHmmss}{Path.GetFileNameWithoutExtension(fileData.FileName)}.pdf";
                                    #region 在minion上存儲轉檔文件
                                    var putObjectArgs = new PutObjectArgs()
                                                                    .WithBucket(_minIOConfig.Bucket)
                                                                    .WithObject(pdfPath)
                                                                    .WithStreamData(pdfStream)
                                                                    .WithObjectSize(pdfStream.Length)
                                                                    .WithContentType("application/octet-stream");
                                    await _minioClient.PutObjectAsync(putObjectArgs).ConfigureAwait(false);
                                    #endregion

                                    #region 儅minio上傳成功后，存儲一筆轉檔數據
                                    SysUploadFile sysUploadFile = SysUploadFileDataService.FindByKey(fileData.Fileid);
                                    sysUploadFile.Fileid = null;
                                    sysUploadFile.FileSize = pdfStream.Length;
                                    sysUploadFile.FileName = $"{Path.GetFileNameWithoutExtension(fileData.FileName)}.pdf";
                                    sysUploadFile.FileType = "pdf";
                                    sysUploadFile.FilePath = pdfPath;
                                    sysUploadFile.ModifyUser = MvcContext.UserInfo.current_emp;
                                    sysUploadFile.ModifyTime = DateTime.UtcNow;
                                    sysUploadFile.IsWatermake = (int)YesOrNoUtils.Yes;
                                    sysUploadFile.OriginalFileId = fileData.Fileid;
                                    //新增
                                    SysUploadFileDataService.Create(sysUploadFile);
                                    #endregion
                                    //給文件流添加機密等級+合約編號+申請單號
                                    WatermarkService.AddWatermarkTextToPdf(pdfStream, waterData);
                                    //存儲數據
                                    fileContentResults.Add(File(pdfStream.ToArray(), "application/json", ArchivalAttachmentsService.GetPdfFileName(fileData.FileName, confidenLevel)));
                                }
                            }
                            catch
                            {
                                //給定前端，該原檔無法進行轉檔作業
                                errorFileByShift.Add(fileData.FileName);
                                //修改原檔數據
                                SysUploadFileDataService.Update(new SysUploadFile() { Fileid = fileData.Fileid, FileStatus = (int)FileStatusUtil.None });
                            }
                            finally
                            {
                                //釋放資源
                                pdfStream.Close();
                                memoryStream.Close();
                            }
                        }
                        #endregion
                    }
                    catch
                    {
                        //並且給定前端表示轉檔文件已無原檔
                        noFileByMinio.Add(fileData.FileName);
                        //修改原檔數據
                        SysUploadFileDataService.Update(new SysUploadFile() { Fileid = fileData.Fileid, FileStatus = (int)FileStatusUtil.None });
                    }
                    finally
                    {
                        //釋放轉檔資源的文件流
                        memoryStream.Close();
                    }
                    #endregion

                    #endregion
                }
                #endregion
            }
            #endregion
        }
        #endregion


        /// <summary>
        /// 其他档案转PDF，仅转档
        /// </summary>
        /// <param name="originalFile">源文件</param>
        /// <param name="pdfFile">PDF文件，如果为null则表是数据库无转档文件记录，如果不是null则表示重新转档</param>
        /// <param name="currentUser">用戶信息</param>
        /// <returns></returns>
        private async Task ToPdfFileShiftOnly(SysUploadFile originalFile, SysUploadFile pdfFile, string currentUser)
        {
            //修改狀態為轉檔中
            SysUploadFileDataService.Update(new SysUploadFile()
            {
                Fileid = originalFile.Fileid,
                FileStatus = (int)FileStatusUtil.Turning,
                ModifyTime = DateTime.UtcNow,
            });

            await Task.Run(async () => //使用一個新的任務來執行
            {
                MemoryStream memoryStream = new MemoryStream();//定義原檔文件流
                MemoryStream pdfStream = new MemoryStream();// 定義转档后的文件流
                try
                {
                    memoryStream = await DownLoadFile(originalFile.FilePath, Convert.ToInt64(originalFile.FileSize)); //源文件
                    pdfStream = (MemoryStream)ToPdfService.ToPdfStream(originalFile.FileType, memoryStream);//转档文件

                    //拼接pdf路徑
                    string pdfPath = Path.GetDirectoryName(originalFile.FilePath) + "/" + $"{DateTime.Now:yyyyMMddHHmmss}{Path.GetFileNameWithoutExtension(originalFile.FileName)}.pdf";

                    #region 在minion上存儲轉檔文件
                    var putObjectArgs = new PutObjectArgs()
                                                    .WithBucket(_minIOConfig.Bucket)
                                                    .WithObject(pdfPath)
                                                    .WithStreamData(pdfStream)
                                                    .WithObjectSize(pdfStream.Length)
                                                    .WithContentType("application/octet-stream");
                    await _minioClient.PutObjectAsync(putObjectArgs).ConfigureAwait(false);
                    #endregion

                    if (pdfFile == null)
                    {
                        #region 當minio上傳成功后，存儲一筆轉檔數據
                        var sysUploadFile = SysUploadFileDataService.FindByKey(originalFile.Fileid);
                        sysUploadFile.Fileid = null;
                        sysUploadFile.FileSize = pdfStream.Length;
                        sysUploadFile.FileName = $"{Path.GetFileNameWithoutExtension(sysUploadFile.FileName)}.pdf";
                        sysUploadFile.FileType = "pdf";
                        sysUploadFile.FilePath = pdfPath;
                        sysUploadFile.ModifyUser = currentUser;
                        sysUploadFile.ModifyTime = DateTime.UtcNow;
                        sysUploadFile.IsWatermake = (int)YesOrNoUtils.Yes;
                        sysUploadFile.OriginalFileId = originalFile.Fileid;
                        //新增
                        SysUploadFileDataService.Create(sysUploadFile);
                        #endregion
                    }
                    else
                    {
                        //更新转档记录
                        //转档成功后更新转档文件记录
                        SysUploadFileDataService.Update(new SysUploadFile()
                        {
                            Fileid = pdfFile.Fileid,
                            IsWatermake = (int)YesOrNoUtils.Yes,
                            ModifyTime = DateTime.UtcNow,
                            ModifyUser = currentUser,
                            FileSize = pdfStream.Length,
                            FilePath = pdfPath //更新PATH
                        });

                    }

                    //转档成功后更新原文件状态
                    SysUploadFileDataService.Update(new SysUploadFile()
                    {
                        Fileid = originalFile.Fileid,
                        HasWatermake = (int)YesOrNoUtils.Yes,
                        FileStatus = (int)FileStatusUtil.Turned,
                        ModifyTime = DateTime.UtcNow,
                        ModifyUser = currentUser
                    });

                }
                catch (Exception)
                {
                    //表示 Gotenberg 服務出現問題，無法轉檔
                    SysUploadFileDataService.Update(new SysUploadFile()
                    {
                        Fileid = originalFile.Fileid,
                        FileStatus = (int)FileStatusUtil.None,
                        ModifyTime = DateTime.UtcNow,
                        ModifyUser = currentUser
                    });
                }
                finally
                {
                    if (memoryStream != null) memoryStream.Close();
                    if (pdfStream != null) pdfStream.Close();
                }


            });

        }

        /// <summary>
        /// 判斷一個文件在MINIO是否存在
        /// </summary>
        /// <param name="filePath"></param>
        /// <returns></returns>
        private bool MinioFileExist(string filePath)
        {
            Boolean hasMinioData = false;
            try
            {
                //获取文件元数据信息
                _ = _minioClient.StatObjectAsync(new StatObjectArgs().WithBucket(_minIOConfig.Bucket).WithObject(filePath)).Result;
                hasMinioData = true;
            }
            catch (Exception)
            {
                hasMinioData = false;
            }
            return hasMinioData;
        }


        /// <summary>
        /// 下載一個文件
        /// </summary>
        /// <returns></returns>
        private async Task<MemoryStream> DownLoadFile(string filePath, long fileSize)
        {


            if (!MinioFileExist(filePath)) throw new Exception("400");

            MemoryStream memoryStream = new MemoryStream();

            await _minioClient.GetObjectAsync(
                          new GetObjectArgs()
                          .WithBucket(_minIOConfig.Bucket)
                          .WithObject(filePath)
                          .WithCallbackStream((stream) => { stream.CopyTo(memoryStream); memoryStream.Seek(0, SeekOrigin.Begin); }))
                          .ConfigureAwait(false);
            if (Math.Abs(memoryStream.Length - fileSize) > (1 * 1024)) //下载的文件与原始文件大小比对，差距超过  1024
            {
                throw new Exception("402");//文件下载过程中丢失内容
            }
            return memoryStream;
        }

    }
}