﻿using Elegal.Flow.Api.Services;
using Elegal.Flow.Api.Services.Paper;
using Elegal.Interface.Api.Common.Control.Web;
using Elegal.Interface.Api.Common.Model.ParaModel.FlowApi;
using Elegal.Interface.Api.Common.Model.ResultModel;
using Elegal.Interface.Api.Common.Model.ViewModel.FlowApi;
using Elegal.Orm.Dtos;
using Microsoft.AspNetCore.Mvc;
using System.Text;

namespace Elegal.Flow.Api.Controllers
{
    /// <summary>
    /// 特殊主體-部門設定
    /// </summary>
    [Route("[controller]")]
    [ApiController]
    public class FnpEntityDeptController : BaseController
    {
        /// <summary>
        /// 特殊主體查詢
        /// </summary>
        [HttpPost]
        [Route("Query")]
        public async Task<ApiResultModelByObject> Query([FromBody] qryFnpEntityDept qry)
        {
            PageResult<FnpEntityDeptModel> res = FnpEntityDeptService.Query(qry);

            ApiResultModelByObject apiResult = new();
            apiResult.listData = res.Data;
            apiResult.totalCount = res.TotalRows;
            apiResult.rtnSuccess = true;

            InitLogRecord(qry, logRecord =>
            {
                StringBuilder stringBuilder = new();

                if (!string.IsNullOrEmpty(qry.deptid)) stringBuilder.AppendLine($"部門代號:  {qry.deptid}");
                string entity = string.Empty;
                if (qry.entity != null && qry.entity.Count > 0)
                {
                    entity = PaperBasicDataService.GetEntityString(string.Join(",", qry.entity));
                }
                if (!string.IsNullOrEmpty(entity)) stringBuilder.AppendLine($"特殊主體:  {entity}");

                logRecord.Detail = stringBuilder.ToString();
            });

            return await Task.FromResult(apiResult);
        }

        /// <summary>
        /// 特殊主體查詢
        /// </summary>
        [HttpPost]
        [Route("ExportDeptQuery")]
        public FileContentResult ExportDeptQuery([FromBody] qryFnpEntityDept qry)
        {
            var excelBytes = FnpEntityDeptService.ExportDeptQuery(qry);
            string filleName = $"特殊主體設定_部門設定_{DateTime.Now.ToString("yyyyMMdd")}.xlsx";

            InitLogRecord(qry, logRecord =>
            {
                StringBuilder stringBuilder = new();

                if (!string.IsNullOrEmpty(qry.deptid)) stringBuilder.AppendLine($"部門代號:  {qry.deptid}");
                string entity = string.Empty;
                if (qry.entity != null && qry.entity.Count > 0)
                {
                    entity = PaperBasicDataService.GetEntityString(string.Join(",", qry.entity));
                }
                if (!string.IsNullOrEmpty(entity)) stringBuilder.AppendLine($"特殊主體:  {entity}");

                logRecord.Detail = stringBuilder.ToString();
            });

            return File(excelBytes, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", filleName);
        }

        /// <summary>
        /// 特殊主體明細查詢
        /// </summary>
        [HttpGet]
        [Route("QueryDetails")]
        public async Task<ApiResultModelByObject> QueryDetails(string? deptid)
        {
            ApiResultModelByObject apiResult = new();
            apiResult.listData = FnpEntityDeptService.QueryDetails(deptid);
            apiResult.rtnSuccess = true;


            InitLogRecord<object>(new(), logRecord =>
            {
                StringBuilder stringBuilder = new();
                if (!string.IsNullOrEmpty(deptid)) stringBuilder.AppendLine($"部門代號: {deptid}");

                logRecord.Detail = stringBuilder.ToString();
            });

            return await Task.FromResult(apiResult);
        }

        /// <summary>
        /// 更新特殊主體-人員設定
        /// </summary>
        [HttpPost]
        [Route("UpdateFnpEntityDept")]
        public async Task<ApiResultModelByObject> UpdateFnpEntityDept([FromBody] updateFnpEntity data)
        {
            List<FnpEntityDeptDetailModel> oldData = FnpEntityDeptService.QueryDetails(data.deptid);

            ApiResultModelByObject apiResult = new();
            apiResult.listData = FnpEntityDeptService.UpdateFnpEntityDept(data.deptid, data.entity_id_list);
            apiResult.rtnSuccess = true;

            InitLogRecord(data, logRecord =>
            {
                StringBuilder stringBuilder = new();
                stringBuilder.AppendLine($"部門代號:  {data.deptid}");
                stringBuilder.AppendLine($"特殊主體:  {PaperBasicDataService.GetEntityString(string.Join(",", data.entity_id_list))}");

                logRecord.Detail = stringBuilder.ToString();

                if (oldData != null && oldData.Count > 0)
                {
                    string entityList = string.Join(", ", oldData.Select(x => x.dept_entity));
                    if (!string.IsNullOrEmpty(entityList)) logRecord.DetailFormer = "特殊主體: " + entityList;
                }
            });

            return await Task.FromResult(apiResult);
        }
    }
}
