﻿using Elegal.Interface.Api.Common.Control.Filter;
using Elegal.Interface.Api.Common.Model.ParaModel.FlowApi;
using Elegal.Interface.Api.Common.Model.ViewModel.FlowApi;
using Elegal.Interface.Api.Common.Repository;
using Elegal.Orm;
using Elegal.Orm.Dtos;

namespace Elegal.Flow.Api.Repository.Paper
{
    /// <summary>
    /// 紙本追蹤批次作業查询模型
    /// </summary>
    public class PaperTrackingBathJobRepository
    {
        /// <summary>
        /// 查询列表数据
        /// </summary>
        /// <param name="condition"></param>
        /// <returns></returns>
        public List<PaperTrackingBathJobViewModel> GetListData(PaperTrackingBathJobSearchModel condition)
        {
            string userSql = @"SELECT
	                                emplid,
	                                name,
	                                name_a,
	                                email_address_a 
                                FROM
	                                ps_sub_ee_lgl_vw_a
                                UNION
                                SELECT
	                                aff_empid AS emplid,
	                                aff_emp_cname AS name,
	                                aff_emp_ename AS name_a,
	                                aff_emp_email AS email_address_a
                                FROM
	                                affiliate_emp";
            string paperTrackingMax = @"SELECT
	                                        track_id,
	                                        apply_number,
	                                        track_status,
	                                        receiving_type,
	                                        receiving_emplid,
	                                        receiving_date,
	                                        paper_number,
	                                        remind_count,
	                                        track_remark,
	                                        create_time,
	                                        create_user,
	                                        ROW_NUMBER() OVER (PARTITION BY apply_number
                                        ORDER BY
	                                        receiving_date DESC) AS rn
                                        FROM
	                                        paper_track_work";
            string sql = $@"WITH paper_max_data AS ({paperTrackingMax}) SELECT
                            ptw.track_id trackId,
                            af.apply_number applyNum,--申請單號
                            case
	                            when af.apply_type = 'C' then 2
	                            when af.form_type = 'R' then 4
	                            else null
                            end as applicationType,
                            af.apply_type applyType,--申请单类型
                            af.apply_form applyForm,--申请单类型
                            af.form_type formType,--申请单类型
                            af.legal_affairs_emplid legalEmplid,--承辦法務
                            lee.email_address_a legalMail,--承辦法務邮箱
                            lee.name legalCname,--承辦法務(中文名稱)
                            lee.name_a legalEname,--承辦法務(英文名稱)
                            af.pic_emplid handlerEmplid,--經辦人
                            pee.email_address_a handlerMail,--經辦人邮箱
                            pee.name handlerCname,--經辦人(中文名稱)
                            pee.name_a handlerEname,--經辦人(英文名稱)
                            af.fill_emplid fillerEmplid,--填單人
                            fee.email_address_a fillerMail,--填單人邮箱
                            fee.name fillerCname,--填單人(中文名稱)
                            fee.name_a fillerEname,--填單人(英文名稱)
                            af.contract_name contract_name,--合約/文件名稱
                            af.contract_name contractName,--合約/文件名稱
                            af.incumbent_emplid currentContactPerson,--現任聯絡人
                            iee.name currentContactPersonCname,--現任聯絡人(中文名稱)
                            iee.name_a currentContactPersonEname,--現任聯絡人(英文名稱)
                            ptw.track_status paperTracking,--追蹤狀態
                            sp.fun_name paperTrackingName,--追蹤狀態名称
                            ptw.receiving_type,--收件類型
                            ptw.receiving_emplid recipientEmplid,--收件者
                            ree.name recipientCname,--收件者(中文名稱)
                            ree.name_a recipientEname,--收件者(英文名稱)
                            ptw.receiving_date receiptDate,--收件日
                            ptw.remind_count remindCount,--提醒次數
                            ptw.paper_number paperPiece,--紙本份數
                            ptw.track_remark notes,--備註
                            ptw.create_time operate_time,--更新時間
                            ptw.create_user,--更新人
                            cee.name operate_cuser,--更新人(中文名稱)
                            cee.name_a operate_euser,--更新人(英文名稱)
                            af.other_party partyA,--他方名称
                            af.entity_id entityId,--我方主体
                            af.application_state applicationState,--我方主体
                            sp2.fun_name applicationStateName,--申请单状态
                            af.confiden_level confiden_level--机密等级
                            FROM (select * from V_GetAllApplication where apply_type = 'C' or form_type = 'R') AS af
                            LEFT JOIN (SELECT track_id,apply_number,track_status,receiving_type,receiving_emplid,receiving_date,paper_number,remind_count,track_remark,create_time,create_user 
                            FROM paper_max_data WHERE rn = 1) AS ptw ON ptw.apply_number = af.apply_number--獲取最新一筆的數據
                            LEFT JOIN (select fun_name,func_code from sys_parameters where para_code = 'lib_paperTrackStatus' and lang_type='{MvcContext.UserInfo.logging_locale}') sp on sp.func_code = ptw.track_status
                            LEFT JOIN (select fun_name,func_code from sys_parameters where para_code = 'applicationState' and lang_type='{MvcContext.UserInfo.logging_locale}') sp2 on sp2.func_code = af.application_state
                            LEFT JOIN ({userSql}) AS lee ON lee.emplid = af.legal_affairs_emplid--承辦法務人員
                            LEFT JOIN ({userSql}) AS pee ON pee.emplid = af.pic_emplid--經辦人
                            LEFT JOIN ({userSql}) AS fee ON fee.emplid = af.fill_emplid--填單人
                            LEFT JOIN ({userSql}) AS iee ON iee.emplid = isnull(af.incumbent_emplid,af.pic_emplid)--現任聯絡人/沒有時未經辦人
                            LEFT JOIN ({userSql}) AS cee ON cee.emplid = ptw.create_user--更新人
                            LEFT JOIN ({userSql}) AS ree ON ree.emplid = ptw.receiving_emplid--收件者
                            WHERE af.application_state !='T'";
            SearchItemGroup searchItemGroup = new SearchItemGroup() { };
            //申請單號
            if (!string.IsNullOrEmpty(condition.applyNum)) searchItemGroup.Items.Add(new SearchItem() { Field = "af.apply_number", Compare = CompareOperator.LIKE, Logic = LogicOperator.And, Value = condition.applyNum });
            //我方主體
            if (condition.myEntity.Any()) searchItemGroup.Items.Add(new SearchItem() { Field = "af.entity_id", Compare = CompareOperator.ARRAYIN, Logic = LogicOperator.And, Values = condition.myEntity.Select(s => s.EntityId) });
            //承辦法務
            if (condition.legalAffairs.Any()) searchItemGroup.Items.Add(new SearchItem() { Field = "af.legal_affairs_emplid", Compare = CompareOperator.ARRAYIN, Logic = LogicOperator.And, Values = condition.legalAffairs.Select(s => s.Emplid) });
            //收件者
            if (condition.recipient.Any()) searchItemGroup.Items.Add(new SearchItem() { Field = "ptw.receiving_emplid", Compare = CompareOperator.ARRAYIN, Logic = LogicOperator.And, Values = condition.recipient.Select(s => s.Emplid) });
            //合約/文件名稱
            if (!string.IsNullOrEmpty(condition.contractFileName)) searchItemGroup.Items.Add(new SearchItem() { Field = "af.contract_name", Compare = CompareOperator.LIKE, Logic = LogicOperator.And, Value = condition.contractFileName });
            //紙本追蹤狀態
            if (condition.paperTracking.Any()) searchItemGroup.Items.Add(new SearchItem() { Field = "ptw.track_status", Compare = CompareOperator.ARRAYIN, Logic = LogicOperator.And, Values = condition.paperTracking.Select(s => s.FuncCode) });
            //收件日
            if (condition.receiptDateStart.HasValue)
            {
                searchItemGroup.Items.Add(new SearchItem() { Field = "ptw.receiving_date", Logic = LogicOperator.And, Value = condition.receiptDateStart.Value.ToString("yyyy/MM/dd HH:mm:ss"), Compare = CompareOperator.GE });
            }
            if (condition.receiptDateEnd.HasValue)
                searchItemGroup.Items.Add(new SearchItem() { Field = "ptw.receiving_date", Logic = LogicOperator.And, Value = condition.receiptDateEnd.Value.ToString("yyyy/MM/dd HH:mm:ss"), Compare = CompareOperator.LE });
            if (searchItemGroup.Items.Any()) sql += Util.GetSearchItemGroup(searchItemGroup);
            //填單人/經辦人
            if (condition.fillOrHandler.Any())
            {
                searchItemGroup.Items.Clear();
                searchItemGroup.Items.AddRange([
                    new SearchItem(){ Field = "af.fill_emplid",Logic = LogicOperator.Or,Values = condition.fillOrHandler.Select(s=>s.Emplid),Compare = CompareOperator.ARRAYIN },
                    new SearchItem(){ Field = "af.pic_emplid",Logic = LogicOperator.Or,Values = condition.fillOrHandler.Select(s=>s.Emplid),Compare = CompareOperator.ARRAYIN },
                    ]);
                if (searchItemGroup.Items.Any()) sql += Util.GetSearchItemGroup(searchItemGroup);
            }
            //他方 需要進行特殊處理，不能直接使用查詢寫法
            if (condition.otherEntitys.Count > 0)
            {
                string applyNumbers = string.Join(',', new PublicHelperRepository().GetOtherPartApplyNumber(condition.otherEntitys));
                sql += $@"AND EXISTS(SELECT 1 FROM (SELECT value AS apply_number FROM STRING_SPLIT(N'{applyNumbers}', ',')) AS oan WHERE oan.apply_number = af.apply_number)";
            }
            return DbAccess.Database.SqlQuery<PaperTrackingBathJobViewModel>(sql).ToList();
        }
    }
}
