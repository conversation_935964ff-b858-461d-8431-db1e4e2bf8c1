﻿using Elegal.Flow.Api.Repository;
using Elegal.Interface.Api.Common.Model;
using Elegal.Interface.Api.Common.Model.ResultModel.FlowApi;

namespace Elegal.Flow.Api.Services
{
    /// <summary>
    /// 承辦窗口相關功能
    /// </summary>
    public static class UndertakeWindowService
    {

        private static readonly UndertakeWindowRepository _repository = new();

        /// <summary>
        /// 條件查詢承辦窗口數據
        /// </summary>
        public static List<UndertakeWindow> QueryUndertakeWindow(UndertakeWindow undertake,string logging_locale, string time_zone)
        {
            return _repository.QueryUndertakeWindow(undertake, logging_locale, time_zone);
        }

        /// <summary>
        /// 根據newid查詢承辦窗口數據
        /// </summary>
        public static UndertakeWindow QueryUndertakeWindowByFnid(int fnid,string logging_locale,string time_zone)
        {
            UndertakeWindow undertake = _repository.QueryUndertakeWindowByFnid(fnid, logging_locale, time_zone);
            return undertake; 
        }

        /// <summary>
        /// 根據newid刪除承辦窗口數據
        /// </summary>
        public static int DeleteUndertakeWindow(int fnid)
        {
            return _repository.DeleteUndertakeWindow(fnid);
        }

        /// <summary>
        /// 插入承辦窗口數據
        /// </summary>
        public static int InsertUndertakeWindow(UndertakeWindow undertake)
        {
            return _repository.InsertUndertakeWindow(undertake);
        }

        /// <summary>
        /// 檢查是否存在重複的公告數據
        /// </summary>
        public static Boolean DuplicateVerificationUndertakeWindow(UndertakeWindow undertake)
        {
            return _repository.DuplicateVerificationUndertakeWindow(undertake);
        }

        /// <summary>
        /// 修改承辦窗口數據
        /// </summary>
        public static int UpdateUndertakeWindow(UndertakeWindow undertake)
        {
            return _repository.UpdateUndertakeWindow(undertake);
        }

        /// <summary>
        /// 查詢項目下拉數據
        /// </summary>
        public static List<DropDownListModel> QueryContact()
        {
            return _repository.QueryContact();
        }
    }
}
