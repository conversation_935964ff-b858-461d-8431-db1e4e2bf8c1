﻿using Azure.Messaging;
using Elegal.Flow.Api.Services.Paper;
using Elegal.Interface.Api.Common.Control.Filter;
using Elegal.Interface.Api.Common.Control.Web;
using Elegal.Interface.Api.Common.FuncHelper;
using Elegal.Interface.Api.Common.Model.Enum;
using Elegal.Interface.Api.Common.Model.ParaModel.FlowApi;
using Elegal.Interface.Api.Common.Model.ResultModel;
using Elegal.Interface.Api.Common.Model.ViewModel.FlowApi;
using Elegal.Interface.ApiData.Service.FuncService;
using Elegal.Interface.ApiData.Service.Model.QueryCondition;
using Elegal.Orm.Dtos;
using Elegal.Orm.Utils;
using Microsoft.AspNetCore.Mvc;
using System.Text;

namespace Elegal.Flow.Api.Controllers.Paper
{
    /// <summary>
    /// 紙本追蹤批次作業
    /// </summary>
    [Route("[controller]")]
    [ApiController]
    public class PaperTrackingBathJobController : BaseController
    {
        /// <summary>
        /// 查询列表数据
        /// </summary>
        /// <param name="condition">查询参数</param>
        /// <returns></returns>
        [HttpPost]
        [Route("GetListData")]
        public async Task<ApiResultModelByObject> GetListData(PaperTrackingBathJobSearchModel condition)
        {
            ApiResultModelByObject apiResult = new ApiResultModelByObject()
            {
                listData = PaperTrackingBathJobService.GetListData(condition),
                rtnSuccess = true,
            };
            InitLogRecord(log =>
            {
                StringBuilder stringBuilder = new StringBuilder();
                if (condition.fillOrHandler.Any())
                    stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("PaperTrackingBathJob_fillOrHandler", true)}：{string.Join(',', condition.fillOrHandler.Select(s => s.NameA))}");
                if (!string.IsNullOrEmpty(condition.applyNum))
                    stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("PaperTrackingBathJob_applyNum", true)}：{condition.applyNum}");
                if (!condition.selectNoEntity)
                    stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("PaperTrackingBathJob_myEntity", true)}：{string.Join(',', condition.myEntity.Select(s => s.Entity))}");
                if (!string.IsNullOrEmpty(condition.otherEntity))
                    stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("PaperTrackingBathJob_otherEntity", true)}：{condition.otherEntity}");
                if (condition.legalAffairs.Any())
                    stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("PaperTrackingBathJob_legalAffairs", true)}：{string.Join(',', condition.legalAffairs.Select(s => s.NameA))}");
                if (condition.recipient.Any())
                    stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("PaperTrackingBathJob_recipient", true)}：{string.Join(',', condition.recipient.Select(s => s.NameA))}");
                if (!string.IsNullOrEmpty(condition.contractFileName))
                    stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("PaperTrackingBathJob_contractFileName", true)}：{condition.contractFileName}");
                if (condition.paperTracking.Any())
                    stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("PaperTrackingBathJob_paperTracking", true)}：{string.Join(',', condition.paperTracking.Select(s => s.FunName))}");
                string start = string.Empty, end = string.Empty;
                if (condition.receiptDateStart.HasValue)
                    start = TimeZoneInfoConvertHelper.ConvertDateByTimeZoneByUtc(condition.receiptDateStart, MvcContext.UserInfo.time_zone).ToString("yyyy/MM/dd");
                if (condition.receiptDateEnd.HasValue)
                    end = TimeZoneInfoConvertHelper.ConvertDateByTimeZoneByUtc(condition.receiptDateEnd, MvcContext.UserInfo.time_zone).ToString("yyyy/MM/dd");
                if (!string.IsNullOrEmpty(start) || !string.IsNullOrEmpty(end))
                    stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("PaperTrackingBathJob_receiptDate", true)}：{start} ~ {end}");
                if (condition.filterList.Any())
                {
                    stringBuilder.AppendLine(CommonUtil.GetDivisionLine(ActionFilter.GetMultilingualValue("PaperTrackingBathJob_filter", true)));
                    stringBuilder.AppendLine(string.Join("\r\n", condition.filterList.Select(s => $"{ActionFilter.GetMultilingualValue($"PaperTrackingBathJob_export:{s.Key}", true)}：{s.Value}")));
                }
                log.Detail = stringBuilder.ToString();
            });
            return await Task.FromResult(apiResult);
        }
        /// <summary>
        /// 邮件提醒
        /// </summary>
        /// <param name="models"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("SendMail")]
        public async Task<ApiResultModelByObject> SendMail(List<PaperTrackingBathJobViewModel> models)
        {
            if (!models.Any())
            {
                return new ApiResultModelByObject()
                {
                    messageContent = new List<string> { ActionFilter.GetMultilingualValue("custom:messageContent:SelectData") },
                    messageType = MessageTypeUtils.Warning.ToString()
                };
            }
            var oldList = PaperTrackWorkDataService.Query(new PaperTrackWorkQueryCondition()
            {
                SearchItemGroup = new SearchItemGroup()
                {
                    Items = new List<SearchItem>
                    {
                        new SearchItem()
                        {
                            Field = "apply_number",
                            Compare = CompareOperator.ARRAYIN,
                            Logic = LogicOperator.And,
                            Values =models.Select(s=>s.applyNum)
                        }
                    }
                },
                OrderBys = new List<OrderByParam>
                {
                    new OrderByParam()
                    {
                        Field = "create_time",
                        Order = OrderBy.DESC,
                    }
                }
            });
            //发送前判断纸本追踪是否有变动
            if (models.Where(w => !oldList.First(f => f.ApplyNumber.Equals(w.applyNum)).TrackStatus.Equals(w.paperTracking)).Any())
            {
                return new ApiResultModelByObject()
                {
                    messageContent = new List<string> { ActionFilter.GetMultilingualValue("custom:messageContent:dataChurn") },
                    messageType = MessageTypeUtils.Warning.ToString()
                };
            }
            var rlist = PaperTrackingBathJobService.SendMail(models);

            ApiResultModelByObject apiResult = new ApiResultModelByObject()
            {
                rtnSuccess = true,
                listData = rlist
            };

           
            return await Task.FromResult(apiResult);
        }
        /// <summary>
        /// 邮件提醒内容
        /// </summary>
        /// <param name="condition">查询参数</param>
        /// <returns></returns>
        [HttpPost]
        [Route("SendMailNotice")]
        public async Task<ApiResultModelByObject> SendMailNotice(SendMailNoticeParaModel condition)
        {
            bool _ = false;
            ApiResultModelByObject apiResult = new ApiResultModelByObject
            {
                listData = PaperTrackingBathJobService.SendMailNotice(condition.PaperTracking, condition.ApplicationType, ref _),
                rtnSuccess = _
            };
            return await Task.FromResult(apiResult);
        }
        /// <summary>
        /// 新增
        /// </summary>
        /// <param name="condition">查询参数</param>
        /// <returns></returns>
        [HttpPost]
        [Route("Add")]
        public async Task<ApiResultModelByObject> Add(PaperTrackingBathJobAddModel condition)
        {
            ApiResultModelByObject apiResult = new ApiResultModelByObject()
            {
                listData = PaperTrackingBathJobService.Add(condition),
                rtnSuccess = true,
            };
            InitLogRecord(log =>
            {
                StringBuilder stringBuilder = new StringBuilder();
                stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("PaperTrackingBathJob_applyNum", true)}：{string.Join(",", condition.applyNum)}");
                stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("PaperTrackingBathJob_paperTracking", true)}：{condition.paperTracking.FunName}");
                stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("PaperTrackingBathJob_receiptType", true)}：{ActionFilter.GetMultilingualValue($"PaperTrackingBathJob_receiptType{condition.receiptType}", true)}");
                if(!string.IsNullOrEmpty(condition.consignmentNumber))
                    stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("PaperTrackingBathJob_ConsignmentNumber", true)}：{condition.consignmentNumber}");
                stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("PaperTrackingBathJob_recipient", true)}：{condition.recipient.NameA}");
                stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("PaperTrackingBathJob_receiptDate", true)}：{condition.receipt.ConvertDateByTimeZoneByUtc(MvcContext.UserInfo.time_zone).ToString("yyyy/MM/dd HH:mm")}");
                stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("PaperTrackingBathJob_paperPiece", true)}：{condition.paperPiece}");
                stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("PaperTrackingBathJob_notes", true)}：{condition.notes}");
                log.Detail = stringBuilder.ToString();
            });
            return await Task.FromResult(apiResult);
        }

        /// <summary>
        /// 列表数据导出日志记录
        /// </summary>
        /// <param name="condition">查询参数</param>
        /// <returns></returns>
        [HttpPost]
        [Route("ExportLog")]
        public async Task<ApiResultModelByObject> ExportLog(PaperTrackingBathJobSearchModel condition)
        {
            ApiResultModelByObject apiResult = new ApiResultModelByObject()
            {
                listData = true,
                rtnSuccess = true,
            };
            InitLogRecord(log =>
            {
                StringBuilder stringBuilder = new StringBuilder();
                if (condition.fillOrHandler.Any())
                    stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("PaperTrackingBathJob_fillOrHandler", true)}：{string.Join(',', condition.fillOrHandler.Select(s => s.NameA))}");
                if (!string.IsNullOrEmpty(condition.applyNum))
                    stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("PaperTrackingBathJob_applyNum", true)}：{condition.applyNum}");
                if (condition.myEntity.Any())
                    stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("PaperTrackingBathJob_myEntity", true)}：{string.Join(',', condition.myEntity.Select(s => s.Entity))}");
                if (!string.IsNullOrEmpty(condition.otherEntity))
                    stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("PaperTrackingBathJob_otherEntity", true)}：{condition.otherEntity}");
                if (condition.legalAffairs.Any())
                    stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("PaperTrackingBathJob_legalAffairs", true)}：{string.Join(',', condition.legalAffairs.Select(s => s.NameA))}");
                if (condition.recipient.Any())
                    stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("PaperTrackingBathJob_recipient", true)}：{string.Join(',', condition.recipient.Select(s => s.NameA))}");
                if (!string.IsNullOrEmpty(condition.contractFileName))
                    stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("PaperTrackingBathJob_contractFileName", true)}：{condition.contractFileName}");
                if (condition.paperTracking.Any())
                    stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("PaperTrackingBathJob_paperTracking", true)}：{string.Join(',', condition.paperTracking.Select(s => s.FunName))}");
                string start = string.Empty, end = string.Empty;
                if (condition.receiptDateStart.HasValue)
                    start = TimeZoneInfoConvertHelper.ConvertDateByTimeZoneByUtc(condition.receiptDateStart, MvcContext.UserInfo.time_zone).ToString("yyyy/MM/dd");
                if (condition.receiptDateEnd.HasValue)
                    end = TimeZoneInfoConvertHelper.ConvertDateByTimeZoneByUtc(condition.receiptDateEnd, MvcContext.UserInfo.time_zone).ToString("yyyy/MM/dd");
                if (!string.IsNullOrEmpty(start) || !string.IsNullOrEmpty(end))
                    stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("PaperTrackingBathJob_receiptDate", true)}：{start} ~ {end}");
                if (condition.filterList.Any())
                {
                    stringBuilder.AppendLine(CommonUtil.GetDivisionLine(ActionFilter.GetMultilingualValue("PaperTrackingBathJob_filter", true)));
                    stringBuilder.AppendLine(string.Join("\r\n", condition.filterList.Select(s => $"{ActionFilter.GetMultilingualValue($"PaperTrackingBathJob_export:{s.Key}", true)}：{s.Value}")));
                }
                log.Detail = stringBuilder.ToString();
            });
            return await Task.FromResult(apiResult);
        }
    }
}
