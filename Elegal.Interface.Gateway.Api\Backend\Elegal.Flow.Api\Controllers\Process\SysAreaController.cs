﻿using Elegal.Flow.Api.Services.Process;
using Elegal.Interface.Api.Common.Control.Filter;
using Elegal.Interface.Api.Common.Control.Web;
using Elegal.Interface.Api.Common.Model.Enum;
using Elegal.Interface.Api.Common.Model.ResultModel;
using Elegal.Interface.ApiData.Service.FuncService;
using Elegal.Interface.ApiData.Service.Model.Condition;
using Elegal.Interface.ApiData.Service.Model.DbModel;
using Elegal.Interface.ApiData.Service.Model.QueryCondition;
using Elegal.Orm.Dtos;
using Microsoft.AspNetCore.Mvc;

namespace Elegal.Flow.Api.Controllers.Process
{
    /// <summary>
    /// 區域維護
    /// </summary>
    [Route("[controller]")]
    [ApiController]
    public class SysAreaController : BaseController
    {
        #region 獲取區域下拉數據源
        /// <summary>
        /// 獲取區域下拉數據源
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("GetSelectData")]
        public async Task<ApiResultModelByObject> GetSelectData()
        {
            SysAreaQueryCondition sysAreaQueryCondition = new SysAreaQueryCondition()
            {
                OrderBys =
                [
                    new OrderByParam()
                    {
                        Field="AreaName",
                        Order=OrderBy.ASC
                    }
                ]
            };
            return await Task.FromResult(new ApiResultModelByObject()
            {
                listData = SysAreaDataService.Query(sysAreaQueryCondition),
                rtnSuccess = true
            });
        }
        #endregion

        #region 獲取區域列表數據
        /// <summary>
        /// 獲取區域列表數據
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("GetAreaList")]
        public async Task<ApiResultModelByObject> GetSysAreaList()
        {
            ApiResultModelByObject apiResult = new()
            {
                listData = SysAreaService.GetSysAreaList(),
                rtnSuccess = true
            };
            return await Task.FromResult(apiResult);
        }
        #endregion

        #region 根據區域ID獲取區域數據
        /// <summary>
        /// 根據區域ID獲取區域數據
        /// </summary>
        /// <param name="area_id">區域ID</param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetArea")]
        public async Task<ApiResultModelByObject> GetSysArea(int area_id)
        {
            var area = SysAreaDataService.FindByKey(area_id);
            #region 校驗數據是否存在
            if (area == null)
                return new ApiResultModelByObject()
                {
                    messageTitle = ActionFilter.GetMultilingualValue("custom:messageTitle:dataNotexist"),
                    messageContent = [ActionFilter.GetMultilingualValue("custom:messageContent:dataChurn")],
                    messageType = MessageTypeUtils.Warning.ToString()
                };
            #endregion
            return await Task.FromResult(new ApiResultModelByObject()
            {
                listData = area,
                rtnSuccess = true
            });
        }
        #endregion

        #region 根據區域ID刪除區域數據
        /// <summary>
        /// 根據區域ID刪除區域數據
        /// </summary>
        /// <param name="area_id">區域ID</param>
        /// <returns></returns>
        [HttpDelete]
        [Route("DeleteArea")]
        public async Task<ApiResultModel> DeleteSysArea(int area_id)
        {
            ApiResultModel apiResult = new();
            SysArea sysArea = SysAreaDataService.FindByKey(area_id);
            #region 校驗數據是否存在
            if (sysArea == null)
                return new ApiResultModel()
                {
                    messageTitle = ActionFilter.GetMultilingualValue("custom:messageTitle:dataNotexist"),
                    messageContent = [ActionFilter.GetMultilingualValue("custom:messageContent:dataChurn")],
                    messageType = MessageTypeUtils.Warning.ToString()
                };
            #endregion

            #region 校驗所刪除的區域是否有設定主體資訊
            if (FnpEntityDataService.Exists(new FnpEntityCondition() { AreaId = area_id }))
            {
                return await Task.FromResult(new ApiResultModel()
                {
                    messageTitle = ActionFilter.GetMultilingualValue("custom:messageTitle:dataUsed"),
                    messageContent = new List<string> { ActionFilter.GetMultilingualValue("custom:messageContent:areaUsed") },
                    messageType = MessageTypeUtils.Warning.ToString()
                });
            }
            #endregion

            #region 刪除區域數據
            SysAreaDataService.DeleteByKey(area_id);
            #endregion

            #region log日志記錄
            InitLogRecord(LogRecord =>
            {
                LogRecord.Detail = @$"{ActionFilter.GetMultilingualValue("SysArea_areaName", true)}：{sysArea.AreaName}";
            });
            #endregion
            return await Task.FromResult(new ApiResultModel()
            {
                rtnSuccess = true
            });
        }
        #endregion

        #region 修改區域數據
        /// <summary>
        /// 修改區域數據
        /// </summary>
        /// <param name="area_id">區域ID</param>
        /// <param name="area_name">修改后的區域名稱</param>
        /// <returns></returns>
        [HttpPut]
        [Route("UpdateArea")]
        public async Task<ApiResultModel> UpdateSysArea(int area_id, string area_name)
        {
            SysArea sysArea = SysAreaDataService.FindByKey(area_id);
            #region 校驗數據是否存在
            if (sysArea == null)
                return new ApiResultModel()
                {
                    messageTitle = ActionFilter.GetMultilingualValue("custom:messageTitle:dataNotexist"),
                    messageContent = [ActionFilter.GetMultilingualValue("custom:messageContent:dataChurn")],
                    messageType = MessageTypeUtils.Warning.ToString()
                };
            #endregion

            #region 校驗修改后的區域名稱是否存在(條件：區域名稱有變更&變更后的區域名稱已存在)
            if (!sysArea.AreaName.Equals(area_name) && SysAreaDataService.Exists(new SysAreaCondition() { AreaName = area_name }))
                return new ApiResultModel()
                {
                    messageTitle = ActionFilter.GetMultilingualValue("custom:messageTitle:dataExists"),
                    messageContent = new List<string> { ActionFilter.GetMultilingualValue("custom:messageContent:areaNameExist") },
                    messageType = MessageTypeUtils.Warning.ToString(),
                };
            #endregion

            #region 修改區域數據
            SysAreaDataService.Update(new SysArea()
            {
                AreaId = area_id,
                AreaName = area_name,
                ModifyUser = MvcContext.UserInfo.current_emp,
                ModifyTime = DateTime.UtcNow
            });
            #endregion

            #region log日志記錄
            InitLogRecord(LogRecord =>
            {
                LogRecord.Detail = @$"{ActionFilter.GetMultilingualValue("SysArea_areaName", true)}：{area_name}";
                LogRecord.DetailFormer = @$"{ActionFilter.GetMultilingualValue("SysArea_areaName", true)}：{sysArea.AreaName}";
            });
            #endregion

            return await Task.FromResult(new ApiResultModel() { rtnSuccess = true });
        }
        #endregion

        #region 新增區域
        /// <summary>
        /// 新增區域
        /// </summary>
        /// <param name="area_name">區域名稱</param>
        /// <returns></returns>
        [HttpPost]
        [Route("AddArea")]
        public async Task<ApiResultModel> AddSysArea(string area_name)
        {
            #region 校驗區域名稱是否已存在
            if (SysAreaDataService.Exists(new SysAreaCondition() { AreaName = area_name }))
            {
                return new ApiResultModel()
                {
                    messageTitle = ActionFilter.GetMultilingualValue("custom:messageTitle:dataExists"),
                    messageContent = [ActionFilter.GetMultilingualValue("custom:messageContent:areaNameExist")],
                    messageType = MessageTypeUtils.Warning.ToString()
                };
            }
            #endregion

            #region 新增區域數據
            SysAreaDataService.Create(new SysArea()
            {
                AreaName = area_name,
                CreateUser = MvcContext.UserInfo.current_emp,
                CreateTime = DateTime.UtcNow
            });
            #endregion

            #region log日志記錄
            InitLogRecord(LogRecord =>
            {
                LogRecord.Detail = @$"{ActionFilter.GetMultilingualValue("SysArea_areaName",true)}：{area_name}";
            });
            #endregion
            return await Task.FromResult(new ApiResultModel() { rtnSuccess = true });
        }
        #endregion
    }
}
