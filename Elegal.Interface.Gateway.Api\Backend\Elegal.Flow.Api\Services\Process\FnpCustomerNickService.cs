﻿using Elegal.Flow.Api.Repository.Process;
using Elegal.Interface.Api.Common.Control.Filter;
using Elegal.Interface.Api.Common.FuncHelper;
using Elegal.Interface.Api.Common.FuncService;
using Elegal.Interface.Api.Common.Model.Enum;
using Elegal.Interface.Api.Common.Model.ResultModel;
using Elegal.Interface.Api.Common.Model.ViewModel.FlowApi;
using Elegal.Interface.ApiData.Service.FuncService;
using Elegal.Interface.ApiData.Service.Model.DbModel;
using Elegal.Orm;
using Minio;
using Minio.DataModel.Args;
using NPOI.HSSF.UserModel;
using NPOI.SS.UserModel;
using NPOI.XSSF.UserModel;
using System.Text.RegularExpressions;

namespace Elegal.Flow.Api.Services.Process
{
    /// <summary>
    /// 客户昵称
    /// </summary>
    public static class FnpCustomerNickService
    {

        private static readonly string Key = AppSettingHelper.GetValue("MinIO:Key");
        private static readonly string IV = AppSettingHelper.GetValue("MinIO:Iv");
        private static readonly FnpCustomerNickRepository _fnpCustomerNickRepository = new();

        #region 查詢客戶暱稱
        /// <summary>
        /// 查詢客戶暱稱
        /// </summary>
        /// <returns></returns>
        public static ApiResultModelByObject SearchData()
        {
            ApiResultModelByObject apiResult = new();
            List<string> resulr = [];
            var list = DbAccess.GetAll<FnpCustomerNick>().OrderBy(o => o.HypocorismName).Where(w => !string.IsNullOrEmpty(w.HypocorismName.Trim()));
            if (!list.Any()) return apiResult;
            foreach (var group in list.GroupBy(b => b.HypocorismName[..1].ToLower()).OrderBy(o => o.Key))
            {
                resulr.Add(string.Join(", ", group.OrderBy(b => b.HypocorismName).Select(s => s.HypocorismName)));
            }
            var fnpCustomerNick = list.First();
            var psSubEeLglVwA = PsSubEeLglVwADataService.FindByKey(fnpCustomerNick.CreateUser);
            FnpCustomerNickModel model = new FnpCustomerNickModel()
            {
                GroupName = resulr,
                Name = list.Select(s => s.HypocorismName).ToList(),
                operate_euser = psSubEeLglVwA?.NameA,
                operate_cuser = psSubEeLglVwA?.Name,
                operate_time = fnpCustomerNick.CreateTime
            };
            apiResult.listData = model;
            apiResult.rtnSuccess = true;
            return apiResult;
        }
        #endregion

        #region 根據特定欄位獲取excel中對應的數值
        /// <summary>
        /// 根據特定欄位獲取excel中對應的數值
        /// </summary>
        /// <param name="suf">已上傳文件</param>
        /// <param name="specialCode">特定欄位</param>
        /// <param name="sheetName">sheet名稱</param>
        /// <returns></returns>
        public static async Task<Dictionary<string, List<string>>> GetNickNameBySpecialCode(SysUploadFile suf, string specialCode, string sheetName = "")
        {
            #region 定義返回值
            Dictionary<string, List<string>> dic = new Dictionary<string, List<string>>();
            dic.Add("success", new List<string>());//獲取到集合後的信息
            dic.Add("error", new List<string>());//錯誤信息
            List<string> errorMessage = new List<string>();//錯誤信息集合
            #endregion

            //是否獲取到特定欄位，已經改變就不會變動
            bool getSpecialField = false;

            #region 根據fileid獲取minio上的文件流
            //實例化
            var minio = new MinioClient()
                .WithEndpoint(AppSettingHelper.Configuration["MinIO:Endpoint"])
                .WithCredentials(AppSettingHelper.Configuration["MinIO:AccessKey"], AppSettingHelper.Configuration["MinIO:SecretKey"])
                .WithSSL()
                .Build();

            await minio.StatObjectAsync(new StatObjectArgs()
                .WithBucket(AppSettingHelper.Configuration["MinIO:Bucket"])
                .WithObject(suf.FilePath))
                .ConfigureAwait(false);

            //將文件轉換為流
            using (var memoryStream = new MemoryStream())
            {
                await minio.GetObjectAsync(new GetObjectArgs()
                   .WithBucket(AppSettingHelper.Configuration["MinIO:Bucket"])
                   .WithObject(suf.FilePath)
                   .WithCallbackStream((stream) =>
                   {
                       //暱稱集合
                       List<string> nikeName = new List<string>();
                       //操作excel工作台
                       IWorkbook workBook = null;

                       #region 根據文件類型將文件流轉換為表格
                       switch (suf.FileType.ToLower())
                       {
                           case "xlsx":
                               //獲取sheet中的數據
                               workBook = new XSSFWorkbook(stream);
                               break;
                           case "xls":
                               workBook = new HSSFWorkbook(stream);
                               break;
                       }
                       #endregion

                       #region 操作表格
                       if (workBook != null)
                       {
                           ISheet sheet = string.IsNullOrEmpty(sheetName) ? workBook.GetSheetAt(0) : workBook.GetSheet(sheetName);
                           //如果sheet不為空，進行作業
                           if (sheet != null)
                           {
                               //獲取特定欄位的列數
                               int cellNum = -1;
                               //是否獲取到特定欄位
                               bool isSelect = cellNum > -1;
                               //循環遍歷
                               for (int i = 0; i <= sheet.LastRowNum; i++)
                               {
                                   //在循環過程中是否獲取到特定欄位的列
                                   isSelect = cellNum > -1;
                                   //獲取當前行
                                   IRow row = sheet.GetRow(i);
                                   //如果當前行不為空，進行作業
                                   if (row != null)
                                   {
                                       //驗證是否為空行
                                       if (!row.Cells.All(cell => string.IsNullOrWhiteSpace(cell.ToString())))
                                       {
                                           #region cellNum == -1 查找剩餘行數中是否存在特定值
                                           if (cellNum == -1)
                                           {
                                               for (int j = 0; j < row.LastCellNum; j++)
                                               {
                                                   ICell cell = row.GetCell(j);
                                                   if (cell != null)
                                                   {
                                                       if (cell?.ToString().ToLower() == specialCode.ToLower())
                                                       {
                                                           //是否獲取到特定欄位 一旦修改，只有在整個sheet流程走完後才能重新賦值
                                                           getSpecialField = true;
                                                           //將特定欄位的列數賦值給當前定義參數，以便後面使用，每次重新獲取到就賦值為最新的值
                                                           cellNum = j;
                                                           //這裡賦值為false，避免走下一步去獲取表頭欄位的值
                                                           isSelect = false;
                                                           break;
                                                       }
                                                   }
                                               }
                                           }
                                           #endregion

                                           #region cellNum > -1 表示已經找到特定欄位，從後就需要進行獲取特定欄位的值
                                           if (cellNum > -1 && isSelect)
                                           {
                                               ICell cellSpecific = row.GetCell(cellNum);
                                               string cellValue = string.Empty;
                                               if (cellSpecific != null)
                                               {
                                                   #region 是否存在換行符 如果存在，這直接獲取最後一筆，反之則需要檢查單筆是否有刪除線
                                                   if (cellSpecific.CellStyle.WrapText)
                                                   {
                                                       //根據換行符進行分割數據
                                                       List<string> listName = cellSpecific?.ToString().Split('\n').ToList();
                                                       //如果數值為1 表示當前欄位是有換行符，且只有一個，需要進行驗證是否存在刪除線，存在刪除線，需要將賦值清空
                                                       if (listName.Count() == 1 && workBook.GetFontAt(cellSpecific.CellStyle.FontIndex).IsStrikeout)
                                                       {
                                                           cellValue = string.Empty;
                                                       }
                                                       else
                                                       {
                                                           //排除多行文本中的刪除文檔
                                                           cellValue = listName[listName.Count() - 1].ToString();
                                                       }
                                                   }
                                                   else
                                                   {
                                                       //如果不存在換行符，則需要驗證當前欄位是否存在刪除線
                                                       if (!workBook.GetFontAt(cellSpecific.CellStyle.FontIndex).IsStrikeout)
                                                       {
                                                           cellValue = cellSpecific.ToString();
                                                       }
                                                   }
                                                   #endregion

                                                   //正則去掉中文，並替換掉空白的括號
                                                   cellValue = Regex.Replace(cellValue, @"([\u4e00-\u9fa5])", "").Replace(@"()", "");
                                                   if (!string.IsNullOrEmpty(cellValue))
                                                   {
                                                       //將單引號替換為雙引號
                                                       cellValue = cellValue.Replace(@"'", @"''").Trim();
                                                       //驗證是否超過特定長度
                                                       if (cellValue.Length > 50)
                                                       {
                                                           nikeName.Clear();//清空
                                                           errorMessage.Add(string.Format(ActionFilter.GetMultilingualValue("custom:messageContent:nikeMaxWord"), cellValue, 50));
                                                           break;
                                                       }
                                                       else { nikeName.Add(cellValue); }
                                                   }
                                               }
                                           }
                                           #endregion
                                       }
                                       else
                                       {
                                           //空行，將cellNum賦值為初始值
                                           cellNum = -1;
                                           //跳過空行
                                           continue;
                                       }
                                   }
                               }
                           }
                       }
                       #endregion

                       //將獲取到的暱稱集合添加到字典中(不用區分大小寫的去處重複資料)
                       dic["success"] = nikeName.Select(s => s.Trim()).Distinct(StringComparer.InvariantCultureIgnoreCase).ToList();

                   })).ConfigureAwait(false);
                memoryStream.Position = 0;
            }
            #endregion

            //驗證是否獲取得到過特定欄位並且有獲取到參數
            if (!getSpecialField && dic["success"].Count() == 0)
            {
                errorMessage.Add(ActionFilter.GetMultilingualValue("custom:messageContent:nikeNameFileError"));
            }

            //將錯誤信息添加到字典中
            dic["error"] = errorMessage;

            return dic;
        }
        #endregion

        #region 新增客戶暱稱
        /// <summary>
        /// 新增客戶暱稱
        /// </summary>
        /// <param name="nikeName"></param>
        /// <param name="emplid"></param>
        /// <returns></returns>
        public static bool InsertNikeName(List<string> nikeName, string emplid)
        {
            return _fnpCustomerNickRepository.InsertNikeName(nikeName, emplid);
        }
        #endregion

        #region 下载范本
        /// <summary>
        /// 下载范本
        /// </summary>
        /// <returns></returns>
        internal static async Task<ApiResultModelByObject> DownloadTemplate(MinioClient client)
        {
            ApiResultModelByObject apiResult = new();
            try
            {
                string filePath = $"CusNickname/{AppSettingHelper.Configuration["MinIO:OEMFileTemplate"]}";
                string presignedUrl = string.Empty;
                int expiryInSeconds = 604800;
                var args = new PresignedGetObjectArgs()
                    .WithBucket(AppSettingHelper.Configuration["MinIO:Bucket"])
                    .WithObject(filePath)
                    .WithExpiry(expiryInSeconds);
                presignedUrl = await client.PresignedGetObjectAsync(args);
                apiResult.listData = AesService.Encrypt(presignedUrl, Key, IV);
                apiResult.rtnSuccess = true;
                return apiResult;
            }
            catch (Exception)
            {
                return new()
                {
                    rtnSuccess = false,
                    messageType = MessageTypeUtils.Warning.ToString(),
                    messageContent = new List<string>() { ActionFilter.GetMultilingualValue("custom:messageContent:noTemplateFile"), AppSettingHelper.Configuration["MinIO:OEMFileTemplate"] ?? "" }
                };
            }
        }
        #endregion
    }
}
