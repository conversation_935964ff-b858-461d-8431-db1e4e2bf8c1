﻿using System.Reflection;
using Autofac;
using Autofac.Extensions.DependencyInjection;

namespace Elegal.Flow.Api
{
    /// <summary>
    /// flowAPI  Program
    /// </summary>
    public static class Program
    {
        /// <summary>
        /// Main
        /// </summary>
        public static void Main(string[] args)
        {
            var host = CreateHostBuilder(args).Build();

            var lifetime = host.Services.GetRequiredService<IHostApplicationLifetime>();
            lifetime.ApplicationStopping.Register(() =>
            {
                Console.WriteLine("Application is stopping. Perform cleanup here.");
                //添加你的程式清理碼
            });

            host.Run();
        }

        /// <summary>
        /// CreateHostBuilder
        /// </summary>
        public static IHostBuilder CreateHostBuilder(string[] args) =>
            Host.CreateDefaultBuilder(args)
                .ConfigureWebHostDefaults(webBuilder =>
                {
                    webBuilder.ConfigureKestrel(serverOptions =>
                        {
                            serverOptions.Limits.MaxRequestBodySize = 80 * 1024 * 1024; // 设置最大请求体大小为100MB
                        })
                        .UseStartup<Startup>();
                }) // 使用Autofac
                .UseServiceProviderFactory(new AutofacServiceProviderFactory())
                .ConfigureContainer<ContainerBuilder>(builder =>
                {
                    // 假设你的 FormApplyRefactorService 位于 Elegal.Flow.Common 程序集
                    // 获取包含 IFormApplyRefactorService 接口的程序集
                    // 可以通过接口或实现类来获取，这里我们用接口
                    var commonAssembly = Assembly.Load("Elegal.Flow.Common");

                    // 注册 Elegal.Flow.Api 程序集中的类型
                    var apiAssembly = Assembly.GetExecutingAssembly();
                    builder.RegisterAssemblyTypes(apiAssembly)
                        .Where(t => t.Name.Contains("Refactor"))
                        .AsSelf()
                        .InstancePerLifetimeScope();

                    // 注册 Elegal.Flow.Common 程序集中的类型
                    builder.RegisterAssemblyTypes(commonAssembly)
                        .Where(t => t.Name.Contains("Refactor")) // 假设 FormApplyRefactorService 的名称中包含 "Refactor"
                        .AsSelf()
                        .InstancePerLifetimeScope();
                    
                    // 注册 Elegal.Interface.Api.Common 程序集中的类型
                    var commonInterfaceAssembly = Assembly.Load("Elegal.Interface.Api.Common");
                    builder.RegisterAssemblyTypes(commonInterfaceAssembly)
                        .Where(t => t.Name.Contains("Refactor")) // 假设 FormApplyRefactorService 的名称中包含 "Refactor"
                        .AsSelf()
                        .InstancePerLifetimeScope();
                    
                });
    }
}
