﻿using Elegal.Flow.Api.Services.Application;
using Elegal.Flow.Common.Repository.GuanEnterpriseApply;
using Elegal.Interface.Api.Common.Control.Filter;
using Elegal.Interface.Api.Common.Model.ViewModel.FlowApi.GuanEnterpriseApply;

namespace Elegal.Flow.Api.Services.ApplicationPrint
{
    /// <summary>
    /// 關企建檔列印
    /// </summary>
    public static class GuanEnterpriseApplyPrintService
    {
        private static readonly GuanEnterpriseApplyRepository _guanEnterpriseApplyRepository = new GuanEnterpriseApplyRepository();

        #region 獲取資料建檔申請單資訊(列印)
        /// <summary>
        /// 獲取資料建檔申請單資訊(列印)
        /// </summary>
        /// <param name="applyNumber"></param>
        /// <returns></returns>
        public static GuanEnterpriseApplyPrintModel GetGuanEnterpriseApplicationByPrint(string applyNumber)
        {
            if (string.IsNullOrEmpty(applyNumber)) throw new Exception(ActionFilter.GetMultilingualValue("custom:messageTitle:dataNotexist"));
            GuanEnterpriseApplyPrintModel ggapm = new GuanEnterpriseApplyPrintModel();
            //主表資訊
            ggapm.guanEnterpriseApply = _guanEnterpriseApplyRepository.GetGuanEnterpriseApplyInfoByPrint(applyNumber, MvcContext.UserInfo.logging_locale);
            //財務資訊
            ggapm.guanEnterpriseApplyFinance = _guanEnterpriseApplyRepository.GetGuanEnterpriseApplicationFinanceByPrint(applyNumber, MvcContext.UserInfo.logging_locale);
            //合約管理作業
            ggapm.contractValidityModel = EnterpriseOriginalArchiveService.GetBaseInformation(applyNumber);
            return ggapm;
        }
        #endregion
    }
}
