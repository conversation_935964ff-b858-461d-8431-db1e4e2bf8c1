﻿using Elegal.Flow.Api.Repository.Application;
using Elegal.Flow.Common.Repository.FlowStep;
using Elegal.Flow.Common.Services;
using Elegal.Flow.Common.Services.GuanEnterpriseApply;
using Elegal.Interface.Api.Common.Control.Filter;
using Elegal.Interface.Api.Common.FuncHelper;
using Elegal.Interface.Api.Common.FuncService;
using Elegal.Interface.Api.Common.Model.Enum;
using Elegal.Interface.Api.Common.Model.ResultModel;
using Elegal.Interface.Api.Common.Model.ViewModel.FlowApi.Application;
using Elegal.Interface.ApiData.Service.FuncService;
using Elegal.Interface.ApiData.Service.Model.Condition;
using Elegal.Interface.ApiData.Service.Model.DbModel;
using Elegal.Interface.ApiData.Service.Model.QueryCondition;
using Elegal.Orm;
using Elegal.Orm.Dtos;
using Elegal.Orm.Utils;
using System.Data;
#nullable disable
namespace Elegal.Flow.Api.Services.Application
{
    /// <summary>
    /// 關企建檔 -> 合約管理作業
    /// </summary>
    public static class EnterpriseOriginalArchiveService
    {
        private static EnterpriseOriginalArchiveRepository _repository = new EnterpriseOriginalArchiveRepository();
        private static readonly ContractNumberRepository _contractNumberRepository = new ContractNumberRepository();
        private static readonly FlowStepRepository _flowRepository = new FlowStepRepository();
        //關企建檔固定流程為12
        private static readonly int flowId = 12;

        #region 基本資訊查詢
        /// <summary>
        /// 基本資訊查詢
        /// </summary>
        /// <param name="applyNumber"></param>
        /// <returns></returns>
        public static EnterpriseContractViewModel GetBaseInformation(string applyNumber)
        {
            EnterpriseApplication enterpriseApplication = EnterpriseApplicationDataService.FindByKey(applyNumber);
            OriginalArchiveRecord originalArchiveRecord = OriginalArchiveRecordDataService.FindByKey(applyNumber);
            PaperApplicationData paperApplicationData = PaperApplicationDataDataService.Find(new PaperApplicationDataCondition() { ApplyNumber = applyNumber });
            var sysParams = SysParametersService.GetParameters(["dateOptions", "effDateOptions"]);
            var flowStepSigners = DbAccess.Query<FlowStepSigner>(new FlowStepSignerCondition { ApplyNumber = applyNumber });
            //当关签核人员
            var users = new List<PsSubEeLglVwA>();
            if (flowStepSigners.Length > 0)
                users = DbAccess.Query<PsSubEeLglVwA>(new PsSubEeLglVwAQueryCondition()
                {
                    SearchItemGroup = new SearchItemGroup()
                    {
                        Items = new List<SearchItem>()
                    {
                        new SearchItem()
                        {
                            Compare = CompareOperator.ARRAYIN,
                            Field = "emplid",
                            Values =  flowStepSigners.Select(s=>s.SignerEmplid)
                        }
                    }
                    }
                }).ToList();
            var applicationUsers = PsSubEeLglVwADataService.Query(new PsSubEeLglVwAQueryCondition()
            {
                SearchItemGroup = new SearchItemGroup()
                {
                    Items = [
                        new()
                        {
                            Compare = CompareOperator.ARRAYIN,
                            Logic = LogicOperator.And,
                            Field = "emplid",
                            Values = [enterpriseApplication?.FillEmplid, enterpriseApplication?.PicEmplid,enterpriseApplication?.LegalAffairsEmplid]
                        }
                    ]
                }
            });
            var fill = applicationUsers.FirstOrDefault(f => f.Emplid.Equals(enterpriseApplication?.FillEmplid));//填单人
            var handler = applicationUsers.FirstOrDefault(f => f.Emplid.Equals(enterpriseApplication?.PicEmplid));//经办人
            var legal = applicationUsers.FirstOrDefault(f => f.Emplid.Equals(enterpriseApplication?.LegalAffairsEmplid));//承办法务
            EnterpriseContractViewModel result = new()
            {
                ApplyNumber = applyNumber,
                ApplyDate = enterpriseApplication?.ApplyTime,
                HasPaper = paperApplicationData?.HavingPaper.ToString(),
                HasPaperDetail = paperApplicationData?.IsPaperDetail.ToString(),
                MyEntity = enterpriseApplication?.EntityId,
                MyEntityName = DbAccess.FindByKey<AffiliateCompany, string>(enterpriseApplication?.EntityId)?.AffCompanyAbb,
                PartyA = enterpriseApplication?.OtherParty,
                ContractName = enterpriseApplication?.ContractName,
                ContractNumber = enterpriseApplication?.ContractNumber,
                PicDeptid = enterpriseApplication?.PicDeptid,
                FormType = enterpriseApplication?.FormType,
                EnterpArchivieType = enterpriseApplication?.EnterpArchivieType,//關企建檔類型
                ConfidenLevel = enterpriseApplication?.ConfidenLevel,
                FillEmplid = enterpriseApplication?.FillEmplid,
                FillCname = fill?.Name,
                FillEname = fill?.NameA,
                FillMail = fill?.EmailAddressA,
                fillTermination = fill?.Termination,//CR：385
                HandlerEmplid = enterpriseApplication?.PicEmplid,
                HandlerCname = handler?.Name,
                HandlerEname = handler?.NameA,
                HandlerMail = enterpriseApplication?.PicEmail,
                handlerTermination = handler?.Termination,//CR：385
                LegalEmplid = enterpriseApplication?.LegalAffairsEmplid,
                LegalCname = enterpriseApplication.IsOldSystem==1? enterpriseApplication.OldLegalEmplid: legal?.Name ,
                LegalEname = enterpriseApplication.IsOldSystem == 1 ? enterpriseApplication.OldLegalEmplid: legal?.NameA,
                legalTermination = legal?.Termination,//CR：385
                ApplicationState = enterpriseApplication?.ApplicationState,
                ConfirmEffDate = enterpriseApplication?.EffDate,
                EffDate = enterpriseApplication?.EffDate,
                ConfirmEffDateType = enterpriseApplication?.EffType,
                ConfirmEffDateTypeName = sysParams.FirstOrDefault(f => f.ParaCode.Equals("effDateOptions") && f.FuncCode.Equals(enterpriseApplication?.EffType))?.FunName,
                OtherEffDate = enterpriseApplication?.OtherEffDate,
                ConfirmSignDate = enterpriseApplication?.SignDate,
                SignDate = enterpriseApplication?.SignDate,
                ConfirmSignDateType = enterpriseApplication?.SignType,
                ConfirmSignDateTypeName = sysParams.FirstOrDefault(f => f.ParaCode.Equals("dateOptions") && f.FuncCode.Equals(enterpriseApplication?.SignType))?.FunName,
                OtherSignDate = enterpriseApplication?.OtherSignDate,
                ConfirmExpDate = enterpriseApplication?.ExpDate,
                ExpDate = enterpriseApplication?.ExpDate,
                ConfirmExpDateType = enterpriseApplication?.ExpType,
                ConfirmExpDateTypeName = sysParams.FirstOrDefault(f => f.ParaCode.Equals("dateOptions") && f.FuncCode.Equals(enterpriseApplication?.ExpType))?.FunName,
                HasExpExtend = enterpriseApplication?.HasExpExtend,
                OtherExpDate = enterpriseApplication?.OtherExpDate,
                MainContractNumber = enterpriseApplication?.MainContractNumber,
                GroupContractNumber = enterpriseApplication?.GroupContractNumber,
                RefNumber = enterpriseApplication?.RefNumber,
                StepId = flowStepSigners.FirstOrDefault()?.StepId.ToString(),
                ShouldSigners = string.Join(", ", users.Select(u => u.NameA)),
                PaperBasicDatas = paperApplicationData == null ? [] : PaperBasicDataDataService.Query(new PaperBasicDataQueryCondition() { PaperApplicaId = paperApplicationData?.ApplicationId }).Select(CommonUtil.Map<PaperBasicData, Interface.ApiData.Service.Model.ViewModel.PaperBasicDataViewModel>).ToList(),
                SysUploadFiles = GetPaperFile(applyNumber),
                FlowContractHistorys = ContractOriginalArchiveService.GetContractHistoryData(applyNumber),
                ArchiveRecordsResults = new ContractOriginalArchiveRepository().ArchiveRecords(applyNumber).ToList(),
                //0407 確認到期日 是否延展
                confirmHasExpExtend = enterpriseApplication.HasExpExtend,
            };
            if (originalArchiveRecord != null)
            {
                result.OriginArchiveRemark = originalArchiveRecord?.OriginArchiveRemark;
                result.OriginArchiveType = originalArchiveRecord?.OriginArchiveType;
                result.OriginArchiveTime = originalArchiveRecord.OriginArchiveDate;
                result.confirmHasExpExtend = originalArchiveRecord?.ConfirmHasExpExtend;
                result.ConfirmEffDate = originalArchiveRecord?.ConfirmEffDate;
                result.ConfirmSignDate = originalArchiveRecord?.ConfirmSignDate;
                result.ConfirmExpDate = originalArchiveRecord?.ConfirmExpDate;
                result.isRealDate = true;
            }
            result.EnterpriseContractNumber = EnterpriseContractNumberDataService.FindByKey(applyNumber) ?? default;
            #region 效期确认
            //var obj = _repository.GetContractValidityInformationRecord(applyNumber);
            #endregion
            #region 其他单绑定时提示信息
            if (enterpriseApplication.ApplicationState.Equals("F"))
                result.OtherNotice = new ContractOriginalArchiveRepository().GetOtherNotice(applyNumber);
            #endregion
            TimeZoneInfoConvertHelper.ConvertObjectDateByUtc(result);
            return result;
        }
        #endregion
        /// <summary>
        /// 提交归档资讯
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public static ApiResultModelByObject SetOriginalArchive(EnterpriseContractViewModel model)
        {
            ApiResultModelByObject result = new ApiResultModelByObject();
            DbAccess.PerformInTransaction(dbContext =>
            {
                result.rtnSuccess = GuanEnterpriseApplyService.HandleEnterpriseOriginalArchiveAndPaperToTransaction(dbContext, model);
            });

            //是否可發送郵件
            if (result.rtnSuccess)
            {
                int step = _flowRepository.GetContractMaTaskStepid(flowId);
                SendMailService.SendMail("A", model.ApplyNumber, MvcContext.UserInfo.current_emp, step, [], new List<MailTypeUtils> { MailTypeUtils.F5 });
            }

            ActionFilter.InitLogRecord();
            result.rtnSuccess = true;
            return result;
        }

        #region 獲取紙本資訊附檔
        /// <summary>
        /// 獲取紙本資訊附檔
        /// </summary>
        /// <param name="applyNumber"></param>
        /// <returns></returns>
        public static List<SysUploadFile> GetPaperFile(string applyNumber)
        {
            return [.. SysUploadFileDataService.Query(new SysUploadFileQueryCondition()
            {
                UploadType = 6,
                UploadKey = applyNumber,
                ArchivePurposes = 3,
                IsWatermake = (int)YesOrNoUtils.No
            })];
        }
        #endregion
    }
}
