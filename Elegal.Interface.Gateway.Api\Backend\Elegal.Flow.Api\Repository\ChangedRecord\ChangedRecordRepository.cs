﻿using CommunityToolkit.HighPerformance.Helpers;
using Elegal.Interface.Api.Common.Control.Filter;
using Elegal.Interface.Api.Common.Model.ParaModel.FlowApi.ChangedRecord;
using Elegal.Interface.Api.Common.Model.ResultModel.FlowApi.ChangedRecord;
using Elegal.Interface.Api.Common.Repository;
using Elegal.Orm;
using Elegal.Orm.Dtos;
using Minio.DataModel;
using Quartz.Util;
using System.Text;

namespace Elegal.Flow.Api.Repository.ChangedRecord;

/// <summary>
/// 異動案件查詢
/// </summary>
public class ChangedRecordRepository : BaseRepository
{
    /// <summary>
    /// 返回分頁後的數據
    /// </summary>
    /// <param name="serachPara"></param>
    /// <returns></returns>
    public ChangePageResult<ChangeRecordResultDBmodel> GetPageDBResult(QryChangeRecordPara serachPara, string langType = "ZH-TW")
    {
        //保存sql 參數信息
        Dictionary<string, object> sqlPara = new Dictionary<string, object>();

        #region  
        StringBuilder serachSQL = new StringBuilder();
        //申請單號
        if (!string.IsNullOrWhiteSpace(serachPara.work_key))
        {
            serachSQL.AppendLine("AND scr.work_key LIKE  @work_key ");
            sqlPara.Add("work_key", "%" + serachPara.work_key + "%");
        }

        //異動時間
        if (serachPara.startTime.HasValue)
        {
            serachSQL.AppendLine("AND scr.create_time >=  @startTime ");
            sqlPara.Add("startTime", serachPara.startTime.Value);
        }
        //異動時間
        if (serachPara.endTime.HasValue)
        {
            serachSQL.AppendLine("AND scr.create_time <=  @endTime ");
            sqlPara.Add("endTime", serachPara.endTime);
        }

        //異動員工(查詢工號與姓名)  //改為前端組件搜索，後端接收就是完整的 empid
        if (!string.IsNullOrWhiteSpace(serachPara.empid))
        {

            serachSQL.AppendLine("AND scr.empid = @empid");
            sqlPara.Add("empid", serachPara.empid);
        }

        //申請類型
        if (serachPara.change_types.Count > 0)
        {
            int i = 0;
            string change_types = string.Join(",", serachPara.change_types.Select(e =>
            {
                i++;
                sqlPara.Add("change_type_" + i.ToString(), e.Key);
                return "@change_type_" + i.ToString();
            })
                );
            serachSQL.AppendLine(@$" AND  scr.change_type in ({change_types})");
        }
        //主體檢視權限

        serachSQL.AppendLine($@"AND exists(select 1  from  V_GetAllApplication vg --是否存在满足条件
                where
	                vg.apply_number = scr.work_key -- 当前单据
                    and 
	                (vg.apply_type = N'O' --首先O單沒有主體檢視權限
	                    or
	                    EXISTS (
	                        select 1 from V_GetRoleExtremely refr
		                    where refr.r_id in ({string.Join(",",MvcContext.UserInfo.current_roles)})  --當前用戶的角色 
                               and refr.r_id in (select r_id from   p_role_function_action pra  --當前菜單(異動案件查詢-檢視)的角色
		                      								where f_id ='306' 
		                      								and 1 in (select value from STRING_SPLIT(action_id,',')) ) 
		                      and (refr.selecttype = 0  or refr.entity_id=vg.entity_id) --主體，如果 fcv.selecttype = 0 表示全部主體 ,否則需要匹配單據主體
		                      and (vg.confiden_level!='01'  or (vg.confiden_level = N'01' and refr.fnextremely=N'1') ) --如果不是幾機密，或者（是級機密並且有權限）
	                    )
	                )
                )
	    ");


        //是否包含附件
        if (serachPara.isModifyAnnexs.Count > 0)
        {
            int i = 0;
            string isModifyAnnexs = string.Join(",", serachPara.isModifyAnnexs.Select(e =>
            {
                i++;
                sqlPara.Add("isModifyAnnexs_" + i.ToString(), e == "Y" ? true : false);
                return "@isModifyAnnexs_" + i.ToString();
            })
                );
            serachSQL.AppendLine(@$" AND  scr.is_modify_annex in ({isModifyAnnexs})");
        }
        string serachSQLstr = serachSQL.ToString();
        #endregion

        //對 group by 之後的 的結果進行count計算
        string countsql = @$"
                            select count(1) from 
								(
									select 
										scr.batch_id
										,scr.work_key  --申請單號
										,scr.change_type --申請類型
										,scr.change_next_type -- 申請類型二級
										,scr.empid --異動工號
										,scr.name --異動姓名
										,scr.is_modify_annex --是否修改附件
										,min(scr.create_time) as  create_time --異動日期
									from sys_change_records scr
									WHERE 1=1 
										{serachSQLstr}
									group by scr.batch_id
										,scr.work_key 
										,scr.change_type
										,scr.change_next_type
										,scr.empid
										,scr.is_modify_annex
										,scr.name 
								) as groupT
                            ";

        #region 獲取當前頁碼內的數據
        //用來獲取當前頁碼內的數據

        Func<string, string> getOrderStr = (tableName) =>
        {
            string orderStr = @$"order by {tableName}.create_time desc";
            if (serachPara.order != null && !string.IsNullOrWhiteSpace(serachPara.order?.Field))
            {
                if (serachPara.order.Field == "change_type") //申请类型字段做特殊处理
                {
                    orderStr = @$"order by {tableName}.change_type  {serachPara.order.Order.ToString()} ,  {tableName}.change_next_type {serachPara.order.Order.ToString()}";
                }
                else
                {
                    orderStr = @$"order by {tableName}.{serachPara.order.Field}    {serachPara.order.Order.ToString()}";
                }

            }
            return orderStr;

        };

        string pageStr = "OFFSET 0 ROW FETCH NEXT 25 ROW ONLY";
        if (serachPara.page != null)
        {
            pageStr = @$"OFFSET  {(serachPara.page.PageIndex - 1) * serachPara.page.PageSize} ROW FETCH NEXT {serachPara.page.PageSize} ROW ONLY";
        }

        string sql = $@"
									with pageData(batch_id,work_key) --當前頁碼內的數據，根據批次ID與單號共同確定唯一 (其實單獨batch_id 就可以，但是由於造的測試數據混亂導致加上單號來確定)
									as
									(
										select groupT.batch_id,groupT.work_key  from 
										(
											select 
												scr.batch_id
												,scr.work_key 
												,scr.change_type
												,scr.change_next_type
												,scr.empid
												,scr.name --異動姓名
												,scr.is_modify_annex
												,min(scr.create_time) as  create_time
											from sys_change_records scr
											WHERE 1=1
											{serachSQLstr} 
											group by scr.batch_id --分組
												,scr.work_key 
												,scr.change_type
												,scr.change_next_type
												,scr.empid
												,scr.name --異動姓名
												,scr.is_modify_annex
										) as groupT
										{getOrderStr("groupT")}
										{pageStr}
									)
									select 
										 scr.work_key --申請單號
										,scr.batch_id --批次單號
										,scr.change_type--申請類型一級
										,apt.fun_name AS change_type_name--申請類型一級中文名稱
										,change_next_type--申請類型二級
										,(
											SELECT top 1 fun_name 
											FROM dbo.sys_parameters 
											WHERE para_code = CONCAT(N'formType_',scr.change_type) 
                                            AND func_code = scr.change_next_type 
											AND lang_type = N'{langType}'
										) AS change_next_type_name--申請類型二級中文名稱
										,scr.create_time  --異動日期
										,scr.empid --異動工號
										,(pselva.emplid+'/ '+ pselva.name_a ) as  name --異動姓名
										,scr.is_modify_annex --是否修改附件
										,scr.change_code  --異動欄位
										,scr.change_code_ename 
										,scr.change_code_cname 
										,dbo.F_ConputeChangeContent(scr.content_before,scr.change_code_type,scr.change_code_source,N'{langType}') AS content_before--異動前內容
										,dbo.F_ConputeChangeContent(scr.content_after,scr.change_code_type,scr.change_code_source,N'{langType}')  AS content_after--異動後內容
										,scr.children_code   
										,scr.children_code_ename
										,scr.children_code_cname
										,scr.change_code_type
									from sys_change_records  scr
									LEFT JOIN (SELECT func_code,fun_name FROM dbo.sys_parameters WHERE para_code = N'applicationType' AND lang_type = N'{langType}') 
											  AS apt ON apt.func_code = scr.change_type
									left join ps_sub_ee_lgl_vw_a pselva on scr.empid =pselva.emplid 	
									where EXISTS(select 1 from pageData pd where pd.batch_id=scr.batch_id and pd.work_key=scr.work_key) --只查詢本頁碼內的數據
									{getOrderStr("scr")}   --对页码内的数据进行排序
									;
                            ";
        #endregion


        //獲取導出excel時候的數據條數，跟web頁面上顯示不一樣
        string excelCountSql = @$"select 
												count(1)
											from sys_change_records scr
											WHERE 1=1
											{serachSQLstr} ";



        ChangePageResult<ChangeRecordResultDBmodel> res = new ChangePageResult<ChangeRecordResultDBmodel>();
        res.TotalRows = DbAccess.Database.SqlQuery<int>(countsql, sqlPara).FirstOrDefault();
        res.Data = DbAccess.Database.SqlQuery<ChangeRecordResultDBmodel>(sql, sqlPara).ToList();
        res.ExportExcelCount = DbAccess.Database.SqlQuery<int>(excelCountSql, sqlPara).FirstOrDefault();

        return res;
    }





    /// <summary>
    /// 返回导出需要的数据
    /// </summary>
    /// <param name="serachPara"></param>
    /// <returns></returns>
    public List<ChangeRecordResultDBmodel> GetDBResultForExport(QryChangeRecordExportPara serachPara, string langType = "ZH-TW")
    {
        //保存sql 參數信息
        Dictionary<string, object> sqlPara = new Dictionary<string, object>();

        #region 
        StringBuilder serachSQL = new StringBuilder();
        //申請單號
        if (!string.IsNullOrWhiteSpace(serachPara.work_key))
        {
            serachSQL.AppendLine("AND scr.work_key LIKE  @work_key ");
            sqlPara.Add("work_key", "%" + serachPara.work_key + "%");
        }

        //異動時間
        if (serachPara.startTime.HasValue)
        {
            serachSQL.AppendLine("AND scr.create_time >=  @startTime ");
            sqlPara.Add("startTime", serachPara.startTime.Value);
        }
        //異動時間
        if (serachPara.endTime.HasValue)
        {
            serachSQL.AppendLine("AND scr.create_time <=  @endTime ");
            sqlPara.Add("endTime", serachPara.endTime);
        }

        //異動員工(查詢工號與姓名)
        if (!string.IsNullOrWhiteSpace(serachPara.empid))
        {

            serachSQL.AppendLine("AND (scr.empid like  @empid or scr.name like  @empid  )");
            sqlPara.Add("empid", "%" + serachPara.empid + "%");
        }

        //申請類型
        if (serachPara.change_types.Count > 0)
        {
            int i = 0;
            string change_types = string.Join(",", serachPara.change_types.Select(e =>
            {
                i++;
                sqlPara.Add("change_type_" + i.ToString(), e.Key);
                return "@change_type_" + i.ToString();
            })
                );
            serachSQL.AppendLine(@$" AND  scr.change_type in ({change_types})");
        }

        //是否包含附件
        if (serachPara.isModifyAnnexs.Count > 0)
        {
            int i = 0;
            string isModifyAnnexs = string.Join(",", serachPara.isModifyAnnexs.Select(e =>
            {
                i++;
                sqlPara.Add("isModifyAnnexs_" + i.ToString(), e == "Y" ? 1 : 0);
                return "@isModifyAnnexs_" + i.ToString();
            })
                );
            serachSQL.AppendLine(@$" AND  scr.is_modify_annex in ({isModifyAnnexs})");
        }


        //主體檢視權限
        serachSQL.AppendLine($@"AND exists(select 1  from  V_GetAllApplication vg --是否存在满足条件
                where
	                vg.apply_number = scr.work_key -- 当前单据
                    and 
	                (vg.apply_type = N'O' --首先O單沒有主體檢視權限
	                    or
	                    EXISTS (
	                        select 1 from V_GetRoleExtremely refr
		                    where refr.r_id in ({string.Join(",", MvcContext.UserInfo.current_roles)})  --當前用戶的角色
                              and refr.r_id in (select r_id from   p_role_function_action pra  --當前菜單(異動案件查詢-檢視)的角色
		                      								where f_id ='306' 
		                      								and 1 in (select value from STRING_SPLIT(action_id,',')) ) 
		                      and (refr.selecttype = 0  or refr.entity_id=vg.entity_id) --主體，如果 fcv.selecttype = 0 表示全部主體 ,否則需要匹配單據主體
		                      and (vg.confiden_level!='01'  or (vg.confiden_level = N'01' and refr.fnextremely=N'1') ) --如果不是幾機密，或者（是級機密並且有權限）
	                    )
	                )
                )
	    ");
        string serachSQLstr = serachSQL.ToString();
        #endregion



        #region 搜索

        Func<string, string> getOrderStr = (tableName) =>
        {
            string orderStr = @$"order by {tableName}.create_time desc";
            if (serachPara.order != null && !string.IsNullOrWhiteSpace(serachPara.order?.Field))
            {
                orderStr = @$"order by {tableName}.{serachPara.order.Field}    {serachPara.order.Order.ToString()}";
            }
            return orderStr;

        };


        string sql = $@"
									with WhereData(changeid) --（搜索结果）
									as
									(
											select 
												scr.changeid -- 表主键
											from sys_change_records scr
											WHERE 1=1
											{serachSQLstr} 
									)
									select 
										 scr.work_key --申請單號
										,scr.batch_id --批次單號
										,scr.create_time  --異動日期
										,scr.empid --異動工號
                                        ,scr.change_type--申請類型一級
										,apt.fun_name AS change_type_name--申請類型一級中文名稱
										,change_next_type--申請類型二級
										,(
											SELECT top 1 fun_name 
											FROM dbo.sys_parameters 
											WHERE para_code = CONCAT(N'formType_',scr.change_type) 
                                            AND func_code = scr.change_next_type 
											AND lang_type = N'{langType}'
										) AS change_next_type_name--申請類型二級中文名稱
										,(pselva.emplid+'/ '+ pselva.name_a ) as  name --異動姓名
										,scr.is_modify_annex as is_modify_annex  --是否修改附件
										,scr.change_code  --異動欄位
										,scr.change_code_ename 
										,scr.change_code_cname 
										,dbo.F_ConputeChangeContent(scr.content_before,scr.change_code_type,scr.change_code_source,N'{langType}') AS content_before--異動前內容
										,dbo.F_ConputeChangeContent(scr.content_after,scr.change_code_type,scr.change_code_source,N'{langType}')  AS content_after--異動後內容
										,scr.children_code   
										,scr.children_code_ename
										,scr.children_code_cname
										,scr.change_code_type
									from sys_change_records  scr
                                    LEFT JOIN (SELECT func_code,fun_name FROM dbo.sys_parameters WHERE para_code = N'applicationType' AND lang_type = N'{langType}') 
											                                      AS apt ON apt.func_code = scr.change_type
									left join ps_sub_ee_lgl_vw_a pselva on scr.empid =pselva.emplid 	
									where EXISTS(select 1 from WhereData wd where wd.changeid=scr.changeid ) --只返回搜索结果的数据
									{getOrderStr("scr")}   --对数据进行排序
									;
                            ";
        #endregion
        var res = DbAccess.Database.SqlQuery<ChangeRecordResultDBmodel>(sql, sqlPara).ToList();

        return res;
    }
}
