﻿using Elegal.Interface.Api.Common.Model.ParaModel.FlowApi.LendAppliction;
using Elegal.Interface.Api.Common.Model.ResultModel.FlowApi.LendAppliction;
using Elegal.Interface.Api.Common.Repository;
using Elegal.Orm;
using System.Text;

namespace Elegal.Flow.Api.Repository.LendAppliction;

/// <summary>
/// 資料查詢 -> 我的借出 -> 歷史借出
/// </summary>
public class LendSearchHisForUserRepository : BaseRepository
{
    /// <summary>
    /// 實例化
    /// </summary>
    public LendSearchHisForUserRepository() : base() { }

    #region 查詢用戶的歷史借出
    /// <summary>
    /// 查詢用戶的歷史借出
    /// </summary>
    /// <param name="para"></param>
    /// <param name="currentEmplid"></param>
    /// <param name="langType"></param>
    /// <param name="actFunc"></param>
    /// <returns></returns>
    public List<LendUserHis> QueryLendHisForUser(LendHisForUserPara para, string currentEmplid, string langType = "ZH-TW", Action<LendUserHis> actFunc = null)
    {
        Dictionary<string, object> sqlPara = new Dictionary<string, object>();

        #region 拼接查詢條件
        StringBuilder searchSql = new StringBuilder();

        searchSql.AppendLine(@" AND lend_handler_emplid = @currentEmplid");
        sqlPara.Add("currentEmplid", currentEmplid);

        //紙本/合約編號
        if (!string.IsNullOrWhiteSpace(para.ContractNumber))
        {
            searchSql.AppendLine(@" AND (paper_code LIKE CONCAT(N'%', @ContractNumber, N'%')
                                            OR fa.contract_number LIKE CONCAT(N'%', @ContractNumber, N'%')
                                           )"
                                 );
            sqlPara.Add("ContractNumber", para.ContractNumber);
        }

        //紙本/合約名稱
        if (!string.IsNullOrWhiteSpace(para.ContractName))
        {
            searchSql.AppendLine(@" AND (paper_name LIKE CONCAT(N'%', @ContractName, N'%')
                                            OR fa.contract_name LIKE CONCAT(N'%', @ContractName, N'%')
                                            )");
            sqlPara.Add("ContractName", para.ContractName);
        }

        //取件日期
        if (para.PickupTime.Start.HasValue)
        {
            searchSql.AppendLine(@" AND pickup_time>= @PickupTimeBegin");
            sqlPara.Add("PickupTimeBegin", para.PickupTime.Start.Value);
        }
        if (para.PickupTime.End.HasValue)
        {
            searchSql.AppendLine(@" AND pickup_time< @PickupTimeEnd");
            sqlPara.Add("PickupTimeEnd", para.PickupTime.End.Value.AddDays(1));
        }

        //實際歸還日期  edit by SpringJiang 20241125 歷史案件取實際歸還日
        searchSql.AppendLine(@$"AND {para.ReturnTime.ToSqlString("actual_return_time", "actual_return_time", sqlPara)}");

        //借出/調閱單號
        if (!string.IsNullOrWhiteSpace(para.LendNumber))
        {
            searchSql.AppendLine("AND (lend_number LIKE @LendNumber OR retrieve_number LIKE @LendNumber)");
            sqlPara.Add("LendNumber", "%" + para.LendNumber + "%");
        }

        //申請單狀態
        if (para.LendStatus.Count > 0)
        {
            int i = 0;
            string sqlLendStatusPara = string.Join(" OR ", para.LendStatus.Select(e =>
            {
                i++;
                sqlPara.Add("LendStatu_" + i.ToString(), e);
                return "lend_status = @LendStatu_" + i.ToString();
            })
                                                    );
            searchSql.AppendLine(@$"AND ({sqlLendStatusPara}) ");
        }
        #endregion

        string sql = @$" SELECT 
                                DISTINCT
                                lend_number as LendNumber,--借出單號
                                retrieve_number as RetrieveNumber,--調閱單號
                                application_time as ApplicationtTime,--申請日期
                                (SELECT COUNT(history_id) FROM paper_lending_history AS cplh1 WHERE cplh1.lend_number = plh.lend_number and is_pickup_lend=1) AS LendCount,--共多少紙本
								(SELECT COUNT(history_id) FROM paper_lending_history as cplh  WHERE cplh.lend_number = plh.lend_number AND is_return = 1) AS ReturnCount,--已還多少紙本
								(case 
								when exists(select 1 from paper_lending_history cplh where cplh.lend_number = plh.lend_number and cplh.overdue_day>0 and cplh.is_return=1)
								then 1
								else 0
								end) as IsOverdueAndReturn, --是否有逾期已归还的纸本
                                lend_status as LendStatus,--申請單狀態
                                (SELECT fun_name FROM sys_parameters WHERE para_code = N'lib_lendStatus' AND lang_type = N'{langType}' AND func_code = lend_status) AS LendStatusName--申請單狀態中文名稱
                           FROM paper_lending_history AS plh
                           LEFT JOIN (SELECT basic_id,(SELECT apply_number FROM paper_application_data WHERE application_id = paper_applica_id) AS apply_number FROM paper_basic_data) AS pbd ON pbd.basic_id = plh.paper_basic_id
                           LEFT JOIN (SELECT apply_number,contract_number,contract_name FROM v_getunconfirmedapplication) AS fa ON fa.apply_number = pbd.apply_number
                           WHERE 1 = 1
                                 {searchSql}
                            order by LendNumber desc
                          ";

        List<LendUserHis> list = NpgsqlSearchByList(sql, sqlPara, actFunc);
        return list;
    }
    #endregion

    #region 我的借出，歷史借出，導出
    /// <summary>
    /// 我的借出，歷史借出，導出
    /// </summary>
    /// <param name="para"></param>
    /// <param name="currentEmplid"></param>
    /// <param name="langType"></param>
    /// <returns></returns>
    public List<LendUserHisExport> QueryLendHisForUserExprt(LendHisForUserExportPara para, string currentEmplid, string langType = "ZH-TW")
    {
        Dictionary<string, object> sqlPara = new Dictionary<string, object>();

        #region 拼接查詢條件
        StringBuilder searchSql = new StringBuilder();

        searchSql.AppendLine(@" AND plh.lend_handler_emplid = @currentEmplid");
        sqlPara.Add("currentEmplid", currentEmplid);

        //紙本/合約編號
        if (!string.IsNullOrWhiteSpace(para.ContractNumber))
        {
            searchSql.AppendLine(@" AND (plh.paper_code LIKE CONCAT(N'%', @ContractNumber, N'%')
                                            OR fa.contract_number LIKE CONCAT(N'%', @ContractNumber, N'%')
                                           )"
                                 );
            sqlPara.Add("ContractNumber", para.ContractNumber);
        }

        //紙本/合約名稱
        if (!string.IsNullOrWhiteSpace(para.ContractName))
        {
            searchSql.AppendLine(@" AND (plh.paper_name LIKE CONCAT(N'%', @ContractName, N'%')
                                            OR fa.contract_name LIKE CONCAT(N'%', @ContractName, N'%')
                                            )");
            sqlPara.Add("ContractName", para.ContractName);
        }

        //取件日期
        if (para.PickupTime.Start.HasValue)
        {
            searchSql.AppendLine(@" AND plh.pickup_time>= @PickupTimeBegin");
            sqlPara.Add("PickupTimeBegin", para.PickupTime.Start.Value);
        }
        if (para.PickupTime.End.HasValue)
        {
            searchSql.AppendLine(@" AND plh.pickup_time< @PickupTimeEnd");
            sqlPara.Add("PickupTimeEnd", para.PickupTime.End.Value.AddDays(1));
        }

        //歸還日期  edit by SpringJiang 20241114 修改為使用取唯一欄位方法，優先實際歸還日期
        searchSql.AppendLine(@$"AND (
                                          {para.ReturnTime.ToSqlByCoalesce(new List<string> { "actual_return_time", "should_return_time" }, "actual_return_time", sqlPara)}
                                          )
                                ");

        //借出/調閱單號
        if (!string.IsNullOrWhiteSpace(para.LendNumber))
        {
            searchSql.AppendLine("AND (plh.should_return_time LIKE @LendNumber OR retrieve_number LIKE @LendNumber)");
            sqlPara.Add("LendNumber", "%" + para.LendNumber + "%");
        }

        //申請單狀態
        if (para.LendStatus.Count > 0)
        {
            int i = 0;
            string sqlLendStatusPara = string.Join(" OR ", para.LendStatus.Select(e =>
            {
                i++;
                sqlPara.Add("LendStatu_" + i.ToString(), e);
                return "plh.lend_status = @LendStatu_" + i.ToString();
            })
                                                    );
            searchSql.AppendLine(@$"AND ({sqlLendStatusPara}) ");
        }
        #endregion

        string sql = @$"SELECT
                        DISTINCT
                        plh.lend_number as LendNumber,--借出單號
                        plh.retrieve_number as RetrieveNumber,--調閱單號
                        plh.application_time as ApplicationtTime,--申請日期
                        (SELECT fun_name FROM sys_parameters WHERE para_code = N'lib_lendStatus' AND lang_type = N'{langType}' AND func_code = plh.lend_status) AS LendStatusName,--申請單狀態中文名稱
                        fa.contract_number as ContractNumber,--合約編號
                        plh.paper_code as PaperCode,--紙本編號
                        plh.paper_name as PaperName,--紙本名稱
                        plh.should_return_time as ShouldReturnTime,--應歸還日期
                        plh.actual_return_time as ActualReturnTime,--歸還日期
                        (SELECT STRING_AGG(fun_name, ', ') FROM sys_parameters WHERE para_code = N'lib_returnStatus' AND lang_type = N'{langType}' AND func_code IN(SELECT value  FROM STRING_SPLIT(plh.lend_return_status, ','))) AS ReturnStatusName,--歸還現狀中文名稱
                        plh.lend_status as LendStatus,--申請單狀態
                        (SELECT fun_name FROM sys_parameters WHERE para_code = N'lib_lendStatus' AND lang_type = N'{langType}' AND func_code = plh.lend_status) AS LendStatusName,--申請單狀態中文名稱
                        is_pickup_lend as IsPickupLend, --是否已经取件
                         plh.overdue_day as OverdueDay,--逾期天数
                        iif(plh.overdue_day>0 and plh.is_return=1,1,0) as IsOverdueAndReturn --是否逾期已归还
                        FROM paper_lending_history AS plh
                        LEFT JOIN (SELECT basic_id,(SELECT apply_number FROM paper_application_data WHERE application_id = paper_applica_id) AS apply_number FROM paper_basic_data) AS pbd ON pbd.basic_id = plh.paper_basic_id
                        LEFT JOIN (SELECT apply_number,contract_number,contract_name FROM v_getunconfirmedapplication) AS fa ON fa.apply_number = pbd.apply_number
                        WHERE 1 = 1
                              {searchSql}
                         order by LendNumber desc ,PaperCode asc
                    ";
        return NpgsqlSearchByList<LendUserHisExport>(sql, sqlPara);
    }
    #endregion

    #region 查詢歷史借出的紙本借閲詳情
    /// <summary>
    /// 查詢歷史借出的紙本借閲詳情
    /// </summary>
    /// <param name="lendNumber"></param>
    /// <param name="actFunc"></param>
    /// <returns></returns>
    public List<PaperLendHisDetail> QueryPaperLendHisDetailList(string lendNumber, Action<PaperLendHisDetail> actFunc, string langType = "ZH-TW")
    {
        string sql = @$"
                        SELECT
                        fa.contract_number as ContractNumber,--合約編號
                        plh.paper_code as PaperCode,--紙本編號
                        plh.paper_name as PaperName,--紙本名稱
                        plh.should_return_time as ShouldReturnTime,--應歸還日期
                        plh.actual_return_time as ActualReturnTime,--實際歸還日期
                        plh.overdue_day as OverdueDay,--逾期天數
                        plh.lend_return_status as PaperReturnStatus,--歸還現狀
                        (SELECT STRING_AGG(fun_name, ', ') FROM sys_parameters WHERE para_code = N'lib_returnStatus' AND lang_type = N'{langType}' AND func_code IN(SELECT value  FROM STRING_SPLIT(plh.lend_return_status, ','))) AS ReturnStatusName,--歸還現狀中文名稱
						plh.is_return as IsReturn, --歸還狀態1：已歸還；0：未歸還
                        is_pickup_lend as IsPickupLend
                        FROM paper_lending_history AS plh
                        LEFT JOIN (SELECT basic_id,(SELECT apply_number FROM paper_application_data WHERE application_id = paper_applica_id) AS apply_number FROM paper_basic_data) AS pbd ON pbd.basic_id = plh.paper_basic_id
                        LEFT JOIN (SELECT apply_number,contract_number,contract_name FROM v_getunconfirmedapplication) AS fa ON fa.apply_number = pbd.apply_number
                        WHERE lend_number =@lendNumber and  (is_pickup_lend=1 or lend_status='05')
                        ";
        return NpgsqlSearchByList(sql, new { lendNumber }, actFunc);
    }
    #endregion

    #region 获取历史借出填单人与经办人信息
    /// <summary>
    /// 获取历史借出填单人与经办人信息 
    /// </summary>
    /// <param name="lendNumber">借出单号</param>
    /// <returns></returns>
    public PersonnelInfo QueryPersonnelInfoByHisLendNumber(string lendNumber, string langType)
    {
        string sql = @$"SELECT
                        lend_number as LendNumber,--借出查詢
                        application_time as ApplicationTime,--申請日期
                        lend_fill_emplid as LendFillEmplid,--填單人工號
                        lend_fill_deptid as LendFillDeptid,--填單人部門代號
                        fee.name AS LendFillCName,--填單人中文名
                        fee.name_a AS LendFillEName,--填單人英文名
                        fee.phone_a AS LendFillPhone,--填單人分機
                        fee.prefix_dial_code_a AS LendFillPrefixDialCode,--填單人區碼
                        fee.email_address_a AS LendFillEmail,--填單人郵件
                        lend_handler_emplid as LendHandlerEmplid,--經辦人工號
                        lend_handler_deptid as LendHandlerDeptid,--經辦人部門代號
                        hee.name AS LendHandlerCName,--經辦人中文名
                        hee.name_a AS DendHandlerEName,--經辦人英文名
                        hee.phone_a AS LendHandlerPhone,--經辦人分機
                        hee.prefix_dial_code_a AS LendHandlerPrefixDialCode,--經辦人區碼
                        hee.email_address_a AS LendHandlerEmail,--經辦人郵件
                        pla.lend_status as LendStatus, --申请单状态编码
                        (select fun_name from sys_parameters where para_code = N'lib_lendStatus' and lang_type=N'{langType}' and func_code= pla.lend_status) as LendStatusName,
                        void_reason as VoidReason,
						lend_fill_bg as LendFillBg,
						lend_handler_bg as  LendHandlerBg,
                        iif(fee.termination is null , 1,0) as FillOnJobStatus, --填单人在职状态,1在职，0离职
                            iif(hee.termination is null , 1,0) as HandlerOnJobStatus --经办人在职状态,1在职，0离职
                        FROM (SELECT 
									DISTINCT
			                        lend_number,
			                        application_time,
			                        lend_fill_emplid,
			                        lend_fill_deptid,
			                        lend_handler_emplid,
			                        lend_handler_deptid,
                                    void_reason,
									lend_status,
									lend_fill_bg ,
									lend_handler_bg 
	                           FROM paper_lending_history 
	                           WHERE lend_number = @lendNumber
	                           ) AS pla
                        LEFT JOIN (SELECT emplid,name,name_a,phone_a,email_address_a,termination, prefix_dial_code_a FROM ps_sub_ee_lgl_vw_a) AS fee ON fee.emplid = pla.lend_fill_emplid
                        LEFT JOIN (SELECT emplid,name,name_a,phone_a,email_address_a,termination, prefix_dial_code_a FROM ps_sub_ee_lgl_vw_a) AS hee ON hee.emplid = pla.lend_handler_emplid
                        ";
        var data = NpgsqlSearchBySingle<PersonnelInfo>(sql, new { lendNumber });
        return data;
    }
    #endregion

    #region 获取历史出借需求咨询信息 (借出检视页面所需)
    /// <summary>
    /// 获取历史出借需求咨询信息 (借出检视页面所需)
    /// </summary>
    /// <param name="lendNumber">借出单号</param>
    /// <returns></returns>
    public List<DemandInfo> QueryDemandInfoByHisLendNumber(string lendNumber)
    {
        string sql = @"SELECT 
                            DISTINCT
                            plh.retrieve_number as RetrieveNumber,--調閱單號
                            plh.borrow_days as BorrowDays,--借出天數
                            plh.demand_reason as DemandReason,--申請原因
                            plh.retrieve_reason as RetrieveReason, --其他说明
                            vo.discloseperson as  Discloseperson,--顯示內部同仁/其他同仁：00：無；01：其他同仁；10：內部同仁；11：內部同仁+其他同仁
                            vo.empid as Empid,--工號
                            vo.ename as EName,--英文名稱
                            vo.depid as Depid,--部門代碼
                            vo.company as Company,--部門公司別
                            vo.otherperson as Otherperson,--其他同仁，一串字符，直接顯示
                            vo.other as Other,--其他說明
                            vo.applynumber  as Applynumber,--調閱案件單號
							vo.auth_date_start as AuthDateStart, --实际开放时间
							vo.auth_date_end as AuthDateEnd, --实际开放时间 
                            plh.Actual_Return_Time as ActualReturnTime --實際歸還日
                            FROM paper_lending_history AS plh
                            left JOIN V_GetOtherRetrieveNumber AS vo ON vo.retrieve_number = plh.retrieve_number
                            WHERE lend_number = @lendNumber
                            ";
        return NpgsqlSearchByList<DemandInfo>(sql, new { lendNumber });
    }
    #endregion

    #region 获取出借历史 取件咨询信息 (借出检视页面所需)
    /// <summary>
    /// 获取出借历史 取件咨询信息 (借出检视页面所需)
    /// </summary>
    /// <param name="lendNumber">出借历史表主键</param>
    public PickUpInfo QueryPickUpInfoByHisLendNumber(string lendNumber, string langType)
    {
        string sql = @$"
                            SELECT DISTINCT
                                pickup_status as PickupStatus,--取件狀態
                                pickup_time as PickupTime,--取件日期
                                consignment_number as ConsignmentNumber,--取件單號
                                pickup_emplid as PickupEmplid,--取件者工號
                                ee.name as CName,--取件者中文名稱
                                ee.name_a as EName,--取件者英文名稱
                                actual_pickup_time as ActualPickupTime,--實際取件時間
                                 (sELECT fun_name FROM sys_parameters WHERE para_code = N'lib_pickupStatus' and sys_parameters.func_code=pld.pickup_status and lang_type=N'{langType}') as PickupStatusName
                                FROM paper_lending_history AS pld
                                LEFT JOIN (SELECT emplid,name,name_a FROM ps_sub_ee_lgl_vw_a) AS ee ON ee.emplid = pld.pickup_emplid
                                WHERE lend_number = @lendNumber
                        ";

        return DbAccess.Database.SqlQuery<PickUpInfo>(sql, new { lendNumber }).FirstOrDefault(f => !string.IsNullOrWhiteSpace(f.PickupStatus)) ?? new PickUpInfo();
        //NpgsqlSearchBySingle<PickUpInfo>(sql, new { lendNumber });
    }
    #endregion

    #region 查询历史纸本借阅详情 (检视页面对象)
    /// <summary>
    /// 查询历史纸本借阅详情 (检视页面对象)
    /// </summary>
    /// <returns></returns>
    public List<Pldv> QueryLendHisDetailListForView(string lendNumber, string langType, Action<Pldv> actFunc)
    {
        string sql = @$"SELECT
                        plh.history_id,
                        fa.contract_number as ContractNumber,--合約編號
                        plh.paper_code as PaperCode,--紙本編號
                        plh.paper_name as PaperName,--紙本名稱
                        retrieve_number as RetrieveNumber,--調閱單號
                        (SELECT DISTINCT auth_date_end FROM V_GetOtherRetrieveNumber WHERE retrieve_number = plh.retrieve_number) AS AuthDateEnd,--調閱單號結束時間
                        should_return_time as ShouldReturnTime,--應歸還日期(當主表中，申請單狀態為待取件時，應歸還日期應該為pldd.retrieve_start_time)
						pba.paper_position as PaperPosition, --存放位置
						pba.parer_borrow_days as ParerBorrowDays, --借閱日上限，天數
						(SELECT STRING_AGG(fun_name, ', ') FROM sys_parameters WHERE para_code = N'lib_returnStatus' AND lang_type = N'{langType}' AND func_code IN(SELECT value  FROM STRING_SPLIT(plh.lend_return_status, ','))) AS ReturnStatusName,--紙本現狀中文名稱
						is_return as 'IsReturn',--歸還狀態1：已歸還；0：未歸還
                        is_pickup_lend as IsPickupLend,
                        actual_return_time as  ActualReturnTime, --实际归还日期
                        pba.paper_entry_status as PaperEntryStatus,
                         plh.overdue_day as OverdueDay,
		                (SELECT fun_name FROM sys_parameters WHERE lang_type = N'{langType}' AND para_code = N'lib_paperEntryStatus' AND func_code = pba.paper_entry_status) AS PaperEntryStatusName--入庫狀態
                        FROM paper_lending_history AS plh
						left join (SELECT basic_id,(SELECT apply_number FROM paper_application_data WHERE application_id = paper_applica_id) AS apply_number,paper_entry_status,paper_position,parer_borrow_days FROM paper_basic_data) pba on plh.paper_basic_id=pba.basic_id
                        LEFT JOIN (SELECT apply_number,contract_number,contract_name FROM v_getunconfirmedapplication) AS fa ON fa.apply_number = pba.apply_number
                        WHERE lend_number = @lendNumber 
                           ";
        return NpgsqlSearchByList(sql, new { lendNumber }, actFunc);
    }
    #endregion

    #region 查询纸本未借出明細((检视页面用)
    /// <summary>
    /// 查询纸本未借出明細((检视页面用)
    /// </summary>
    /// <returns></returns>
    public List<Pldv> QueryPaperUnlendingDetailList(string lendNumber, string langType, Action<Pldv> actFunc)
    {
        string sql = @$"SELECT
                                pld.detail_id,
                                pld.paper_basic_id as 'PaperBasicId',
                                fa.contract_number as 'ContractNumber',--合約編號
                                pld.paper_code as 'PaperCode',--紙本編號
                                pld.paper_name as 'PaperName',--紙本名稱
                                pldd.retrieve_number as 'RetrieveNumber',--調閱單號
                                (SELECT DISTINCT auth_date_end FROM V_GetOtherRetrieveNumber WHERE retrieve_number = pldd.retrieve_number) AS AuthDateEnd,--調閱單號結束時間
                                pld.should_return_time as 'ShouldReturnTime', --應歸還日期(當主表中，申請單狀態為待取件時，應歸還日期應該為vo.auth_date_end)
                                pld.paper_entry_status as PaperEntryStatus,
		                        (SELECT fun_name FROM sys_parameters WHERE lang_type = N'{langType}' AND para_code = N'lib_paperEntryStatus' AND func_code = pld.paper_entry_status) AS PaperEntryStatusName,--入庫狀態
		                        pba.paper_position as PaperPosition, --存放位置
		                        pba.parer_borrow_days as ParerBorrowDays, --借閱日上限，天數
		                        (SELECT STRING_AGG(fun_name, ', ') FROM sys_parameters WHERE para_code = N'lib_returnStatus' AND lang_type = N'{langType}' AND func_code IN(SELECT value  FROM STRING_SPLIT(pba.paper_return_status, ','))) AS ReturnStatusName,--紙本現狀中文名稱
                                is_return as 'IsReturn',--歸還狀態1：已歸還；0：未歸還
                                is_pickup_lend as IsPickupLend,
                        		actual_return_time as  ActualReturnTime
                                FROM paper_unlending_detail AS pld
                                LEFT JOIN paper_lending_demand AS pldd ON pld.paper_demand_id = pldd.demand_id
                                left join (SELECT basic_id,(SELECT apply_number FROM paper_application_data WHERE application_id = paper_applica_id) AS apply_number,paper_return_status,paper_position,parer_borrow_days FROM paper_basic_data) pba on pld.paper_basic_id=pba.basic_id
                                LEFT JOIN (SELECT apply_number,contract_number,contract_name FROM v_getunconfirmedapplication) AS fa ON fa.apply_number = pba.apply_number
                                WHERE pld.lend_number = @lendNumber
                           ";
        return NpgsqlSearchByList(sql, new { lendNumber }, actFunc);
    }
    #endregion
}
