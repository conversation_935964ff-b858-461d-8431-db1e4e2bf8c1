﻿using Elegal.Interface.Api.Common.Control.Filter;
using Elegal.Interface.Api.Common.Model.ViewModel.FlowApi;
using Elegal.Interface.Api.Common.Model.ViewModel.Minio;
using Elegal.Orm;
#nullable disable
namespace Elegal.Flow.Api.Repository.Application
{
    /// <summary>
    /// 紙本进度追踪
    /// </summary>
    public class PaperTrackingRepository
    {
        private readonly string userSql = @"select
	                                emplid,
	                                name,
	                                name_a,
	                                email_address_a
                                from
	                                ps_sub_ee_lgl_vw_a
                                union
                                select
	                                aff_empid emplid,
	                                aff_emp_cname name,
	                                aff_emp_ename name_a,
	                                aff_emp_email email_address_a
                                from
	                                affiliate_emp";
        #region 获取申请单基本信息
        /// <summary>
        /// 获取申请单基本信息
        /// </summary>
        /// <param name="applyNumber">申请单号</param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public PaperTrackingViewModel GetBaseInformation(string applyNumber)
        {
            string sql = $@"select
	                            af.apply_number applyNum,
	                            case
		                            when af.apply_type = 'C' then 2
		                            when af.apply_type = 'A' and af.form_type = 'R' then 4  --二級菜單同步調整
		                            else null
	                            end as applicationType,
                                af.legal_affairs_emplid legalEmplid,--承辦法務
                                lee.email_address_a legalMail,--承辦法務邮箱
                                lee.name legalCname,--承辦法務(中文名稱)
                                lee.name_a legalEname,--承辦法務(英文名稱)
	                            af.fill_emplid fillerEmplid,
                                pee.name handlerCname,
                                pee.name_a handlerEname,
                                pee.email_address_a handlerMail,
	                            af.pic_emplid handlerEmplid,
                                fee.name fillerCname,
                                fee.name_a fillerEname,
                                fee.email_address_a fillerMail,
	                            af.incumbent_emplid currentContactPerson,
                                iee.name currentContactPersonCname,
                                iee.name_a currentContactPersonEname,
                                af.contract_name contractName,
                                af.other_party partyA,
                                af.entity_id entityId,
	                            af.confiden_level confidenLevel,
	                            sp1.fun_name confidenLevelName,
                                af.application_state applicationState,
                                flh.handover_remark notes
                            from
	                            V_GetAllApplication af
                            left join form_legal_handover flh on flh.apply_number = af.apply_number
                            left join (select fun_name,func_code,lang_type from sys_parameters where para_code = 'confidentStatus' and lang_type='{MvcContext.UserInfo.logging_locale}') sp1 on sp1.func_code=af.confiden_level
                            left join ({userSql}) lee on lee.emplid = af.legal_affairs_emplid--承辦法務人員
                            left join ({userSql}) pee on pee.emplid = af.pic_emplid--經辦人
                            left join ({userSql}) fee on fee.emplid = af.fill_emplid--填單人
                            left join ({userSql}) iee on iee.emplid = af.incumbent_emplid--現任聯絡人
                            where af.apply_number = {DbAccess.Database.GetParameterPrefix()}applyNumber";
            return DbAccess.Database.SqlQuery<PaperTrackingViewModel>(sql, new { applyNumber }).FirstOrDefault();
        }
        #endregion

        #region 紙本收發歷程
        /// <summary>
        /// 紙本收發歷程
        /// </summary>
        /// <param name="applyNumber">申请单号</param>
        /// <returns></returns>
        public IEnumerable<PaperTrackingBathJobViewModel> GetPaperTrackWorkData(string applyNumber)
        {
            string sql = $@"select
	                            row_number() over (order by	ptw.receiving_date) seq,
                                ptw.track_id trackId,
	                            ptw.apply_number applyNum,--申请单号
	                            ptw.receiving_date receiptDate,--收件日
	                            ptw.track_status paperTracking,--追蹤狀態
                                sp.fun_name paperTrackingName,--追蹤狀態名称
	                            ptw.receiving_emplid recipientEmplid,--收件者
                                ree.name recipientCname,--收件者(中文名稱)
                                ree.name_a recipientEname,--收件者(英文名稱)
	                            ptw.receiving_type receivingType,--收件方式
	                            sp2.fun_name receivingTypeName,--收件方式名稱
	                            ptw.consignment_number consignmentNumber,--托運單號
                                ptw.paper_number paperPiece,--紙本份數
                                ptw.remind_count remindCount,--提醒次數
                                ptw.track_remark notes,--備註
                                ptw.create_time operate_time,--更新時間
                                ptw.create_user,--更新人
                                cee.name operate_cuser,--更新人(中文名稱)
                                cee.name_a operate_euser--更新人(英文名稱)
                            from paper_track_work ptw
                            left join (select fun_name,func_code,lang_type from sys_parameters where para_code = 'lib_paperTrackStatus' and lang_type = @langType) sp on sp.func_code = ptw.track_status--追蹤狀態
                            left join (select fun_name,func_code,lang_type from sys_parameters where para_code = 'lib_receiptStatus' and lang_type = @langType) sp2 on sp2.func_code = ptw.receiving_type--收件方式
                            left join ({userSql}) cee on cee.emplid = ptw.create_user--更新人
                            left join ({userSql}) ree on ree.emplid = ptw.receiving_emplid--收件者
                            where apply_number = {DbAccess.Database.GetParameterPrefix()}applyNumber and sp.lang_type='{MvcContext.UserInfo.logging_locale}'";
            return DbAccess.Database.SqlQuery<PaperTrackingBathJobViewModel>(sql, new { applyNumber, langType = MvcContext.UserInfo.logging_locale });
        }
        #endregion

        #region 获取檔案附件(法務行政交接 archive_purposes = 4)列表(区分申请类型[2：合約申請；4：資料建檔])
        /// <summary>
        /// 获取檔案附件(法務行政交接 archive_purposes = 4)列表(区分申请类型[2：合約申請；4：資料建檔])
        /// </summary>
        /// <param name="upload_key">业务关联主键</param>
        /// <param name="upload_type">申请类型</param>
        /// <returns></returns>
        public IEnumerable<SysUploadFileModel> GetFileList(string upload_key, int upload_type)
        {
            string sql = $@"select
                                row_number() over (order by	create_time asc) seq, --修改序号
                                fileid,
                                file_name,
                                file_size,
                                isnull(modify_time,create_time) operate_time,
                                ee.name operate_cuser,
                                ee.name_a operate_euser
                            from sys_upload_file 
                            left join ps_sub_ee_lgl_vw_a ee on ee.emplid = isnull(modify_user,create_user)
                            where upload_type = {DbAccess.Database.GetParameterPrefix()}upload_type and archive_purposes = 4 and is_watermake = 0 and upload_key = {DbAccess.Database.GetParameterPrefix()}upload_key
                            order by create_time asc";
            return DbAccess.Database.SqlQuery<SysUploadFileModel>(sql, new { upload_key, upload_type });
        }
        #endregion

        #region 获取檔案曆程(法務行政交接備注)
        /// <summary>
        /// 获取檔案曆程
        /// </summary>
        /// <param name="applyNumber"></param>
        /// <returns></returns>
        public IEnumerable<HandoverFileHistoryModel> GetFileHistoryData(string applyNumber)
        {
            string sql = $@"select
	                            row_number() over (order by	hfh.create_time asc) Seq, --修改序号
                                hfh.action_id ActionId,
                                sa.action_name ActionName,
                                hfh.file_name FileName,
                                hfh.create_time operate_time,
                                ee.name operate_cuser,
                                ee.name_a operate_euser
                            from handover_file_history hfh
                            left join sys_action sa on sa.action_code = hfh.action_id and sa.lang_type ='{MvcContext.UserInfo.logging_locale}' 
                            left join ps_sub_ee_lgl_vw_a ee on ee.emplid = hfh.create_user
                            where hfh.apply_number = {DbAccess.Database.GetParameterPrefix()}applyNumber
                            order by hfh.create_time asc";
            return DbAccess.Database.SqlQuery<HandoverFileHistoryModel>(sql, new { applyNumber });
        }
        #endregion
    }
}
