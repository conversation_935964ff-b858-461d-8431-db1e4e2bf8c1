﻿using Elegal.Flow.Api.Services.Individual;
using Elegal.Interface.Api.Common.Control.Filter;
using Elegal.Interface.Api.Common.Model;
using Elegal.Interface.Api.Common.Model.Enum;
using Elegal.Interface.Api.Common.Model.ResultModel.PermissionApi;
using Elegal.Interface.ApiData.Service.FuncService;
using Elegal.Interface.ApiData.Service.Model.DbModel;
using Elegal.Interface.ApiData.Service.Model.QueryCondition;
using Elegal.Orm;
using Newtonsoft.Json;

namespace Elegal.Flow.Api.Services
{
    /// <summary>
    /// 首頁定位佈局
    /// </summary>
    public static class SysHomePositionService
    {
        /// <summary>
        /// 获取定位信息
        /// </summary>
        /// <returns></returns>
        public static SysHomePositionView GetSysHomePositionView()
        {
            SysHomePositionView sysHomePositionView = new();
            SysHomePosition sysHomePosition = SysHomePositionDataService.FindByKey(MvcContext.UserInfo.current_emp) ?? SysHomePositionDataService.FindByKey("sys.admin");
            List<KeyValueObj<string, bool>> BlockShowList = GetBlockShowList();
            List<SysHomePositionModel> sysHomePositionModelList;
            if (sysHomePosition == null)
            {
                sysHomePositionView.HomeLock = true;
                sysHomePositionModelList = SysHomeBlockService.SysHomePositionModelList;
            }
            else
            {
                sysHomePositionView.HomeLock = sysHomePosition.HomeLock == 1;
                sysHomePositionModelList = JsonConvert.DeserializeObject<List<SysHomePositionModel>>(sysHomePosition.HomePosition) ?? [];
            }
            foreach (var sysHomePositionModel in sysHomePositionModelList)
            {
                var block = BlockShowList.FirstOrDefault(f => f.Key.Equals(sysHomePositionModel.code));
                sysHomePositionModel.title = block?.Title;
                sysHomePositionModel.show = block?.Value ?? true;
            }
            sysHomePositionView.SysHomePositionModelList = sysHomePositionModelList.Where(w => w.show).ToList();
            SysTimezone sysTimezone = SysTimezoneDataService.FindByKey(MvcContext.UserInfo.current_emp);
            sysHomePositionView.GuidePageCompleted = sysTimezone != null && sysTimezone.Guided.Equals((int)YesOrNoUtils.Yes);
            sysHomePositionView.HomeRemind = sysTimezone != null && sysTimezone.HomeRemind.Equals((int)YesOrNoUtils.Yes);
            sysHomePositionView.SysUserGuideList = [.. SysUserGuideDataService.Query(new SysUserGuideQueryCondition(){ Used = (int)YesOrNoUtils.Yes}).OrderBy(b => b.SortOrder).Select(s => {
                s.TipTitle = MvcContext.UserInfo.logging_locale.Equals("EN-US")?s.TipEtitle:s.TipTitle;
                s.TipText = MvcContext.UserInfo.logging_locale.Equals("EN-US")?s.TipEtext:s.TipText;
                return s;
                })];
            sysHomePositionView.GuidePageEnable = sysHomePositionView.SysUserGuideList.Any(a => a.Enable.Equals((int)YesOrNoUtils.Yes));
            return sysHomePositionView;
        }
        /// <summary>
        /// 首页区块显示状态
        /// </summary>
        /// <returns></returns>
        public static List<KeyValueObj<string, bool>> GetBlockShowList()
        {
            var user = MvcContext.UserInfo;
            var blockStatus = SysHomeBlockStatusDataService.Query(new SysHomeBlockStatusQueryCondition() { Emplid = user.current_emp });
            return DbAccess.GetAll<SysHomeBlock>().Select(s =>
            {
                //区块不可关闭时，不读取个人配置
                if (s.Closeable == (int)YesOrNoUtils.No) return new KeyValueObj<string, bool>(s.Code, true) { Title = user.logging_locale.Equals("EN-US") ? s.ETitle : s.CTitle };
                bool status = true;
                //可关闭时，读取个人区块配置状态
                if (blockStatus.Any(a => a.BlockId.Equals(s.Rowid)))
                {
                    var block = blockStatus.FirstOrDefault(f => f.BlockId.Equals(s.Rowid));
                    status = block?.Status == (int)YesOrNoUtils.Yes;
                }
                return new KeyValueObj<string, bool>(s.Code, status) { Title = user.logging_locale.Equals("EN-US") ? s.ETitle : s.CTitle };
            }).ToList();
        }
        /// <summary>
        /// 关闭布局提醒
        /// </summary>
        /// <returns></returns>
        public static bool CloseHomeRemind()
        {
            return SysTimezoneDataService.Update(new SysTimezone() { Emplid = MvcContext.UserInfo.current_emp, HomeRemind = 0 });
        }

    }
}
