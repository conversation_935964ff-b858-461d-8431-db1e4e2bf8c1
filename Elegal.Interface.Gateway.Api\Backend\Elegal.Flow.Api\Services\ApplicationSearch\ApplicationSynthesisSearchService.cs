﻿using Elegal.Interface.Api.Common.Control.Filter;
using Elegal.Interface.Api.Common.Model.ParaModel.FlowApi.ApplicationSynthesisSearch;
using Elegal.Interface.Api.Common.Model.ViewModel.FlowApi;
using Elegal.Interface.Api.Common.Model.ViewModel.FlowApi.ApplicationSearch;
using Elegal.Interface.Api.Common.Model.ViewModel.PermissionApi.EmpManage;
using Elegal.Interface.Api.Common.Repository;
using Elegal.Interface.ApiData.Service.FuncService;
using Elegal.Interface.ApiData.Service.Model.DbModel;
using NPOI.SS.UserModel;
using NPOI.XSSF.UserModel;
using Elegal.Interface.Api.Common.FuncService;
using Elegal.Interface.Api.Common.FuncHelper;
namespace Elegal.Flow.Api.Services.ApplicationSearch
{
    /// <summary>
    /// 資料查詢 -> 申請單綜合查詢 -> 案件申請查詢
    /// </summary>
    public static class ApplicationSynthesisSearchService
    {
        private static ApplicationSearchCommonRepository _applicationSearchCommonRepository = new ApplicationSearchCommonRepository();

        #region 根據查詢參數獲取案件申請單集合
        /// <summary>
        /// 根據查詢參數獲取案件申請單集合
        /// </summary>
        /// <param name="asp">查詢欄位</param>
        /// <param name="empID">當前登陸者工號</param>
        /// <param name="userRole">當前登陸者工號</param>
        /// <param name="totalCount">總筆數</param>
        /// <param name="langType">語係</param>
        /// <returns></returns>
        public static List<ApplicationSearchViewModel> GetApplicationData(ApplicationSynthesisSearchParaModel asp, string empID, List<int> userRole, out int totalCount, string langType = "ZH-TW")
        {
            List<ApplicationSearchViewModel> listData = new List<ApplicationSearchViewModel>();
            totalCount = 0;

            //獲取主體集合 -> 檢視權控
            List<FnpEntityModel> listEntity = ApplicationSearchService.GetFnpEntityByCaseStatus(2, asp.menuCode, empID, userRole);
            //改為使用後端查詢  20250625
            if (asp.defaultEntity.Any())
            {
                //大綜合查詢不需要進行區分，直接主體查詢即可 -> Issue：323
                //大綜合查詢需要根據主體+角色處理 edit by SpringJiang 20250627
                string listApplyNumber = _applicationSearchCommonRepository.GetSynthesisApplyNumberByRole(userRole, asp.defaultEntity, asp.menuCode);
                //查詢主體信息
                listData = _applicationSearchCommonRepository.GetApplicationData(asp, out totalCount, langType, listApplyNumber);
            }
            return listData;
        }



        /// <summary>
        /// 匯出
        /// </summary>
        /// <param name="asp">查詢欄位</param>
        /// <param name="empID">當前登陸者工號</param>
        /// <param name="userRole">當前登陸者工號</param>
        /// <param name="totalCount">總筆數</param>
        /// <param name="langType">語係</param>
        /// <returns></returns>
        public static byte[]  GetApplicationDataExport(ApplicationSynthesisExportParaModel asp, string empID, List<int> userRole, string langType = "ZH-TW")
        {
            insertLog("GetApplicationDataExport_Export_Begin");
            var data = GetApplicationDataNew(asp, empID, userRole, langType);
            string yes = ActionFilter.GetMultilingualValue("ApplicationSearch_Export:yes", true);//異動時間
            string no = ActionFilter.GetMultilingualValue("ApplicationSearch_Export:no", true);//異動時間
            string signType1 = ActionFilter.GetMultilingualValue("ApplicationSearch_Export:signType1", true);//異動時間

            //获取时区，仅仅获取一次，仅仅调用一次当前线程的 （Accessor.HttpContext会从线程池拿到当前请求的httpcontext；不适合放在一个超多数据的循环里）
            var timeZone = MvcContext.UserInfo.time_zone;
            insertLog("GetApplicationDataExport_seach_end");
            //创建工作表
            IWorkbook workbook = new XSSFWorkbook();
            ISheet sheet = workbook.CreateSheet("Sheet1");

            //定义一个字体
            IFont font = workbook.CreateFont().configFont(f =>
            {
                f.FontHeightInPoints = 12;
                f.FontName = "Calibri";
            });
            //定义一个样式
            ICellStyle defaultstyle = sheet.Workbook.CreateCellStyle().SetDefaultTitleBorder().AddFont(font).SetwrapTextOn().VerticalCenter();

            //設置表頭
            sheet.SetHeader(asp.ExportColumns.Select(e => e.header).ToList(), style => {
                var font = sheet.Workbook.CreateFont();
                font.IsBold = true; // 设置加粗
                style.AddFont(font);
                style.SetDefaultTitleBorder();
            });

            int columnLength = asp.ExportColumns.Count;
            //設置列寬
            sheet.SetColumnAutoWidth(0, columnLength);


            //写入数据
            sheet.WriteData(data, (item, row) =>
            {
                for (int i = 0; i < columnLength; i++)
                {
                    var column = asp.ExportColumns[i];
                    if (column.field == "currentSigner")
                    {
                        var data1 = item.current_signer.EmptyDefault(string.Empty).Split(",").Where(e => !string.IsNullOrEmpty(e))
                            .Select(e => e + (item.is_invitee == 1 ? signType1 : string.Empty));
                        var data2 = item.current_signer_over.EmptyDefault(string.Empty).EmptyDefault(string.Empty).Split(",").Where(e => !string.IsNullOrEmpty(e))
                            .Select(e => e + (item.is_invitee == 1 ? signType1 : string.Empty));
                        item["currentSigner", timeZone] = string.Join("\r\n", data1.Concat(data2));
                    }
                    if (column.field == "hasStampDuty")
                    {
                        item["hasStampDuty", timeZone] = item.has_stamp_duty == "1" ? yes : item.has_stamp_duty == "0" ? no : item.has_stamp_duty;
                    }
                    row.CreateElegalDefaultCell(i, defaultstyle).SetCellValue(item[column.field, timeZone]);
                }

            });
            insertLog("GetApplicationDataExport_Export_End");
            return workbook.ToBytes();
        }

        /// <summary>
        /// 匯出查詢
        /// </summary>
        /// <param name="asp">查詢欄位</param>
        /// <param name="empID">當前登陸者工號</param>
        /// <param name="userRole">當前登陸者工號</param>
        /// <param name="totalCount">總筆數</param>
        /// <param name="langType">語係</param>
        /// <returns></returns>
        public static List<ApplicationSearchViewModel> GetApplicationDataNew(ApplicationSynthesisSearchParaModel asp, string empID, List<int> userRole,string langType = "ZH-TW")
        {
            int i = 0;
            //这里不需要out i ,真正的方法体，由spring补全
            return GetApplicationData(asp,empID,userRole,out i,langType);
        }
        #endregion

        #region 根據查詢參數獲取其他申請單集合
        /// <summary>
        /// 根據查詢參數獲取其他申請單集合
        /// </summary>
        /// <param name="asp">查詢欄位</param>
        /// <param name="empID">當前登陸者工號</param>
        /// <param name="userRole">當前登陸者工號</param>
        /// <param name="totalCount">總筆數</param>
        /// <param name="langType">語係</param>
        /// <returns></returns>
        public static List<OtherApplicationSearchViewModel> GetOtherApplicationData(OtherApplicationSynthesisSearchParaModel asp, string empID, List<int> userRole, out int totalCount, string langType = "ZH-TW")
        {
            List<OtherApplicationSearchViewModel> listResult = new List<OtherApplicationSearchViewModel>();
            totalCount = 0;

            #region 根據查詢結果分組顯示數據
            List<OtherApplicationSearchResultModel> listData = _applicationSearchCommonRepository.GetOtherApplicationData(asp, string.Empty, out totalCount, langType);
            if (listData.Any())
            {
                OtherApplicationSearchViewModel oasv = new OtherApplicationSearchViewModel();
                //根據申請單號將集合分組
                var groupApplication = listData.GroupBy(ga => ga.apply_number);
                foreach (var ga in groupApplication)
                {
                    oasv = new OtherApplicationSearchViewModel();
                    List<OtherApplicationSearchResultModel> listGroupData = listData.Where(lga => lga.apply_number.ToLower() == ga.Key.ToLower()).ToList();
                    if (listGroupData.Any())
                    {
                        #region 默認欄位
                        oasv.apply_number = listGroupData[0].apply_number;
                        oasv.apply_time = listGroupData[0].apply_time;
                        oasv.pic_deptid = listGroupData[0].pic_deptid;
                        oasv.pic_emplid = listGroupData[0].pic_emplid;
                        oasv.pic_cname = listGroupData[0].pic_cname;
                        oasv.pic_ename = listGroupData[0].pic_ename;
                        oasv.form_type = listGroupData[0].form_type;
                        oasv.form_type_pfn = listGroupData[0].form_type_pfn;
                        oasv.application_state = listGroupData[0].application_state;
                        oasv.application_state_pfn = listGroupData[0].application_state_pfn;
                        #region 循環承辦法務人員
                        foreach (OtherApplicationSearchResultModel oasvm in listGroupData)
                        {
                            if (!string.IsNullOrEmpty(oasvm.legal_emplid))
                            {
                                oasv.legal_emplid.Add(new EmpManagementModel
                                {
                                    name_a = oasvm.legal_ename,
                                    name = oasvm.legal_cname,
                                    emplid = oasvm.legal_emplid
                                });
                            }
                        }
                        //去除重複數據
                        oasv.legal_emplid = new List<EmpManagementModel>(new HashSet<EmpManagementModel>(oasv.legal_emplid));
                        #endregion
                        oasv.current_levels = listGroupData[0].current_levels;
                        oasv.current_levels_pfn = listGroupData[0].current_levels_pfn;
                        oasv.current_signer = listGroupData[0].current_signer;
                        oasv.current_signer_over = listGroupData[0].current_signer_over;
                        oasv.is_invitee = listGroupData[0].is_invitee;
                        #endregion

                        #region 其他欄位
                        oasv.dept_type = listGroupData[0].dept_type;
                        oasv.dept_type_pfn = listGroupData[0].dept_type_pfn;
                        #region 循環必要揭露人員(內部同仁)
                        foreach (OtherApplicationSearchResultModel oasvm in listGroupData)
                        {
                            if (!string.IsNullOrEmpty(oasvm.expose_emplid))
                            {
                                oasv.expose_emplid.Add(new EmpManagementModel
                                {
                                    name_a = oasvm.expose_ename,
                                    name = oasvm.expose_cname,
                                    emplid = oasvm.expose_emplid
                                });
                            }
                        }
                        //去除重複數據
                        oasv.expose_emplid = new List<EmpManagementModel>(new HashSet<EmpManagementModel>(oasv.expose_emplid));
                        #endregion
                        oasv.expose_other_person = string.Join(", ", listGroupData.Where(s => !string.IsNullOrEmpty(s.expose_other_person)).Select(eo => eo.expose_other_person).Distinct().ToList());
                        oasv.retrieve_apply_number = string.Join(", ", listGroupData.Where(s => !string.IsNullOrEmpty(s.retrieve_apply_number)).Select(eo => eo.retrieve_apply_number).Distinct().ToList());
                        oasv.estimate_apply_number = string.Join(", ", listGroupData.Where(s => !string.IsNullOrEmpty(s.estimate_apply_number)).Select(eo => eo.estimate_apply_number).Distinct().ToList());
                        oasv.voided_apply_number = listGroupData[0].voided_apply_number;
                        oasv.seal_apply_number = listGroupData[0].seal_apply_number;
                        #region 我方主體
                        foreach (OtherApplicationSearchResultModel fem in listGroupData)
                        {
                            if (!string.IsNullOrEmpty(fem.entity_id))
                            {
                                oasv.entity_id.Add(new FnpEntityModel
                                {
                                    EntityId = fem.entity_id,
                                    EntityNamec = fem.entity_namec,
                                    EntityNamee = fem.entity_namee,
                                    Entity = fem.entity
                                });
                            }
                        }
                        //去除重複數據
                        oasv.entity_id = new List<FnpEntityModel>(new HashSet<FnpEntityModel>(oasv.entity_id));
                        #endregion
                        #region 他方
                        foreach (OtherApplicationSearchResultModel op in listGroupData)
                        {
                            if (!string.IsNullOrEmpty(op.other_party))
                            {
                                oasv.other_party.Add(op.other_party);
                            }
                        }
                        //去除重複數據
                        oasv.other_party = oasv.other_party.Distinct().ToList();
                        #endregion
                        #region 合約編號
                        foreach (OtherApplicationSearchResultModel op in listGroupData)
                        {
                            if (!string.IsNullOrEmpty(op.contract_number))
                            {
                                oasv.contract_number.Add(op.contract_number);
                            }
                        }
                        //去除重複數據
                        oasv.contract_number = oasv.contract_number.Distinct().ToList();
                        #endregion
                        #region 申請主體
                        foreach (OtherApplicationSearchResultModel fem in listGroupData)
                        {
                            if (!string.IsNullOrEmpty(fem.history_entity_id))
                            {
                                oasv.history_entity_id.Add(new FnpEntityModel
                                {
                                    EntityId = fem.history_entity_id,
                                    EntityNamec = fem.h_entity_namec,
                                    EntityNamee = fem.h_entity_namee,
                                    Entity = fem.h_entity
                                });
                            }
                        }
                        //去除重複數據
                        oasv.history_entity_id = new List<FnpEntityModel>(new HashSet<FnpEntityModel>(oasv.history_entity_id));
                        #endregion
                        #region 特殊主體權限
                        foreach (OtherApplicationSearchResultModel fem in listGroupData)
                        {
                            if (!string.IsNullOrEmpty(fem.special_entity_id))
                            {
                                oasv.special_entity_id.Add(new FnpEntityModel
                                {
                                    EntityId = fem.special_entity_id,
                                    EntityNamec = fem.s_entity_namec,
                                    EntityNamee = fem.s_entity_namee,
                                    Entity = fem.s_entity
                                });
                            }
                        }
                        //去除重複數據
                        oasv.special_entity_id = new List<FnpEntityModel>(new HashSet<FnpEntityModel>(oasv.special_entity_id));
                        #endregion
                        oasv.doc_type = listGroupData[0].doc_type;
                        oasv.doc_type_pfn = listGroupData[0].doc_type_pfn;
                        oasv.actual_date_start = listGroupData[0].actual_date_start;
                        oasv.actual_date_end = listGroupData[0].actual_date_end;
                        oasv.closed_date = listGroupData[0].closed_date;
                        #endregion
                    }
                    listResult.Add(oasv);
                }
            }
            #endregion

            return listResult;
        }
        #endregion


        private static void insertLog(string step, string log = null)
        {
            UploadLogDataService.Create(new UploadLog()
            {
                UploadKey = "history query",
                UploadType = "history query",
                UploadStep = step,
                CreateUser = MvcContext.UserInfo.current_emp,
                LogDetail = log
            });
        }
    }
}
