﻿using Elegal.Flow.Api.Repository.Report;
using Elegal.Interface.Api.Common.Control.Filter;
using Elegal.Interface.Api.Common.FuncHelper;
using Elegal.Interface.Api.Common.Model.ParaModel.FlowApi.Report;
using Elegal.Orm.Utils;
using System.Text;
#nullable disable
namespace Elegal.Flow.Api.Services.Report
{
    /// <summary>
    /// 纸本借出统计表
    /// </summary>
    public class LendReportService
    {
        private static readonly LendReportRepository _repository = new();
        /// <summary>
        /// 查询列表数据
        /// </summary>
        /// <param name="condition"></param>
        /// <returns></returns>
        internal static IEnumerable<object> GetListData(LendReportQueryCondition condition)
        {
            IEnumerable<object> result = new List<object>();
            switch (condition.Dimension.ToLower())
            {
                //主体维度查询
                case "entity":
                    result = condition.PaperType.Any() ? _repository.GetLendReportDataEntityType(condition) : _repository.GetLendReportDataEntityNoType(condition);
                    break;
                //经办人部门维度查询
                case "dept":
                    result = condition.PaperType.Any() ? _repository.GetLendReportDataHandlerDeptType(condition) : _repository.GetLendReportDataHandlerDeptNoType(condition);
                    break;
                //经办人维度查询
                case "handler":
                    result = condition.PaperType.Any() ? _repository.GetLendReportDataHandlerType(condition) : _repository.GetLendReportDataHandlerNoType(condition);
                    break;
            }
            return result;
        }

        /// <summary>
        /// 查询列表详情数据
        /// </summary>
        /// <param name="condition"></param>
        /// <returns></returns>
        internal static IEnumerable<object> GetDetailListData(LendDetailListCondition condition)
        {
            return _repository.GetDetailListData(condition);
        }

        /// <summary>
        /// 获取日志记录详情内容
        /// </summary>
        /// <param name="condition"></param>
        /// <returns></returns>
        internal static string GetLogDetail(LendReportQueryCondition condition)
        {
            StringBuilder stringBuilder = new StringBuilder();
            string start = string.Empty, end = string.Empty;
            if (condition.ApplicationTimeStart.HasValue)
                start = TimeZoneInfoConvertHelper.ConvertDateByTimeZoneByUtc(condition.ApplicationTimeStart, MvcContext.UserInfo.time_zone).ToString("yyyy/MM/dd");
            if (condition.ApplicationTimeEnd.HasValue)
                end = TimeZoneInfoConvertHelper.ConvertDateByTimeZoneByUtc(condition.ApplicationTimeEnd, MvcContext.UserInfo.time_zone).ToString("yyyy/MM/dd");
            if (!string.IsNullOrEmpty(start) || !string.IsNullOrEmpty(end))
                stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("LendReport_ApplicationTime", true)}：{start} ~ {end}");
            if (condition.HasRetrieveNumber.Any())
                stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("LendReport_HasRetrieveNumber", true)}：{string.Join(',', condition.HasRetrieveNumber.Select(s => s.Value))}");
            if (condition.IsOverdue.Any())
                stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("LendReport_IsOverdue", true)}：{string.Join(',', condition.IsOverdue.Select(s => s.Value))}");
            if (condition.PaperConfidenLevel.Any())
                stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("LendReport_PaperConfidenLevel", true)}：{string.Join(',', condition.PaperConfidenLevel.Select(s => s.Value))}");
            if (condition.LendStatus.Any())
                stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("LendReport_LendStatus", true)}：{string.Join(',', condition.LendStatus.Select(s => s.Value))}");
            if (condition.PaperType.Any())
                stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("LendReport_PaperType", true)}：{string.Join(',', condition.PaperType.Select(s => s.Value))}");
            if (condition.Entity.Any())
                stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("LendReport_Entity", true)}：{string.Join(',', condition.Entity.Select(s => s.Value))}");
            if (condition.LendHandlerDeptid.Any())
                stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("LendReport_LendHandlerDeptid", true)}：{string.Join(',', condition.LendHandlerDeptid.Select(s => s.Key))}");
            if (condition.LendHandler.Any())
                stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("LendReport_LendHandler", true)}：{string.Join(',', condition.LendHandler.Select(s => s.Value))}");
            if (condition.Filter.Any())
            {
                stringBuilder.AppendLine(CommonUtil.GetDivisionLine(ActionFilter.GetMultilingualValue("LendReport_Filter", true)));
                foreach (var filter in condition.Filter)
                {
                    stringBuilder.AppendLine($"{filter.Key}:{filter.Value}");
                }
            }
            return stringBuilder.ToString();
        }
    }
}
