﻿using Elegal.Flow.Api.Services.Report;
using Elegal.Interface.Api.Common.Control.Filter;
using Elegal.Interface.Api.Common.Control.Web;
using Elegal.Interface.Api.Common.Model.Enum;
using Elegal.Interface.Api.Common.Model.ParaModel.FlowApi.LendAppliction;
using Elegal.Interface.Api.Common.Model.ParaModel.FlowApi.Report;
using Elegal.Interface.Api.Common.Model.ResultModel;
using Elegal.Orm.Filters;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System.Text;

namespace Elegal.Flow.Api.Controllers.Report
{
    /// <summary>
    /// 纸本盘点统计表
    /// </summary>
    [Route("[controller]")]
    [ApiController]
    public class InventoryReportController : BaseController
    {
        /// <summary>
        /// 查询列表数据
        /// </summary>
        /// <param name="condition">查询参数</param>
        /// <returns></returns>
        [HttpPost]
        [Route("GetListData")]
        public async Task<ApiResultModelByObject> GetListData(InventoryReportQueryCondition condition)
        {
            //机密等级查询条件必填
            if (!condition.PaperConfidenLevel.Any())
                return await Task.FromResult(new ApiResultModelByObject()
                {
                    messageContent = new List<string>() {
                        string.Format(ActionFilter.GetMultilingualValue("custom:messageContent:searchreQuired"),ActionFilter.GetMultilingualValue("InventoryReport_PaperConfidenLevel",true)),
                    },
                    messageType = MessageTypeUtils.Warning.ToString(),
                });
            //入库状态查询条件必填
            if (!condition.PaperEntryStatus.Any())
                return await Task.FromResult(new ApiResultModelByObject()
                {
                    messageContent = new List<string>() {
                        string.Format(ActionFilter.GetMultilingualValue("custom:messageContent:searchreQuired"),ActionFilter.GetMultilingualValue("InventoryReport_PaperEntryStatus",true)),
                    },
                    messageType = MessageTypeUtils.Warning.ToString(),
                });
            //区域、我方主体必填一项
            if (!condition.Area.Any() && !condition.Entity.Any())
                return await Task.FromResult(new ApiResultModelByObject()
                {
                    messageContent = new List<string>() {
                        string.Format(ActionFilter.GetMultilingualValue("custom:messageContent:searchreQuired"),ActionFilter.GetMultilingualValue("InventoryReport_AreaOrEntity",true)),
                    },
                    messageType = MessageTypeUtils.Warning.ToString(),
                });
            ApiResultModelByObject apiResult = new ApiResultModelByObject()
            {
                listData = InventoryReportService.GetListData(condition),
                rtnSuccess = true,
            };
            InitLogRecord(log =>
            {
                log.Detail = InventoryReportService.GetLogDetail(condition);
            });
            return await Task.FromResult(apiResult);
        }
        /// <summary>
        /// 查询列表详情数据
        /// </summary>
        /// <param name="condition">参数</param>
        /// <returns></returns>
        [HttpPost]
        [Route("GetDetailListData")]
        public async Task<ApiResultModelByObject> GetDetailListData(DetailListCondition condition)
        {
            ApiResultModelByObject apiResult = new ApiResultModelByObject()
            {
                listData = InventoryReportService.GetDetailListData(condition),
                rtnSuccess = true,
            };
            return await Task.FromResult(apiResult);
        }
        /// <summary>
        /// 列表数据导出日志记录
        /// </summary>
        /// <param name="condition"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("ExportLog")]
        public async Task<ApiResultModelByObject> ExportLog(InventoryReportQueryCondition condition)
        {
            ApiResultModelByObject apiResult = new ApiResultModelByObject()
            {
                listData = true,
                rtnSuccess = true,
            };
            InitLogRecord(log =>
            {
                log.Detail = InventoryReportService.GetLogDetail(condition);
            });
            return await Task.FromResult(apiResult);
        }
    }
}
