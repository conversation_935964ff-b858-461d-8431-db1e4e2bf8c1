﻿using Elegal.Flow.Api.Repository.Application;
using Elegal.Flow.Api.Services.Individual;
using Elegal.Flow.Common.Repository.FlowStep;
using Elegal.Flow.Common.Services;
using Elegal.Flow.Common.Services.FlowStep;
using Elegal.Flow.Common.Services.FormApply;
using Elegal.Interface.Api.Common.Control.Filter;
using Elegal.Interface.Api.Common.FuncHelper;
using Elegal.Interface.Api.Common.FuncService;
using Elegal.Interface.Api.Common.GetLogCommon;
using Elegal.Interface.Api.Common.Model.Enum;
using Elegal.Interface.Api.Common.Model.ParaModel.FlowApi;
using Elegal.Interface.Api.Common.Model.ResultModel;
using Elegal.Interface.Api.Common.Model.ResultModel.FlowApi.FlowStep;
using Elegal.Interface.Api.Common.Model.ViewModel.FlowApi;
using Elegal.Interface.ApiData.Service.FuncService;
using Elegal.Interface.ApiData.Service.Model.Condition;
using Elegal.Interface.ApiData.Service.Model.DbModel;
using Elegal.Interface.ApiData.Service.Model.QueryCondition;
using Elegal.Orm;
using Elegal.Orm.Dtos;
using Elegal.Orm.Utils;
using System.Text;
#nullable disable
namespace Elegal.Flow.Api.Services.Application
{
    /// <summary>
    /// 合約申請 -> 合約管理作業
    /// </summary>
    public class ContractOriginalArchiveService
    {
        private static string time_zone = AppSettingHelper.GetValue("TimeZone");
        private static ContractOriginalArchiveRepository _repository = new ContractOriginalArchiveRepository();
        private static readonly FlowStepRepository _flowRepository = new FlowStepRepository();

        #region 基本資訊查詢
        /// <summary>
        /// 基本資訊查詢
        /// </summary>
        /// <param name="applyNumber"></param>
        /// <returns></returns>
        public static ContractValidityModel GetBaseInformation(string applyNumber)
        {
            FormApplication formApplication = FormApplicationDataService.FindByKey(applyNumber);
            OriginalArchiveRecord originalArchiveRecord = OriginalArchiveRecordDataService.FindByKey(applyNumber);
            PaperApplicationData paperApplicationData = PaperApplicationDataDataService.Find(new PaperApplicationDataCondition() { ApplyNumber = applyNumber });
            List<string> list = ["dateOptions", "effDateOptions", "formType", "capitalContractStep", "acknowledgeStep", "hrContractStep", "generalContractStep", "spInviteByPic", "spInviteByLegal", "rdContractStep", "otherAContractStep", "otherBContractStep", "otherCContractStep", "otherDContractStep", "otherEContractStep", "otherFContractStep", "otherGContractStep", "geContractStep"];
            var sysParams = SysParametersService.GetParameters(list);
            var flowStepSigners = DbAccess.Query<FlowStepSigner>(new FlowStepSignerCondition { ApplyNumber = applyNumber });
            //当关签核人员
            var users = new List<PsSubEeLglVwA>();
            if (flowStepSigners.Length > 0)
                users = DbAccess.Query<PsSubEeLglVwA>(new PsSubEeLglVwAQueryCondition()
                {
                    SearchItemGroup = new SearchItemGroup()
                    {
                        Items = new List<SearchItem>()
                    {
                        new SearchItem()
                        {
                            Compare = CompareOperator.ARRAYIN,
                            Field = "emplid",
                            Values =  flowStepSigners.Select(s=>s.SignerEmplid)
                        }
                    }
                    }
                }).ToList();
            //加签签核人员
            var flowStepSignerInvitees = DbAccess.Query<FlowStepSignerInvitee>(new FlowStepSignerInviteeCondition { ApplyNumber = applyNumber });
            flowStepSignerInvitees = flowStepSignerInvitees.Where(w => w.SpInviteLevel.Equals(flowStepSignerInvitees.OrderBy(b => b.SpInviteLevel).FirstOrDefault().SpInviteLevel)).ToArray();
            var inviteeUsers = new List<PsSubEeLglVwA>();
            if (flowStepSignerInvitees.Length != 0)
                inviteeUsers = DbAccess.Query<PsSubEeLglVwA>(new PsSubEeLglVwAQueryCondition()
                {
                    SearchItemGroup = new SearchItemGroup()
                    {
                        Items =
                        [
                            new()
                            {
                                Compare = CompareOperator.ARRAYIN,
                                Field = "emplid",
                                Values =  flowStepSignerInvitees.Select(s=>s.InviteeEmplid)
                            }
                        ]
                    }
                }).ToList();
            var fill = PsSubEeLglVwADataService.FindByKey(formApplication?.FillEmplid);//填单人
            var handler = PsSubEeLglVwADataService.FindByKey(formApplication?.PicEmplid);//经办人
            var current = PsSubEeLglVwADataService.FindByKey(formApplication?.IncumbentEmplid);//现任联络人
            var legal = PsSubEeLglVwADataService.FindByKey(formApplication?.LegalAffairsEmplid);//承办法务

            #region Issue：238 -> 列印時，會簽關卡需要特殊處理
            string currStepName = sysParams.FirstOrDefault(f => f.FuncCode.Equals(flowStepSigners.FirstOrDefault()?.StepId.ToString()))?.FunName;
            if (Convert.ToInt32(flowStepSigners.FirstOrDefault()?.StepId.ToString()) == _flowRepository.GetAcknowledgeStepid(Convert.ToInt32(flowStepSigners.FirstOrDefault()?.FlowId.ToString()))
                    && flowStepSigners.FirstOrDefault()?.AcknowledgeStep != null)
            {
                currStepName = sysParams.FirstOrDefault(f => f.FuncCode.Equals(flowStepSigners.FirstOrDefault()?.AcknowledgeStep.ToString()))?.FunName;
            }
            #endregion

            ContractValidityModel result = new()
            {
                ApplyNumber = applyNumber,
                ApplyDate = formApplication?.ApplyTime,
                IsFirstTime = originalArchiveRecord == null,
                HasPaper = paperApplicationData?.HavingPaper.ToString(),
                HasPaperDetail = paperApplicationData?.IsPaperDetail.ToString(),
                MyEntity = formApplication?.EntityId,
                PartyA = formApplication?.OtherParty,
                ContractName = formApplication?.ContractName,
                ContractNumber = formApplication?.OtherPartyNumber,
                PicDeptid = formApplication?.PicDeptid,
                FormType = formApplication?.FormType,
                ConfidenLevel = formApplication?.ConfidenLevel,
                FormTypeName = sysParams.Where(w => w.ParaCode.Equals("formType") && w.FuncCode.Equals(formApplication.FormType)).FirstOrDefault()?.FunName,
                FillEmplid = formApplication?.FillEmplid,
                FillCname = fill?.Name,
                FillEname = fill?.NameA,
                FillMail = fill?.EmailAddressA,
                fillTermination = fill?.Termination,//CR：385
                HandlerEmplid = formApplication?.PicEmplid,
                HandlerCname = handler?.Name,
                HandlerEname = handler?.NameA,
                HandlerMail = handler?.EmailAddressA,
                handlerTermination = handler?.Termination,//CR：385
                CurrentContactPerson = formApplication?.IncumbentEmplid,
                CurrentContactPersonCname = current?.Name,
                CurrentContactPersonEname = current?.NameA,
                currentContactPersonTermination = current?.Termination,//CR：385
                LegalEmplid = formApplication?.LegalAffairsEmplid,
                LegalCname = legal?.Name,
                LegalEname = legal?.NameA,
                legalTermination = legal?.Termination,//CR：385
                applicationState = formApplication?.ApplicationState,
                //Issue：238 -> 列印時，會簽關卡需要特殊處理
                currStepName = currStepName,
                shouldSigners = string.Join(", ", users.Select(u => u.NameA)),
                specialInviteeUser = string.Join(", ", inviteeUsers.Select(u => u.NameA)),
                PaperBasicDatas = paperApplicationData == null ? [] : PaperBasicDataDataService.Query(new PaperBasicDataQueryCondition() { PaperApplicaId = paperApplicationData?.ApplicationId }).Select(CommonUtil.Map<PaperBasicData, Elegal.Interface.ApiData.Service.Model.ViewModel.PaperBasicDataViewModel>).ToList(),
                SysUploadFiles = GetPaperFile(applyNumber),
                FlowContractHistorys = GetContractHistoryData(applyNumber),
                ArchiveRecordsResults = _repository.ArchiveRecords(applyNumber).ToList(),
                //CR：312 -> 新增欄位【合約管理作業注意事項】
                contract_task_remark = _repository.GetContractTaskRemark(applyNumber),
            };
           
            #region 第1层级签核(申請人/經辦人/現任聯絡人)
            FlowStepSignerInvitee flowStepSignerInvitee = flowStepSignerInvitees.FirstOrDefault(f => f.SpInviteLevel.Equals(1));
            if (flowStepSignerInvitee != null)
            {
                result.inviteeTask = string.Join(",", flowStepSignerInvitee.PicSpReason.Split(",").Select(s => sysParams.Where(w => w.ParaCode.Equals("spInviteByPic")).FirstOrDefault(f => f.FuncCode.Equals(s)).FunName));
                result.inviteeReason = flowStepSignerInvitee.PicSpReamrk;
            }
            else
            {
                #region 第2层级签核(法務)
                flowStepSignerInvitee = flowStepSignerInvitees.FirstOrDefault(f => f.SpInviteLevel.Equals(2));
                if (flowStepSignerInvitee != null)
                {
                    result.inviteeTask = string.Join(",", flowStepSignerInvitee.LegalSpReason.Split(",").Select(s => sysParams.Where(w => w.ParaCode.Equals("spInviteByLegal")).FirstOrDefault(f => f.FuncCode.Equals(s)).FunName));
                    result.inviteeReason = flowStepSignerInvitee.LegalSpReamrk;
                }
                #endregion
            }
            #endregion
            #region 效期确认
            var obj = _repository.GetContractValidityInformation(applyNumber);
            if (obj != null)
            {
                result.SignDate = obj.ConfirmSignDate;
                result.ExpDate = obj.ConfirmExpDate;
                result.EffDate = obj.ConfirmEffDate;
                result.ConfirmEffDate = obj.ConfirmEffDate;
                result.OtherEffDate = obj.OtherEffDate;
                result.ConfirmSignDate = obj.ConfirmSignDate;
                result.OtherSignDate = obj.OtherSignDate;
                result.ConfirmExpDate = obj.ConfirmExpDate;
                result.OtherExpDate = obj.OtherExpDate;
                result.ConfirmEffDateType = obj.ConfirmEffDateType;
                result.ConfirmExpDateType = obj.ConfirmExpDateType;
                result.ConfirmSignDateType = obj.ConfirmSignDateType;
                result.ConfirmEffDateTypeName = sysParams.FirstOrDefault(f => f.ParaCode.Equals("effDateOptions") && f.FuncCode.Equals(obj.ConfirmEffDateType))?.FunName;
                result.ConfirmSignDateTypeName = sysParams.FirstOrDefault(f => f.ParaCode.Equals("dateOptions") && f.FuncCode.Equals(obj.ConfirmSignDateType))?.FunName;
                result.ConfirmExpDateTypeName = sysParams.FirstOrDefault(f => f.ParaCode.Equals("dateOptions") && f.FuncCode.Equals(obj.ConfirmExpDateType))?.FunName;
                result.HasExpExtend = obj.HasExpExtend;
                result.confirmHasExpExtend = obj.HasExpExtend;
            }
            obj = _repository.GetContractValidityInformationRecord(applyNumber);
            if (obj != null)
            {
                result.ConfirmEffDate = obj?.ConfirmEffDate;
                result.ConfirmSignDate = obj?.ConfirmSignDate;
                result.ConfirmExpDate = obj?.ConfirmExpDate;
            }
            if (originalArchiveRecord != null)
            {
                //0407 確認到期日 是否延展
                result.confirmHasExpExtend = originalArchiveRecord?.ConfirmHasExpExtend;
                result.OriginArchiveRemark = originalArchiveRecord?.OriginArchiveRemark;
                result.OriginArchiveType = originalArchiveRecord?.OriginArchiveType;
                result.OriginArchiveTime = originalArchiveRecord?.OriginArchiveDate;
            }
            #endregion
            #region 其他单绑定时提示信息
            result.OtherNotice = _repository.GetOtherNotice(applyNumber);
            #endregion
            TimeZoneInfoConvertHelper.ConvertObjectDateByUtc(result);
            return result;
        }
        #endregion

        #region 提交歸檔資訊
        /// <summary>
        /// 提交歸檔資訊
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public static ApiResultModelByObject SetOriginalArchive(ContractValidityModel model)
        {
            ApiResultModelByObject result = new ApiResultModelByObject();
            DbAccess.PerformInTransaction(dbContext =>
            {
                result.rtnSuccess = FormApplyService.HandleFormOriginalArchiveToTransaction(dbContext, model);
            });

            #region 发送邮件
            if (result.rtnSuccess)
            {
                //Convert.ToInt32(model.FormType == "1" ? "118" : (model.FormType == "2" ? "217" : "315"));
                int step = _flowRepository.GetContractMaTaskStepid(Convert.ToInt32(model.FormType));
                SendMailService.SendMail("C", model.ApplyNumber, MvcContext.UserInfo.current_emp, step, [], new List<MailTypeUtils> { MailTypeUtils.C11 });
            }
            #endregion

            ActionFilter.InitLogRecord(LogRecord => { 
                LogRecord.Detail = $"申请单号：{model.ApplyNumber}";
            });
            result.rtnSuccess = true;
            return result;
        }
        #endregion

        #region 獲取紙本資訊附檔
        /// <summary>
        /// 獲取紙本資訊附檔
        /// </summary>
        /// <param name="applyNumber"></param>
        /// <returns></returns>
        public static List<SysUploadFile> GetPaperFile(string applyNumber)
        {
            return [.. SysUploadFileDataService.Query(new SysUploadFileQueryCondition()
            {
                UploadType = 2,
                UploadKey = applyNumber,
                ArchivePurposes = 3,
                IsWatermake = (int)YesOrNoUtils.No
            })];
        }
        #endregion

        #region 保存特殊加簽設定
        /// <summary>
        /// 保存特殊加簽設定
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public static ApiResultModelByObject AddSpecialSignature(SpecialSignatureModel model)
        {
            ApiResultModelByObject result = new ApiResultModelByObject() { rtnSuccess = true };
            var user = MvcContext.UserInfo;
            FormApplication formApplication = FormApplicationDataService.FindByKey(model.ApplyNumber);//申请单信息
            if (formApplication == null)
                return new ApiResultModelByObject()
                {
                    listData = false,
                    rtnSuccess = false,
                    messageTitle = ActionFilter.GetMultilingualValue("Common_notFound", true),
                    messageType = MessageTypeUtils.Warning.ToString(),
                };
            int _flowId = Convert.ToInt32(formApplication.FormType);//合約申請流程ID(1:一般合約,2:資金合約,3:人力資源合約)
            var currentUser = PsSubEeLglVwADataService.FindByKey(user.current_emp);//操作者
            var loggingUser = PsSubEeLglVwADataService.FindByKey(user.logging_emp);//登录者
            var fill = PsSubEeLglVwADataService.FindByKey(formApplication.FillEmplid);//填单人
            var handler = PsSubEeLglVwADataService.FindByKey(formApplication.PicEmplid);//经办人
            var legal = PsSubEeLglVwADataService.FindByKey(formApplication.LegalAffairsEmplid);//承办法务
            var incumbent = PsSubEeLglVwADataService.FindByKey(formApplication.IncumbentEmplid);//现任联络人
            var sysParameters = SysParametersDataService.Query(new SysParametersQueryCondition()
            {
                LangType = user.logging_locale,
                SearchItemGroup = new SearchItemGroup()
                {
                    Items = new List<SearchItem>() { new SearchItem() { Field = "para_code", Values = ["spInviteSetting", "spInviteByPic", "spInviteByLegal"], Compare = CompareOperator.ARRAYIN } }
                }
            });//参数
            var flowStepSignerList = DbAccess.Query<FlowStepSigner>(new FlowStepSignerQueryCondition() { ApplyNumber = model.ApplyNumber });//当关签核人
            if (flowStepSignerList.Any(a => a.IsInvitee.Equals((int)YesOrNoUtils.Yes)))
                return new ApiResultModelByObject()
                {
                    listData = false,
                    rtnSuccess = false,
                    messageTitle = ActionFilter.GetMultilingualValue("Common_repetAction", true),
                    messageType = MessageTypeUtils.Warning.ToString(),
                };
            var flowStepSigner = flowStepSignerList.FirstOrDefault(f => f.SignerEmplid.Equals(currentUser.Emplid));//当前操作者是否是当关者，若不是，则为代理人
            var shouldSigner = currentUser;//应签核人
            if (flowStepSigner == null)//代理人
            {
                var agentList = SysAgentService.GetBeAgentListByEmplid(user.current_emp);
                var list = agentList.Where(w => flowStepSignerList.FirstOrDefault(f => f.SignerEmplid.Equals(w.AgentEmpid)) != null);
                shouldSigner = PsSubEeLglVwADataService.FindByKey(flowStepSignerList.FirstOrDefault()?.SignerEmplid);
            }
            //Issue：185 合約申請特殊加簽通知 必要參數
            bool isSendMail = false;
            List<MailRecipientResultModel> inviteSigner = new List<MailRecipientResultModel>();
            StringBuilder stepOpinion = new StringBuilder();
            DbAccess.PerformInTransaction(dbContext =>
            {
                FlowStepSignerInvitee entity = new FlowStepSignerInvitee();
                //合约管理作业历程
                FlowContractHistory flowContractHistory = new FlowContractHistory()
                {
                    FlowId = _flowId,
                    ApplyNumber = model.ApplyNumber,
                    StepId = flowStepSignerList.FirstOrDefault()?.StepId,
                    ApplySequence = 1,
                    StepAction = 18,
                    ActualSignerEmplid = currentUser.Emplid,
                    ActualSignerName = currentUser.Name,
                    ActualSignerNameA = currentUser.NameA,
                    ActualSignerDeptid = currentUser.Deptid,
                    ShouldSignerEmplid = shouldSigner.Emplid,
                    ShouldSignerName = shouldSigner.Name,
                    ShouldSignerNameA = shouldSigner.NameA,
                    ShouldSignerDeptid = shouldSigner.Deptid,
                    ContractOptions = "特殊加簽",
                    CreateTime = DateTime.UtcNow
                };
                stepOpinion.AppendLine($"{ActionFilter.GetMultilingualValue("Common_order", true)}：{sysParameters.Where(w => w.ParaCode.Equals("spInviteSetting")).FirstOrDefault(f => f.FuncCode.Equals(model.SignatureSetting))?.FunName}");
                //特殊加签设定类型区分
                switch (model.SignatureSetting)
                {
                    //申請人/經辦人/現任聯絡人
                    case "01":
                        entity = new FlowStepSignerInvitee
                        {
                            ApplyNumber = model.ApplyNumber,
                            FlowId = _flowId,
                            StepId = flowStepSignerList.FirstOrDefault()?.StepId,
                            CreateUser = user.current_emp,
                            InviteeType = 2,//1：一般加簽；2：特殊加簽
                            PicSpReason = string.Join(",", model.SignatureResonHandler),
                            PicSpReamrk = model.SignatureRemarkHandler,
                            SpInviteLevel = 1,
                            SpInviteeSetting = "01"
                        };
                        if (!string.IsNullOrEmpty(formApplication.IncumbentEmplid))
                        {
                            entity.InviteeEmplid = incumbent.Emplid;
                            entity.InviteeDeptid = incumbent.Deptid;
                            dbContext.Create(entity);
                            //將加簽人員添加到特定集合中 Issue：185
                            inviteSigner.Add(new MailRecipientResultModel
                            {
                                recipient_emplid = incumbent.Emplid,
                                recipient_deptid = incumbent.Deptid
                            });
                            stepOpinion.AppendLine($"申請人/經辦人/現任聯絡人:{string.Join("/", incumbent.NameA)}");
                        }
                        else
                        {
                            entity.InviteeEmplid = fill.Emplid;
                            entity.InviteeDeptid = fill.Deptid;
                            dbContext.Create(entity);
                            //將加簽人員添加到特定集合中 Issue：185
                            inviteSigner.Add(new MailRecipientResultModel
                            {
                                recipient_emplid = fill.Emplid,
                                recipient_deptid = fill.Deptid
                            });
                            if (!formApplication.FillEmplid.Equals(formApplication.PicEmplid))
                            {
                                entity.InviteeEmplid = handler.Emplid;
                                entity.InviteeDeptid = handler.Deptid;
                                dbContext.Create(entity);
                                //將加簽人員添加到特定集合中 Issue：185
                                inviteSigner.Add(new MailRecipientResultModel
                                {
                                    recipient_emplid = handler.Emplid,
                                    recipient_deptid = handler.Deptid
                                });
                            }
                            stepOpinion.AppendLine($"申請人/經辦人/現任聯絡人:{string.Join("/", handler.NameA)}");
                        } 
                        if (model.SignatureResonHandler.Count > 0)
                            stepOpinion.AppendLine($"{ActionFilter.GetMultilingualValue("Common_specialWork1", true)} ：{string.Join("、", model.SignatureResonHandler.Select(s => sysParameters.Where(w => w.ParaCode.Equals("spInviteByPic")).FirstOrDefault(f => f.FuncCode.Equals(s))?.FunName))}");
                        if (!string.IsNullOrEmpty(model.SignatureRemarkHandler)) stepOpinion.AppendLine($"{ActionFilter.GetMultilingualValue("Common_specialInviteRemark1", true)}：{model.SignatureRemarkHandler}");
                        break;
                    //法務
                    case "02":
                        entity = new FlowStepSignerInvitee
                        {
                            ApplyNumber = model.ApplyNumber,
                            FlowId = _flowId,
                            StepId = flowStepSignerList.FirstOrDefault()?.StepId,
                            CreateUser = user.current_emp,
                            InviteeType = 2,//1：一般加簽；2：特殊加簽
                            LegalSpReason = string.Join(",", model.SignatureResonLegal),
                            LegalSpReamrk = model.SignatureRemarkLegal,
                            InviteeEmplid = legal?.Emplid,
                            InviteeDeptid = legal?.Deptid,
                            SpInviteLevel = 2,
                            SpInviteeSetting = "02"
                        };
                        dbContext.Create(entity);
                        //將加簽人員添加到特定集合中 Issue：185
                        inviteSigner.Add(new MailRecipientResultModel
                        {
                            recipient_emplid = legal?.Emplid,
                            recipient_deptid = legal?.Deptid
                        });
                        if (legal != null)
                            stepOpinion.AppendLine($"承辦法務: {legal.NameA}");
                        if (model.SignatureResonLegal.Count > 0)
                            stepOpinion.AppendLine($"{ActionFilter.GetMultilingualValue("Common_specialWork2", true)}：{string.Join("、", model.SignatureResonLegal.Select(s => sysParameters.Where(w => w.ParaCode.Equals("spInviteByLegal")).FirstOrDefault(f => f.FuncCode.Equals(s))?.FunName))}");
                        if (!string.IsNullOrEmpty(model.SignatureRemarkLegal)) stepOpinion.AppendLine($"{ActionFilter.GetMultilingualValue("Common_specialInviteRemark2", true)}：{model.SignatureRemarkLegal}");
                        break;
                    //申請人/經辦人/現任聯絡人 > 法務
                    case "03":
                        entity = new FlowStepSignerInvitee
                        {
                            ApplyNumber = model.ApplyNumber,
                            FlowId = _flowId,
                            StepId = flowStepSignerList.FirstOrDefault()?.StepId,
                            CreateUser = user.current_emp,
                            InviteeType = 2,//1：一般加簽；2：特殊加簽
                            PicSpReason = string.Join(",", model.SignatureResonHandler),
                            PicSpReamrk = model.SignatureRemarkHandler,
                            SpInviteLevel = 1,
                            SpInviteeSetting = "03"
                        };
                        if (!string.IsNullOrEmpty(formApplication.IncumbentEmplid))
                        {
                            entity.InviteeEmplid = incumbent.Emplid;
                            entity.InviteeDeptid = incumbent.Deptid;
                            dbContext.Create(entity);
                            //將加簽人員添加到特定集合中 Issue：185
                            inviteSigner.Add(new MailRecipientResultModel
                            {
                                recipient_emplid = incumbent.Emplid,
                                recipient_deptid = incumbent.Deptid
                            });
                            stepOpinion.AppendLine($"申請人/經辦人/現任聯絡人:{string.Join("/", incumbent.NameA)}");
                        }
                        else
                        {
                            entity.InviteeEmplid = fill.Emplid;
                            entity.InviteeDeptid = fill.Deptid;
                            dbContext.Create(entity);
                            //將加簽人員添加到特定集合中 Issue：185
                            inviteSigner.Add(new MailRecipientResultModel
                            {
                                recipient_emplid = fill.Emplid,
                                recipient_deptid = fill.Deptid
                            });
                            if (!formApplication.FillEmplid.Equals(formApplication.PicEmplid))
                            {
                                entity.InviteeEmplid = handler.Emplid;
                                entity.InviteeDeptid = handler.Deptid;
                                dbContext.Create(entity);
                                //將加簽人員添加到特定集合中 Issue：185
                                inviteSigner.Add(new MailRecipientResultModel
                                {
                                    recipient_emplid = handler.Emplid,
                                    recipient_deptid = handler.Deptid
                                });
                            }
                            stepOpinion.AppendLine($"申請人/經辦人/現任聯絡人:{string.Join("/", handler.NameA)}");
                        }
                        entity = new FlowStepSignerInvitee
                        {
                            ApplyNumber = model.ApplyNumber,
                            FlowId = _flowId,
                            StepId = flowStepSignerList.FirstOrDefault()?.StepId,
                            CreateUser = user.current_emp,
                            InviteeType = 2,//1：一般加簽；2：特殊加簽
                            LegalSpReason = string.Join(",", model.SignatureResonLegal),
                            LegalSpReamrk = model.SignatureRemarkLegal,
                            InviteeEmplid = legal?.Emplid,
                            InviteeDeptid = legal?.Deptid,
                            SpInviteLevel = 2,
                            SpInviteeSetting = "03"
                        };
                        dbContext.Create(entity);
                        //將加簽人員添加到特定集合中 Issue：185
                        inviteSigner.Add(new MailRecipientResultModel
                        {
                            recipient_emplid = legal?.Emplid,
                            recipient_deptid = legal?.Deptid
                        });
                        if (model.SignatureResonHandler.Count > 0)
                            stepOpinion.AppendLine($"{ActionFilter.GetMultilingualValue("Common_specialWork1", true)}：{string.Join("、", model.SignatureResonHandler.Select(s => sysParameters.Where(w => w.ParaCode.Equals("spInviteByPic")).FirstOrDefault(f => f.FuncCode.Equals(s))?.FunName))}");
                        if (!string.IsNullOrEmpty(model.SignatureRemarkHandler)) stepOpinion.AppendLine($"{ActionFilter.GetMultilingualValue("Common_specialInviteRemark1", true)}：{model.SignatureRemarkHandler}");
                        if (legal != null)
                            stepOpinion.AppendLine($"承辦法務: {legal.NameA}");
                        if (model.SignatureResonLegal.Count > 0)
                            stepOpinion.AppendLine($"{ActionFilter.GetMultilingualValue("Common_specialWork2", true)}：{string.Join("、", model.SignatureResonLegal.Select(s => sysParameters.Where(w => w.ParaCode.Equals("spInviteByLegal")).FirstOrDefault(f => f.FuncCode.Equals(s))?.FunName))}");
                        if (!string.IsNullOrEmpty(model.SignatureRemarkLegal)) stepOpinion.AppendLine($"{ActionFilter.GetMultilingualValue("Common_specialInviteRemark2", true)}：{model.SignatureRemarkLegal}");
                        break;
                }
                flowContractHistory.StepOpinion = stepOpinion.ToString();//特殊加签时签核意见
                dbContext.Create(flowContractHistory);
                var flowStepSigners = dbContext.Query<FlowStepSigner>(new FlowStepSignerQueryCondition() { ApplyNumber = model.ApplyNumber, StepId = flowStepSignerList.FirstOrDefault()?.StepId });
                dbContext.BatchUpdate(flowStepSigners.Select(s => { s.IsInvitee = 1; return s; }).ToArray());
                //加簽者工號
                dbContext.Update(new FormApplication() { ApplyNumber = model.ApplyNumber, Signatory = user.current_emp });
                isSendMail = true;
            });
            //Issue：185 -> 合約申請特殊加簽通知
            if (isSendMail)
            {
                SendMailService.SendMail("C", model.ApplyNumber, MvcContext.UserInfo.current_emp, (int)(flowStepSignerList.FirstOrDefault()?.StepId), [], new List<MailTypeUtils> { MailTypeUtils.C13 }, inviteSigner);
            }
            ActionFilter.InitLogRecord(log =>
            {
                log.ControlName = log.ControlName.Replace("SpecialInvite", ActionFilter.GetMultilingualValue("Common_specialInvite", true));
                log.Detail = $"申请单号：{model.ApplyNumber}\r\n" + stepOpinion.ToString();

            });
            return result;
        }
        #endregion

        #region 刪除特殊加簽人員
        /// <summary>
        /// 刪除特殊加簽人員
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public static ApiResultModel DeleteSpecialSignature(DeleteSpecialSignatureModel model)
        {
            var user = MvcContext.UserInfo;
            FormApplication formApplication = FormApplicationDataService.FindByKey(model.ApplyNumber);//申请单信息
            var currentUser = PsSubEeLglVwADataService.FindByKey(user.current_emp);//操作者
            var flowStepSignerList = DbAccess.Query<FlowStepSigner>(new FlowStepSignerQueryCondition() { ApplyNumber = model.ApplyNumber });//当关签核人
            var flowStepSigner = flowStepSignerList.FirstOrDefault(f => f.SignerEmplid.Equals(currentUser.Emplid));//当前操作者是否是当关者，若不是，则为代理人
            var shouldSigner = currentUser;//应签核人
            if (flowStepSigner == null)//代理人
            {
                var agentList = SysAgentService.GetBeAgentListByEmplid(user.current_emp);
                var list = agentList.Where(w => flowStepSignerList.FirstOrDefault(f => f.SignerEmplid.Equals(w.AgentEmpid)) != null);
                shouldSigner = PsSubEeLglVwADataService.FindByKey(flowStepSignerList.FirstOrDefault()?.SignerEmplid);
            }
            //Issue：185 -> 取消加簽必要參數
            bool isSendMail = false;
            List<MailRecipientResultModel> inviteSigner = new List<MailRecipientResultModel>();
            StringBuilder stringBuilder = new StringBuilder();
            DbAccess.PerformInTransaction(dbContext =>
            {
                var oldDatas = dbContext.Query<FlowStepSignerInvitee>(new FlowStepSignerInviteeQueryCondition()
                {
                    SearchItemGroup = new SearchItemGroup()
                    {
                        Items = new List<SearchItem>()
                        {
                            new SearchItem()
                            {
                                Compare = CompareOperator.ARRAYIN,
                                Values =model.Rowids.Select(s=>s.ToString()).ToArray(),
                                Field = "rowid"
                            }
                        }
                    }
                });
                var handle = oldDatas.Where(w => w.SpInviteLevel == 1);
                var legal = oldDatas.Where(w => w.SpInviteLevel == 2);
                //删除加签人
                dbContext.BatchDeleteSql<FlowStepSignerInvitee, int?>([.. model.Rowids]);
                //合约管理作业历程(簽核人只顯示英文名)
                
                if (handle.Any())
                {
                    //將刪除的加簽人員添加到特定集合中  Issue：185
                    foreach (FlowStepSignerInvitee dsi in handle)
                    {
                        inviteSigner.Add(new MailRecipientResultModel
                        {
                            recipient_emplid = dsi.InviteeEmplid,
                            recipient_deptid = dsi.InviteeDeptid
                        });
                    }
                    stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("Common_spInvite1", true)}：{string.Join(",", handle.Select(s => dbContext.FindByKey<PsSubEeLglVwA, string>(s.InviteeEmplid)).Select(s => $"{s.NameA}"))}");
                }

                if (legal.Any())
                {
                    //將刪除的加簽人員添加到特定集合中  Issue：185
                    foreach (FlowStepSignerInvitee dsi in legal)
                    {
                        inviteSigner.Add(new MailRecipientResultModel
                        {
                            recipient_emplid = dsi.InviteeEmplid,
                            recipient_deptid = dsi.InviteeDeptid
                        });
                    }
                    stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("Common_spInvite2", true)}：{string.Join(",", legal.Select(s => dbContext.FindByKey<PsSubEeLglVwA, string>(s.InviteeEmplid)).Select(s => $"{s.NameA}"))}");
                }
                FlowContractHistory flowContractHistory = new FlowContractHistory()
                {
                    FlowId = Convert.ToInt32(formApplication.FormType),
                    ApplyNumber = model.ApplyNumber,
                    StepId = flowStepSignerList.FirstOrDefault()?.StepId,
                    ApplySequence = 1,
                    StepAction = 15,
                    ActualSignerEmplid = currentUser.Emplid,
                    ActualSignerName = currentUser.Name,
                    ActualSignerNameA = currentUser.NameA,
                    ActualSignerDeptid = currentUser.Deptid,
                    ShouldSignerEmplid = shouldSigner.Emplid,
                    ShouldSignerName = shouldSigner.Name,
                    ShouldSignerNameA = shouldSigner.NameA,
                    ShouldSignerDeptid = shouldSigner.Deptid,
                    ContractOptions = "取消特殊加簽",
                    CreateTime = DateTime.UtcNow,
                    StepOpinion = stringBuilder.ToString()
                };
                dbContext.Create(flowContractHistory);
                isSendMail = true;
            });
            if (DbAccess.Query<FlowStepSignerInvitee>(new FlowStepSignerInviteeQueryCondition() { ApplyNumber = model.ApplyNumber }).Length == 0)
            {
                var flowStepSigners = DbAccess.Query<FlowStepSigner>(new FlowStepSignerQueryCondition() { ApplyNumber = model.ApplyNumber, StepId = flowStepSignerList.FirstOrDefault()?.StepId, });
                DbAccess.BatchUpdate(flowStepSigners.Select(s => { s.IsInvitee = 0; return s; }).ToArray());
                //加簽者工號
                DbAccess.Update(new FormApplication() { ApplyNumber = model.ApplyNumber, Signatory = "" });
            }
            //Issue：185 -> 合約申請取消特殊加簽
            if (isSendMail)
            {
                SendMailService.SendMail("C", model.ApplyNumber, MvcContext.UserInfo.current_emp, (int)(flowStepSignerList.FirstOrDefault()?.StepId), [], new List<MailTypeUtils> { MailTypeUtils.C10 }, inviteSigner);
            }
            ActionFilter.InitLogRecord(log =>
            {
                log.ControlName = log.ControlName.Replace("CancelSpecialInvite", ActionFilter.GetMultilingualValue("Common_cancelSpecialInvite", true));
                log.Detail = $"申请单号：{model.ApplyNumber}\r\n" + stringBuilder.ToString(); ;
            });
            return new ApiResultModel() { rtnSuccess = true }; ;
        }
        #endregion

        #region 合約管理作業歷程顯示
        /// <summary>
        /// 合約管理作業歷程顯示
        /// </summary>
        /// <param name="applyNumber"></param>
        /// <returns></returns>
        public static List<FlowContractHistory> GetContractHistoryData(string applyNumber)
        {
            return FlowContractHistoryDataService.Query(new FlowContractHistoryQueryCondition()
            {
                ApplyNumber = applyNumber,
                OrderBys = [new OrderByParam() { Field = "create_time", Order = OrderBy.ASC }] //247  統一歷程類的顯示規則，將到序顯示(新在上)的都調整為正序顯示(舊在上) 4.合約管理作業>合約管理作業歷程 
            }).Select(s => { s.CreateTime = TimeZoneInfo.ConvertTimeFromUtc(s.CreateTime.Value, TimeZoneInfo.FindSystemTimeZoneById(MvcContext.UserInfo.time_zone)); return s; }).ToList();
        }
        #endregion

        #region 保存效期確認
        /// <summary>
        /// 保存效期確認
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public static ApiResultModelByObject SaveDate(ContractValidityDate model)
        {
            ApiResultModelByObject result = new ApiResultModelByObject();

            DbAccess.PerformInTransaction(dbContext =>
            {
                // 此方法只用与特殊加签处理，saveData 
                result.listData = FlowStepService.SaveDateToTransaction(dbContext, model, "C", "saveData").ToString();
                result.rtnSuccess = true;
            });

            return result;
        }
        #endregion
    }
}
