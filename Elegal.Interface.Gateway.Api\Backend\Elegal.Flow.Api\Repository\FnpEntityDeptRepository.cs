﻿using Elegal.Interface.Api.Common.Control.Filter;
using Elegal.Interface.Api.Common.Model.ParaModel.FlowApi;
using Elegal.Interface.Api.Common.Model.ViewModel.FlowApi;
using Elegal.Interface.Api.Common.Repository;
using Elegal.Orm;
using Elegal.Orm.Dtos;
using System.Data;
using System.Text;

namespace Elegal.Flow.Api.Repository
{
    /// <summary>
    /// 特殊主體設定 -> 部門設定
    /// </summary>
    public class FnpEntityDeptRepository : BaseRepository
    {
        private static FnpEntityRepository _fnpEntityRepository = new FnpEntityRepository();

        #region 查詢特殊主體
        /// <summary>
        /// 查詢特殊主体
        /// </summary>
        public PageResult<FnpEntityDeptModel> QueryDept(qryFnpEntityDept qry)
        {
            PageResult<FnpEntityDeptModel> result = new PageResult<FnpEntityDeptModel>();

            StringBuilder sql = new StringBuilder();
            sql.Append(@"SELECT DISTINCT 
                            og.deptid,
							og.descr COLLATE Chinese_Traditional_Pinyin_100_CI_AS as descr,
							og.descr_a,
                            og.company AS dept_company,
							(SELECT STUFF((SELECT ', ' + fee.entity
                                FROM (SELECT company,entity_id FROM fnp_ec_relation UNION SELECT company,entity_id FROM fnp_ac_relation) AS fear
                                INNER JOIN fnp_entity fee ON fear.entity_id = fee.entity_id 
                                WHERE fear.company = og.company
                                FOR XML PATH('')), 1, 1, '')) AS company_entity,
                            (SELECT STUFF((SELECT ', ' + CONCAT(fe.entity, '[dept]', 
                                            CASE WHEN b.open_date_start IS NULL AND b.open_date_end IS NULL 
                                            THEN ''
                                            ELSE CONCAT('(', ISNULL(CONVERT(VARCHAR(10), b.open_date_start, 111), 'NA'), '~', ISNULL(CONVERT(VARCHAR(10), b.open_date_end, 111), 'NA'), ')')
                                            END)
                                FROM fnp_entity_dept_special B 
								LEFT JOIN (SELECT entity_id, entity, entity_namec, entity_namee FROM fnp_entity UNION SELECT aff_company_code AS entity_id, aff_company_abb AS entity, aff_company_cname AS entity_namec, aff_company_ename AS entity_namee FROM affiliate_company) fe on B.entity_id = fe.entity_id
								where B.deptid = og.deptid AND b.is_delete = 0
								FOR XML PATH('')), 1, 1, '')) AS dept_entity,
                            ISNULL(ee_modified.name, ee_create.name) AS operate_cuser,
							ISNULL(ee_modified.name_a, ee_create.name_a) AS operate_euser,
		                    ISNULL(A.modify_time, A.create_time) AS operate_time
                          FROM ps_sub_og_lgl_vw_a og 
						  LEFT JOIN fnp_entity_dept A on A.deptid = og.deptid
                          LEFT JOIN ps_sub_ee_lgl_vw_a ee_create on ee_create.emplid = A.create_user
                          LEFT JOIN ps_sub_ee_lgl_vw_a ee_modified on ee_modified.emplid = A.modify_user ");

            StringBuilder csql = new StringBuilder();
            csql.Append(@"SELECT DISTINCT
                            count(1)
                          FROM ps_sub_og_lgl_vw_a og 
						  LEFT JOIN fnp_entity_dept A on A.deptid = og.deptid
                          LEFT JOIN ps_sub_ee_lgl_vw_a ee_create on ee_create.emplid = A.create_user
                          LEFT JOIN ps_sub_ee_lgl_vw_a ee_modified on ee_modified.emplid = A.modify_user ");

            StringBuilder condition = new StringBuilder();

            //特殊主体
            if (qry.entity != null && qry.entity.Count > 0)
            {
                condition.Append($@" INNER JOIN fnp_entity_dept_special fnds ON fnds.deptid = og.deptid AND fnds.is_delete = 0 AND fnds.entity_id IN ({string.Join(",", qry.entity.Select(s => $"'{s}'"))})");
            }

            condition.Append(@" WHERE (1=1) ");

            //部門代號
            if (!string.IsNullOrEmpty(qry.deptid))
            {
                condition.AppendLine(@" AND og.deptid LIKE CONCAT(@deptid, N'%') ");
            }

            csql.Append(condition.ToString());
            int totalCount = DbAccess.Database.SqlQuery<int>(csql.ToString(), new
            {
                deptid = qry.deptid
            }).FirstOrDefault();

            sql.Append(condition.ToString());
            if (qry.page == null || (qry.page.PageSize == 0 && qry.page.PageIndex == 0))
                qry.page = new PageParam() { PageIndex = 1, PageSize = 25 };

            string orderStr = string.Empty;
            if (qry.order == null || string.IsNullOrEmpty(qry.order.Field))
                orderStr = "deptid ASC, og.company ASC";
            else
                orderStr = qry.order.Field + " " + qry.order.Order.ToString();

            int offset = (qry.page.PageIndex - 1) * qry.page.PageSize;
            sql.Append(@$" ORDER BY {orderStr} OFFSET {offset} ROWS FETCH NEXT {qry.page.PageSize} ROWS ONLY");
            result.Data = DbAccess.Database.SqlQuery<FnpEntityDeptModel>(sql.ToString(),
            new
            {
                deptid = qry.deptid
            }).ToList();

            result.TotalRows = totalCount;
            return result;
        }

        #endregion

        public bool UpdateFnpEntityDept(string deptid, List<string> entity_id)
        {
            string sql_check = $@"SELECT COUNT(1) FROM fnp_entity_dept WHERE deptid = @deptid";
            bool isExist = this.NpgsqlSearchBySingle<int>(sql_check, new { deptid = deptid }) > 0;

            string entity_id_str = string.Join(",", entity_id);

            if (isExist)
            {
                string sql_update = $@"UPDATE fnp_entity_dept SET 
                               entity_id = @entity_id_str,
                               modify_user = @modify_user,
                               modify_time = getutcdate()
                            WHERE deptid = @deptid";
                return this.ExecuteCommand(sql_update, new { deptid = deptid, entity_id_str = entity_id_str, modify_user = MvcContext.UserInfo.current_emp }) > 0;
            }
            else
            {
                string sql_insert = $@"INSERT INTO fnp_entity_dept (
                               deptid,
                               entity_id,
                               create_user,
                               create_time)
                                VALUES ( 
                                @deptid,
                                @entity_id_str,
                                @create_user,
                                getutcdate())";
                return this.ExecuteCommand(sql_insert, new { deptid = deptid, entity_id_str = entity_id_str, create_user = MvcContext.UserInfo.current_emp }) > 0;
            }
        }

        public bool UpdateFnpEntityDeptSpecial(string deptid, List<string> entity_id_lsit)
        {
            string sql_delete = $@"UPDATE fnp_entity_dept_special SET 
                               is_delete = 1,
                               modify_user = @modify_user,
                               modify_time = getutcdate()
                            WHERE deptid = @deptid AND open_date_start is null AND open_date_end is null";
            this.ExecuteCommand(sql_delete, new { deptid = deptid, modify_user = MvcContext.UserInfo.current_emp });

            foreach (string entity_id in entity_id_lsit)
            {
                string sql_insert = $@"INSERT INTO fnp_entity_dept_special (
                               deptid,
                               entity_id,
                               create_user,
                               create_time,
                               is_delete)
                                VALUES ( 
                                @deptid,
                                @entity_id,
                                @create_user,
                                getutcdate(),
                                0)";
                this.ExecuteCommand(sql_insert, new { deptid = deptid, entity_id = entity_id, create_user = MvcContext.UserInfo.current_emp });
            }

            return true;
        }

        public List<string> DeleteFnpEntityDept(string entity_id)
        {
            string sql_special = $@"SELECT DISTINCT deptid FROM fnp_entity_dept_special WHERE is_delete = 0 AND entity_id = @entity_id ORDER BY deptid";
            List<string> deptList = this.NpgsqlSearchByList<string>(sql_special, new { entity_id = entity_id });

            foreach (string deptid in deptList)
            {
                string sql_entity = $@"SELECT entity_id FROM fnp_entity_dept WHERE deptid = @deptid";
                string dept_entity_id = this.NpgsqlSearchBySingle<string>(sql_entity, new { deptid = deptid });

                if (!string.IsNullOrEmpty(dept_entity_id))
                {
                    string[] dept_entity = dept_entity_id.Split(',');
                    List<string> new_entity = new List<string>();
                    foreach (string entity in dept_entity)
                    {
                        if (entity != entity_id)
                            new_entity.Add(entity);
                    }

                    string entity_id_str = string.Join(",", new_entity);

                    string sql_update = $@"UPDATE fnp_entity_dept SET 
                               entity_id = @entity_id_str,
                               modify_user = @modify_user,
                               modify_time = getutcdate()
                            WHERE deptid = @deptid";
                    this.ExecuteCommand(sql_update, new { deptid = deptid, entity_id_str = entity_id_str, modify_user = MvcContext.UserInfo.current_emp });
                }
            }

            string sql_delete = $@"UPDATE fnp_entity_dept_special SET 
                               is_delete = 1,
                               modify_user = @modify_user,
                               modify_time = getutcdate()
                            WHERE entity_id = @entity_id";
            this.ExecuteCommand(sql_delete, new { entity_id = entity_id, modify_user = MvcContext.UserInfo.current_emp });

            return deptList;
        }

        #region 託管人員公司
        /// <summary>
        /// 根據部門獲取託管人員公司別
        /// </summary>
        /// <param name="deptID"></param>
        /// <returns></returns>
        public List<string> GetHostCompany(string deptID)
        {
            string sql = string.Format(@"
                               SELECT DISTINCT company FROM (
                               {0}
                               ) AS fds WHERE deptid = @deptID;", _fnpEntityRepository.GetHostDeptEntity());

            return this.NpgsqlSearchByList<string>(sql, new { DeptID = deptID });
        }

        #endregion

        #region 託管部門主體
        /// <summary>
        /// 託管部門主體
        /// </summary>
        /// <param name="deptID"></param>
        /// <returns></returns>
        public List<string> GetHostEntity(string deptID)
        {
            string sql = string.Format(@"
                               SELECT STRING_AGG(entitys, ', ') AS custodyDeptEntity FROM (
                               SELECT STRING_AGG(entity, ', ') WITHIN GROUP (ORDER BY entity,company) AS entitys FROM (
                               SELECT DISTINCT company,entity FROM (
                               {0}
                               ) AS fds
                               WHERE [status] = N'0' AND deptid = @DeptID) AS ce GROUP BY company
                               ) AS ce;", _fnpEntityRepository.GetHostDeptEntity());

            return this.NpgsqlSearchByList<string>(sql, new { DeptID = deptID });
        }

        #endregion
    }
}
