﻿using Elegal.Flow.Api.Services.LendAppliction;
using Elegal.Interface.Api.Common.Control.Filter;
using Elegal.Interface.Api.Common.Control.Web;
using Elegal.Interface.Api.Common.FuncHelper;
using Elegal.Interface.Api.Common.Model.ParaModel.FlowApi.LendAppliction;
using Elegal.Interface.Api.Common.Model.ResultModel;
using Elegal.Interface.Api.Common.Model.ResultModel.FlowApi.LendAppliction;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using System.Text;

namespace Elegal.Flow.Api.Controllers.LendAppliction;

/// <summary>
/// 资料查询，我的借出，历史借出（查询用户自己的历史借出）【主表，子表，导出，检视】
/// </summary>
[Route("[controller]")]
[ApiController]
public class LendSearchHisForUserController : BaseController
{
    /// <summary>
    /// 查詢用戶的歷史借出
    /// </summary>
    /// <param name="para"></param>
    /// <returns></returns>
    [HttpPost]
    [Route("QueryLendHisForUser")]
    public async Task<ApiResultModelByObject> QueryLendHisForUser([FromBody] LendHisForUserPara para)
    {
        ApiResultModelByObject apiResult = new ApiResultModelByObject();
        List<LendUserHis> data = LendSearchHisForUserService.QueryLendHisForUser(para, MvcContext.UserInfo);
        apiResult.listData = data;
        apiResult.rtnSuccess = true;
        #region 記錄日誌
        InitLogRecord(para, log =>
        {
            StringBuilder logBuilder = new StringBuilder();
            logBuilder.AppendLine(string.Format(ActionFilter.GetMultilingualValue("LendSearch_currentUser", true), MvcContext.UserInfo.current_emp));
            if (!string.IsNullOrWhiteSpace(para.ContractNumber))
            {
                logBuilder.AppendLine(string.Format(ActionFilter.GetMultilingualValue("LendSearch_contractNumber", true), para.ContractNumber));
            }
            if (!string.IsNullOrWhiteSpace(para.ContractName))
            {
                logBuilder.AppendLine(string.Format(ActionFilter.GetMultilingualValue("LendSearch_contractName", true), para.ContractName));
            }
            if (para.PickupTime.Start.HasValue || para.ReturnTime.End.HasValue)
            {
                string d1 = string.Empty;
                string d2 = string.Empty;
                if (para.PickupTime.Start.HasValue)
                {
                    d1 = TimeZoneInfoConvertHelper.ConvertDateByTimeZoneByLocal(para.PickupTime.Start.Value, MvcContext.UserInfo.time_zone).ToString("yyyy/MM/dd");
                }
                if (para.PickupTime.End.HasValue)
                {
                    d2 = TimeZoneInfoConvertHelper.ConvertDateByTimeZoneByLocal(para.PickupTime.End.Value, MvcContext.UserInfo.time_zone).ToString("yyyy/MM/dd");
                }

                logBuilder.AppendLine(string.Format(ActionFilter.GetMultilingualValue("LendSearch_lendTakeTime", true), d1, d2));
            }

            if (para.ReturnTime.Start.HasValue || para.ReturnTime.End.HasValue)
            {
                string d1 = string.Empty;
                string d2 = string.Empty;
                if (para.ReturnTime.Start.HasValue)
                {
                    d1 = TimeZoneInfoConvertHelper.ConvertDateByTimeZoneByLocal(para.ReturnTime.Start.Value, MvcContext.UserInfo.time_zone).ToString("yyyy/MM/dd");
                }
                if (para.ReturnTime.End.HasValue)
                {
                    d2 = TimeZoneInfoConvertHelper.ConvertDateByTimeZoneByLocal(para.ReturnTime.End.Value, MvcContext.UserInfo.time_zone).ToString("yyyy/MM/dd");
                }

                logBuilder.AppendLine(string.Format(ActionFilter.GetMultilingualValue("LendSearch_returnTime", true), d1, d2));
            }

            if (!string.IsNullOrWhiteSpace(para.LendNumber))
            {
                logBuilder.AppendLine(string.Format(ActionFilter.GetMultilingualValue("LendSearch_lendNumber", true), para.LendNumber));
            }
            if (para.LendStatus.Count > 0)
            {
                List<string> LendStatuslist = para.LendStatus.Select(e =>
                {
                    if (e == "04") return "已結案";
                    else return "已作廢";
                }).ToList(); ;
                logBuilder.AppendLine(string.Format(ActionFilter.GetMultilingualValue("LendSearch_lendStatus", true), string.Join(",", LendStatuslist)));
            }
            log.Detail = logBuilder.ToString();
        });
        #endregion

        return await Task.FromResult<ApiResultModelByObject>(apiResult);
    }

    /// <summary>
    /// 查詢歷史借出的紙本借閲詳情
    /// </summary>
    /// <returns></returns>
    [HttpGet]
    [Route("QueryPaperLendHisDetailListForUser")]
    public async Task<ApiResultModelByObject> QueryPaperLendHisDetailListForUser(string lendNumber)
    {
        ApiResultModelByObject apiResult = new ApiResultModelByObject();

        var data = LendSearchHisForUserService.QueryPaperLendHisDetailListForUser(lendNumber, MvcContext.UserInfo);
        apiResult.listData = data;
        apiResult.totalCount = data.Count;
        apiResult.rtnSuccess = true;
        #region 記錄日誌
        InitLogRecord(lendNumber, log =>
        {
            log.Detail = "借出單號:" + lendNumber;
        });
        #endregion

        return await Task.FromResult<ApiResultModelByObject>(apiResult);
    }

    /// <summary>
    /// 我的借出，历史借出導出
    /// </summary>
    /// <param name="para"></param>
    /// <returns></returns>
    [HttpPost]
    [Route("QueryLendHisForUserExprt")]
    public FileContentResult QueryLendHisForUserExprt([FromBody] LendHisForUserExportPara para)
    {
        ApiResultModelByObject apiResult = new ApiResultModelByObject();
        var excelBytes = LendSearchHisForUserService.QueryLendHisForUserExprt(para, MvcContext.UserInfo);
        #region 記錄日誌
        InitLogRecord(para, log =>
        {
            StringBuilder logBuilder = new StringBuilder();
            logBuilder.AppendLine(string.Format(ActionFilter.GetMultilingualValue("LendSearch_currentUser", true), MvcContext.UserInfo.current_emp));
            if (!string.IsNullOrWhiteSpace(para.ContractNumber))
            {
                logBuilder.AppendLine(string.Format(ActionFilter.GetMultilingualValue("LendSearch_contractNumber", true), para.ContractNumber));
            }
            if (!string.IsNullOrWhiteSpace(para.ContractName))
            {
                logBuilder.AppendLine(string.Format(ActionFilter.GetMultilingualValue("LendSearch_contractName", true), para.ContractName));
            }
            if (para.PickupTime.Start.HasValue || para.ReturnTime.End.HasValue)
            {
                string d1 = string.Empty;
                string d2 = string.Empty;
                if (para.PickupTime.Start.HasValue)
                {
                    d1 = TimeZoneInfoConvertHelper.ConvertDateByTimeZoneByLocal(para.PickupTime.Start.Value, MvcContext.UserInfo.time_zone).ToString("yyyy/MM/dd");
                }
                if (para.PickupTime.End.HasValue)
                {
                    d2 = TimeZoneInfoConvertHelper.ConvertDateByTimeZoneByLocal(para.PickupTime.End.Value, MvcContext.UserInfo.time_zone).ToString("yyyy/MM/dd");
                }

                logBuilder.AppendLine(string.Format(ActionFilter.GetMultilingualValue("LendSearch_lendTakeTime", true), d1, d2));
            }

            if (para.ReturnTime.Start.HasValue || para.ReturnTime.End.HasValue)
            {
                string d1 = string.Empty;
                string d2 = string.Empty;
                if (para.ReturnTime.Start.HasValue)
                {
                    d1 = TimeZoneInfoConvertHelper.ConvertDateByTimeZoneByLocal(para.ReturnTime.Start.Value, MvcContext.UserInfo.time_zone).ToString("yyyy/MM/dd");
                }
                if (para.ReturnTime.End.HasValue)
                {
                    d2 = TimeZoneInfoConvertHelper.ConvertDateByTimeZoneByLocal(para.ReturnTime.End.Value, MvcContext.UserInfo.time_zone).ToString("yyyy/MM/dd");
                }

                logBuilder.AppendLine(string.Format(ActionFilter.GetMultilingualValue("LendSearch_returnTime", true), d1, d2));
            }

            if (!string.IsNullOrWhiteSpace(para.LendNumber))
            {
                logBuilder.AppendLine(string.Format(ActionFilter.GetMultilingualValue("LendSearch_lendNumber", true), para.LendNumber));
            }
            if (para.LendStatus.Count > 0)
            {
                List<string> LendStatuslist = para.LendStatus.Select(e =>
                {
                    if (e == "04") return "已結案";
                    else return "已作廢";
                }).ToList(); ;
                logBuilder.AppendLine(string.Format(ActionFilter.GetMultilingualValue("LendSearch_lendStatus", true), string.Join(",", LendStatuslist)));
            }
            log.Detail = logBuilder.ToString();
        });
        #endregion
        return File(excelBytes, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", MvcContext.UserInfo.current_name + "_歷史借出清單" + DateTime.UtcNow.ConvertDateByTimeZoneByUtc(MvcContext.UserInfo.time_zone).ToString("yyyyMMdd") + ".xlsx");
    }

    /// <summary>
    /// 借出历史信息檢視
    /// </summary>
    /// <param name="lendId">LendNumber </param>
    /// <returns></returns>
    [HttpGet]
    [Route("QueryLendHisDetailsViewForUser")]
    public async Task<ApiResultModelByObject> QueryLendHisDetailsViewForUser([FromQuery] string lendId)
    {
        ApiResultModelByObject apiResult = new ApiResultModelByObject();
        apiResult.listData = LendSearchHisForUserService.QueryLendHisDetailsViewForUser(lendId, MvcContext.UserInfo);
        apiResult.rtnSuccess = true;
        #region 記錄日誌
        InitLogRecord(lendId, log =>
        {
            log.Detail = "申請單號:" + lendId;
        });
        #endregion

        return await Task.FromResult<ApiResultModelByObject>(apiResult);
    }
}
