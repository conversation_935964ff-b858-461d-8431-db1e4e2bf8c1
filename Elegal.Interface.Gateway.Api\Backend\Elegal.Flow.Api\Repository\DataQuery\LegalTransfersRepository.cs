using Elegal.Interface.Api.Common.Control.Filter;
using Elegal.Interface.Api.Common.Model.ParaModel.FlowApi;
using Elegal.Interface.Api.Common.Model.ResultModel.FlowApi;
using Elegal.Interface.Api.Common.Repository;
using Elegal.Interface.ApiData.Service.Model.ViewModel;
using Elegal.Orm;
using System.Text;

namespace Elegal.Flow.Api.Repository.DataQuery;

/// <summary>
/// 承办法务转单
/// </summary>
public class LegalTransfersRepository : BaseRepository
{
    /// <summary>
    /// 实例化
    /// </summary>
    public LegalTransfersRepository() : base() { }

    /// <summary>
    /// 列表数据
    /// </summary>
    /// <returns></returns>
    public IEnumerable<UndertakeLegalTransferResultModel> ListData(TransferSearchModel model)
    {
        StringBuilder sqlBuilder = new();
        sqlBuilder.Append(@$"SELECT
                                appData.apply_number,--申請單號
                                appData.apply_time,--申請日期
                                appData.apply_form,--案件類型層級拼接
                                appData.apply_type,--申請單類型(一級)
                                appData.form_type,--申請單類型(二級)
                                fe.entity,--我方(簡稱)
                                appData.other_party,--他方
                                CASE WHEN appData.apply_type = N'O'
                            THEN (SELECT fun_name FROM dbo.sys_parameters WHERE para_code = N'formType_O' AND lang_type = N'{MvcContext.UserInfo.logging_locale}' AND func_code = CONVERT(NVARCHAR,appData.form_type))
                            ELSE appData.contract_name END AS contract_name,
                                appData.confiden_level,--機密等級，當數值=N'01'時，需要對[合約名稱]進行隱碼顯示
                                appData.application_state,--申請單狀態
                                (SELECT fun_name FROM dbo.sys_parameters WHERE para_code = N'confidentStatus' AND lang_type = '{MvcContext.UserInfo.logging_locale}' AND func_code =  appData.confiden_level) AS confiden_level_name,--機密等級名稱
                                tph.apply_legal_emplid as legal_affairs_emplid,--原承辦法務人員
                                olee.name AS legal_cuser,--原承辦法務人員中文名稱
                                olee.name_a AS legal_euser,--原承辦法務人員英文名稱
                                iif(olee.termination is null,0,1)  AS legal_cuser_IsResign ,--原承辦法務是否离职
                                tph.handover_emplid,--交接人
                                tee.name AS han_cuser,--交接人中文名稱
                                tee.name_a AS han_euser,--交接人英文名稱
                                iif(tee.termination is null,0,1)  AS han_euser_IsResign ,--交接人是否离职
                                tph.transfer_remarks,--備註
                                tph.create_user AS operate_user,--更新人
                                oee.name AS operate_cuser,--更新人
                                oee.name_a AS operate_euser,--更新人
                                tph.create_time AS operate_time--更新時間
                                FROM (
                                SELECT apply_number,apply_legal_emplid,handover_emplid,transfer_remarks,create_user,create_time FROM dbo.transfer_legal_history
                                WHERE 1 = 1 ");
        if (!string.IsNullOrEmpty(model.apply_number))
            sqlBuilder.Append(@$" AND apply_number LIKE CONCAT(N'%', @apply_number ,N'%')");
        if (!string.IsNullOrEmpty(model.emp?.Emplid))
            sqlBuilder.Append(@$" AND (apply_legal_emplid like CONCAT(N'%', @emplid ,N'%') OR handover_emplid LIKE CONCAT(N'%', @emplid ,N'%'))");
            //承辦法務轉單查詢時候去掉申請單狀態篩選20250317
        sqlBuilder.Append(@$" ) AS tph
                                LEFT JOIN (
                                SELECT apply_number,apply_form,form_type,apply_time,apply_type,entity_id,other_party,contract_name,confiden_level,application_state FROM dbo.v_getallapplication WHERE (apply_type = N'C' OR form_type = N'R' OR apply_type = N'O')
                                ) AS appData ON appData.apply_number = tph.apply_number
                                --原承辦法務
                                LEFT JOIN (SELECT emplid,name,name_a,termination FROM dbo.ps_sub_ee_lgl_vw_a) AS olee ON olee.emplid = tph.apply_legal_emplid
                                --交接人
                                LEFT JOIN (SELECT emplid,name,name_a,termination FROM dbo.ps_sub_ee_lgl_vw_a) AS tee ON tee.emplid = tph.handover_emplid
                                --創建者
                                LEFT JOIN (SELECT emplid,name,name_a FROM dbo.ps_sub_ee_lgl_vw_a) AS oee ON oee.emplid = tph.create_user
                                LEFT JOIN (SELECT entity,entity_id,status,entity_namec,entity_namee,group_entityid,1 AS entity_type FROM dbo.fnp_entity
                                UNION
                                SELECT aff_company_abb AS entity,aff_company_code AS entity_id,CASE WHEN aff_status = 1 THEN 0 ELSE 1 END AS status,aff_company_cname AS entity_namec,aff_company_ename AS entity_namee,aff_group_entity AS group_entityid,2 AS entity_type FROM dbo.affiliate_company) AS fe ON fe.entity_id = appData.entity_id
                                ORDER BY tph.create_time DESC;");
        return DbAccess.Database.SqlQuery<UndertakeLegalTransferResultModel>(sqlBuilder.ToString(), new { model.apply_number, emplid = model.emp?.Emplid });
    }

    /// <summary>
    /// 案件清单
    /// </summary>
    /// <param name="emplid">工号</param>
    /// <param name="applyNumber">申请单号</param>
    /// <returns></returns>
    public IEnumerable<UndertakeLegalTransferResultModel> CaseData(string emplid = "",string applyNumber = "")
    {
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.Append(@$"SELECT 
                                appData.apply_number,--申請單號
                                appData.apply_time,--申請日期
                                appData.apply_form,--案件類型層級拼接
                                appData.apply_type,--申請單類型(一級)
                                appData.form_type,--申請單類型(二級)
                                fe.entity,--我方(簡稱)
                                appData.other_party,--他方
                               CASE WHEN appData.apply_type = N'O'
                            THEN (SELECT fun_name FROM dbo.sys_parameters WHERE para_code = N'formType_O' AND lang_type = N'{MvcContext.UserInfo.logging_locale}' AND func_code = CONVERT(NVARCHAR,appData.form_type))
                            ELSE appData.contract_name END AS contract_name,--合約名稱
                                appData.confiden_level,--機密等級，當數值=N'01'時，需要對[合約名稱]進行隱碼顯示
                                (SELECT fun_name FROM dbo.sys_parameters WHERE para_code = N'confidentStatus' AND lang_type = '{MvcContext.UserInfo.logging_locale}' AND func_code = CONVERT(NVARCHAR, appData.confiden_level)) AS confiden_level_name,--機密等級名稱
                                appData.legal_affairs_emplid,--承辦法務人員
                                -- (SELECT string_agg(name,',') name FROM dbo.ps_sub_ee_lgl_vw_a where emplid in (select value from String_Split(appData.legal_affairs_emplid,','))) AS legal_cuser,--承辦法務人員中文名稱
                                -- (SELECT string_agg(name_a,',') name_a FROM dbo.ps_sub_ee_lgl_vw_a where emplid in (select value from String_Split(appData.legal_affairs_emplid,','))) AS legal_euser,--承辦法務人員英文名稱
                                isnull((select name,name_a,iif(termination is null,'false','true') as IsResign from ps_sub_ee_lgl_vw_a where emplid in (select value from String_Split(appData.legal_affairs_emplid,','))   FOR JSON PATH),'[]') AS legal_user, --这里是一个JSON 包含 【中文名，英文名，是否离职】 （是个数组，因为O单承办法务可能是多个人 ）
                                appData.application_state,--案件狀態
                                (SELECT fun_name FROM dbo.sys_parameters WHERE para_code = N'applicationState' AND lang_type = '{MvcContext.UserInfo.logging_locale}' AND func_code = CONVERT(NVARCHAR, appData.application_state)) AS application_state_name--案件狀態名稱
                                FROM (
                                SELECT DISTINCT apply_form,form_type,vg.apply_number,vg.apply_time,vg.apply_type,vg.entity_id,vg.other_party,vg.contract_name,vg.confiden_level,vg.application_state
                                ,COALESCE(vg.legal_affairs_emplid,(select string_agg(legal_emplid,',') from other_application_legal o where o.apply_number = vg.apply_number)) as legal_affairs_emplid
                                FROM dbo.v_getallapplication vg
                                left join other_application_legal oal on vg.apply_number= oal.apply_number 
                                WHERE (vg.application_state = N'I' OR vg.application_state = N'A' OR vg.application_state = N'F')
                                AND (vg.apply_type = N'C' OR vg.form_type = N'R' OR vg.apply_type = N'O') ");//20250115與Lily確認用戶想要承辦法務轉單查詢有O單
        if (!string.IsNullOrEmpty(emplid))
            sqlBuilder.Append($@" AND COALESCE(vg.legal_affairs_emplid,oal.legal_emplid) = CONCAT(N'',  @emplid) ");
        if (!string.IsNullOrEmpty(applyNumber))
            sqlBuilder.Append($@" AND vg.apply_number = @applyNumber");
        sqlBuilder.Append(@$") AS appData
                                --主體
                                LEFT JOIN ({new FnpEntityRepository().GetAllEntity()}) AS fe ON fe.entity_id = appData.entity_id;");
        return DbAccess.Database.SqlQuery<UndertakeLegalTransferResultModel>(sqlBuilder.ToString(), new { emplid = emplid, applyNumber = applyNumber });
    }

    /// <summary>
    /// 获取交接人
    /// </summary>
    /// <param name="emplid">工号</param>
    /// <returns></returns>
    public List<PsSubEeLglVwAViewModel> GetHandover(string emplid)
    {
        StringBuilder sqlBuilder = new StringBuilder();
        //20250321 承辦法務轉單查詢交接人，不需要部門公司描述等信息
        sqlBuilder.Append(@$"SELECT ee.emplid,ee.name,ee.name_a FROM (SELECT emplid,name,name_a FROM ps_sub_ee_lgl_vw_a WHERE ISNULL(termination,N'') = N'') AS ee
                                --建議item使用配置文件動態，後續便於維護
                                INNER JOIN (SELECT max(item) item,empid FROM fnp_contact WHERE item IN(N'02',N'03',N'04') group by empid) AS fc ON fc.empid = ee.emplid
                                where (ee.emplid LIKE CONCAT(N'%', @emplid, N'%') OR ee.name LIKE CONCAT(N'%', @emplid, N'%') OR ee.name_a LIKE CONCAT(N'%', @emplid ,N'%'))
                                ORDER BY fc.item ASC,STUFF(ee.emplid, 1, PATINDEX('%[A-z]%', ee.emplid) - 1, '') ASC,LEN(ee.emplid) DESC,ee.emplid DESC");
        return NpgsqlSearchByList<PsSubEeLglVwAViewModel>(sqlBuilder.ToString(), new { emplid }, "time_zone", MvcContext.UserInfo.time_zone);
    }

    /// <summary>
    /// 插入承辦法務轉單
    /// </summary>
    /// <param name="model">新增数据</param>
    /// <returns></returns>
    public int InsertTransferHistory(ModifyHandlerTransferModel model, IDbContext? context = null)
    {
        model.apply_number = string.Join(",", model.apply_number_list);
        string sql = @"INSERT INTO dbo.transfer_legal_history(apply_number, apply_legal_emplid, handover_emplid, transfer_remarks, create_user,transfer_pic_number)
                        select value, CONCAT(N'', @apply_legal_emplid), CONCAT(N'', @handover_emplid), CONCAT(N'', @transfer_remarks), CONCAT(N'', @create_user),@transfer_pic_number from STRING_SPLIT(@apply_number, ',');";
        return ExecuteCommandToTransaction(sql, context, new { model.apply_number, apply_legal_emplid = model.apply_pic_emp.Emplid, handover_emplid = model.handover_emp.Emplid, model.transfer_remarks, model.create_user, transfer_pic_number = model.transfer_pic_number });
    }

    /// <summary>
    /// 插入承辦法務轉單
    /// </summary>
    /// <param name="model">新增数据</param>
    /// <returns></returns>
    internal IEnumerable<TransferEmpInfoModel> QuaryTransferMailInfo(ModifyHandlerTransferModel model)
    {
        string sql = @"select he.name as handover_name,he.name_a as handover_name_a,he.email_address_a as handover_email_address_a,
	   operator.name as operator_name,operator.name_a as operator_name_a,operator.email_address_a as operator_email_address_a
	   from (select emplid,name,name_a,email_address_a from ps_sub_ee_lgl_vw_a) he  
	   left join (select emplid,name,name_a,email_address_a from ps_sub_ee_lgl_vw_a) operator on operator.emplid  = @operator_emp
       where he.emplid = @handover_emplid";
        return DbAccess.Database.SqlQuery<TransferEmpInfoModel>(sql, new { operator_emp = model.create_user , handover_emplid = model.handover_emp.Emplid });
    }

    /// <summary>
    /// 修改其他申請單承辦法務
    /// </summary>
    /// <param name="model">新增数据</param>
    /// <returns></returns>
    internal int UpdateOtherLegal(ModifyHandlerTransferModel model, string apply_number, IDbContext? dbContext = null)
    {
        string sql = @"UPDATE other_application_legal
                    SET legal_emplid= @legal_emplid, create_user=@create_user, create_time=getutcdate()
                    WHERE apply_number= @apply_number and legal_emplid= @old_legal_emplid;";
        return ExecuteCommandToTransaction(sql, dbContext, new { apply_number = apply_number, old_legal_emplid = model.apply_pic_emp?.Emplid, legal_emplid = model.handover_emp.Emplid, create_user = model.create_user });
    }
}
