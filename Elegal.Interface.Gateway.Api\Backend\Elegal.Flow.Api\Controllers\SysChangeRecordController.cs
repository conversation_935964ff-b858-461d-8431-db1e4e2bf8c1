﻿using Elegal.Interface.Api.Common.Control.Web;
using Elegal.Interface.Api.Common.FuncService;
using Elegal.Interface.Api.Common.Model.ParaModel.FlowApi;
using Elegal.Interface.Api.Common.Model.ResultModel;
using Microsoft.AspNetCore.Mvc;

namespace Elegal.Flow.Api.Controllers
{
    /// <summary>
    /// 異動紀錄
    /// </summary>
    [Route("[controller]")]
    [ApiController]
    public class SysChangeRecordController : BaseController
    {
        #region 新增異動紀錄
        /// <summary>
        /// 新增異動紀錄
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [Route("AddSysChangeRecords")]
        public async Task<ApiResultModel> AddSysChangeRecords([FromBody] SysChangeRecordParaModel para)
        {
            ApiResultModel apiResult = new ApiResultModel();

            apiResult.rtnSuccess = SysChangeRecordService.AddSysChangeRecords(para);

            InitLogRecord<object>(new());
            return await Task.FromResult<ApiResultModel>(apiResult);
        }
        #endregion
    }
}
