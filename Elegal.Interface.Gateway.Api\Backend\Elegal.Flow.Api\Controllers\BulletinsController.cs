﻿using Elegal.Flow.Api.Services;
using Elegal.Interface.Api.Common.Control.Filter;
using Elegal.Interface.Api.Common.Control.Web;
using Elegal.Interface.Api.Common.Model;
using Elegal.Interface.Api.Common.Model.ResultModel;
using Elegal.Interface.Api.Common.Model.ResultModel.FlowApi;
using Elegal.Interface.Api.Common.Model.ViewModel;
using Elegal.Interface.Api.Common.Model.ViewModel.FlowApi;
using Elegal.Orm.Utils;
using Microsoft.AspNetCore.Mvc;
using Microsoft.IdentityModel.Tokens;
using Minio;
using System.Text;

namespace Elegal.Flow.Api.Controllers
{
    /// <summary>
    /// 公告管理服務
    /// </summary>
    [Route("[controller]")]
    [ApiController]
    public class BulletinsController : BaseController
    {

        #region 構造函数，注入MinioClient、MinIOConfig
        private readonly MinioClient _minioClient;
        private readonly MinIOConfig _minIOConfig;
        /// <summary>
        /// 構造函数
        /// </summary>
        /// <param name="minioClient"></param>
        /// <param name="minIOConfig"></param>
        public BulletinsController(MinioClient minioClient, MinIOConfig minIOConfig)
        {
            _minioClient = minioClient;
            _minIOConfig = minIOConfig;
        }
        #endregion


        /// <summary>
        /// 公告管理頁面查詢
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [Route("QueryBulletins")]
        public async Task<ApiResultModelByObject> QueryBulletins([FromBody] Bulletins bulletins)
        {
            ApiResultModelByObject apiResult = new();
            UserInfoModel user = MvcContext.UserInfo;

            apiResult.listData = BulletinsService.QueryBulletins(bulletins, user.logging_locale, user.time_zone);
            apiResult.rtnSuccess = true;
            InitLogRecord(bulletins, log =>
            {
                StringBuilder stringBuilder = new();
                if (!string.IsNullOrEmpty(bulletins.entity_id_log))
                {
                    stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("SystemLog_custom:Entity", true)}：{bulletins.entity_id_log}");
                }
                if (!string.IsNullOrEmpty(bulletins.leaf_ids.Key))
                    stringBuilder.AppendLine($"{AttributeUtil.GetParameterDescribe<Bulletins>("leaf_ids")}：{bulletins.leaf_ids.Value}");
                if (!string.IsNullOrEmpty(bulletins.news_subject))
                    stringBuilder.AppendLine($"{AttributeUtil.GetParameterDescribe<Bulletins>("news_subject")}：{bulletins.news_subject}");
                if (bulletins.news_status != null)
                {
                    string status = bulletins.news_status.Equals(0) ? $"{AttributeUtil.GetParameterDescribe<Bulletins>("news_status")}：停用" : $"{AttributeUtil.GetParameterDescribe<Bulletins>("news_status")}：啟用";
                    stringBuilder.AppendLine(status);
                }
                if (bulletins.news_start_time != null)
                    stringBuilder.AppendLine($"{AttributeUtil.GetParameterDescribe<Bulletins>("news_start_time")}：{GetLocalTimeToString(bulletins.news_start_time)}");
                if (bulletins.news_end_time != null)
                    stringBuilder.AppendLine($"{AttributeUtil.GetParameterDescribe<Bulletins>("news_end_time")}：{GetLocalTimeToString(bulletins.news_end_time)}");
                log.Detail = stringBuilder.ToString();
            });

            return await Task.FromResult(apiResult);
        }

        /// <summary>
        /// 根據newid查詢公告數據
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("QueryBulletinsByNewid")]
        public async Task<ApiResultModelByObject> QueryBulletinsByNewid(int newid)
        {
            ApiResultModelByObject apiResult = new();
            UserInfoModel user = MvcContext.UserInfo;

            BulletinsResult bulletins = BulletinsService.QueryBulletinsByNewid(newid, user.time_zone, user.logging_locale);
            if (bulletins != null && bulletins.newid > 0)
            {
                InitLogRecord(bulletins, log =>
                {
                    StringBuilder stringBuilder = new StringBuilder();
                    InitUpdateLog(bulletins, stringBuilder);
                    if (bulletins.news_end_time != null)
                        stringBuilder.AppendLine($"{AttributeUtil.GetParameterDescribe<Bulletins>("news_end_time")}：{GetLocalTimeToString(bulletins.news_end_time)}");
                    string status = 0.Equals(bulletins.news_status) ? $"{AttributeUtil.GetParameterDescribe<Bulletins>("news_status")}：停用" : $"{AttributeUtil.GetParameterDescribe<Bulletins>("news_status")}：啟用";
                    stringBuilder.AppendLine(status);
                    log.Detail = stringBuilder.ToString();
                });
                apiResult.listData = bulletins;
                apiResult.rtnSuccess = true;
            }
            else
            {
                apiResult.messageTitle = ActionFilter.GetMultilingualValue("custom:messageTitle:dataNotexist");
                apiResult.messageContent = new List<string> { string.Format(ActionFilter.GetMultilingualValue("custom:messageContent:dataChurn")) };
            }

            return await Task.FromResult(apiResult);
        }

        /// <summary>
        /// 根據newid刪除公告數據
        /// </summary>
        /// <returns></returns>
        [HttpDelete]
        [Route("DeleteBulletins")]
        public async Task<ApiResultModelByObject> DeleteBulletins(int newid, string entity_id = "")
        {
            ApiResultModelByObject apiResult = new();
            UserInfoModel user = MvcContext.UserInfo;

            if (newid > 0)
            {
                BulletinsResult bulletins = BulletinsService.QueryBulletinsByNewid(newid, user.time_zone, user.logging_locale);
                //驗證刪除公告是否存在
                if (bulletins.newid > 0)
                {
                    BulletinsEntityRepetition(newid, entity_id, apiResult, bulletins);
                }
                else
                {
                    apiResult.messageTitle = ActionFilter.GetMultilingualValue("custom:messageTitle:dataNotexist");
                    apiResult.messageContent = new List<string> { string.Format(ActionFilter.GetMultilingualValue("custom:messageContent:dataChurn")) };
                }
            }

            return await Task.FromResult(apiResult);
        }

        #region 公告重複校驗
        private void BulletinsEntityRepetition(int newid, string entity_id, ApiResultModelByObject apiResult, BulletinsResult bulletins)
        {
            if (bulletins.entity_id != null && !string.IsNullOrEmpty(bulletins.entity_id) && !bulletins.entity_id.Contains(entity_id))
            {
                apiResult.messageTitle = ActionFilter.GetMultilingualValue("custom:messageTitle:doNotDelete");
                apiResult.messageContent = new List<string> { string.Format(ActionFilter.GetMultilingualValue("custom:messageContent:entityNotInNews")) };
            }
            else
            {
                int count = BulletinsService.DeleteBulletins(newid, entity_id);
                InitLogRecord(bulletins, log =>
                {
                    StringBuilder stringBuilder = new StringBuilder();
                    InitUpdateLog(bulletins, stringBuilder);
                    InitUpdateLog2(bulletins, stringBuilder);
                    log.Detail = stringBuilder.ToString();
                });
                apiResult.listData = count;
                apiResult.rtnSuccess = true;
            }
        }
        #endregion

        #region 初始化日誌
        private static void InitUpdateLog2(BulletinsResult bulletins, StringBuilder stringBuilder)
        {
            if (bulletins.news_end_time != null)
                stringBuilder.AppendLine($"{AttributeUtil.GetParameterDescribe<Bulletins>("news_end_time")}：{GetLocalTimeToString(bulletins.news_end_time)}");
            string status = 0.Equals(bulletins.news_status) ? $"{AttributeUtil.GetParameterDescribe<Bulletins>("news_status")}：停用" : $"{AttributeUtil.GetParameterDescribe<Bulletins>("news_status")}：啟用";
            stringBuilder.AppendLine(status);

        }
        #endregion

        /// <summary>
        /// 驗證公告數據是否重複
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [Route("DuplicateVerificationBulletins")]
        public async Task<ApiResultModelByObject> DuplicateVerificationBulletins(Bulletins bulletins)
        {
            ApiResultModelByObject apiResult = new() { rtnSuccess = true };

            UserInfoModel user = MvcContext.UserInfo;
            bulletins.operate_user = user.current_emp;
            bulletins.operate_time = DateTime.Now;
            apiResult.listData = BulletinsService.DuplicateVerificationBulletins(bulletins);
            apiResult.rtnSuccess = true;

            return await Task.FromResult(apiResult);
        }

        /// <summary>
        /// 新增公告數據
        /// </summary>
        /// <returns></returns>
        [HttpPut]
        [Route("InsertBulletins")]
        public async Task<ApiResultModelByObject> InsertBulletins(Bulletins bulletins)
        {
            UserInfoModel user = MvcContext.UserInfo;
            bulletins.operate_user = MvcContext.UserInfo.current_emp;
            bulletins.operate_time = DateTime.Now;
            ApiResultModelByObject apiResult = new();

            if (bulletins.news_subject.Length > 200)
                return await Task.FromResult(new ApiResultModelByObject()
                {
                    messageTitle = ActionFilter.GetMultilingualValue("custom:messageTitle:createFail"),
                    messageContent = new List<string> { string.Format(ActionFilter.GetMultilingualValue("custom:messageContent:bulletinsNewSubjectLength")) }
                });
            if (TimeVaild(bulletins, user.time_zone))
                return await Task.FromResult(new ApiResultModelByObject()
                {
                    messageTitle = ActionFilter.GetMultilingualValue("custom:messageTitle:createFail"),
                    messageContent = new List<string> { string.Format(ActionFilter.GetMultilingualValue("custom:messageContent:bulletinsNewTime")) }
                });
            int count = InsertBulletinsLogInit(bulletins);
            apiResult.listData = count;
            apiResult.rtnSuccess = true;

            return await Task.FromResult(apiResult);

        }

        /// <summary>
        /// 公告時間校驗
        /// </summary>
        /// <returns></returns>
        public static bool TimeVaild(Bulletins bulletins, string time_zone)
        {
            return (bulletins.news_end_time != null && GetLocalTime(bulletins.news_end_time, time_zone) < GetLocalTime(bulletins.new_time, time_zone));
        }

        /// <summary>
        /// 將時間轉化為用戶時區時間
        /// </summary>
        /// <returns></returns>
        public static DateTime GetLocalTime(DateTime? time, string time_zone = "Taipei Standard Time")
        {
            if (time != null)
            {
                DateTime dateTime = TimeZoneInfo.ConvertTimeFromUtc((DateTime)time, TimeZoneInfo.FindSystemTimeZoneById(time_zone));
                return dateTime.Date;
            }
            return new DateTime();
        }

        /// <summary>
        /// 將時間轉化為用戶時區時間（string）
        /// </summary>
        /// <returns></returns>
        public static string GetLocalTimeToString(DateTime? time, string time_zone = "Taipei Standard Time")
        {
            if (time != null)
            {
                string dateTime = TimeZoneInfo.ConvertTimeFromUtc((DateTime)time, TimeZoneInfo.FindSystemTimeZoneById(time_zone)).ToString("yyyy/MM/dd");
                return dateTime;
            }
            return "";
        }

        /// <summary>
        /// 初始化插入公告日誌1
        /// </summary>
        /// <param name="bulletins"></param>
        /// <returns></returns>
        private int InsertBulletinsLogInit(Bulletins bulletins)
        {
            int count = BulletinsService.InsertBulletins(bulletins);

            InitLogRecord(bulletins, log =>
            {
                StringBuilder stringBuilder = new();
                InitInsertLog(bulletins, stringBuilder);
                if (bulletins.news_end_time != null)
                    stringBuilder.AppendLine($"{AttributeUtil.GetParameterDescribe<Bulletins>("news_end_time")}：{GetLocalTimeToString(bulletins.news_end_time)}");
                if (bulletins.news_status != null)
                {
                    string status = bulletins.news_status.Equals(0) ? $"{AttributeUtil.GetParameterDescribe<Bulletins>("news_status")}：停用" : $"{AttributeUtil.GetParameterDescribe<Bulletins>("news_status")}：啟用";
                    stringBuilder.AppendLine(status);
                }
                log.Detail = stringBuilder.ToString();
            });
            return count;
        }

        /// <summary>
        /// 初始化插入公告日誌2
        /// </summary>
        /// <param name="bulletins"></param>
        /// <param name="stringBuilder"></param>
        /// <returns></returns>
        private static void InitInsertLog(Bulletins bulletins, StringBuilder stringBuilder)
        {
            if (!string.IsNullOrEmpty(bulletins.leaf_ids.Key))
                stringBuilder.AppendLine($"{AttributeUtil.GetParameterDescribe<Bulletins>("leaf_ids")}：{bulletins.leaf_ids.Value}");
            if (!string.IsNullOrEmpty(bulletins.entity_id.Key))
                stringBuilder.AppendLine($"{AttributeUtil.GetParameterDescribe<Bulletins>("entity_id")}：{bulletins.entity_id.Value}");
            if (!string.IsNullOrEmpty(bulletins.news_subject))
                stringBuilder.AppendLine($"{AttributeUtil.GetParameterDescribe<Bulletins>("news_subject")}：{bulletins.news_subject}");
            if (!string.IsNullOrEmpty(bulletins.news_content))
            {
                string filedName = AttributeUtil.GetParameterDescribe<Bulletins>("news_content");
                if (bulletins.is_builtin != null && bulletins.is_builtin == 1 && "02".Equals(bulletins.leaf_ids.Key))
                    filedName = "備註";
                stringBuilder.AppendLine($"{filedName}：{bulletins.news_content}");
            }
            if (bulletins.file_id != null && bulletins.file_id > 0)
                stringBuilder.AppendLine($"{AttributeUtil.GetParameterDescribe<Bulletins>("news_filename")}：{BulletinsService.getFileName(bulletins.file_id)}");
            if (bulletins.news_start_time != null)
                stringBuilder.AppendLine($"{AttributeUtil.GetParameterDescribe<Bulletins>("news_start_time")}：{GetLocalTimeToString(bulletins.news_start_time)}");
            if (bulletins.new_time != null)
                stringBuilder.AppendLine($"{AttributeUtil.GetParameterDescribe<Bulletins>("new_time")}：{GetLocalTimeToString(bulletins.new_time)}");
        }

        /// <summary>
        /// 修改公告數據
        /// </summary>
        /// <returns></returns>
        [HttpPut]
        [Route("UpdateBulletins")]
        public async Task<ApiResultModelByObject> UpdateBulletins(Bulletins bulletins)
        {
            ApiResultModelByObject apiResult = new();
            UserInfoModel user = MvcContext.UserInfo;
            bulletins.operate_user = user.current_emp;
            bulletins.operate_time = DateTime.Now;
            //入參校驗
            if (VaildBulletins(bulletins))
            {
                //驗證公告的時間是否符合規則
                if (TimeVaild(bulletins, user.time_zone))
                {
                    apiResult.messageTitle = ActionFilter.GetMultilingualValue("custom:messageTitle:createFail");
                    apiResult.messageContent = new List<string> { string.Format(ActionFilter.GetMultilingualValue("custom:messageContent:bulletinsNewTime")) };
                    return await Task.FromResult(apiResult);
                }
                BulletinsResult bulletinsRs = BulletinsService.QueryBulletinsByNewid(bulletins.newid, user.time_zone, user.logging_locale);
                if (bulletinsRs != null && bulletinsRs.newid > 0)
                {
                    await ExecuteUpdateBulletinsAsync(bulletins, apiResult, bulletinsRs);
                }
                else
                {
                    apiResult.messageTitle = ActionFilter.GetMultilingualValue("custom:messageTitle:dataNotexist");
                    apiResult.messageContent = new List<string> { string.Format(ActionFilter.GetMultilingualValue("custom:messageContent:dataChurn")) };
                }
            }

            return await Task.FromResult(apiResult);
        }

        /// <summary>
        /// 入參校驗
        /// </summary>
        /// <param name="bulletins"></param>
        /// <returns></returns>
        private static bool VaildBulletins(Bulletins bulletins)
        {
            return bulletins != null && bulletins.newid > 0;
        }

        /// <summary>
        /// 執行修改公告操作
        /// </summary>
        /// <param name="bulletins"></param>
        /// <param name="apiResult"></param>
        /// <param name="bulletinsRs"></param>
        /// <returns></returns>
        private async Task ExecuteUpdateBulletinsAsync(Bulletins bulletins, ApiResultModelByObject apiResult, BulletinsResult bulletinsRs)
        {
            if (bulletins.news_subject.Length < 200)
            {
                int count = await UpdateBulletinsLogInit(bulletins, bulletinsRs);
                apiResult.listData = count;
                apiResult.rtnSuccess = true;
            }
            else
            {
                apiResult.messageTitle = ActionFilter.GetMultilingualValue("custom:messageTitle:updateFail");
                apiResult.messageContent = new List<string> { string.Format(ActionFilter.GetMultilingualValue("custom:messageContent:bulletinsNewSubjectLength")) };
            }
        }

        /// <summary>
        /// 初始化修改公告日誌
        /// </summary>
        /// <param name="bulletins"></param>
        /// <param name="bulletinsFormer"></param>
        /// <returns></returns>
        private async Task<int> UpdateBulletinsLogInit(Bulletins bulletins, BulletinsResult bulletinsFormer)
        {
            UserInfoModel user = MvcContext.UserInfo;
            int count = await BulletinsService.updateBulletins(bulletins, _minioClient, _minIOConfig);

            InitLogRecord(bulletins, log =>
            {
                StringBuilder stringBuilder = new();
                InitInsertLog(bulletins, stringBuilder);
                if (bulletins.news_end_time != null)
                    stringBuilder.AppendLine($"{AttributeUtil.GetParameterDescribe<Bulletins>("news_end_time")}：{GetLocalTimeToString(bulletins.news_end_time)}");
                if (bulletins.news_status >= 0)
                {
                    string status = bulletins.news_status.Equals(0) ? $"{AttributeUtil.GetParameterDescribe<Bulletins>("news_status")}：停用" : $"{AttributeUtil.GetParameterDescribe<Bulletins>("news_status")}：啟用";
                    stringBuilder.AppendLine(status);
                }
                log.Detail = stringBuilder.ToString();
                StringBuilder former = new();
                InitUpdateLog(bulletinsFormer, former);
                if (bulletinsFormer.news_end_time != null)
                    former.AppendLine($"{AttributeUtil.GetParameterDescribe<Bulletins>("news_end_time")}：{GetLocalTimeToString(bulletinsFormer.news_end_time)}");
                if (bulletinsFormer.news_status >= 0)
                {
                    string status = bulletinsFormer.news_status.Equals(0) ? $"{AttributeUtil.GetParameterDescribe<Bulletins>("news_status")}：停用" : $"{AttributeUtil.GetParameterDescribe<Bulletins>("news_status")}：啟用";
                    former.AppendLine(status);
                }
                log.DetailFormer = former.ToString();
            });
            return count;
        }

        /// <summary>
        /// 初始化修改公告日誌
        /// </summary>
        /// <param name="bulletinsFormer"></param>
        /// <param name="stringBuilderFormer"></param>
        /// <returns></returns>
        private static void InitUpdateLog(BulletinsResult bulletinsFormer, StringBuilder stringBuilderFormer)
        {
            if (!string.IsNullOrEmpty(bulletinsFormer.leaf_id))
                stringBuilderFormer.AppendLine($"{AttributeUtil.GetParameterDescribe<Bulletins>("leaf_ids")}：{bulletinsFormer.leaf_name}");
            if (!string.IsNullOrEmpty(bulletinsFormer.entity_id))
                stringBuilderFormer.AppendLine($"{AttributeUtil.GetParameterDescribe<Bulletins>("entity_id")}：{bulletinsFormer.entity_name}");
            if (!string.IsNullOrEmpty(bulletinsFormer.news_subject))
                stringBuilderFormer.AppendLine($"{AttributeUtil.GetParameterDescribe<Bulletins>("news_subject")}：{bulletinsFormer.news_subject}");
            if (!string.IsNullOrEmpty(bulletinsFormer.news_content))
                stringBuilderFormer.AppendLine($"{AttributeUtil.GetParameterDescribe<Bulletins>("news_content")}：{bulletinsFormer.news_content}");
            if (bulletinsFormer.file_id > 0)
                stringBuilderFormer.AppendLine($"{AttributeUtil.GetParameterDescribe<Bulletins>("news_filename")}：{BulletinsService.getFileName(bulletinsFormer.file_id)}");
            if (bulletinsFormer.new_time != null)
                stringBuilderFormer.AppendLine($"{AttributeUtil.GetParameterDescribe<Bulletins>("new_time")}：{GetLocalTimeToString(bulletinsFormer.new_time)}");
            if (bulletinsFormer.news_start_time != null)
                stringBuilderFormer.AppendLine($"{AttributeUtil.GetParameterDescribe<Bulletins>("news_start_time")}：{GetLocalTimeToString(bulletinsFormer.news_start_time)}");
        }
    }
}
