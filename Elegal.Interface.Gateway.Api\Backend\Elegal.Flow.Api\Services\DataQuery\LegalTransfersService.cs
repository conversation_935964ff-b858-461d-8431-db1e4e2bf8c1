﻿using Elegal.Flow.Api.Repository.DataQuery;
using Elegal.Flow.Common.Services;
using Elegal.Interface.Api.Common.Control.Filter;
using Elegal.Interface.Api.Common.FuncService;
using Elegal.Interface.Api.Common.Model.Enum;
using Elegal.Interface.Api.Common.Model.ParaModel.FlowApi;
using Elegal.Interface.Api.Common.Model.ResultModel.FlowApi;
using Elegal.Interface.ApiData.Service.FuncService;
using Elegal.Interface.ApiData.Service.Model.Condition;
using Elegal.Interface.ApiData.Service.Model.DbModel;
using Elegal.Interface.ApiData.Service.Model.QueryCondition;
using Elegal.Interface.ApiData.Service.Model.ViewModel;
using Elegal.Orm;

namespace Elegal.Flow.Api.Services.DataQuery
{
    /// <summary>
    /// 承办法务转单
    /// </summary>
    public static class LegalTransfersService
    {
        private static readonly LegalTransfersRepository _repository = new();

        /// <summary>
        /// 列表数据
        /// </summary>
        /// <returns></returns>
        internal static IEnumerable<UndertakeLegalTransferResultModel> ListData(TransferSearchModel model)
        {
            return _repository.ListData(model).Select(s => { s.time_zone = MvcContext.UserInfo.time_zone; return s; });
        }

        /// <summary>
        /// 根据人员查询案件清单
        /// </summary>
        /// <param name="emplid">查詢参数</param>
        /// <returns></returns>
        internal static IEnumerable<UndertakeLegalTransferResultModel> CaseData(string emplid)
        {
            return _repository.CaseData(emplid).Select(s => { s.time_zone = MvcContext.UserInfo.time_zone; return s; });
        }

        /// <summary>
        /// 根据单号查询案件
        /// </summary>
        /// <param name="applyNumber">查詢参数</param>
        /// <returns></returns>
        internal static UndertakeLegalTransferResultModel CaseDataByApplyNumber(string applyNumber)
        {
            return _repository.CaseData("", applyNumber).Select(s => { s.time_zone = MvcContext.UserInfo.time_zone; return s; }).ToList()[0];
        }

        /// <summary>
        /// 获取交接人
        /// </summary>
        /// <param name="emplid">工号</param>
        /// <returns></returns>
        internal static List<PsSubEeLglVwAViewModel> GetHandover(string emplid)
        {
            return _repository.GetHandover(emplid);
        }

        /// <summary>
        /// 插入承辦法務轉單
        /// </summary>
        /// <param name="model">新增数据</param>
        /// <returns></returns>
        internal static int InsertTransferHistory(ModifyHandlerTransferModel model)
        {
            //Issue：274 獲取申請單序號改為使用存儲過程
            model.transfer_pic_number = SysApplicationSequenceService.GetApplicationNumber("transfer_legal", "TL").ToString();
            //写入转单历史
            //20250321新增事物
            int ModifyCount = 0;
            DbAccess.PerformInTransaction(context =>
            {
                ModifyCount = _repository.InsertTransferHistory(model, context);
                //特殊加签人员，自动转单业务
                AutoTransfer(model, context);
            });
            TransferEmpInfoModel empinfo = _repository.QuaryTransferMailInfo(model).FirstOrDefault();
            SysEmail sysEmail = new SysEmail();
            List<string> mailList = new List<string>();
            sysEmail.EReceiver = empinfo.handover_email_address_a;
            sysEmail.ECc = empinfo.operator_email_address_a;
            SendMailService.SendTransferMail(model.transfer_pic_number, new List<MailTypeUtils> { MailTypeUtils.TL });
            return ModifyCount;
        }

        /// <summary>
        /// 自動轉單
        /// </summary>
        /// <param name="model">修改数据</param>
        /// <returns></returns>
        internal static void AutoTransfer(ModifyHandlerTransferModel model, IDbContext? context = null)
        {
        	//待刪除的加簽信息
            List<int?> delFlowStepSignerInvitee = new();
            //待修改的加簽信息
            List<FlowStepSignerInvitee> updateFlowStepSignerInvitee = new();
            //待修改的簽核流程信息
            List<FlowStepProcess> updateFlowStepProcess = new();
            //待修改的當前簽核信息
            List<FlowStepSigner> updateFlowStepSigner = new();

            //查詢承辦法務對應的關卡集合
            List<string> stepIdList = FlowStepDataService.Query(new FlowStepQueryCondition() { StepName = "WHQ_LOW_PRO" }).Select(s => s.StepId.ToString()).ToList();


            foreach (var item in model.apply_number_list) {
                List<FlowStepSignerInvitee> flowStepSignerInvitees = FlowStepSignerInviteeDataService.Query(new FlowStepSignerInviteeQueryCondition()
                {
                    ApplyNumber = item,
                    InviteeType = 2,
                    SpInviteLevel = 2
                });
                //存在特殊單據時修改動作
                if (flowStepSignerInvitees.Count > 1)
                {
                    flowStepSignerInvitees.Remove(flowStepSignerInvitees[0]);
                    delFlowStepSignerInvitee.Add(flowStepSignerInvitees[0].Rowid);
                }
                if (flowStepSignerInvitees.Count > 0)
                {
                    //自動轉單動作
                    updateFlowStepSignerInvitee.Add(new FlowStepSignerInvitee()
                    {
                        InviteeDeptid = model.handover_deptid,
                        InviteeEmplid = model.handover_emp.Emplid,
                        InviteeType = 2,
                        SpInviteLevel = 2,
                        Rowid = flowStepSignerInvitees[0].Rowid
                    });
                }

                //根據申請單號查詢該申請單在承辦法務這一關卡的信息
                FlowStepProcess flowStepProcesses =
                            FlowStepProcessDataService.Query(new FlowStepProcessQueryCondition() { ApplyNumber = item })
                            .Where(p => stepIdList.Contains(p.StepId.ToString())).ToList().FirstOrDefault();
                //修改申請單在承辦法務關卡的簽核人員和部門
                PsSubEeLglVwA psSubEeLglVwA = PsSubEeLglVwADataService.FindByKey(model.handover_emp.Emplid);
                if (flowStepProcesses != null)
                {
                    flowStepProcesses.SignerEmplid = model.handover_emp.Emplid;
                    if (psSubEeLglVwA != null)
                    {
                        flowStepProcesses.SignerDeptid = psSubEeLglVwA.Deptid;
                        flowStepProcesses.SignerCName = psSubEeLglVwA.Name;
                        flowStepProcesses.SignerEName = psSubEeLglVwA.NameA;
                    }
                    updateFlowStepProcess.Add(flowStepProcesses);
                }

                //查詢簽核表中當前簽核信息
                FlowStepSigner flowStepSigner = FlowStepSignerDataService.Find(new FlowStepSignerCondition() { ApplyNumber = item });
                //若關卡在承辦法務則修改簽核表人員
                if (flowStepSigner != null && stepIdList.Contains(flowStepSigner.StepId.ToString()))
                {
                    flowStepSigner.SignerEmplid = model.handover_emp.Emplid;
                    flowStepSigner.SignerDeptid = psSubEeLglVwA != null ? psSubEeLglVwA.Deptid : null;
                    updateFlowStepSigner.Add(flowStepSigner);
                }
            }

			//刪除的加簽信息
            delFlowStepSignerInvitee.ForEach(e => { context.DeleteByKey<FlowStepSignerInvitee, int?>(e); });
            //修改的加簽信息
            updateFlowStepSignerInvitee.ForEach(e => { context.Update(e); });
            //修改的簽核流程信息
            updateFlowStepProcess.ForEach(e => { context.Update(e); });
            //修改的當前簽核信息
            updateFlowStepSigner.ForEach(e => { context.Update(e); });

            foreach (var item in model.apply_number_list)
            {
                if (CaseDataByApplyNumber(item).apply_type.Equals("C"))
                {
                    //修改C類申請單承辦法務((無論有沒有特殊加簽都需要修改的)
                    context.Update(new FormApplication()
                    {
                        LegalAffairsEmplid = model.handover_emp.Emplid,
                        LegalAffairsDeptid = model.handover_deptid,
                        ApplyNumber = item
                    });
                }
                else if (CaseDataByApplyNumber(item).form_type.Equals("R"))
                {
                    //修改R類申請單承辦法務(無論有沒有特殊加簽都需要修改的)
                    context.Update(new LiteratureApplication()
                    {
                        LegalAffairsEmplid = model.handover_emp.Emplid,
                        LegalAffairsDeptid = model.handover_deptid,
                        ApplyNumber = item
                    });
                }
                //20250115承辦法務轉單與用戶確認可以支持O單轉單
                else if (CaseDataByApplyNumber(item).apply_type.Equals("O"))
                {
                    //修改O類申請單承辦法務((無論有沒有特殊加簽都需要修改的)
                    int count = _repository.UpdateOtherLegal(model, CaseDataByApplyNumber(item).apply_number, context);
                }

                
            }
        }
    }
}
