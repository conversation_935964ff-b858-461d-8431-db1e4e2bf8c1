﻿using Elegal.Flow.Api.Repository.Paper;
using Elegal.Flow.Common.Services;
using Elegal.Interface.Api.Common.Control.Filter;
using Elegal.Interface.Api.Common.FuncHelper;
using Elegal.Interface.Api.Common.Model.Enum;
using Elegal.Interface.Api.Common.Model.ParaModel.FlowApi;
using Elegal.Interface.Api.Common.Model.ResultModel.FlowApi;
using Elegal.Interface.Api.Common.Model.ViewModel.FlowApi;
using Elegal.Interface.ApiData.Service.FuncService;
using Elegal.Interface.ApiData.Service.Model.Condition;
using Elegal.Interface.ApiData.Service.Model.DbModel;
using Elegal.Interface.ApiData.Service.Model.QueryCondition;
using Elegal.Orm;
using Elegal.Orm.Dtos;
using Elegal.Orm.Utils;
using Newtonsoft.Json;
using System.Data;
using System.Reflection;
using System.Text;
#nullable disable
namespace Elegal.Flow.Api.Services.Paper
{
    /// <summary>
    /// 紙本追蹤批次作業查询模型
    /// </summary>
    public class PaperTrackingBathJobService
    {
        private static readonly PaperTrackingBathJobRepository _repository = new PaperTrackingBathJobRepository();
        /// <summary>
        /// 当前操作用户
        /// </summary>
        private static PsSubEeLglVwA CurrentUser { get; set; } = DbAccess.FindByKey<PsSubEeLglVwA, string>(MvcContext.UserInfo.current_emp);
        /// <summary>
        /// 法务行政人员
        /// </summary>
        private static List<LegalAdministrationModel> LegalUser { get; set; } = [];
        /// <summary>
        /// 郵件模版
        /// </summary>
        private static UserEmailContent UserEmailContent { get; set; }
        /// <summary>
        /// 开放栏位
        /// </summary>
        private static List<UserEmailDictionary> UserEmailDictionaryList { get; set; } = [];
        /// <summary>
        /// 查询列表数据
        /// </summary>
        /// <param name="condition"></param>
        /// <returns></returns>
        public static List<PaperTrackingBathJobViewModel> GetListData(PaperTrackingBathJobSearchModel condition)
        {
            List<PaperTrackingBathJobViewModel> list = [];
            foreach (PaperTrackingBathJobViewModel resultModel in _repository.GetListData(condition))
            {
                //極機密案件且當前案件狀態為已核准狀態需要進行隱碼處理
                if (resultModel.confiden_level?.ToUpper() == "01".ToUpper() &&
                    (
                        resultModel.applicationState.ToUpper() == "A".ToUpper() ||
                        resultModel.applicationState.ToUpper() == "F".ToUpper() ||
                        resultModel.applicationState.ToUpper() == "E".ToUpper()
                    )
                )
                    list.Add(HiddenValueConvertHelper.ConvertToHiddenBySingle(resultModel));
                else { list.Add(resultModel); }
            }
            return list;
        }
        /// <summary>
        /// 邮件提醒
        /// </summary>
        /// <param name="models"></param>
        /// <param name="tracks">批次作業</param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public static List<UserEmailTrackResult> SendMail(List<PaperTrackingBathJobViewModel> models, bool tracks = true)
        {

            #region  確定要使用的郵件模板
            string funcModule = tracks ? "06" : "05";
            Dictionary<string, string> dic = new Dictionary<string, string>()
              {
                  {"01",MailTypeUtils.PR.ToString() },
                  {"02",MailTypeUtils.PC.ToString() },
                  {"05",MailTypeUtils.PS.ToString() },
                  {"07",MailTypeUtils.PI.ToString() },
                  {"09",MailTypeUtils.PF.ToString() },
              };
            var thatUserEmailContent = UserEmailContentDataService.Find(new UserEmailContentCondition() { FuncModule = funcModule, MailType = dic.GetValueOrDefault(models.First().paperTracking) });
            #endregion
            #region 發送郵件
            //发送邮件(并返回发送成功與失敗的单据)
            List<UserEmailTrackResult> rList = SendMailService.SendPaperTrackingMail(models.Select(e => e.trackId.ToString()).ToList(), thatUserEmailContent);
            #endregion

            #region 更新主表数据
            //选择的数据
            var paperTrackWorks = PaperTrackWorkDataService.Query(new PaperTrackWorkQueryCondition()
            {
                SearchItemGroup = new SearchItemGroup()
                {
                    Items = new List<SearchItem>
                                              {
                                                  new SearchItem()
                                                  {
                                                      Compare = CompareOperator.ARRAYIN,
                                                      Field = "track_id",
                                                      Logic = LogicOperator.And,
                                                      Values = models.Select(s=>s.trackId.ToString())
                                                  }
                                              }
                }
            });
            List<PaperTrackWork> listUpdate = [];//记录需要更新的数据
            foreach (var model in models)
            {
                if (rList.Any(e => e.TrackId == model.trackId.ToString() && e.succeed)) //如果这个单子发送成功了
                {//如果该单据发送成功则更新主表数据
                    listUpdate.Add(new PaperTrackWork() { TrackId = model.trackId, RemindCount = paperTrackWorks.First(f => f.TrackId.Equals(model.trackId)).RemindCount + 1 });
                }
            }
            DbAccess.PerformInTransaction(context =>
            {
                context.BatchUpdateSql(listUpdate.ToArray());
            });
            #endregion

            #region 对发送成功的单据记录日志
            var dq = (from r in rList.Where(e => e.succeed) //发送成功的单据  
                      join pv in models on r.TrackId equals pv.trackId.ToString()//记录日志所需要的部分前端数据 。paperTrackingName
                      select new
                      {
                          ApplyNum = pv.applyNum,
                          PaperTrackingName = pv.paperTrackingName,
                          ReceiverName = r.Sjr,
                          ECcName = r.Csr,
                      }).ToList();

            ActionFilter.InitLogRecord(log =>
            {
                StringBuilder stringBuilder = new StringBuilder();
                log.Detail = string.Join(CommonUtil.GetDivisionLine() + "\r\n", dq.Select(s =>
                {
                    StringBuilder stringBuilder = new StringBuilder();
                    stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("PaperTrackingBathJob_applyNum", true)}：{s.ApplyNum}");
                    stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("PaperTrackingBathJob_export:paperTrackingStatuName", true)}：{s.PaperTrackingName}");
                    stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("PaperTrackingBathJob_recipient", true)}：{s.ReceiverName}");
                    stringBuilder.AppendLine($"CC：{s.ECcName}");
                    return stringBuilder.ToString();
                }));
            });
            #endregion
            return rList;
        }
        /// <summary>
        /// 新增
        /// </summary>
        /// <param name="condition"></param>
        /// <returns></returns>
        public static bool Add(PaperTrackingBathJobAddModel condition)
        {
            var func = (string paperTracking) => { return paperTracking == "01" || paperTracking == "02" || paperTracking == "05" || paperTracking == "07" || paperTracking == "09"; };
            var oldList = PaperTrackWorkDataService.Query(new PaperTrackWorkQueryCondition()
            {
                SearchItemGroup = new SearchItemGroup()
                {
                    Items = new List<SearchItem>
                    {
                        new SearchItem()
                        {
                            Field = "apply_number",
                            Compare = CompareOperator.ARRAYIN,
                            Logic = LogicOperator.And,
                            Values = condition.applyNum
                        }
                    }
                },
                OrderBys = new List<OrderByParam>
                {
                    new OrderByParam()
                    {
                        Field = "create_time",
                        Order = OrderBy.DESC,
                    }
                }
            });
            return DbAccess.BatchCreateSql(condition.applyNum.Select(s =>
            {
                return new PaperTrackWork()
                {
                    ApplyNumber = s,
                    CreateTime = DateTime.UtcNow,
                    CreateUser = MvcContext.UserInfo.current_emp,
                    PaperNumber = condition.paperPiece,
                    ReceivingDate = condition.receipt,
                    ReceivingEmplid = condition.recipient.Emplid,
                    ReceivingType = condition.receiptType,
                    TrackStatus = condition.paperTracking.FuncCode,
                    TrackRemark = condition.notes,
                    RemindCount = func(condition.paperTracking.FuncCode) ? 0 : null,
                    ConsignmentNumber = condition.consignmentNumber ?? "",
                };
            }).ToArray()) > 0;
        }
        /// <summary>
        /// 获取法务行政人员
        /// </summary>
        /// <returns></returns>
        private static List<LegalAdministrationModel> GetLegalAdministrationUser(IEnumerable<string> applyNums)
        {
            string sql = $@"select
	                            af.apply_number ApplyNumber,
	                            sa.emp_id Emplid,
	                            ee.name Name,
	                            ee.name_a NameA,
	                            ee.phone_a PhoneA,
                                ee.email_address_a EmailAddressA
                            from
	                            form_application AS af
                            left join sys_approver_management sa on	sa.entity_id = af.entity_id
                            left join ps_sub_ee_lgl_vw_a ee on ee.emplid = sa.emp_id
                            where sa.approver_type = 'ck_whq_low_admin' and af.apply_number in(N'{string.Join("',N'", applyNums)}')";
            return DbAccess.Database.SqlQuery<LegalAdministrationModel>(sql).ToList();
        }
        /// <summary>
        /// 邮件提醒内容
        /// </summary>
        /// <param name="paperTracking">纸本追踪状态</param>
        /// <param name="applicationType">申请单类型</param>
        /// <param name="notSendEmail">不可发送邮件</param>
        /// <returns></returns>
        public static string SendMailNotice(string paperTracking, int applicationType, ref bool notSendEmail)
        {
            string msg = string.Empty;
            if (string.IsNullOrEmpty(paperTracking)) return ActionFilter.GetMultilingualValue("otherLang:paperTrackingBathJob:paperTracking00");
            Dictionary<string, string> dic = new Dictionary<string, string>()
            {
                {"01",ActionFilter.GetMultilingualValue("otherLang:paperTrackingBathJob:paperTracking01")},
                {"02",ActionFilter.GetMultilingualValue("otherLang:paperTrackingBathJob:paperTracking02")},
                {"05",ActionFilter.GetMultilingualValue("otherLang:paperTrackingBathJob:paperTracking05")},
                {"07",ActionFilter.GetMultilingualValue("otherLang:paperTrackingBathJob:paperTracking07")},
                {"09",ActionFilter.GetMultilingualValue("otherLang:paperTrackingBathJob:paperTracking09")}
            };
            switch (applicationType)
            {
                case 2:
                    notSendEmail = dic.TryGetValue(paperTracking, out string content);
                    msg = content;
                    break;
                case 4:
                    msg = ActionFilter.GetMultilingualValue("otherLang:paperTrackingBathJob:paperTracking00");
                    break;
                default:
                    break;
            }
            return msg ?? ActionFilter.GetMultilingualValue("otherLang:paperTrackingBathJob:paperTracking00");
        }

        #region Action
        /// <summary>
        /// 设定邮件收件人邮箱
        /// </summary>
        /// <param name="userEmailTrackModel"></param>
        /// <param name="model"></param>
        private static void SetMailReceiver(UserEmailTrackModel userEmailTrackModel, PaperTrackingBathJobViewModel model)
        {
            string receiver = string.Empty, receiverName = string.Empty;
            if (model.fillerEmplid.Equals(model.handlerEmplid))
            {
                receiver = model.fillerMail ?? "";
                receiverName = model.fillerEname ?? "";
            }
            else
            {
                receiver = string.Join(";", [model.fillerMail, model.handlerMail]);
                receiverName = $"{model.fillerEname};{model.handlerEname}";
            }
            userEmailTrackModel.ReceiverName = receiverName;
            var legalUser = LegalUser.Where(w => w.ApplyNumber.Equals(model.applyNum));
            var legal = CommonUtil.Map<PsSubEeLglVwA, LegalAdministrationModel>(CurrentUser);
            switch (model.paperTracking)
            {
                case "01": userEmailTrackModel.EReceiver = receiver; break;//提醒經辧提供紙本
                case "02": userEmailTrackModel.EReceiver = model.legalMail; userEmailTrackModel.ReceiverName = model.legalEname; break;//經辧提供紙本
                case "05": userEmailTrackModel.EReceiver = receiver; break;//紙本送簽中
                case "07": userEmailTrackModel.EReceiver = receiver; break;//待經辦自取
                case "09": userEmailTrackModel.EReceiver = receiver; break;//託運給經辦
            }
            legalUser = legalUser.Append(legal);
            userEmailTrackModel.ECc = string.Join(";", legalUser.Select(s => s.EmailAddressA).Where(w => !string.IsNullOrEmpty(w) && !w.Equals("NULL") && !w.Equals("null")).Distinct());
            userEmailTrackModel.ECcName = string.Join(";", legalUser.Where(w => !string.IsNullOrEmpty(w.EmailAddressA) && !w.EmailAddressA.Equals("NULL") && !w.EmailAddressA.Equals("null")).Select(s => s.NameA).Distinct());
        }
        /// <summary>
        /// 设定邮件主题内容
        /// </summary>
        /// <param name="userEmailTrackModel"></param>
        /// <returns></returns>
        private static void SetMailSubject(UserEmailTrackModel userEmailTrackModel)
        {
            foreach (var email in UserEmailDictionaryList)
            {
                var prop = userEmailTrackModel.GetType().GetProperties().FirstOrDefault(f =>
                {
                    var fieldCode = f.GetCustomAttribute(typeof(FieldCodeAttribute)) as FieldCodeAttribute;
                    return fieldCode != null && fieldCode.Name.Equals(email.FieldCode);
                });
                if (prop == null) continue;
                userEmailTrackModel.MailSubject = userEmailTrackModel.MailSubject.Replace($"{{{email.FieldCode}_zh}}", prop.GetValue(userEmailTrackModel)?.ToString());
            }
        }
        /// <summary>
        /// 设定单笔邮件基本数据
        /// </summary>
        /// <param name="userEmailTrackModel"></param>
        /// <param name="model"></param>
        private static void SetMailFieldContent(UserEmailTrackModel userEmailTrackModel, PaperTrackingBathJobViewModel model)
        {
            if (model.fillerEmplid.Equals(model.handlerEmplid)) userEmailTrackModel.FillPic = $"{model.fillerCname}({model.fillerEname})";
            else userEmailTrackModel.FillPic = $"{model.fillerCname}({model.fillerEname})/{model.handlerCname}({model.handlerEname})";
            var legalUser = LegalUser.Where(w => w.ApplyNumber.Equals(model.applyNum));//法务行政
            userEmailTrackModel.LegalDministration = LegalUser.Any() ? string.Join("，", legalUser.Select(s => $"{s.Name}({s.NameA}) #{s.PhoneA}")) : "";
            switch (model.paperTracking)
            {
                case "01": break;//提醒經辧提供紙本
                case "02": userEmailTrackModel.LegalEmplid = $"{model.legalCname}({model.legalEname})"; break;//經辧提供紙本
                case "05": break;//紙本送簽中
                case "07": break;//待經辦自取
                case "09": userEmailTrackModel.ConsignmentNumberLink = AppSettingHelper.Configuration["consignmentNumber"] ?? ""; break;//託運給經辦
            }
        }
        /// <summary>
        /// 设定邮件内容
        /// </summary>
        /// <param name="userEmailTrackModel"></param>
        private static void SetMailContent(UserEmailTrackModel userEmailTrackModel)
        {
            foreach (var email in UserEmailDictionaryList)
            {
                var prop = userEmailTrackModel.GetType().GetProperties().FirstOrDefault(f =>
                {
                    var fieldCode = f.GetCustomAttribute(typeof(FieldCodeAttribute)) as FieldCodeAttribute;
                    return fieldCode != null && fieldCode.Name.Equals(email.FieldCode);
                });
                if (prop == null) continue;
                userEmailTrackModel.MailContent = userEmailTrackModel.MailContent.Replace($"{{{email.FieldCode}_zh}}", prop.GetValue(userEmailTrackModel)?.ToString());
                userEmailTrackModel.MailContent = userEmailTrackModel.MailContent.Replace($"{{{email.FieldCode}_en}}", prop.GetValue(userEmailTrackModel)?.ToString());
            }
            userEmailTrackModel.MailContent = userEmailTrackModel.MailContent.Replace("{se_url_zh}", "貨件轉運情形查詢");
            userEmailTrackModel.MailContent = userEmailTrackModel.MailContent.Replace("{se_url_link}", userEmailTrackModel.ConsignmentNumberLink);
        }
        /// <summary>
        /// 设定邮件表格内容
        /// </summary>
        /// <param name="list"></param>
        /// <param name="sysEmail"></param>
        private static void SetMailTableContent(IEnumerable<UserEmailTrackModel> list, SysEmail sysEmail)
        {
            var tableJsons = JsonConvert.DeserializeObject<List<TableJson>>(UserEmailContent.TableJson ?? "") ?? [];
            var table = tableJsons.FirstOrDefault(f => sysEmail.EContent.Contains(f.tableId));
            if (table != null)
            {
                StringBuilder stringBuilder = new StringBuilder();
                stringBuilder.AppendLine("<table border=\"1\" style=\"border-collapse: collapse;border: 1px solid #ccc;text-align: left;font-size: 12pt;\">");
                stringBuilder.AppendLine("  <tbody>");
                stringBuilder.AppendLine("      <tr>");
                foreach (var col in table.columns)
                {
                    var field = UserEmailDictionaryList.FirstOrDefault(f => $"{{{f.FieldCode}}}".Equals(col));
                    var title = table.lang.Equals("ZH-TW") ? field.FieldCname : field.FieldEname;
                    stringBuilder.AppendLine($"          <td width=\"{(col.Equals("{other}") ? "400px" : "200px")}\">{title}</td>");
                }
                stringBuilder.AppendLine("      </tr>");
                foreach (var item in list)
                {
                    stringBuilder.AppendLine("      <tr>");
                    StringBuilder tbBuilder = new StringBuilder();
                    foreach (var col in table.columns)
                    {
                        var field = UserEmailDictionaryList.FirstOrDefault(f => $"{{{f.FieldCode}}}".Equals(col));
                        var title = $"{{{field.FieldCode}_{(table.lang.Equals("ZH-TW") ? "zh" : "en")}}}";
                        tbBuilder.AppendLine($"          <td>{title}</td>");
                    }
                    tbBuilder = tbBuilder
                        .Replace("{apply_number_zh}", item.ApplyNum)
                        .Replace("{contract_name_zh}", item.contractName)
                        .Replace("{entity_zh}", item.Entity)
                        .Replace("{other_zh}", item.Other)
                        .Replace("{apply_number_en}", item.ApplyNum)
                        .Replace("{contract_name_en}", item.contractName)
                        .Replace("{entity_en}", item.Entity)
                        .Replace("{other_en}", item.Other);
                    stringBuilder.Append(tbBuilder);
                    stringBuilder.AppendLine("      </tr>");
                }
                stringBuilder.AppendLine("  </tbody>");
                stringBuilder.AppendLine("</table>");

                sysEmail.EContent = sysEmail.EContent.Replace($"{{{table.tableId}}}", stringBuilder.ToString());
            }
        }
        #endregion
    }
}
