﻿using Elegal.Flow.Api.Repository.LendAppliction;
using Elegal.Flow.Api.Services.Paper;
using Elegal.Interface.Api.Common.Control.Filter;
using Elegal.Interface.Api.Common.FuncHelper;
using Elegal.Interface.Api.Common.Model.ParaModel.FlowApi.LendAppliction;
using Elegal.Interface.Api.Common.Model.ResultModel.FlowApi.LendAppliction;
using Elegal.Interface.Api.Common.Model.ViewModel;
using NPOI.SS.UserModel;
using NPOI.XSSF.UserModel;
using Elegal.Interface.Api.Common.FuncService;
using System.Drawing;

namespace Elegal.Flow.Api.Services.LendAppliction;

public static class LendSearchForAdminService
{
    private static LendSearchForAdminRepository _LendSearchRepository = new LendSearchForAdminRepository();
    private static LendSearchForUserRepository _LendSearchRepositoryForUser = new LendSearchForUserRepository();
    /// <summary>
    /// 分页查询  当前借出 
    /// </summary>
    /// <param name="para"></param>
    /// <param name="user"></param>
    /// <returns></returns>
    public static (List<LendAdminModel>, int) QueryLendForAdmin(LendForAdminPara para, UserInfoModel user)
    {
        para.ShouldReturnTime.SetUserTimeZone(user.time_zone);
        para.PickupTime.SetUserTimeZone(user.time_zone);
        return _LendSearchRepository.QueryLendForAdmin(para, user.logging_locale, o =>
        {
            o.CreateTime = o.CreateTime.ConvertDateByTimeZoneByUtc(user.time_zone);
        });
    }

    /// <summary>
    /// 查询纸本借阅详情(管理員查詢子表)
    /// </summary>
    /// <returns></returns>
    public static List<PaperLendingDetail> QueryPaperLendingDetailListForAdmin(int paperLendId, UserInfoModel user)
    {

        return _LendSearchRepository.QueryPaperLendingDetailListForAdmin(paperLendId, user.logging_locale, row =>
        {
            //逾期未归还计算
            row.IsOverdueAndNotReturn = (row.OverdueDay.HasValue && row.OverdueDay.Value>0)
                                      || (!row.ActualReturnTime.HasValue && row.ShouldReturnTime.HasValue && row.ShouldReturnTime.Value < DateTime.UtcNow.Date)
                                        ;
            //转换时间
            row.ContractNumber = row.ContractNumber?.Replace("\u001f", "");
            row.PickupTime = row.PickupTime.ConvertDateByTimeZoneByUtc(user.time_zone);
            row.AuthDateEnd = row.AuthDateEnd.ConvertDateByTimeZoneByUtc(user.time_zone);
            row.ShouldReturnTime = row.ShouldReturnTime.ConvertDateByTimeZoneByUtc(user.time_zone);
            //未取件的没有归还状态
            if (row.IsPickupLend != 1) row.IsReturn = null;
        });
    }

    /// <summary>
    /// 资料查询，借出中查询 导出
    /// </summary>
    /// <param name="para"></param>
    /// <param name="user"></param>
    /// <returns></returns>
    public static byte[] QueryLendForAdminExport(LendForAdminExportPara para, UserInfoModel user)
    {
        para.PickupTime.SetUserTimeZone(user.time_zone);
        para.ShouldReturnTime.SetUserTimeZone(user.time_zone);//設置用戶時區
        List<LendAdminModelExport> data = _LendSearchRepository.QueryLendForAdminExport(para, user.logging_locale);

        //创建工作表
        IWorkbook workbook = new XSSFWorkbook();
        ISheet sheet = workbook.CreateSheet("Sheet1");
        //定义一个字体
        IFont font = workbook.CreateFont().configFont(f => {
            f.FontHeightInPoints = 12;
            f.FontName = "Calibri";
        });

        //定义一个字体
        IFont fontRed = workbook.CreateFont().configFont(f => {
            f.FontHeightInPoints = 12;
            f.FontName = "Calibri";
            f.Color = IndexedColors.Red.Index;
        });
        //定义一个样式
        ICellStyle redStyle = workbook.CreateCellStyle().SetDefaultBorder().AddFont(fontRed);
        ICellStyle defaultstyle = sheet.Workbook.CreateCellStyle().SetDefaultBorder().AddFont(font);
        ICellStyle styleCenter = sheet.Workbook.CreateCellStyle().SetDefaultBorder().HorizontallyCenter().AddFont(font);

        //設置表頭
        sheet.SetHeader(new List<string>()
        {
            ActionFilter.GetMultilingualValue("export:lendId"),
            ActionFilter.GetMultilingualValue("export:accessNumber"),
            ActionFilter.GetMultilingualValue("export:filler"),
            ActionFilter.GetMultilingualValue("export:handler"),
            ActionFilter.GetMultilingualValue("export:applyDate"),
            ActionFilter.GetMultilingualValue("export:applyStatus"),
            ActionFilter.GetMultilingualValue("export:contractNum"),
            ActionFilter.GetMultilingualValue("export:paperId"),
            ActionFilter.GetMultilingualValue("export:paperName"),
            ActionFilter.GetMultilingualValue("export:inboundStatus"),
            ActionFilter.GetMultilingualValue("export:copiesStatus"),
            ActionFilter.GetMultilingualValue("export:saveLocation"),
            ActionFilter.GetMultilingualValue("export:maxBorrowDay"),
            ActionFilter.GetMultilingualValue("export:shouldReturnDate"),
            ActionFilter.GetMultilingualValue("export:returnDate")

        });
        //设置列宽
        sheet.SetColumnListWidth(new List<int>() {4000,4000,7000,7000,4000,4000,4000,6000,10000,3000,3000,6000,4000,4000,4000 });


        //写入数据
        sheet.WriteData(data, (item, row) =>
        {
            item.ContractNumber = item.ContractNumber?.Replace("\u001f", "") ?? string.Empty;
            row.CreateElegalDefaultCell(0, defaultstyle).SetCellValue(item.LendNumber);
            row.CreateElegalDefaultCell(1, defaultstyle).SetCellValue(item.RetrieveNumber);
            row.CreateElegalDefaultCell(2, defaultstyle).SetCellValue($"{item.LendFillEName} ({item.LendFillCName})");
            row.CreateElegalDefaultCell(3, defaultstyle).SetCellValue($"{item.LendHandlerEName} ({item.LendHandlerCName})");
            row.CreateElegalDefaultCell(4, defaultstyle).SetCellValue(item.CreateTime.ConvertDateByTimeZoneByUtc(user.time_zone).ToString("yyyy/MM/dd"));
            row.CreateElegalDefaultCell(5, defaultstyle).SetCellValue(item.LendStatusName);
            row.CreateElegalDefaultCell(6, defaultstyle).SetCellValue(item.ContractNumber);
            row.CreateElegalDefaultCell(7, defaultstyle).SetCellValue(item.PaperCode);
            row.CreateElegalDefaultCell(8, defaultstyle).SetCellValue(item.PaperName);
            row.CreateElegalDefaultCell(9, defaultstyle).SetCellValue(item.PaperEntryStatus);
            row.CreateElegalDefaultCell(10, defaultstyle).SetCellValue(item.ReturnStatusName);
            row.CreateElegalDefaultCell(11, defaultstyle).SetCellValue(item.PaperPosition);
            row.CreateElegalDefaultCell(12, defaultstyle).SetCellValue(item.ParerBorrowDays);
            row.CreateElegalDefaultCell(13, defaultstyle).SetCellValue(item.ShouldReturnTime.ConvertDateByTimeZoneByUtc(user.time_zone).ToString("yyyy/MM/dd"));
            row.CreateElegalDefaultCell(14, defaultstyle).SetCellValue(item.ActualReturnTime.ConvertDateByTimeZoneByUtc(user.time_zone).ToString("yyyy/MM/dd"));

            // 应归还日期为今天00:00之前，也就是昨天以前 (逾期未归还)
            if ((item.OverdueDay.HasValue && item.OverdueDay.Value > 0)
                                  || (!item.ActualReturnTime.HasValue && item.ShouldReturnTime.HasValue && item.ShouldReturnTime.Value < DateTime.UtcNow.Date)
                                    )
            {
                row.Cells[13].CellStyle = redStyle;
            }

        });
        return workbook.ToBytes();
    }



    /// <summary>
    /// 借出检视 详情数据(管理員用戶_借出中查詢_檢視)
    /// </summary>
    /// <param name="lendId">出借表主键</param>
    /// <returns></returns>
    public static LoanDetailsView QueryLendDetailsViewForAdmin(int lendId, UserInfoModel user)
    {
        return new LoanDetailsView()
        {
            PersonnelInfo = _LendSearchRepositoryForUser.QueryPersonnelInfo(lendId,user.logging_locale).Assign(o =>
            {
                o.ApplicationTime = o.ApplicationTime.ConvertDateByTimeZoneByUtc(user.time_zone);
            }),
            DemandInfo = new DemandInfoWebView().Assign(e =>
            {
                List<DemandInfo> list = _LendSearchRepository.QueryDemandInfoForAdmin(lendId);
                if (list == null || list.Count == 0) return;
                var d1 = list[0];
                e.RetrieveNumber = d1.RetrieveNumber;
                e.BorrowDays = d1.BorrowDays;
                e.DemandReason = d1.DemandReason;
                e.Discloseperson = d1.Discloseperson;
                e.Other = d1.Other + " " + d1.RetrieveReason;
                e.ActualReturnTime = list.Select(e => e.ActualReturnTime).FirstOrDefault(e => e.HasValue); ;
                e.Otherperson = d1.Otherperson;
                e.AuthDateEnd = d1.AuthDateEnd.ConvertDateByTimeZoneByUtc(user.time_zone);
                e.AuthDateStart = d1.AuthDateStart.ConvertDateByTimeZoneByUtc(user.time_zone);
                e.Applynumber = string.Join(",", list.Select(e => e.Applynumber).Distinct());
                string dcstr = ActionFilter.GetMultilingualValue("commonWord:DepartmentCode");
                string dacstr = ActionFilter.GetMultilingualValue("commonWord:DepartmentAndCompany");
                e.InHouseStaff = string.Join("<br />", list.Select(item => $"{item.EName}({item.Empid}),{dcstr}:{item.Depid},{dacstr}{item.Company}").Distinct());
                
            }),
            PickUpInfo = _LendSearchRepositoryForUser.QueryPickUpInfo(lendId, user.logging_locale).Assign(o =>
            {
                o.PickupTime = o.PickupTime.ConvertDateByTimeZoneByUtc(user.time_zone);
                o.ActualPickupTime = o.ActualPickupTime.ConvertDateByTimeZoneByUtc(user.time_zone);
            }),
            LendDetails = _LendSearchRepositoryForUser.QueryPldVList(lendId,user.logging_locale, o =>
            {
                o.ContractNumber = o.ContractNumber?.Replace("\u001f", "");
                o.ActualReturnTime = o.ActualReturnTime.ConvertDateByTimeZoneByUtc(user.time_zone);
                o.AuthDateEnd = o.AuthDateEnd.ConvertDateByTimeZoneByUtc(user.time_zone);
                o.ShouldReturnTime = o.ShouldReturnTime.ConvertDateByTimeZoneByUtc(user.time_zone);
            }),
            UnlendingDetails = _LendSearchRepositoryForUser.QueryPaperUnlendingDetailList(lendId, user.logging_locale, o => {
                o.ContractNumber = o.ContractNumber?.Replace("\u001f", "");
                o.AuthDateEnd = o.AuthDateEnd.ConvertDateByTimeZoneByUtc(user.time_zone);
                o.ShouldReturnTime = o.ShouldReturnTime.ConvertDateByTimeZoneByUtc(user.time_zone);
            })
        }.Assign(res => { //2024/3/13依借出申请的检视接口规格添加同一字段
            if (!string.IsNullOrEmpty(res.DemandInfo.RetrieveNumber))
                res.retrieve_detail = PaperLendingApplicationService.GetOtherApplicationDetail(res.DemandInfo.RetrieveNumber).FirstOrDefault();
        });
    }

    
}
