﻿using Elegal.Interface.Api.Common.Model.ParaModel.FlowApi.LendAppliction;
using Elegal.Interface.Api.Common.Model.ResultModel.FlowApi;
using Elegal.Interface.Api.Common.Model.ResultModel.FlowApi.LendAppliction;
using Elegal.Interface.Api.Common.Repository;
using System.Text;

namespace Elegal.Flow.Api.Repository.LendAppliction;

/// <summary>
/// 資料查詢 -> 我的借出 -> 當前借出
/// </summary>
public class LendSearchForUserRepository : BaseRepository
{
    /// <summary>
    /// 實例化
    /// </summary>
    public LendSearchForUserRepository() : base() { }

    #region 查詢用戶的當前借出
    /// <summary>
    /// 查詢用戶的當前借出
    /// </summary>
    /// <param name="para"></param>
    /// <param name="langType"></param>
    ///  <param name="currentEmplid"></param>
    ///  <param name="actFunc"></param>
    /// <returns></returns>
    public List<LendUserModel> QueryLendForUser(LendForUserPara para, string currentEmplid, string langType = "ZH-TW", Action<LendUserModel> actFunc = null)
    {

        Dictionary<string, Object> sqlPara = new Dictionary<string, object>();

        #region 拼接查詢條件
        StringBuilder searchSql = new StringBuilder();

        searchSql.AppendLine(@" AND pla.lend_handler_emplid = @currentEmplid");
        sqlPara.Add("currentEmplid", currentEmplid);
        //紙本/合約編號
        if (!string.IsNullOrWhiteSpace(para.ContractNumber))
        {
            searchSql.AppendLine(@" AND (pldd.paper_code LIKE CONCAT(N'%', @ContractNumber, N'%')
                                            OR fa.contract_number LIKE CONCAT(N'%', @ContractNumber, N'%')
                                           )"
                                 );
            sqlPara.Add("ContractNumber", para.ContractNumber);
        }
        //紙本/合約名稱
        if (!string.IsNullOrWhiteSpace(para.ContractName))
        {
            searchSql.AppendLine(@" AND (pldd.paper_name LIKE CONCAT(N'%', @ContractName, N'%')
                                            OR fa.contract_name LIKE CONCAT(N'%', @ContractName, N'%')
                                            )");
            sqlPara.Add("ContractName", para.ContractName);
        }

        //取件日期
        if (para.LendTakeTime.Start.HasValue)
        {
            searchSql.AppendLine(@" AND pldd.pickup_time>= @LendTakeTimeBegin");
            sqlPara.Add("LendTakeTimeBegin", para.LendTakeTime.Start.Value);
        }
        if (para.LendTakeTime.End.HasValue)
        {
            searchSql.AppendLine(@" AND pldd.pickup_time< @LendTakeTimeEnd");
            sqlPara.Add("LendTakeTimeEnd", para.LendTakeTime.End.Value.AddDays(1));
        }

        //取件者
        if (para.PickupEmplids.Count > 0)
        {
            int i = 0;
            string sqlEmplidsPara = string.Join(",", para.PickupEmplids.Select(e =>
            {
                i++;
                sqlPara.Add("pickupemplid_" + i.ToString(), e);
                return "@pickupemplid_" + i.ToString();
            })
                );
            searchSql.AppendLine(@$" AND pldd.pickup_emplid in ({sqlEmplidsPara})");
        }

        //借出單號
        if (!string.IsNullOrWhiteSpace(para.LendNumber))
        {
            searchSql.AppendLine("AND (pla.lend_number LIKE @LendNumber OR pld.retrieve_number LIKE @LendNumber)");
            sqlPara.Add("LendNumber", "%" + para.LendNumber + "%");
        }

        //申請單狀態
        if (para.LendStatus.Count > 0)
        {
            int i = 0;
            string sqlLendStatusPara = string.Join(" OR ", para.LendStatus.Select(e =>
            {
                i++;
                sqlPara.Add("LendStatu_" + i.ToString(), e);
                return "pla.lend_status = @LendStatu_" + i.ToString();
            })
              );
            searchSql.AppendLine(@$"AND ({sqlLendStatusPara}) ");
        }
        #endregion

        #region 查詢
        string queryListSql = @$"SELECT
                                    DISTINCT
                                    pla.lend_id as 'LendId',--主表id為子表查詢做要求
                                    pla.lend_number as 'LendNumber',--借出單號
                                    pld.retrieve_number as 'RetrieveNumber',--調閱單號
                                    pla.application_time as 'CreateTime',--申請日期(資料表中是utc時間，需要轉換為操作者的時區時間格式YYYY/MM/DD)
                                    (SELECT COUNT(detail_id) FROM paper_lending_detail WHERE paper_lend_id = pla.lend_id and is_pickup_lend=1) AS LendCount,--共多少紙本
                                    (SELECT COUNT(detail_id) FROM paper_lending_detail WHERE paper_lend_id = pla.lend_id AND is_return = 1) AS ReturnCount,--已還多少紙本
                                    pla.lend_status as 'LendStatus',--申請單狀態
                                    (SELECT TOP 1 fun_name FROM sys_parameters WHERE para_code = N'lib_lendStatus' AND lang_type = N'{langType}' AND func_code = pla.lend_status) AS LendStatusName, --申請單狀態中文名稱
                                    (case when 
		                                exists(SELECT 1 FROM paper_lending_detail WHERE paper_lend_id = pla.lend_id  
		                                		and (
		                                				(overdue_day is not NULL  and overdue_day>0)
		                                			 or (should_return_time is not null and should_return_time is not null and CONVERT(date,should_return_time) < CONVERT(date, getutcdate()) )
		                                		) 
		                                )
		                            then 1
		                            else 0
		                            end
	                                )AS IsOverdueAndNotReturn--时候含有逾期未归还的
                                    FROM paper_lending_application AS pla
                                    LEFT JOIN paper_lending_demand AS pld ON pla.lend_id = pld.paper_lend_id
                                    LEFT JOIN paper_lending_detail AS pldd ON pla.lend_id = pldd.paper_lend_id
                                    LEFT JOIN (SELECT basic_id,(SELECT apply_number FROM paper_application_data WHERE application_id = paper_applica_id) AS apply_number FROM paper_basic_data) AS pbd ON pbd.basic_id = pldd.paper_basic_id
                                    LEFT JOIN (SELECT apply_number,contract_number,contract_name FROM v_getunconfirmedapplication) AS fa ON fa.apply_number = pbd.apply_number
                                    WHERE 1 = 1
                                          {searchSql}
                                    order by LendId
                                    ";

        List<LendUserModel> list = this.NpgsqlSearchByList<LendUserModel>(queryListSql, sqlPara, actFunc);
        #endregion

        return list;
    }
    #endregion

    #region 匯出
    /// <summary>
    /// 匯出
    /// </summary>
    /// <param name="para"></param>
    /// <param name="currentEmplid"></param>
    /// <param name="langType"></param>
    /// <returns></returns>
    public List<OnLendExportModel> QueryLendForUserExport(LendForUserExportPara para, string currentEmplid, string langType = "ZH-TW")
    {
        Dictionary<string, object> sqlPara = new Dictionary<string, object>();

        #region 拼接查詢條件
        StringBuilder searchSql = new StringBuilder();

        searchSql.AppendLine(@" AND  pla.lend_handler_emplid = @currentEmplid");
        sqlPara.Add("currentEmplid", currentEmplid);
        //紙本/合約編號
        if (!string.IsNullOrWhiteSpace(para.ContractNumber))
        {
            searchSql.AppendLine(@" AND (pldd.paper_code LIKE CONCAT(N'%', @ContractNumber, N'%')
                                            OR fa.contract_number LIKE CONCAT(N'%', @ContractNumber, N'%')
                                           )"
                                 );
            sqlPara.Add("ContractNumber", para.ContractNumber);
        }
        //紙本/合約名稱
        if (!string.IsNullOrWhiteSpace(para.ContractName))
        {
            searchSql.AppendLine(@" AND (pldd.paper_name LIKE CONCAT(N'%', @ContractName, N'%')
                                            OR fa.contract_name LIKE CONCAT(N'%', @ContractName, N'%')
                                            )");
            sqlPara.Add("ContractName", para.ContractName);
        }

        //取件日期
        if (para.LendTakeTime.Start.HasValue)
        {
            searchSql.AppendLine(@" AND pldd.pickup_time>= @LendTakeTimeBegin");
            sqlPara.Add("LendTakeTimeBegin", para.LendTakeTime.Start.Value);
        }
        if (para.LendTakeTime.End.HasValue)
        {
            searchSql.AppendLine(@" AND pldd.pickup_time< @LendTakeTimeEnd");
            sqlPara.Add("LendTakeTimeEnd", para.LendTakeTime.End.Value.AddDays(1));
        }

        //取件者
        if (para.PickupEmplids.Count > 0)
        {
            int i = 0;
            string sqlEmplidsPara = string.Join(",", para.PickupEmplids.Select(e =>
            {
                i++;
                sqlPara.Add("pickupemplid_" + i.ToString(), e);
                return "@pickupemplid_" + i.ToString();
            })
                );
            searchSql.AppendLine(@$" AND pldd.pickup_emplid in ({sqlEmplidsPara})");
        }

        //借出單號
        if (!string.IsNullOrWhiteSpace(para.LendNumber))
        {
            searchSql.AppendLine("AND pla.lend_number LIKE @LendNumber");
            sqlPara.Add("LendNumber", para.LendNumber);
        }

        //申請單狀態
        if (para.LendStatus.Count > 0)
        {
            int i = 0;
            string sqlLendStatusPara = string.Join(" OR ", para.LendStatus.Select(e =>
            {
                i++;
                sqlPara.Add("LendStatu_" + i.ToString(), e);
                return "pla.lend_status = @LendStatu_" + i.ToString();
            })
              );
            searchSql.AppendLine(@$"AND ({sqlLendStatusPara}) ");
        }
        #endregion

        string sql = @$"SELECT
                            pla.lend_id as 'LendId',
                            pla.lend_number as 'LendNumber',--借出單號
                            pld.retrieve_number as 'RetrieveNumber',--調閱單號
                            pla.application_time as 'CreateTime',--申請日期(資料表中是utc時間，需要轉換為操作者的時區時間格式YYYY/MM/DD)
                            pla.lend_status  as 'LendStatus',--申請單狀態
                            (SELECT fun_name FROM sys_parameters WHERE para_code = N'lib_lendStatus' AND lang_type = N'{langType}' AND func_code = pla.lend_status) AS LendStatusName,--申請單狀態中文
                            fa.contract_number as 'ContractNumber',--合約編號
                            pldd.paper_code as 'PaperCode',--紙本編號
                            pldd.paper_name as 'PaperName',--紙本名稱
                            (SELECT DISTINCT auth_date_end FROM V_GetOtherRetrieveNumber WHERE retrieve_number = pld.retrieve_number) AS AuthDateEnd,--調閱單號起始時間
                            should_return_time as 'ShouldReturnTime',--應歸還日期(當主表中，申請單狀態為待取件時，應歸還日期應該為pldd.retrieve_start_time)
                            pldd.actual_return_time as ActualReturnTime,--实际归还日,
                            pldd.overdue_day as OverdueDay, --逾期天数
                            is_pickup_lend as IsPickupLend, --是否已经取件
                            is_return  as 'IsReturn' --歸還狀態1：已歸還；0：未歸還
                            FROM paper_lending_application AS pla
                            LEFT JOIN paper_lending_demand AS pld ON pla.lend_id = pld.paper_lend_id
                            LEFT JOIN paper_lending_detail AS pldd ON pla.lend_id = pldd.paper_lend_id
                            LEFT JOIN (SELECT basic_id,(SELECT apply_number FROM paper_application_data WHERE application_id = paper_applica_id) AS apply_number FROM paper_basic_data) AS pbd ON pbd.basic_id = pldd.paper_basic_id
                            LEFT JOIN (SELECT apply_number,contract_number,contract_name FROM v_getunconfirmedapplication) AS fa ON fa.apply_number = pbd.apply_number
                            WHERE 1 = 1
                             {searchSql}
                            order by    LendId , PaperCode asc
                            ";

        return NpgsqlSearchByList<OnLendExportModel>(sql, sqlPara);
    }
    #endregion

    #region 查询纸本借阅详情
    /// <summary>
    /// 查询纸本借阅详情
    /// </summary>
    /// <returns></returns>
    public List<PaperLendingDetail> QueryPaperLendingDetailList(int paperLendId, Action<PaperLendingDetail> actFunc)
    {
        string sql = @$"SELECT
                            fa.contract_number as 'ContractNumber',--合約編號
                            paper_code as 'PaperCode',--紙本編號
                            paper_name as 'PaperName',--紙本名稱
                            pldd.retrieve_number as 'RetrieveNumber',--調閱單號
                            (SELECT DISTINCT auth_date_end FROM V_GetOtherRetrieveNumber WHERE retrieve_number = pldd.retrieve_number) AS AuthDateEnd,--調閱單號結束時間
                            pld.should_return_time as 'ShouldReturnTime', --應歸還日期(當主表中，申請單狀態為待取件時，應歸還日期應該為vo.auth_date_end)
                            pld.actual_return_time as ActualReturnTime,--实际归还日,
                            pld.overdue_day as OverdueDay, --逾期天数
                            is_return as 'IsReturn',--歸還狀態1：已歸還；0：未歸還
                            is_pickup_lend as IsPickupLend
                            FROM paper_lending_detail AS pld
                            LEFT JOIN paper_lending_demand AS pldd ON pld.paper_demand_id = pldd.demand_id
                            left join paper_lending_application as pla on pld.paper_lend_id = pla.lend_id
                            LEFT JOIN (SELECT basic_id,(SELECT apply_number FROM paper_application_data WHERE application_id = paper_applica_id) AS apply_number FROM paper_basic_data) AS pbd ON pbd.basic_id = pld.paper_basic_id
                            LEFT JOIN (SELECT apply_number,contract_number FROM v_getunconfirmedapplication) AS fa ON fa.apply_number = pbd.apply_number
                            WHERE (is_pickup_lend=1 or pla.lend_status='02') and pld.paper_lend_id= @paperLendId
                           ";
        return NpgsqlSearchByList(sql, new { paperLendId }, actFunc);
    }
    #endregion

    #region 查询纸本借阅详情(检视页面用)
    /// <summary>
    /// 查询纸本借阅详情(检视页面用)
    /// </summary>
    /// <returns></returns>
    public List<Pldv> QueryPldVList(int paperLendId, string langType, Action<Pldv> actFunc)
    {
        string sql = @$"
                        SELECT
                                pld.detail_id,
                                pld.paper_basic_id as 'PaperBasicId',
                                fa.contract_number as 'ContractNumber',--合約編號
                                pld.paper_code as 'PaperCode',--紙本編號
                                pld.paper_name as 'PaperName',--紙本名稱
                                pldd.retrieve_number as 'RetrieveNumber',--調閱單號
                                (SELECT DISTINCT auth_date_end FROM V_GetOtherRetrieveNumber WHERE retrieve_number = pldd.retrieve_number) AS AuthDateEnd,--調閱單號結束時間
                                pld.should_return_time as 'ShouldReturnTime', --應歸還日期(當主表中，申請單狀態為待取件時，應歸還日期應該為vo.auth_date_end)
                                pba.paper_entry_status as PaperEntryStatus,
		                        (SELECT fun_name FROM sys_parameters WHERE lang_type = N'{langType}' AND para_code = N'lib_paperEntryStatus' AND func_code = pba.paper_entry_status) AS PaperEntryStatusName,--入庫狀態
		                        pba.paper_position as PaperPosition, --存放位置
		                        pba.parer_borrow_days as ParerBorrowDays, --借閱日上限，天數
		                        (SELECT STRING_AGG(fun_name, ', ') FROM sys_parameters WHERE para_code = N'lib_returnStatus' AND lang_type = N'{langType}' AND func_code IN(SELECT value  FROM STRING_SPLIT(pba.paper_return_status, ','))) AS ReturnStatusName,--紙本現狀中文名稱
                                is_return as 'IsReturn',--歸還狀態1：已歸還；0：未歸還
                                is_pickup_lend as IsPickupLend,
                        		actual_return_time as  ActualReturnTime
                                FROM paper_lending_detail AS pld
                                LEFT JOIN paper_lending_demand AS pldd ON pld.paper_demand_id = pldd.demand_id
		                        left join paper_basic_data pba on pld.paper_basic_id=pba.basic_id
                                LEFT JOIN (SELECT basic_id,(SELECT apply_number FROM paper_application_data WHERE application_id = paper_applica_id) AS apply_number FROM paper_basic_data) AS pbd ON pbd.basic_id = pld.paper_basic_id
                                LEFT JOIN (SELECT apply_number,contract_number FROM v_getunconfirmedapplication) AS fa ON fa.apply_number = pbd.apply_number
                                WHERE pld.paper_lend_id = @paperLendId
                           ";
        return NpgsqlSearchByList(sql, new { paperLendId }, actFunc);
    }
    #endregion

    #region 查询纸本未借出明細((检视页面用)
    /// <summary>
    /// 查询纸本未借出明細((检视页面用)
    /// </summary>
    /// <returns></returns>
    public List<Pldv> QueryPaperUnlendingDetailList(int paperLendId, string langType, Action<Pldv> actFunc)
    {
        string sql = @$"
                                SELECT
                                pld.detail_id,
                                pld.paper_basic_id as 'PaperBasicId',
                                fa.contract_number as 'ContractNumber',--合約編號
                                pld.paper_code as 'PaperCode',--紙本編號
                                pld.paper_name as 'PaperName',--紙本名稱
                                pldd.retrieve_number as 'RetrieveNumber',--調閱單號
                                (SELECT DISTINCT auth_date_end FROM V_GetOtherRetrieveNumber WHERE retrieve_number = pldd.retrieve_number) AS AuthDateEnd,--調閱單號結束時間
                                pld.should_return_time as 'ShouldReturnTime', --應歸還日期(當主表中，申請單狀態為待取件時，應歸還日期應該為vo.auth_date_end)
                                pld.paper_entry_status as PaperEntryStatus,
		                        (SELECT fun_name FROM sys_parameters WHERE lang_type = N'{langType}' AND para_code = N'lib_paperEntryStatus' AND func_code = pld.paper_entry_status) AS PaperEntryStatusName,--入庫狀態
		                        pba.paper_position as PaperPosition, --存放位置
		                        pba.parer_borrow_days as ParerBorrowDays, --借閱日上限，天數
		                        (SELECT STRING_AGG(fun_name, ', ') FROM sys_parameters WHERE para_code = N'lib_returnStatus' AND lang_type = N'{langType}' AND func_code IN(SELECT value  FROM STRING_SPLIT(pba.paper_return_status, ','))) AS ReturnStatusName,--紙本現狀中文名稱
                                is_return as 'IsReturn',--歸還狀態1：已歸還；0：未歸還
                                is_pickup_lend as IsPickupLend,
                        		actual_return_time as  ActualReturnTime
                                FROM paper_unlending_detail AS pld
                                LEFT JOIN paper_lending_demand AS pldd ON pld.paper_demand_id = pldd.demand_id
		                        left join paper_basic_data pba on pld.paper_basic_id=pba.basic_id
                                LEFT JOIN (SELECT basic_id,(SELECT apply_number FROM paper_application_data WHERE application_id = paper_applica_id) AS apply_number FROM paper_basic_data) AS pbd ON pbd.basic_id = pld.paper_basic_id
                                LEFT JOIN (SELECT apply_number,contract_number FROM v_getunconfirmedapplication) AS fa ON fa.apply_number = pbd.apply_number
                                WHERE pld.paper_lend_id = @paperLendId
                           ";
        return NpgsqlSearchByList(sql, new { paperLendId }, actFunc);
    }
    #endregion

    #region 获取填单人与经办人信息 (借出检视页面所需)
    /// <summary>
    /// 获取填单人与经办人信息 (借出检视页面所需)
    /// </summary>
    /// <param name="lendId">出借主表主键</param>
    /// <returns></returns>
    public PersonnelInfo QueryPersonnelInfo(int lendId, string langType)
    {
        string sql = @$"SELECT
                            lend_number as 'LendNumber',--借出單號
                            application_time as 'ApplicationTime',--申請日期
                            lend_fill_emplid as 'LendFillEmplid',--填單人工號
                            lend_fill_deptid as 'LendFillDeptid',--填單人部門代號
                            fee.name AS LendFillCName,--填單人中文名
                            fee.name_a AS LendFillEName,--填單人英文名
                            fee.phone_a AS LendFillPhone,--填單人分機
                            fee.prefix_dial_code_a AS LendFillPrefixDialCode,--填單人區碼
                            fee.email_address_a AS LendFillEmail,--填單人郵件
                            lend_handler_emplid as 'LendHandlerEmplid',--經辦人工號
                            lend_handler_deptid as 'LendHandlerDeptid',--經辦人部門代號
                            hee.name AS LendHandlerCName,--經辦人中文名
                            hee.name_a AS DendHandlerEName,--經辦人英文名
                            hee.phone_a AS LendHandlerPhone,--經辦人分機
                            hee.prefix_dial_code_a AS LendHandlerPrefixDialCode,--經辦人區碼
                            hee.email_address_a AS LendHandlerEmail,--經辦人郵件
                            pla.lend_status as LendStatus, --申请单状态编码
                            (select fun_name from sys_parameters where para_code = N'lib_lendStatus' and lang_type=N'{langType}' and func_code= pla.lend_status) as LendStatusName,
                            lend_fill_bg as LendFillBg,
                            lend_handler_bg as LendHandlerBg,
							void_reason as VoidReason,
                            iif(fee.termination is null , 1,0) as FillOnJobStatus, --填单人在职状态,1在职，0离职
                            iif(hee.termination is null , 1,0) as HandlerOnJobStatus --经办人在职状态,1在职，0离职
                            FROM (SELECT 
			                            lend_number,
			                            application_time,
			                            lend_fill_emplid,
			                            lend_fill_deptid,
			                            lend_handler_emplid,
			                            lend_handler_deptid,
                                        lend_status,
                                       	lend_fill_bg ,
                                       	lend_handler_bg,
                                       	void_reason
	                              FROM paper_lending_application 
	                              WHERE lend_id = @lendId
	                              ) AS pla
                            LEFT JOIN (SELECT emplid,name,name_a,phone_a,email_address_a,termination, prefix_dial_code_a FROM ps_sub_ee_lgl_vw_a) AS fee ON fee.emplid = pla.lend_fill_emplid
                            LEFT JOIN (SELECT emplid,name,name_a,phone_a,email_address_a,termination, prefix_dial_code_a FROM ps_sub_ee_lgl_vw_a) AS hee ON hee.emplid = pla.lend_handler_emplid
                        ";
        var data = NpgsqlSearchBySingle<PersonnelInfo>(sql, new { lendId });
        return data;
    }
    #endregion

    #region 获取需求咨询信息 (借出检视页面所需)
    /// <summary>
    /// 获取需求咨询信息 (借出检视页面所需)
    /// </summary>
    /// <param name="lendId">出借主表主键</param>
    /// <returns></returns>
    public List<DemandInfo> QueryDemandInfo(int lendId)
    {
        string sql = @"SELECT 
                                DISTINCT
                                pld.retrieve_number as RetrieveNumber,--調閱單號
                                pld.borrow_days as BorrowDays,--借出天數
                                pld.demand_reason as DemandReason,--申請原因
                                pld.retrieve_reason as RetrieveReason, 
                                vo.discloseperson as Discloseperson,--顯示內部同仁/其他同仁：00：無；01：其他同仁；10：內部同仁；11：內部同仁+其他同仁
                                vo.empid as Empid,--工號
                                vo.ename as EName,--英文名稱
                                vo.depid as Depid,--部門代碼
                                vo.company as Company,--部門公司別
                                vo.otherperson as Otherperson,--其他同仁，一串字符，直接顯示
                                vo.other as Other,--其他說明
                                vo.applynumber  as Applynumber,--調閱案件單號
								vo.auth_date_start as AuthDateStart, --实际开放时间
								vo.auth_date_end as AuthDateEnd, --实际开放时间 
                                (select    max(pldd.actual_return_time) from  paper_lending_detail AS pldd where pldd.paper_demand_id = pld.demand_id ) as  ActualReturnTime--實際歸還日
                                FROM paper_lending_demand AS pld
                                LEFT JOIN V_GetOtherRetrieveNumber AS vo ON vo.retrieve_number = pld.retrieve_number
                                WHERE pld.paper_lend_id = @lendId
                            ";
        return NpgsqlSearchByList<DemandInfo>(sql, new { lendId });
    }
    #endregion

    #region 获取取件咨询信息 (借出检视页面所需)
    /// <summary>
    /// 获取取件咨询信息 (借出检视页面所需)
    /// </summary>
    /// <param name="lendId">出借主表主键</param>
    public PickUpInfo QueryPickUpInfo(int lendId, string langType)
    {
        string sql = @$"
                            SELECT DISTINCT
                            pickup_status as PickupStatus,--取件狀態
                            pickup_time as PickupTime,--取件日期
                            consignment_number as ConsignmentNumber,--取件單號
                            pickup_emplid as PickupEmplid,--取件者工號
                            ee.name as CName,--取件者中文名稱
                            ee.name_a as EName,--取件者英文名稱
                            actual_pickup_time as ActualPickupTime, --實際取件時間
                            (sELECT fun_name FROM sys_parameters WHERE para_code = N'lib_pickupStatus' and sys_parameters.func_code=pld.pickup_status and lang_type=N'{langType}') as PickupStatusName
                            FROM (SELECT * FROM paper_lending_detail WHERE paper_lend_id = @lendId  and is_pickup_lend=1) AS pld
                            LEFT JOIN (SELECT emplid,name,name_a FROM ps_sub_ee_lgl_vw_a) AS ee ON ee.emplid = pld.pickup_emplid
                        ";
        return NpgsqlSearchBySingle<PickUpInfo>(sql, new { lendId });
    }
    #endregion
}
