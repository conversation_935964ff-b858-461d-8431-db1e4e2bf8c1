﻿using Elegal.Flow.Api.Repository;
using Elegal.Interface.Api.Common.Control.Filter;
using Elegal.Interface.Api.Common.FuncHelper;
using Elegal.Interface.Api.Common.Model.ParaModel.FlowApi;
using Elegal.Interface.Api.Common.Model.ViewModel.FlowApi;
using Elegal.Orm.Dtos;
using Elegal.Interface.Api.Common.FuncService;
using NPOI.SS.UserModel;
using NPOI.XSSF.UserModel;
using NUnit.Framework.Internal.Execution;

namespace Elegal.Flow.Api.Services
{
    /// <summary>
    /// 特殊主體設定 -> 部門設定
    /// </summary>
    public class FnpEntityDeptService
    {
        private static readonly FnpEntityDeptRepository _repository = new FnpEntityDeptRepository();
        private static readonly FnpEntityUserRepository _user_repository = new FnpEntityUserRepository();

        public static PageResult<FnpEntityDeptModel> Query(qryFnpEntityDept qry)
        {
            PageResult<FnpEntityDeptModel> res = _repository.QueryDept(qry);

            if (res.Data != null && res.Data.Count > 0)
            {
                foreach (var item in res.Data)
                {
                    //部門名稱
                    item.dept_name = MvcContext.UserInfo.logging_locale.ToLower() == "ZH-TW".ToLower() ? item.descr : item.descr_a;

                    //託管人員公司
                    List<string> hostedCompany = _repository.GetHostCompany(item.deptid);
                    item.hosted_company = (hostedCompany != null && hostedCompany.Count() > 0) ? string.Join(", ", hostedCompany) : string.Empty;

                    //託管部門主體
                    List<string> hostedEntity = _repository.GetHostEntity(item.deptid);
                    item.hosted_entity = (hostedEntity != null && hostedEntity.Count() > 0) ? string.Join(", ", hostedEntity) : string.Empty;

                    if (item.operate_time != null)
                        item.local_operate_time = TimeZoneInfoConvertHelper.ConvertDateByTimeZoneByUtc(item.operate_time, MvcContext.UserInfo.time_zone);
                }
            }

            return res;
        }

        public static byte[] ExportDeptQuery(qryFnpEntityDept qry)
        {
            qry.page = new PageParam { PageIndex = 1, PageSize = 25 };

            PageResult<FnpEntityDeptModel> res = _repository.QueryDept(qry);

            if (res.Data != null && res.Data.Count > 0)
            {
                foreach (var item in res.Data)
                {
                    //部門名稱
                    item.dept_name = MvcContext.UserInfo.logging_locale.ToLower() == "ZH-TW".ToLower() ? item.descr : item.descr_a;

                    //託管人員公司
                    List<string> hostedCompany = _repository.GetHostCompany(item.deptid);
                    item.hosted_company = (hostedCompany != null && hostedCompany.Count() > 0) ? string.Join(", ", hostedCompany) : string.Empty;

                    //託管部門主體
                    List<string> hostedEntity = _repository.GetHostEntity(item.deptid);
                    item.hosted_entity = (hostedEntity != null && hostedEntity.Count() > 0) ? string.Join(", ", hostedEntity) : string.Empty;

                    if (item.operate_time != null)
                        item.local_operate_time = TimeZoneInfoConvertHelper.ConvertDateByTimeZoneByUtc(item.operate_time, MvcContext.UserInfo.time_zone);
                }
            }
            //创建工作表
            IWorkbook workbook = new XSSFWorkbook();
            ISheet sheet = workbook.CreateSheet("Sheet1");
            //设置表头
            List<string> heards = new List<string>()
            {
                 ActionFilter.GetMultilingualValue("export:export_fnpEntityDept:deptid"),
                 ActionFilter.GetMultilingualValue("export:export_fnpEntityDept:descr"),
                 ActionFilter.GetMultilingualValue("export:export_fnpEntityDept:ogCompany"),
                 ActionFilter.GetMultilingualValue("export:export_fnpEntityDept:ogEntity"),
                 ActionFilter.GetMultilingualValue("export:export_fnpEntityDept:hostedCompany"),
                 ActionFilter.GetMultilingualValue("export:export_fnpEntityDept:hostedEntity"),
                 ActionFilter.GetMultilingualValue("export:export_fnpEntityDept:specialEntitys"),
                 ActionFilter.GetMultilingualValue("export:export_fnpEntityDept:updateTime")
            };

            sheet.SetHeader(heards);

            //設置列寬
            sheet.SetColumnAutoWidth(0, 8);


            ICellStyle defaultstyle = sheet.Workbook.CreateCellStyle().SetDefaultBorder();

            sheet.WriteData(res?.Data ?? [], (item, row) =>
            {
                row.CreateElegalDefaultCell(0, defaultstyle).SetCellValue(item.deptid);
                row.CreateElegalDefaultCell(1, defaultstyle).SetCellValue(item.dept_name);
                row.CreateElegalDefaultCell(2, defaultstyle).SetCellValue(item.dept_company);
                row.CreateElegalDefaultCell(3, defaultstyle).SetCellValue(item.company_entity);
                row.CreateElegalDefaultCell(4, defaultstyle).SetCellValue(item.hosted_company);
                row.CreateElegalDefaultCell(5, defaultstyle).SetCellValue(item.hosted_entity);
                row.CreateElegalDefaultCell(6, defaultstyle).SetCellValue(item.dept_entity);
                row.CreateElegalDefaultCell(7, defaultstyle).SetCellValue(item.local_operate_time != null ? item.local_operate_time.ToString("yyyy/MM/dd HH:mm") + "/" + item.operate_euser : string.Empty);
            });

            return workbook.ToBytes();


            /*
            ExcelPackage.LicenseContext = LicenseContext.NonCommercial; // 設置LicenseContext屬性

            if (res.Data != null)
            {
                using (var package = new ExcelPackage())
                {
                    var worksheet = package.Workbook.Worksheets.Add("Sheet1");

                    // 添加Header
                    var headerRange = worksheet.Cells["A1:H1"];
                    headerRange.Style.Font.Bold = true; // 粗體
                    headerRange.Style.Border.Top.Style = ExcelBorderStyle.Medium;
                    headerRange.Style.Border.Bottom.Style = ExcelBorderStyle.Medium;
                    headerRange.Style.Border.Left.Style = ExcelBorderStyle.Medium;
                    headerRange.Style.Border.Right.Style = ExcelBorderStyle.Medium;
                    headerRange.Style.Font.Size = 12;
                    headerRange.Style.VerticalAlignment = ExcelVerticalAlignment.Center;

                    // 添加Header
                    worksheet.Cells["A1"].Value = ActionFilter.GetMultilingualValue("export:export_fnpEntityDept:deptid");
                    worksheet.Cells["B1"].Value = ActionFilter.GetMultilingualValue("export:export_fnpEntityDept:descr");
                    worksheet.Cells["C1"].Value = ActionFilter.GetMultilingualValue("export:export_fnpEntityDept:ogCompany");
                    worksheet.Cells["D1"].Value = ActionFilter.GetMultilingualValue("export:export_fnpEntityDept:ogEntity");
                    worksheet.Cells["E1"].Value = ActionFilter.GetMultilingualValue("export:export_fnpEntityDept:hostedCompany");
                    worksheet.Cells["F1"].Value = ActionFilter.GetMultilingualValue("export:export_fnpEntityDept:hostedEntity");
                    worksheet.Cells["G1"].Value = ActionFilter.GetMultilingualValue("export:export_fnpEntityDept:specialEntitys");
                    worksheet.Cells["H1"].Value = ActionFilter.GetMultilingualValue("export:export_fnpEntityDept:updateTime");

                    var row = 2;
                    foreach (FnpEntityDeptModel data in res.Data)
                    {
                        var dataRange = worksheet.Cells[$"A{row}:H{row}"];
                        // 邊框
                        dataRange.Style.Border.Top.Style = ExcelBorderStyle.Medium;
                        dataRange.Style.Border.Bottom.Style = ExcelBorderStyle.Medium;
                        dataRange.Style.Border.Left.Style = ExcelBorderStyle.Medium;
                        dataRange.Style.Border.Right.Style = ExcelBorderStyle.Medium;
                        dataRange.Style.Font.Size = 12;
                        dataRange.Style.VerticalAlignment = ExcelVerticalAlignment.Center;

                        worksheet.Cells[$"A{row}"].Value = data.deptid;
                        worksheet.Cells[$"B{row}"].Value = data.dept_name;
                        worksheet.Cells[$"C{row}"].Value = data.dept_company;
                        worksheet.Cells[$"D{row}"].Value = data.company_entity;
                        worksheet.Cells[$"E{row}"].Value = data.hosted_company;
                        worksheet.Cells[$"F{row}"].Value = data.hosted_entity;
                        worksheet.Cells[$"G{row}"].Value = data.dept_entity;
                        worksheet.Cells[$"H{row}"].Value = data.local_operate_time != null ? data.local_operate_time.ToString("yyyy/MM/dd HH:mm") + "/" + data.operate_euser : string.Empty;

                        row++;
                    }

                    worksheet.Columns.AutoFit();

                    worksheet.Columns.Style.WrapText = true;

                    return package.GetAsByteArray();
                }
            }

            return null;
            */
        }

        public static List<FnpEntityDeptDetailModel> QueryDetails(string deptid)
        {
            if (string.IsNullOrEmpty(deptid))
                throw new Exception(ActionFilter.GetMultilingualValue("custom:messageTitle:systemError"));

            List<FnpEntityDeptDetailModel> res = new List<FnpEntityDeptDetailModel>();

            if (!string.IsNullOrEmpty(deptid))
                res = _user_repository.GetDeptDetail(deptid);

            if (res != null && res.Count > 0)
            {
                foreach (var item in res)
                {
                    if (item.operate_time != null)
                        item.local_operate_time = TimeZoneInfoConvertHelper.ConvertDateByTimeZoneByUtc(item.operate_time, MvcContext.UserInfo.time_zone);
                }
            }

            return res;
        }

        public static bool UpdateFnpEntityDept(string deptid, List<string> entity_id_list)
        {
            if (!_repository.UpdateFnpEntityDept(deptid, entity_id_list))
                throw new Exception(ActionFilter.GetMultilingualValue("custom:messageTitle:createFail"));
            if (!_repository.UpdateFnpEntityDeptSpecial(deptid, entity_id_list))
                throw new Exception(ActionFilter.GetMultilingualValue("custom:messageTitle:createFail"));

            return true;
        }

        public static List<string> DeleteFnpEntityDeptByEntityId(string entity_id)
        {
            if (string.IsNullOrEmpty(entity_id))
                throw new Exception(ActionFilter.GetMultilingualValue("custom:messageTitle:systemError"));
            return _repository.DeleteFnpEntityDept(entity_id);
        }
    }
}

