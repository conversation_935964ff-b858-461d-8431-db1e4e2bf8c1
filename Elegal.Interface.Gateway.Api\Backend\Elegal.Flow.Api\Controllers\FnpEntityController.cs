﻿using Elegal.Flow.Api.Services;
using Elegal.Interface.Api.Common.Control.Web;
using Elegal.Interface.Api.Common.Model.ParaModel.FlowApi;
using Elegal.Interface.Api.Common.Model.ResultModel;
using Elegal.Interface.ApiData.Service.FuncService;
using Elegal.Interface.ApiData.Service.Model.QueryCondition;
using Elegal.Orm.Dtos;
using iText.StyledXmlParser.Jsoup.Nodes;
using Microsoft.AspNetCore.Mvc;

namespace Elegal.Flow.Api.Controllers
{
    /// <summary>
    /// 主體
    /// </summary>
    [Route("[controller]")]
    [ApiController]
    public class FnpEntityController : BaseController
    {
        #region 下拉數據源
        /// <summary>
        /// 下拉數據源
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("GetSelectData")]
        public async Task<ApiResultModelByObject> GetSelectData(int? areaId)
        {
            ApiResultModelByObject apiResult = new();

            FnpEntityQueryCondition condition = new()
            {
                AreaId = areaId,
                OrderBys = new List<OrderByParam>()
                    {
                        new OrderByParam()
                        {
                            Field="Entity",
                            Order=OrderBy.ASC
                        }
                    }
            };
            apiResult.listData = FnpEntityDataService.Query(condition);
            apiResult.rtnSuccess = true;

            return await Task.FromResult(apiResult);
        }
        #endregion

        #region 下拉數據源(條件查詢)
        /// <summary>
        /// 下拉數據源(條件查詢)
        /// </summary>
        /// <param name="condition">查询参数</param>
        /// <returns></returns>
        [HttpPost]
        [Route("QueryData")]
        public async Task<ApiResultModelByObject> QueryData(FnpEntityQueryCondition condition)
        {
            ApiResultModelByObject apiResult = new();
            condition.OrderBys = new List<OrderByParam>() { new OrderByParam() { Field = "Entity", Order = OrderBy.ASC } };
            apiResult.listData = FnpEntityDataService.Query(condition);
            apiResult.rtnSuccess = true;

            return await Task.FromResult(apiResult);
        }
        #endregion

        #region 下拉數據源(合約管理人/總部法務行政關卡/兩者的代理人對應的主體信息查詢)
        /// <summary>
        /// 下拉數據源(合約管理人/總部法務行政關卡/兩者的代理人對應的主體信息查詢)
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("QueryByOriginal")]
        public async Task<ApiResultModelByObject> QueryByOriginal()
        {
            ApiResultModelByObject apiResult = new();
            apiResult.listData = FnpEntityService.QueryByOriginal();
            apiResult.rtnSuccess = true;
            return await Task.FromResult(apiResult);
        }
        #endregion

        #region 主體信息查詢(條件查詢)
        /// <summary>
        /// 主體信息查詢(條件查詢)
        /// </summary>
        /// <param name="condition"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("Query")]
        public async Task<ApiResultModelByObject> Query(FnpEntityQueryModel condition)
        {
            ApiResultModelByObject apiResult = new();
            apiResult.listData = FnpEntityService.Query(condition);
            apiResult.rtnSuccess = true;

            return await Task.FromResult(apiResult);
        }
        #endregion

        #region 根據區域查詢主體數據
        /// <summary>
        /// 根據區域查詢主體數據
        /// </summary>
        /// <param name="areaIds"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("QueryDataByArea")]
        public async Task<ApiResultModelByObject> QueryDataByArea(List<int?> areaIds)
        {
            ApiResultModelByObject apiResult = new();
            apiResult.listData = FnpEntityService.QueryDataByArea(areaIds);
            apiResult.rtnSuccess = true;

            return await Task.FromResult(apiResult);
        }
        #endregion

        #region 根據主體id集合獲取主體信息
        /// <summary>
        /// 根據主體id集合獲取主體信息
        /// </summary>
        /// <param name="entityIds"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("GetFnpEntityDataByEntityID")]
        public async Task<ApiResultModelByObject> GetFnpEntityDataByEntityID(List<string> entityIds)
        {
            ApiResultModelByObject apiResult = new();
            apiResult.listData = FnpEntityService.GetFnpEntityDataByEntityID(entityIds);
            apiResult.rtnSuccess = true;

            return await Task.FromResult(apiResult);
        }
        #endregion

        #region 根據主體獲取關聯主體數據
        /// <summary>
        /// 根據主體獲取關聯主體數據
        /// </summary>
        /// <param name="entityIds"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("GetFnpEntityDataByGroupEntityID")]
        public async Task<ApiResultModelByObject> GetFnpEntityDataByGroupEntityID(List<string> entityIds)
        {
            ApiResultModelByObject apiResult = new();
            apiResult.listData = FnpEntityService.GetFnpEntityDataByGroupEntityID(entityIds);
            apiResult.rtnSuccess = true;
            return await Task.FromResult(apiResult);
        } 
        #endregion
    }
}
