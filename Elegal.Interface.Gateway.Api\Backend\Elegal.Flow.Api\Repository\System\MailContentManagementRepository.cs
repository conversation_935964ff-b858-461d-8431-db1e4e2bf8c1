﻿using Elegal.Interface.Api.Common.Control.Filter;
using Elegal.Interface.Api.Common.Model.ParaModel.FlowApi;
using Elegal.Interface.Api.Common.Model.ViewModel;
using Elegal.Interface.Api.Common.Repository;
using System.Text;

namespace Elegal.Flow.Api.Repository.System
{
    /// <summary>
    /// 郵件模板頁面
    /// </summary>
    /// <returns></returns>
    public class MailContentManagementRepository : BaseRepository
    {
        /// <summary>
        /// 實例化
        /// </summary>
        /// <returns></returns>
        public MailContentManagementRepository() : base() { }

        /// <summary>
        /// 郵件模板頁面查詢
        /// </summary>
        /// <returns></returns>
        public List<MailSearchContentModel> QueryMailContent(MailContentModel model)
        {
            StringBuilder sqlBuilder = new();
            UserInfoModel user = MvcContext.UserInfo;
            sqlBuilder.Append(@$"SELECT rowid,
                                func_module,is_linkmail,
                                zh_content_json,en_content_json,mail_rc_code,
                                (SELECT fun_name FROM dbo.sys_parameters WHERE para_code = N'e_funModule' AND lang_type = 'ZH-TW' and func_code = func_module) func_module_name,
                                mail_type,
                                sp.fun_name AS mail_type_name,
                                send_mode,
                                (SELECT fun_name FROM dbo.sys_parameters WHERE para_code = N'e_sendMode' AND lang_type = 'ZH-TW' and func_code = send_mode) send_mode_name,
                                mail_subject, mail_re_type, mail_cc_type,
                                mail_zh_content,having_table,mail_en_content, mail_content, advance_days, interval_days, mail_reamrk,is_re_agent,
                                ee.name as operate_cuser,ee.name_a as operate_euser, COALESCE(modify_time,create_time) AS operate_time ,table_json
                                FROM user_email_content AS uem
                                INNER JOIN (SELECT fun_name,func_code FROM dbo.sys_parameters WHERE para_code = N'e_mailType' AND is_used = 1 AND lang_type = 'ZH-TW') AS sp ON sp.func_code = uem.mail_type
                                LEFT JOIN (SELECT emplid,name,name_a FROM dbo.ps_sub_ee_lgl_vw_a) AS ee ON ee.emplid = COALESCE(modify_user,create_user) WHERE 1 = 1");
            if (model.rowid != null)
                sqlBuilder.Append(@$" AND rowid = @rowid");
            sqlBuilder.Append(@$" order by func_module,mail_type");
            return NpgsqlSearchByList<MailSearchContentModel>(sqlBuilder.ToString(), new { model.rowid },
                "time_zone", user.time_zone);
        }

        /// <summary>
        /// 獲取收件者類型名
        /// </summary>
        /// <param name="approvePerson">關卡人員</param>
        /// <param name="role">角色</param>
        /// <param name="recipients">收件者</param>
        /// <returns></returns>
        internal string GetMailRecipients(string approvePerson, string role, string recipients)
        {
            string sql = $@"
                select string_agg(value,',') from (
	                select fun_name as value FROM dbo.sys_parameters WHERE
	                para_code = N'approverManagement' AND lang_type = '{MvcContext.UserInfo.logging_locale}' and func_code in (SELECT value FROM STRING_SPLIT(@approvePerson, ','))
	                union
	                select r_name as value FROM dbo.p_role WHERE r_id != 0 AND r_id in (SELECT value FROM STRING_SPLIT(@role, ','))
	                union
	                select fun_name as value FROM dbo.sys_parameters WHERE
	                para_code in (N'e_contractRcType',N'e_rcType') AND lang_type = '{MvcContext.UserInfo.logging_locale}' and func_code in (SELECT value FROM STRING_SPLIT(@recipients, ','))
                )t";
            return (string)NpgsqlSearchBySingleValue(sql, new { approvePerson, role, recipients });
        }
    }
}
