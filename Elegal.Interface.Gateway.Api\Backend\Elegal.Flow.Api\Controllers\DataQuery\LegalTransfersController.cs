﻿using Elegal.Flow.Api.Services.DataQuery;
using Elegal.Interface.Api.Common.Control.Filter;
using Elegal.Interface.Api.Common.Control.Web;
using Elegal.Interface.Api.Common.FuncHelper;
using Elegal.Interface.Api.Common.Model.Enum;
using Elegal.Interface.Api.Common.Model.ParaModel.FlowApi;
using Elegal.Interface.Api.Common.Model.ResultModel;
using Elegal.Interface.Api.Common.Model.ResultModel.FlowApi;
using Elegal.Interface.Api.Common.Model.ViewModel;
using Elegal.Interface.ApiData.Service.FuncService;
using Elegal.Interface.ApiData.Service.Model.Condition;
using Elegal.Interface.ApiData.Service.Model.DbModel;
using Microsoft.AspNetCore.Mvc;
using Minio.DataModel.Select;
using System.Text;

namespace Elegal.Flow.Api.Controllers.DataQuery
{

    /// <summary>
    /// 承办法务转单
    /// </summary>
    [Route("[controller]")]
    [ApiController]
    public class LegalTransfersController : BaseController
    {
        /// <summary>
        /// 列表数据
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [Route("ListData")]
        public async Task<ApiResultModelByObject> ListData(TransferSearchModel model)
        {
            var listAllData = LegalTransfersService.ListData(model);
            List<UndertakeLegalTransferResultModel> list = new();
            foreach (UndertakeLegalTransferResultModel resultModel in listAllData)
            {
                //極機密案件且當前案件狀態為已核准狀態需要進行隱碼處理
                if (!string.IsNullOrEmpty(resultModel.confiden_level) && !string.IsNullOrEmpty(resultModel.application_state) && "01".ToUpper().Equals(resultModel.confiden_level.ToUpper())  && (resultModel.application_state.ToUpper() != "I".ToUpper() && resultModel.application_state.ToUpper() != "T".ToUpper()))
                {
                    list.Add(HiddenValueConvertHelper.ConvertToHiddenBySingle<UndertakeLegalTransferResultModel>(resultModel));
                }
                else { list.Add(resultModel); }
            }
            ApiResultModelByObject apiResult = new()
            {
                listData = listAllData,
                rtnSuccess = true
            };
            ActionFilter.InitLogRecord(model, log =>
            {
                StringBuilder stringBuilder = new();
                if (!string.IsNullOrEmpty(model.apply_number))
                    stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("HandlerTransfersManagement_applyNumber", true)}：{model.apply_number}");
                if (model.emp != null)
                    stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("HandlerTransfersManagement_legalTransferEmp", true)}：{model.emp.NameA}/{model.emp.Name}({model.emp.Emplid})");
                log.Detail = stringBuilder.ToString();
            });
            return await Task.FromResult(apiResult);
        }

        /// <summary>
        /// 案件清单
        /// </summary>
        /// <param name="emplid">工号</param>
        /// <returns></returns>
        [HttpGet]
        [Route("CaseData")]
        public async Task<ApiResultModelByObject> CaseData(string emplid)
        {
            var listAllData = LegalTransfersService.CaseData(emplid);
            List<UndertakeLegalTransferResultModel> list = new();
            foreach (UndertakeLegalTransferResultModel resultModel in listAllData)
            {
                //極機密案件且當前案件狀態為已核准狀態需要進行隱碼處理
                if (!string.IsNullOrEmpty(resultModel.confiden_level) && !string.IsNullOrEmpty(resultModel.application_state) && "01".ToUpper().Equals(resultModel.confiden_level.ToUpper()) && (resultModel.application_state.ToUpper() != "I".ToUpper() && resultModel.application_state.ToUpper() != "T".ToUpper()))
                {
                    list.Add(HiddenValueConvertHelper.ConvertToHiddenBySingle<UndertakeLegalTransferResultModel>(resultModel));
                }
                else { list.Add(resultModel); }
            }
            ApiResultModelByObject apiResult = new()
            {
                listData = listAllData,
                rtnSuccess = true
            };
            return await Task.FromResult(apiResult);
        }

        /// <summary>
        /// 获取交接人
        /// </summary>
        /// <param name="emplid">工号</param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetHandover")]
        public async Task<ApiResultModelByObject> GetHandover(string emplid)
        {
            ApiResultModelByObject apiResult = new();
            apiResult.listData = LegalTransfersService.GetHandover(emplid);
            apiResult.rtnSuccess = true;
            return await Task.FromResult(apiResult);
        }

        /// <summary>
        /// 插入承辦法務轉單
        /// </summary>
        /// <param name="model">新增数据</param>
        /// <returns></returns>
        [HttpPut]
        [Route("Submit")]
        public async Task<ApiResultModelByObject> InsertTransferHistory([FromBody] ModifyHandlerTransferModel model)
        {
            ApiResultModelByObject apiResult = new();
            //转单前校验数据，若数据异常则提醒数据变更
            List<ModifyHandlerTransferModel> ModifyList = new();
            model.apply_number_list.ForEach(e => {
                UndertakeLegalTransferResultModel applyModel = LegalTransfersService.CaseDataByApplyNumber(e);
                if (!applyModel.legal_affairs_emplid.Contains(model.apply_pic_emp?.Emplid))
                {
                    ModifyList.Add(model);
                }
            });
            if (ModifyList.Count > 0)
            {
                return new ApiResultModelByObject()
                {
                    messageType = MessageTypeUtils.Warning.ToString(),
                    rtnSuccess = false,
                    messageTitle = ActionFilter.GetMultilingualValue("custom:messageTitle:dataNotexist"),
                    messageContent = new List<string> { ActionFilter.GetMultilingualValue("custom:messageContent:dataChurn") }
                };
            }
            //转单业务
            model.create_user = MvcContext.UserInfo.current_emp;
            apiResult.listData = LegalTransfersService.InsertTransferHistory(model);
            apiResult.rtnSuccess = true;
            ActionFilter.InitLogRecord(model, log =>
            {
                StringBuilder stringBuilder = new();
                if (model.apply_number_list.Count > 0)
                    stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("HandlerTransfersManagement_applyNumber", true)}：{string.Join(",", model.apply_number_list)}");
                if (model.apply_legal_emp != null)
                    stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("HandlerTransfersManagement_applyLegalEmp", true)}：{model.apply_legal_emp.NameA}/{model.apply_legal_emp.Name}({model.apply_legal_emp.Emplid})");
                if (model.handover_emp != null)
                    stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("HandlerTransfersManagement_handoverEmp", true)}：{model.handover_emp.NameA}/{model.handover_emp.Name}({model.handover_emp.Emplid})");
                if (!string.IsNullOrEmpty(model.handover_deptid))
                    stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("HandlerTransfersManagement_handoverDeptid", true)}：{model.handover_deptid}");
                if (!string.IsNullOrEmpty(model.transfer_remarks))
                    stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("HandlerTransfersManagement_transferRemarks", true)}：{model.transfer_remarks}");
                log.Detail = stringBuilder.ToString();
            });
            return await Task.FromResult(apiResult);
        }

        /// <summary>
        /// 汇出记录日志
        /// </summary>
        /// <param name="model">新增数据</param>
        /// <returns></returns>
        [HttpPost]
        [Route("ExportLog")]
        public async Task<ApiResultModelByObject> ExportLog(TransferSearchModel model)
        {
            ApiResultModelByObject apiResult = new();
            ActionFilter.InitLogRecord(model, log =>
            {
                StringBuilder stringBuilder = new();
                if (!string.IsNullOrEmpty(model.apply_number))
                    stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("HandlerTransfersManagement_applyNumber", true)}：{model.apply_number}");
                if (model.emp != null)
                    stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("HandlerTransfersManagement_legalTransferEmp", true)}：{model.emp.NameA}/{model.emp.Name}({model.emp.Emplid})");
                log.Detail = stringBuilder.ToString();
            });
            apiResult.rtnSuccess = true;
            return await Task.FromResult(apiResult);
        }
    }
}
