﻿using Elegal.Flow.Api.Repository.Process;
using Elegal.Interface.Api.Common.Control.Filter;
using Elegal.Interface.Api.Common.FuncService;
using Elegal.Interface.Api.Common.Model.Enum;
using Elegal.Interface.Api.Common.Model.ParaModel.FlowApi;
using Elegal.Interface.Api.Common.Model.ResultModel;
using Elegal.Interface.Api.Common.Model.ViewModel.FlowApi;
using Elegal.Interface.Api.Common.Repository;
using Elegal.Interface.ApiData.Service.FuncService;
using Elegal.Interface.ApiData.Service.Model.DbModel;
using Elegal.Interface.ApiData.Service.Model.QueryCondition;
using Elegal.Interface.ApiData.Service.Model.ViewModel;
using Elegal.Orm;
using Elegal.Orm.Dtos;
using Elegal.Orm.Utils;
using System.Text;

namespace Elegal.Flow.Api.Services.Process
{
    /// <summary>
    /// 關卡人員管理
    /// </summary>
    public static class SysApproverManagementService
    {
        private static readonly SysApproverManagementRepository _repostory = new();
        private static PublicHelperRepository _publicHelperRepository = new PublicHelperRepository();

        #region 新增數據
        /// <summary>
        /// 新增數據
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public static ApiResultModelByObject AddData(SysApproverManagementAddModel model)
        {
            #region 參數驗證

            #region 判斷關卡名稱是否存在
            if (!DbAccess.Exists<SysParameters>(new SysParametersViewModel() { ParaCode = "ApproverManagement", FuncCode = model.ApproverType.Key, LangType = MvcContext.UserInfo.logging_locale }))
            {
                return new ApiResultModelByObject()
                {
                    messageTitle = ActionFilter.GetMultilingualValue("custom:messageTitle:dataNotexist"),
                    messageContent = new List<string> { $"{model.ApproverType.Value} {ActionFilter.GetMultilingualValue("custom:messageContent:approverManagementDeleted")}" }
                };
            }
            #endregion

            #region 判斷主體是否存在
            foreach (var entityId in model.EntityId)
            {
                if (!DbAccess.Exists<FnpEntity>(new FnpEntityViewModel() { EntityId = entityId.Key }))
                {
                    return new ApiResultModelByObject()
                    {
                        messageTitle = ActionFilter.GetMultilingualValue("custom:messageTitle:dataNotexist"),
                        messageContent = new List<string> { $"{ActionFilter.GetMultilingualValue("custom:messageContent:entityDeleted")}：{entityId.Value}" }
                    };
                }
            }
            #endregion

            #region 判斷合約性質是否存在
            foreach (var contractFnid in model.ContractFnid)
            {
                if (!DbAccess.Exists<SysParameters>(new SysParameters() { FuncCode = contractFnid.Key, LangType = MvcContext.UserInfo.logging_locale }))
                {
                    return new ApiResultModelByObject()
                    {
                        messageTitle = ActionFilter.GetMultilingualValue("custom:messageTitle:dataNotexist"),
                        messageContent = new List<string> { $"{ActionFilter.GetMultilingualValue("custom:messageContent:contractDeleted")}：{contractFnid.Value}" }
                    };
                }
            }
            #endregion

            #region 判斷類別是否存在
            if (!DbAccess.Exists<SysParameters>(new SysParametersViewModel() { ParaCode = "accountType", FuncCode = model.PayStyle.Key, LangType = MvcContext.UserInfo.logging_locale }))
            {
                return new ApiResultModelByObject()
                {
                    messageTitle = ActionFilter.GetMultilingualValue("custom:messageTitle:dataNotexist"),
                    messageContent = new List<string> { $"{ActionFilter.GetMultilingualValue("custom:messageContent:accountTypeDeleted")}：{model.PayStyle.Value}" }
                };
            }
            #endregion

            #endregion

            ApiResultModelByObject apiResult = new();

            List<string> repeatData = new();
            List<string> addData = new();
            List<string> logs = new List<string>();
            var users = PsSubEeLglVwADataService.Query(new PsSubEeLglVwAQueryCondition()
            {
                SearchItemGroup = new SearchItemGroup()
                {
                    Logic = LogicOperator.And,
                    Items = new List<SearchItem>()
                    {
                        new SearchItem()
                        {
                            Field="emplid",
                            Value=string.Join(',',model.UserList.Select(s=>s.Emplid)),
                            Compare=CompareOperator.IN
                        }
                    }
                }
            });

            //定義默認參數 -> 創建者+創建時間
            model.CreateUser = MvcContext.UserInfo.current_emp;
            model.CreateTime = DateTime.UtcNow;

            var hasCharEmplid = model.UserList.Where(w => char.IsLetter(w.Emplid[0]));
            var orderEmplid = new List<UserEntityModel>();
            orderEmplid.AddRange(model.UserList.Except(hasCharEmplid).OrderByDescending(o => o.Emplid.Length).OrderByDescending(o => o.Emplid));
            orderEmplid.AddRange(hasCharEmplid.OrderBy(b => b.Emplid));
            foreach (var entity in model.EntityId.OrderBy(b => b.Value))
            {
                foreach (var user in orderEmplid)
                {
                    var psSubEeLglVwA = users.First(f => f.Emplid.Equals(user.Emplid));
                    string statusName = psSubEeLglVwA.Termination.HasValue ? ActionFilter.GetMultilingualValue("commonWord:incumbent_0") : ActionFilter.GetMultilingualValue("commonWord:incumbent_1");
                    string name = string.IsNullOrEmpty(user.Name) ? "" : $"({user.Name})";
                    StringBuilder detail = new();
                    detail.AppendLine($"{ActionFilter.GetMultilingualValue("SysApproverManagement_fnpEntity", true)}：{entity.Value}");
                    detail.AppendLine($"{ActionFilter.GetMultilingualValue("SysApproverManagement_approverManagement", true)}：{model.ApproverType.Value}");
                    detail.AppendLine($"{ActionFilter.GetMultilingualValue("SysApproverManagement_deptid", true)}：{user.Deptid}");
                    detail.AppendLine($"{ActionFilter.GetMultilingualValue("SysApproverManagement_empId", true)}：{user.Emplid}");
                    detail.AppendLine($"{ActionFilter.GetMultilingualValue("SysApproverManagement_cName", true)}：{user.Name}");
                    detail.AppendLine($"{ActionFilter.GetMultilingualValue("SysApproverManagement_eName", true)}：{user.NameA}");
                    detail.AppendLine($"{ActionFilter.GetMultilingualValue("SysApproverManagement_prefixPhone", true)}：{(string.IsNullOrEmpty(psSubEeLglVwA.PrefixDialCodeA) ? "0000" : psSubEeLglVwA.PrefixDialCodeA)}+{psSubEeLglVwA.PhoneA}");
                    detail.AppendLine($"{ActionFilter.GetMultilingualValue("SysApproverManagement_statusName", true)}：{statusName}");

                    string content = string.Empty;
                    SysApproverManagementViewModel sysApproverManagementViewModel = new SysApproverManagementViewModel();
                    switch (model.ApproverType.Key.ToLower())
                    {
                        case "ck_whq_finance_pro"://總部財務承辦
                            #region 總部財務承辦
                            foreach (var contract in model.ContractFnid)
                            {
                                if (model.Site.Any())
                                {
                                    #region CR：269+270 當地承辦財務+總部承辦財務添加查詢欄位 如果存在site走新的添加邏輯，無就使用原有邏輯
                                    foreach (var site in model.Site)
                                    {
                                        content = $"{ActionFilter.GetMultilingualValue("SysApproverManagement_entityId", true)}：{entity.Value}；{ActionFilter.GetMultilingualValue("SysApproverManagement_contract", true)}：{contract.Value}；{ActionFilter.GetMultilingualValue("SysApproverManagement_site", true)}：{site.Value}；{user.NameA}{name}";
                                        sysApproverManagementViewModel = new()
                                        {
                                            ApproverType = model.ApproverType.Key,
                                            EmpId = user.Emplid,
                                            EntityId = entity.Key,
                                            ContractFnid = contract.Key,
                                            PayStyle = model.PayStyle.Key,
                                            SiteIdA = site.Key//CR：269+270 當地承辦財務+總部承辦財務添加查詢欄位
                                        };
                                        if (_repostory.CheckApproverTypeByPara(sysApproverManagementViewModel))
                                        {
                                            repeatData.Add($"<p class='rem_3'>{content}</p>");
                                        }
                                        else
                                        {
                                            SysApproverManagement sysApproverManagement = new()
                                            {
                                                ApproverType = model.ApproverType.Key,
                                                EmpId = user.Emplid,
                                                EntityId = entity.Key,
                                                CreateUser = model.CreateUser,
                                                ContractFnid = contract.Key,
                                                CreateTime = model.CreateTime,
                                                PayStyle = model.PayStyle.Key,
                                                IsBu = string.IsNullOrEmpty(psSubEeLglVwA.Bu) ? 0 : 1,
                                                CheckpointId = SysApproverService.GetCheckpointId(model.ApproverType.Key),
                                                SiteIdA = site.Key//CR：269+270 當地承辦財務+總部承辦財務添加查詢欄位
                                            };
                                            DbAccess.Create(sysApproverManagement);
                                            logs.Add(detail.ToString());
                                            addData.Add(@$"<p class='rem_3'>{content}</p>");
                                        }
                                    }
                                    #endregion
                                }
                                else
                                {
                                    #region 無Site填寫時的邏輯
                                    content = $"{ActionFilter.GetMultilingualValue("SysApproverManagement_entityId", true)}：{entity.Value}；{ActionFilter.GetMultilingualValue("SysApproverManagement_contract", true)}：{contract.Value}；{user.NameA}{name}";
                                    sysApproverManagementViewModel = new()
                                    {
                                        ApproverType = model.ApproverType.Key,
                                        EmpId = user.Emplid,
                                        EntityId = entity.Key,
                                        ContractFnid = contract.Key,
                                        PayStyle = model.PayStyle.Key
                                    };
                                    if (_repostory.CheckApproverTypeByPara(sysApproverManagementViewModel))
                                    {
                                        repeatData.Add($"<p class='rem_3'>{content}</p>");
                                    }
                                    else
                                    {
                                        SysApproverManagement sysApproverManagement = new()
                                        {
                                            ApproverType = model.ApproverType.Key,
                                            EmpId = user.Emplid,
                                            EntityId = entity.Key,
                                            CreateUser = model.CreateUser,
                                            ContractFnid = contract.Key,
                                            CreateTime = model.CreateTime,
                                            PayStyle = model.PayStyle.Key,
                                            IsBu = string.IsNullOrEmpty(psSubEeLglVwA.Bu) ? 0 : 1,
                                            CheckpointId = SysApproverService.GetCheckpointId(model.ApproverType.Key)
                                        };
                                        DbAccess.Create(sysApproverManagement);
                                        logs.Add(detail.ToString());
                                        addData.Add(@$"<p class='rem_3'>{content}</p>");
                                    }
                                    #endregion
                                }
                            }
                            #endregion
                            break;
                        case "ck_local_finance"://當地承辦財務
                            #region 當地承辦財務
                            if (model.Site.Any())
                            {
                                #region CR：269+270 當地承辦財務+總部承辦財務添加查詢欄位
                                foreach (var site in model.Site)
                                {
                                    content = $"{ActionFilter.GetMultilingualValue("SysApproverManagement_entityId", true)}：{entity.Value}；{ActionFilter.GetMultilingualValue("SysApproverManagement_site", true)}：{site.Value}；{user.NameA}{name}";
                                    sysApproverManagementViewModel = new()
                                    {
                                        ApproverType = model.ApproverType.Key,
                                        EmpId = user.Emplid,
                                        EntityId = entity.Key,
                                        SiteIdA = site.Key//CR：269+270 當地承辦財務+總部承辦財務添加查詢欄位
                                    };
                                    if (_repostory.CheckApproverTypeByPara(sysApproverManagementViewModel))
                                    {
                                        repeatData.Add($"<p class='rem_3'>{content}</p>");
                                    }
                                    else
                                    {
                                        //detail.AppendLine($"{ActionFilter.GetMultilingualValue("SysApproverManagement_site", true)}：{site.Value}");
                                        SysApproverManagement sysApproverManagement = new()
                                        {
                                            ApproverType = model.ApproverType.Key,
                                            EmpId = user.Emplid,
                                            EntityId = entity.Key,
                                            CreateUser = model.CreateUser,
                                            CreateTime = model.CreateTime,
                                            IsBu = string.IsNullOrEmpty(psSubEeLglVwA.Bu) ? 0 : 1,
                                            CheckpointId = SysApproverService.GetCheckpointId(model.ApproverType.Key),
                                            SiteIdA = site.Key//CR：269+270 當地承辦財務+總部承辦財務添加查詢欄位
                                        };
                                        DbAccess.Create(sysApproverManagement);
                                        logs.Add(detail.ToString());
                                        addData.Add(@$"<p class='rem_3'>{content}</p>");
                                    }
                                }
                                #endregion
                            }
                            else
                            {
                                #region 無Site填寫時的邏輯
                                content = $"{ActionFilter.GetMultilingualValue("SysApproverManagement_entityId", true)}：{entity.Value}；{user.NameA}{name}";
                                sysApproverManagementViewModel = new()
                                {
                                    ApproverType = model.ApproverType.Key,
                                    EmpId = user.Emplid,
                                    EntityId = entity.Key
                                };
                                if (_repostory.CheckApproverTypeByPara(sysApproverManagementViewModel))
                                {
                                    repeatData.Add($"<p class='rem_3'>{content}</p>");
                                }
                                else
                                {
                                    //detail.AppendLine($"{ActionFilter.GetMultilingualValue("SysApproverManagement_site", true)}：{site.Value}");
                                    SysApproverManagement sysApproverManagement = new()
                                    {
                                        ApproverType = model.ApproverType.Key,
                                        EmpId = user.Emplid,
                                        EntityId = entity.Key,
                                        CreateUser = model.CreateUser,
                                        CreateTime = model.CreateTime,
                                        IsBu = string.IsNullOrEmpty(psSubEeLglVwA.Bu) ? 0 : 1,
                                        CheckpointId = SysApproverService.GetCheckpointId(model.ApproverType.Key)
                                    };
                                    DbAccess.Create(sysApproverManagement);
                                    logs.Add(detail.ToString());
                                    addData.Add(@$"<p class='rem_3'>{content}</p>");
                                }
                                #endregion
                            }
                            #endregion
                            break;
                        case "ck_ceo"://總經理
                            #region 總經理
                            if (model.ExcludeDeptid.Any())
                            {
                                #region CR：271 總經理關卡添加查詢欄位
                                foreach (var deptid in model.ExcludeDeptid)
                                {
                                    content = $"{ActionFilter.GetMultilingualValue("SysApproverManagement_entityId", true)}：{entity.Value}；{ActionFilter.GetMultilingualValue("SysApproverManagement_excludeDeptid", true)}：{deptid.Value}；{user.NameA}{name}";
                                    sysApproverManagementViewModel = new()
                                    {
                                        ApproverType = model.ApproverType.Key,
                                        EmpId = user.Emplid,
                                        EntityId = entity.Key,
                                        ExcludeDeptid = deptid.Key,//CR：271 總經理關卡添加查詢欄位
                                    };
                                    if (_repostory.CheckApproverTypeByPara(sysApproverManagementViewModel))
                                    {
                                        repeatData.Add($"<p class='rem_3'>{content}</p>");
                                    }
                                    else
                                    {
                                        SysApproverManagement sysApproverManagement = new()
                                        {
                                            ApproverType = model.ApproverType.Key,
                                            EmpId = user.Emplid,
                                            EntityId = entity.Key,
                                            CreateUser = model.CreateUser,
                                            CreateTime = model.CreateTime,
                                            IsBu = string.IsNullOrEmpty(psSubEeLglVwA.Bu) ? 0 : 1,
                                            CheckpointId = SysApproverService.GetCheckpointId(model.ApproverType.Key),
                                            ExcludeDeptid = deptid.Key//CR：271 總經理關卡添加查詢欄位
                                        };
                                        DbAccess.Create(sysApproverManagement);
                                        logs.Add(detail.ToString());
                                        addData.Add(@$"<p class='rem_3'>{content}</p>");
                                    }
                                }
                                #endregion
                            }
                            else
                            {
                                #region 無Site填寫時的邏輯
                                content = $"{ActionFilter.GetMultilingualValue("SysApproverManagement_entityId", true)}：{entity.Value}；{user.NameA}{name}";
                                sysApproverManagementViewModel = new()
                                {
                                    ApproverType = model.ApproverType.Key,
                                    EmpId = user.Emplid,
                                    EntityId = entity.Key
                                };
                                if (_repostory.CheckApproverTypeByPara(sysApproverManagementViewModel))
                                {
                                    repeatData.Add($"<p class='rem_3'>{content}</p>");
                                }
                                else
                                {
                                    //detail.AppendLine($"{ActionFilter.GetMultilingualValue("SysApproverManagement_site", true)}：{site.Value}");
                                    SysApproverManagement sysApproverManagement = new()
                                    {
                                        ApproverType = model.ApproverType.Key,
                                        EmpId = user.Emplid,
                                        EntityId = entity.Key,
                                        CreateUser = model.CreateUser,
                                        CreateTime = model.CreateTime,
                                        IsBu = string.IsNullOrEmpty(psSubEeLglVwA.Bu) ? 0 : 1,
                                        CheckpointId = SysApproverService.GetCheckpointId(model.ApproverType.Key)
                                    };
                                    DbAccess.Create(sysApproverManagement);
                                    logs.Add(detail.ToString());
                                    addData.Add(@$"<p class='rem_3'>{content}</p>");
                                }
                                #endregion
                            }
                            #endregion
                            break;
                        default:
                            #region 其他關卡
                            content = $"{ActionFilter.GetMultilingualValue("SysApproverManagement_entityId", true)}：{entity.Value}；{user.NameA}{name}";
                            sysApproverManagementViewModel = new()
                            {
                                ApproverType = model.ApproverType.Key,
                                EmpId = user.Emplid,
                                EntityId = entity.Key,
                            };
                            if (_repostory.CheckApproverTypeByPara(sysApproverManagementViewModel))
                            {
                                repeatData.Add(@$"<p class='rem_3'>{content}</p>");
                            }
                            else
                            {
                                SysApproverManagement sysApproverManagement = new SysApproverManagement()
                                {
                                    ApproverType = model.ApproverType.Key,
                                    EmpId = user.Emplid,
                                    EntityId = entity.Key,
                                    CreateUser = model.CreateUser,
                                    CreateTime = model.CreateTime,
                                    IsBu = string.IsNullOrEmpty(psSubEeLglVwA.Bu) ? 0 : 1,
                                    CheckpointId = SysApproverService.GetCheckpointId(model.ApproverType.Key)
                                };
                                DbAccess.Create(sysApproverManagement);
                                //當地財務
                                if (model.ApproverType.Key.Equals("ck_local_finance"))
                                {
                                    PRole pRole = DbAccess.Find<PRole>(new PRoleViewModel()
                                    {
                                        RName = $"{entity.Value}當地財務"
                                    });
                                    if (pRole == null)
                                    {
                                        int? rid = CommonUtil.GetNextEnableId(PRoleDataService.Query(new PRoleQueryCondition()).Select(s => s.RId).Order().ToArray());
                                        pRole = new PRole
                                        {
                                            RName = $"{entity.Value}當地財務",
                                            RoleType = (int)RoleTypeUtils.Role,
                                            IsBuiltin = 1,
                                            CreateUser = model.CreateUser,
                                            CreateTime = model.CreateTime,
                                            RId = rid
                                        };
                                        PRoleDataService.Create(pRole);
                                    }
                                    DbAccess.Create(new PUserRole()
                                    {
                                        UId = user.Emplid,
                                        RId = pRole.RId,
                                        CreateTime = model.CreateTime,
                                        CreateUser = model.CreateUser,
                                    });
                                }
                                logs.Add(detail.ToString());
                                addData.Add(@$"<p class='rem_3'>{content}</p>");
                            }
                            #endregion
                            break;
                    }
                }
            }
            //没有新增数据
            if (!addData.Any())
            {
                return new()
                {
                    messageTitle = ActionFilter.GetMultilingualValue("custom:messageTitle:createFail"),
                    messageType = MessageTypeUtils.Warning.ToString(),
                    messageContent = new List<string>() { ActionFilter.GetMultilingualValue("custom:messageContent:repeatAll") }
                };
            }
            apiResult.messageContent.Add(ActionFilter.GetMultilingualValue("custom:messageContent:createSuccess"));
            apiResult.messageContent.Add($"{ActionFilter.GetMultilingualValue("SysApproverManagement_approverType", true)}：{model.ApproverType.Value}");
            if (model.ApproverType.Key.Equals("ck_whq_finance_pro"))
            {
                apiResult.messageContent.Add($"{ActionFilter.GetMultilingualValue("SysApproverManagement_payStyle", true)}：{model.PayStyle.Value}");
            }
            if (addData.Any())
                apiResult.messageContent.Add($"{ActionFilter.GetMultilingualValue("commonWord:add")}({addData.Count})：<br />{string.Join("", addData)}");
            if (repeatData.Any())
                apiResult.messageContent.Add($"<div class='red_text'>{ActionFilter.GetMultilingualValue("commonWord:repeat")}({repeatData.Count})：<br />{string.Join("", repeatData)}</div>");
            apiResult.messageTitle = ActionFilter.GetMultilingualValue("custom:messageTitle:createSuccess");
            apiResult.listData = true;
            apiResult.rtnSuccess = true;

            //記錄日誌
            ActionFilter.InitLogRecord<object>(new(), logRecord =>
            {
                logRecord.Detail = string.Join("-------------------------------------\r\n", logs);
            });
            return apiResult;
        }
        #endregion

        #region 刪除數據
        /// <summary>
        /// 刪除數據
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public static ApiResultModelByObject DeleteData(SysApproverManagementModel model)
        {
            SysApproverManagement sysApproverManagement = SysApproverManagementDataService.FindByKey(model.Rowid);
            //判断删除的数据是否存在
            if (sysApproverManagement == null)
            {
                return new ApiResultModelByObject()
                {
                    messageTitle = ActionFilter.GetMultilingualValue("custom:messageTitle:dataNotexist"),
                    messageContent = new List<string> { ActionFilter.GetMultilingualValue("custom:messageContent:dataChurn") }
                };
            }
            //總部法務行政(ck_whq_low_admin)至少保留一人
            if (
                sysApproverManagement.ApproverType.Equals("ck_whq_low_admin") &&
                SysApproverManagementDataService.Query(
                    new SysApproverManagementQueryCondition()
                    {
                        EntityId = sysApproverManagement.EntityId,
                        ApproverType = sysApproverManagement.ApproverType
                    }).Count == 1
                )
            {
                return new ApiResultModelByObject()
                {
                    messageTitle = ActionFilter.GetMultilingualValue("custom:messageTitle:doNotDelete"),
                    messageContent = new List<string>
                        {
                            ActionFilter.GetMultilingualValue("custom:messageContent:keepOneApproverManagement")
                        }
                };
            }
            ////我方公司主體維護中刪除關卡人員時校驗簽核者20241031 pike
            //string applyNumberList = _publicHelperRepository.CheckHasOngingCasesByApproveType(sysApproverManagement.ApproverType, sysApproverManagement.EmpId);
            //if (!string.IsNullOrEmpty(applyNumberList))
            //{
            //    return new ApiResultModelByObject()
            //    {
            //        messageTitle = ActionFilter.GetMultilingualValue("custom:messageTitle:doNotDelete"),
            //        messageContent = new List<string>
            //            {
            //                string.Format(ActionFilter.GetMultilingualValue("custom:messageContent:deleteApproveType"), model.ApproverManagement, applyNumberList)
            //            }
            //    };
            //}


            DbAccess.PerformInTransaction(context =>
            {
                context.DeleteByKey<SysApproverManagement, int?>(sysApproverManagement.Rowid);
                //当地财务时需删除对应角色使用人员
                if (sysApproverManagement.ApproverType.Equals("ck_local_finance"))
                {
                    FnpEntity fnpEntity = context.FindByKey<FnpEntity, string>(sysApproverManagement.EntityId);
                    if (fnpEntity == null) return;
                    PRole pRole = context.Find<PRole>(new PRoleViewModel()
                    {
                        RName = $"{fnpEntity.Entity}當地財務"
                    });
                    if (pRole == null) return;
                    PUserRole pUserRole = context.Find<PUserRole>(new PUserRoleViewModel()
                    {
                        UId = sysApproverManagement.EmpId,
                        RId = pRole.RId
                    });
                    if (pUserRole != null) context.DeleteByKey<PUserRole, int?>(pUserRole.Rowid);
                }
            });
            return new ApiResultModelByObject()
            {
                listData = SysApproverManagementDataService.DeleteByKey(model.Rowid),
                rtnSuccess = true
            };
        }
        #endregion

        #region 根據主體id+關卡+工號驗證是否存在待簽核單據
        /// <summary>
        /// 根據主體id+關卡+工號驗證是否存在待簽核單據
        /// issue 309
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public static ApiResultModelByObject CheckPendingDocumentsForLevelRemover(SysApproverManagementModel model)
        {
            SysApproverManagement sysApproverManagement = SysApproverManagementDataService.FindByKey(model.Rowid);
            //我方公司主體維護中刪除關卡人員時校驗簽核者20241031 pike
            //edit by SpringJiang 20250604 關卡人員管理驗證時，需要連帶主體驗證
            string applyNumberList = _publicHelperRepository.CheckHasOngingCasesByEntity(sysApproverManagement.ApproverType, sysApproverManagement.EntityId, sysApproverManagement.EmpId);
            if (!string.IsNullOrEmpty(applyNumberList))
            {
                return new ApiResultModelByObject()
                {
                    messageTitle = ActionFilter.GetMultilingualValue("custom:messageTitle:doNotDelete"),
                    messageContent = new List<string>
                        {
                            string.Format(ActionFilter.GetMultilingualValue("custom:messageContent:deleteApproveType"), model.ApproverManagement, applyNumberList)
                        }
                };
            }
            else
            {
                return new ApiResultModelByObject()
                {
                    listData = null,
                    rtnSuccess = true
                };
            }
        } 
        #endregion

        #region 查詢列表數據
        /// <summary>
        /// 查詢列表數據
        /// </summary>
        /// <param name="condition"></param>
        /// <returns></returns>
        public static IEnumerable<SysApproverManagementModel> GetDataList(SysApproverManagementSeachModel condition)
        {
            return _repostory.GetDataList(condition);
        }
        #endregion
    }
}
