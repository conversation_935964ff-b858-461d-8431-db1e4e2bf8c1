﻿using Elegal.Flow.Api.Services.Application;
using Elegal.Flow.Common.Repository.LiteratureApply;
using Elegal.Interface.Api.Common.Control.Filter;
using Elegal.Interface.Api.Common.Model.ViewModel.FlowApi.LiteratureApply;

namespace Elegal.Flow.Api.Services.ApplicationPrint
{
    /// <summary>
    /// 資料建檔列印
    /// </summary>
    public static class LiteratureApplyPrintService
    {
        private static readonly LiteratureApplyRepository _literatureApplyRepository = new LiteratureApplyRepository();

        #region 獲取資料建檔申請單資訊(列印)
        /// <summary>
        /// 獲取資料建檔申請單資訊(列印)
        /// </summary>
        /// <param name="applyNumber"></param>
        /// <returns></returns>
        public static LiteratureApplicationPrintModel GetLiteratureApplicationByPrint(string applyNumber)
        {
            if (string.IsNullOrEmpty(applyNumber)) throw new Exception(ActionFilter.GetMultilingualValue("custom:messageTitle:dataNotexist"));
            LiteratureApplicationPrintModel lapm = new LiteratureApplicationPrintModel();
            //主表資訊
            lapm.literatureApplication = _literatureApplyRepository.GetLiteratureApplyInfoByPrint(applyNumber, MvcContext.UserInfo.logging_locale);
            //財務資訊
            lapm.literatureApplicationFinance = _literatureApplyRepository.GetLiteratureApplicationFinanceByPrint(applyNumber, MvcContext.UserInfo.logging_locale);
            //合約管理作業
            lapm.contractValidityModel = DocumentationOriginalArchiveService.GetBaseInformation(applyNumber);
            return lapm;
        }
        #endregion
    }
}
