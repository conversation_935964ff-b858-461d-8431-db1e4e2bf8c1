﻿using Elegal.Flow.Api.Repository.Process;
using Elegal.Interface.Api.Common.Control.Filter;
using Elegal.Interface.Api.Common.Model.Enum;
using Elegal.Interface.Api.Common.Model.ParaModel.FlowApi;
using Elegal.Interface.Api.Common.Model.ResultModel;
using Elegal.Interface.Api.Common.Model.ViewModel.FlowApi;
using Elegal.Interface.ApiData.Service.FuncService;
using Elegal.Interface.ApiData.Service.Model.Condition;
using Elegal.Interface.ApiData.Service.Model.DbModel;
using Elegal.Interface.ApiData.Service.Model.QueryCondition;
using Elegal.Interface.ApiData.Service.Model.ViewModel;
using Elegal.Orm;
using Elegal.Orm.Utils;
using System.Text;

namespace Elegal.Flow.Api.Services.Process
{
    /// <summary>
    /// 签核层级设定
    /// </summary>
    public static class ApproverLevelSettingService
    {
        private static ApproverLevelSettingRepository _repository = new();
        /// <summary>
        /// 查询列表数据
        /// </summary>
        /// <param name="condition">查询参数</param>
        /// <returns></returns>
        public static IEnumerable<EntityCompanyModel> GetDataList(ApproverLevelSettingSearchModel condition)
        {
            return _repository.GetDataList(condition);
        }
        /// <summary>
        /// 编辑数据
        /// </summary>
        /// <param name="condition"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        internal static ApiResultModelByObject EditData(FlowSignLevelViewModel condition)
        {
            FlowSignLevel flowSignLevel = FlowSignLevelDataService.FindByKey(condition.Rowid);
            //数据是否存在
            if (flowSignLevel == null)
                return new ApiResultModelByObject()
                {
                    messageTitle = ActionFilter.GetMultilingualValue("custom:messageTitle:dataNotexist"),
                    messageContent = new List<string> { ActionFilter.GetMultilingualValue("custom:messageContent:dataChurn") },
                    messageType = MessageTypeUtils.Warning.ToString()
                };
            //签核层级不可为0
            if (condition.LevelStart == 0 || condition.LevelEnd == 0)
                return new()
                {
                    messageContent = new List<string> { ActionFilter.GetMultilingualValue("custom:messageContent:notZero") },
                    messageType = MessageTypeUtils.Warning.ToString()
                };
            //起始签核层级不可小于最高签核层级
            if (condition.LevelStart < condition.LevelEnd)
                return new()
                {
                    messageContent = new List<string> { ActionFilter.GetMultilingualValue("custom:messageContent:endLessStart") },
                    messageType = MessageTypeUtils.Warning.ToString()
                };
            //可以修改為-1~9之間的整數
            if (condition.LevelStart > 9 || condition.LevelEnd < -1)
                return new()
                {
                    messageContent = new List<string> { ActionFilter.GetMultilingualValue("custom:messageContent:levelRange") },
                    messageType = MessageTypeUtils.Warning.ToString()
                };
            ApiResultModelByObject apiResult = new()
            {
                listData = FlowSignLevelDataService.Update(CommonUtil.Map<FlowSignLevelViewModel, FlowSignLevel>(condition)),
                rtnSuccess = true
            };
            ActionFilter.InitLogRecord(logRecord =>
            {
                StringBuilder detailBuilder = new();
                detailBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("ApproverLevelSetting_companys", true)}：{condition.Company}");
                detailBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("ApproverLevelSetting_shortTitle", true)}：{condition.LevelStart} ~ {condition.LevelEnd}");
                logRecord.Detail = detailBuilder.ToString();

                StringBuilder detailFormerBuilder = new();
                detailFormerBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("ApproverLevelSetting_companys", true)}：{condition.Company}");
                detailFormerBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("ApproverLevelSetting_shortTitle", true)}：{flowSignLevel.LevelStart} ~ {flowSignLevel.LevelEnd}");
                logRecord.DetailFormer = detailFormerBuilder.ToString();
            });
            return apiResult;
        }
        /// <summary>
        /// 新增数据
        /// </summary>
        /// <param name="condition"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        internal static ApiResultModelByObject AddData(FlowSignLevelViewModel condition)
        {
            ApiResultModelByObject apiResult = new();
            //签核层级不可为0
            if (condition.LevelStart == 0 || condition.LevelEnd == 0)
                return new()
                {
                    messageContent = new List<string> { ActionFilter.GetMultilingualValue("custom:messageContent:notZero") },
                    messageType = MessageTypeUtils.Warning.ToString()
                };
            //起始签核层级不可小于最高签核层级
            if (condition.LevelStart < condition.LevelEnd)
                return new()
                {
                    messageContent = new List<string> { ActionFilter.GetMultilingualValue("custom:messageContent:endLessStart") },
                    messageType = MessageTypeUtils.Warning.ToString()
                };
            //可以修改為-1~9之間的整數
            if (condition.LevelStart > 9 || condition.LevelEnd < -1)
                return new()
                {
                    messageContent = new List<string> { ActionFilter.GetMultilingualValue("custom:messageContent:levelRange") },
                    messageType = MessageTypeUtils.Warning.ToString()
                };
            var old = FlowSignLevelDataService.Find(new FlowSignLevelCondition() { Company = condition.Company });
            if (old != null && old.Rowid != null)
            {
                condition.Rowid = old.Rowid;
                condition.ModifyTime = DateTime.UtcNow;
                condition.ModifyUser = MvcContext.UserInfo.current_emp;
                FlowSignLevel flowSignLevel = FlowSignLevelDataService.FindByKey(condition.Rowid);
                if (flowSignLevel == null)
                    return new ApiResultModelByObject()
                    {
                        messageTitle = ActionFilter.GetMultilingualValue("custom:messageTitle:dataNotexist"),
                        messageContent = new List<string> { ActionFilter.GetMultilingualValue("custom:messageContent:dataChurn") },
                        messageType = MessageTypeUtils.Warning.ToString()
                    };
                apiResult = EditData(condition);
            }
            else
            {
                condition.CreateTime = DateTime.UtcNow;
                condition.CreateUser = MvcContext.UserInfo.current_emp;
                apiResult.listData = FlowSignLevelDataService.Create(CommonUtil.Map<FlowSignLevelViewModel, FlowSignLevel>(condition));
                apiResult.rtnSuccess = true;
                ActionFilter.InitLogRecord(logRecord =>
                {
                    StringBuilder detailBuilder = new();
                    detailBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("ApproverLevelSetting_companys", true)}：{condition.Company}");
                    detailBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("ApproverLevelSetting_shortTitle", true)}：{condition.LevelStart} ~ {condition.LevelEnd}");
                    logRecord.Detail = detailBuilder.ToString();
                });
            }
            return apiResult;
        }
    }
}
