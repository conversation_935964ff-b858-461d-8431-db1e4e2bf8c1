﻿using Elegal.Flow.Api.Services;
using Elegal.Interface.Api.Common.Control;
using Elegal.Interface.Api.Common.Control.Filter;
using Elegal.Interface.Api.Common.Control.Web;
using Elegal.Interface.Api.Common.Model.ResultModel;
using Elegal.Interface.Api.Common.Model.ViewModel.PermissionApi.Home;
using Microsoft.AspNetCore.Mvc;

namespace Elegal.Flow.Api.Controllers
{
    /// <summary>
    /// 進行中案件查詢
    /// </summary>
    [Route("[controller]")]
    [ApiController]
    public class SignCaseController(SignCaseRefactorService signCaseRefactorService) : BaseController
    {
        #region 我的案件查詢
        /// <summary>
        /// 我的案件查詢
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("GetMyCase")]
        public async Task<ApiResultModelByObject> GetMyCase()
        {
            ApiResultModelByObject apiResult = new ApiResultModelByObject();
            
            var userInfoModel = AppStaticServices.GetUserInfoFromHttpContext(this.HttpContext);
            var listData = await signCaseRefactorService.GetMyCaseAsync(userInfoModel.current_emp, userInfoModel.logging_locale);
            apiResult.listData = listData;
            apiResult.rtnSuccess = true;
            apiResult.totalCount = listData.Count;
            InitLogRecord<object>(new());
            return apiResult;
        }
        #endregion

        #region 會審案件查詢
        /// <summary>
        /// 會審案件查詢
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("GetSignerCase")]
        public async Task<ApiResultModelByObject> GetSignerCase()
        {
            ApiResultModelByObject apiResult = new ApiResultModelByObject();
            List<HomeCaseViewModel> listData = SignCaseService.GetSignerCase(MvcContext.UserInfo.current_emp, MvcContext.UserInfo.logging_locale);
            apiResult.listData = listData;
            apiResult.rtnSuccess = true;
            apiResult.totalCount = listData.Count;
            InitLogRecord<object>(new());
            return await Task.FromResult<ApiResultModelByObject>(apiResult);
        }
        #endregion
    }
}
