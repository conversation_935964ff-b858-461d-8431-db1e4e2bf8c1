﻿using Elegal.Flow.Api.Services.Process;
using Elegal.Interface.Api.Common.Control.Web;
using Elegal.Interface.Api.Common.Model.ParaModel.FlowApi.ApplicationSearch;
using Elegal.Interface.Api.Common.Model.ResultModel;
using Elegal.Interface.Api.Common.Model.ResultModel.FlowApi.Process;
using Microsoft.AspNetCore.Mvc;

namespace Elegal.Flow.Api.Controllers.Process
{
    /// <summary>
    /// 修改簽核人員
    /// </summary>
    [Route("[controller]")]
    [ApiController]
    public class SignatoryManagementController : BaseController
    {
        /// <summary>
        /// 修改簽核人員列表查詢
        /// </summary>
        /// <param name="condition">查詢參數</param>
        /// <returns></returns>
        [HttpPost]
        [Route("GetDataList")]
        public async Task<ApiResultModelByObject> GetDataList([FromBody] SignatoryManagementSearchModel condition)
        {
            ApiResultModelByObject apiResult = new()
            {
                listData = SignatoryManagementService.GetDataList(condition),
                rtnSuccess = true
            };
            return await Task.FromResult(apiResult);
        }

        /// <summary>
        /// 查詢申請單明細
        /// </summary>
        /// <param name="apply_number">申請單單號</param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetApplySignatoryDetail")]
        public async Task<ApiResultModelByObject> GetApplySignatoryDetail(string apply_number)
        {
            ApiResultModelByObject apiResult = new()
            {
                listData = SignatoryManagementService.GetApplySignatoryDetail(apply_number),
                rtnSuccess = true
            };
            return await Task.FromResult(apiResult);
        }

        /// <summary>
        /// 查詢簽核、加簽、特殊加簽人員
        /// </summary>
        /// <param name="apply_number">申請單單號</param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetSignatiryEmp")]
        public async Task<ApiResultModelByObject> GetSignatiryEmp(string apply_number)
        {
            ApiResultModelByObject apiResult = new()
            {
                listData = SignatoryManagementService.GetSignatiryEmp(apply_number),
                rtnSuccess = true
            };
            return await Task.FromResult(apiResult);
        }

        /// <summary>
        /// 修改簽核、加簽、特殊加簽人員
        /// </summary>
        /// <param name="apply_number">申請單單號</param>
        /// <returns></returns>
        [HttpPost]
        [Route("UpdateSignatiryEmp")]
        public async Task<ApiResultModelByObject> UpdateSignatiryEmp(ModifySignatoryEmpModel modifySignatoryEmpModel)
        {
            ApiResultModelByObject apiResult = SignatoryManagementService.UpdateSignatiryEmp(modifySignatoryEmpModel);
            return await Task.FromResult(apiResult);
        }

    }
}
