﻿using Elegal.Flow.Api.Repository;
using Elegal.Interface.Api.Common.Control.Filter;
using Elegal.Interface.Api.Common.FuncHelper;
using Elegal.Interface.Api.Common.Model;
using Elegal.Interface.Api.Common.Model.Enum;
using Elegal.Interface.Api.Common.Model.ParaModel.FlowApi;
using Elegal.Interface.Api.Common.Model.ResultModel;
using Elegal.Interface.Api.Common.Model.ResultModel.FlowApi;
using Elegal.Interface.Api.Common.Model.ResultModel.HRApi;
using Elegal.Interface.Api.Common.Model.ViewModel;
using Elegal.Interface.ApiData.Service.FuncService;
using Elegal.Interface.ApiData.Service.Model.DbModel;
using Elegal.Interface.ApiData.Service.Model.QueryCondition;
using Elegal.Orm;
using Microsoft.AspNetCore.Http;
using Newtonsoft.Json;
using System.Text;

namespace Elegal.Flow.Api.Services
{
    /// <summary>
    /// 主體相關功能
    /// </summary>
    public static class EntityManageService
    {

        private static readonly EntityManageRepository _repository = new();

        /// <summary>
        /// 獲取主體公司簡稱
        /// </summary>
        /// <returns></returns>
        public static List<DropDownListModel> GetPrincipalCompanyAbbreviation()
        {
            return _repository.GetPrincipalCompanyAbbreviation();
        }

        /// <summary>
        /// 开放栏位
        /// </summary>
        private static List<UserEmailDictionary> UserEmailDictionaryList { get; set; } = [];

        /// <summary>
        /// 郵件模版
        /// </summary>
        private static UserEmailContent UserEmailContent { get; set; }

        /// <summary>
        /// 獲取區域
        /// </summary>
        /// <returns></returns>
        public static List<DropDownListModel> GetArea()
        {
            return _repository.GetArea();
        }

        /// <summary>
        /// 根據ID獲取區域
        /// </summary>
        /// <returns></returns>
        public static string GetAreaByAreaId(string area_id = "")
        {
            return _repository.GetAreaByAreaId(area_id);
        }

        /// <summary>
        /// 獲取主體信息
        /// </summary>
        /// <returns></returns>
        public static List<EntityManage> QueryEntityManage(EntityManageModel entityManageModel, string logging_locale, string time_zone)
        {
            return _repository.QueryEntityManage(entityManageModel, logging_locale, time_zone);
        }

        /// <summary>
        /// 查詢流程當前關卡
        /// </summary>
        /// <returns></returns>
        public static int QueryEntityCurrentStep(int entity_id)
        {
            return _repository.QueryEntityCurrentStep(entity_id);
        }

        /// <summary>
        /// 查詢授權公司代碼
        /// </summary>
        /// <returns></returns>
        public static List<EntityAuthCompany> QueryAuthCompany()
        {
            return _repository.QueryAuthCompany();
        }

        /// <summary>
        /// 驗證主體是否重複
        /// </summary>
        /// <returns></returns>
        public static bool VerifyEntityRepeat(ModifyEntityInfo createEntityInfo)
        {
            return _repository.VerifyEntityRepeat(createEntityInfo);
        }

        /// <summary>
        /// 建立主體
        /// </summary>
        /// <returns></returns>
        public static int InsertEntityInfo(ModifyEntityInfo fnpEntityViewModel)
        {
            return _repository.InsertEntityInfo(fnpEntityViewModel);
        }

        /// <summary>
        /// 根據主體id查詢對應信息
        /// </summary>
        /// <returns></returns>
        public static EntityManage QueryEntityInfoById(int entity_id, string logging_locale, string time_zone)
        {
            return _repository.QueryEntityInfoById(entity_id, logging_locale, time_zone);
        }

        /// <summary>
        /// 變更主體數據
        /// </summary>
        /// <returns></returns>
        public static int UpdateEntityInfo(ModifyEntityInfo modifyEntityInfo, int status)
        {
            //從啟用更改成停用
            if (status == 0 && modifyEntityInfo.Status != 0)
            {
                //通過前台按鈕來判斷是否移除其他主體公司代碼
                if (modifyEntityInfo.is_delete_other == 1) { 
                    _repository.ClearActualCompanyRelationData(modifyEntityInfo.EntityId);
                    //mail(modifyEntityInfo);
                }
                //uat165 停用主體不移除特殊主體
                //_repository.ClearEntityRelationRole(modifyEntityInfo.EntityId);
            }
            //主體停用時，同步更改主體合約管理人對應公告狀態為停用
            if (modifyEntityInfo.Status != 0)
                _repository.UpdateEntityContractStatus(modifyEntityInfo.EntityId, 0, modifyEntityInfo.ModifyUser);
            //主體啟用時，同步更改主體合約管理人對應公告狀態為啟用
            if (modifyEntityInfo.Status == 0)
                _repository.UpdateEntityContractStatus(modifyEntityInfo.EntityId, 1, modifyEntityInfo.ModifyUser);
            //修改主體數據
            int count = _repository.UpdateEntityInfo(modifyEntityInfo, status);
            return count;
        }

        #region 主體停用移除特殊主體權限發送郵件
        /// <summary>
        /// 發送郵件
        /// </summary>
        /// <returns></returns>
        public static object mail(ModifyEntityInfo modifyEntityInfo)
        {
            List<EntityRelationRole> entityRelationRoles = _repository.QueryOtherEntityRelation(modifyEntityInfo.EntityId);
            DbAccess.PerformInTransaction(context =>
            {
                UserEmailContent userEmailContent = context.FirstOrDefault<UserEmailContent>(f => f.MailType.Equals(MailTypeUtils.EN.ToString()));
                UserEmailDictionaryList = UserEmailDictionaryDataService.Query(new UserEmailDictionaryQueryCondition() { FieldType = userEmailContent.FuncModule });
                List<SysEmail> sysEmails = new();
                SysEmail sysEmail = new()
                {
                    ESubject = userEmailContent.MailSubject.Replace("{entity_zh}", modifyEntityInfo.Entity),
                    ESendtime = DateTime.UtcNow,
                    EContent = userEmailContent.MailContent,
                    EReceiver = AppSettingHelper.Configuration["legalEMail"],
                    ECc = "",
                    ESendnum = 0,
                    EIssend = (int)YesOrNoUtils.No,
                    EType = userEmailContent.MailType
                };
                SetPaperMailTableContent(entityRelationRoles, sysEmail, userEmailContent);
                sysEmails.Add(sysEmail);
                context.BatchCreate<SysEmail>(sysEmails.ToArray());
            });
            return null;
        }

        /// <summary>
        /// 设定邮件表格内容
        /// </summary>
        /// <param name="list"></param>
        /// <param name="sysEmail"></param>
        private static void SetPaperMailTableContent(List<EntityRelationRole> list, SysEmail sysEmail, UserEmailContent sysEmailContent)
        {
            var tableJsons = JsonConvert.DeserializeObject<List<TableJson>>(sysEmailContent.TableJson ?? "") ?? [];
            BuildEntityRoleTable(list, sysEmail, tableJsons, "ZH-TW");
            BuildEntityRoleTable(list, sysEmail, tableJsons, "EN-US");
        }

        private static void BuildEntityRoleTable(List<EntityRelationRole> list, SysEmail sysEmail, List<TableJson> tableJsons, string lang)
        {
            var table = tableJsons.FirstOrDefault(f => sysEmail.EContent.Contains(f.tableId) && lang.Equals(f.lang));
            if (table != null)
            {
                StringBuilder stringBuilder = new StringBuilder();
                stringBuilder.AppendLine("<table border=\"1\" style=\"border-collapse: collapse;border: 1px solid #ccc;text-align: left;font-size: 12px;\">");
                stringBuilder.AppendLine("  <tbody>");
                stringBuilder.AppendLine("      <tr>");
                foreach (var col in table.columns)
                {
                    var field = UserEmailDictionaryList.FirstOrDefault(f => $"{{{f.FieldCode}}}".Equals(col));
                    var title = table.lang.Equals("ZH-TW") ? field.FieldCname : field.FieldEname;
                    stringBuilder.AppendLine($"          <td width=\"{(col.Equals("{other}") ? "400px" : "200px")}\">{title}</td>");
                }
                stringBuilder.AppendLine("      </tr>");
                foreach (var item in list)
                {
                    stringBuilder.AppendLine("      <tr>");
                    StringBuilder tbBuilder = new StringBuilder();
                    foreach (var col in table.columns)
                    {
                        var field = UserEmailDictionaryList.FirstOrDefault(f => $"{{{f.FieldCode}}}".Equals(col));
                        var title = $"{{{field.FieldCode}_{(table.lang.Equals("ZH-TW") ? "zh" : "en")}}}";
                        tbBuilder.AppendLine($"          <td>{title}</td>");
                    }
                    tbBuilder = tbBuilder
                        .Replace("{emp_info_zh}", item.emp_info)
                        .Replace("{deptid_zh}", item.deptid)
                        .Replace("{apply_number_zh}", item.apply_number)
                        .Replace("{reason_zh}", item.reason)
                        .Replace("{entity_zh}", item.entity)
                        .Replace("{emp_info_en}", item.emp_info)
                        .Replace("{deptid_en}", item.deptid)
                        .Replace("{apply_number_en}", item.apply_number)
                        .Replace("{reason_en}", item.reason)
                        .Replace("{entity_en}", item.entity);
                    stringBuilder.Append(tbBuilder);
                    stringBuilder.AppendLine("      </tr>");
                }
                stringBuilder.AppendLine("  </tbody>");
                stringBuilder.AppendLine("</table>");

                sysEmail.EContent = sysEmail.EContent.Replace($"{{{table.tableId}}}", stringBuilder.ToString());
            }
        }

        #endregion

        /// <summary>
        /// 變更主體流程進度
        /// </summary>
        /// <returns></returns>
        public static object ModifyPushStep(ModifyEntityInfo modifyEntityInfo)
        {
            return _repository.ModifyPushStep(modifyEntityInfo);
        }

        /// <summary>
        /// 根據授權公司代碼顯示數據
        /// </summary>
        /// <returns></returns>
        public static List<EntityManage> QueryAuthCompanyByEntity(int entity_id, string time_zone)
        {

            return _repository.QueryAuthCompanyByEntity(entity_id, time_zone);
        }

        /// <summary>
        /// 更新授權公司數據
        /// </summary>
        /// <returns></returns>
        public static int ModifyAuthCompanyAndEntity(List<ModifyEntityInfo> modifyEntityInfos, string user)
        {
            int updateCount = 0;
            modifyEntityInfos.ForEach(e =>
            {
                e.CreateUser = user;
                e.ModifyUser = user;
                updateCount = updateCount + _repository.ModifyAuthCompanyAndEntity(e);
            });
            return updateCount;
        }

        /// <summary>
        /// 根據主體ID查詢關卡詳情
        /// </summary>
        /// <returns></returns>
        public static List<ApproveManagementResult> QueryApproveManagement(int entity_id, string logging_locale, string time_zone)
        {
            return _repository.QueryApproveManagement(entity_id, logging_locale);
        }

        /// <summary>
        /// 顯示主體角色與權限
        /// </summary>
        /// <returns></returns>
        public static List<EntityUserRoleResult> QueryEntityRoleAndUser(int entity_id, string time_zone)
        {
            return _repository.QueryEntityRoleAndUser(entity_id, time_zone);
        }

        /// <summary>
        /// 顯示角色使用者授權
        /// </summary>
        /// <returns></returns>
        public static List<UserRoleAuthorization> QueryUserRoleAuthorization(UserRoleAuthorizationModel model, string time_zone)
        {
            return _repository.QueryUserRoleAuthorization(model, time_zone);
        }

        /// <summary>
        /// 啟用主體
        /// </summary>
        /// <returns></returns>
        public static int EnableEneity(int entity_id, string current_emp)
        {
            _repository.UpdateEntityContractStatus(entity_id.ToString(), 1, current_emp);
            return _repository.EnableEneity(entity_id, current_emp);
        }

        /// <summary>
        /// 檢查是否有總部行政
        /// </summary>
        /// <returns></returns>
        public static bool CheckCkWhqLowAdmin(int entity_id)
        {
            return _repository.CheckCkWhqLowAdmin(entity_id);
        }

        /// <summary>
        /// 檢查區域是否存在
        /// </summary>
        /// <returns></returns>

        public static bool VerifyArea(int? areaId)
        {
            return _repository.VerifyArea(areaId);
        }

        /// <summary>
        /// 获取主体当前流程
        /// </summary>
        /// <returns></returns>

        public static string GetStep(int? entity_stepid, string logging_locale)
        {
            return _repository.GetStep(entity_stepid, logging_locale);
        }
        /// <summary>
        /// 查询主体与授权公司
        /// </summary>
        /// <returns></returns>

        public static List<ModifyEntityInfo> QueryAuthCompanyAndEntity(string entity_id)
        {
            return _repository.QueryAuthCompanyAndEntity(entity_id);
        }

        /// <summary>
        /// 查询实际公司代码被哪些主体使用
        /// </summary>
        /// <returns></returns>
        public static string QueryOtherEntityRelationCompany(string entityId, string actual_company)
        {
            return _repository.QueryOtherEntityRelationCompany(entityId, actual_company);
        }

        /// <summary>
        /// 校验主体实际公司代码
        /// </summary>
        /// <returns></returns>
        public static bool VerifyActualCompany(string actual_company, string entity_id)
        {
            return _repository.VerifyActualCompany(actual_company, entity_id);
        }

        /// <summary>
        /// 校驗實際公司代碼有沒有被作為其他主體的實際公司代碼
        /// </summary>
        /// <returns></returns>
        public static string QueryActualCompany(string actual_company)
        {
            return _repository.QueryActualCompany(actual_company);
        }

        /// <summary>
        /// 查詢簽核層級發生變化的數據
        /// </summary>
        /// <returns></returns>
        public static List<ModifyEntityInfo> QueryRevisedAuthCompanyAndEntity(List<ModifyEntityInfo> modifyEntityInfos)
        {
            return _repository.QueryRevisedAuthCompanyAndEntity(modifyEntityInfos);
        }

        /// <summary>
        /// 查詢修改之前的簽核層級數據
        /// </summary>
        /// <returns></returns>
        public static List<ModifyEntityInfo> QueryFormerAuthCompanyAndEntity(string entityId, string company)
        {
            return _repository.QueryFormerAuthCompanyAndEntity(entityId,company);
        }

        /// <summary>
        /// 檢查合約管理員是否至少一人
        /// </summary>
        /// <returns></returns>
        public static bool CheckContractManager(int entity_id)
        {
            return _repository.CheckContractManager(entity_id);
        }

        /// <summary>
        /// 查詢其他申請單關聯人員部門和申請單
        /// </summary>
        /// <returns></returns>
        internal static List<string> QueryOtherEntityRelation(ApiResultModelByObject apiResult, string entityId)
        {
            UserInfoModel user = MvcContext.UserInfo;
            //根據主體ID查詢主體信息
            EntityManage entityManage = EntityManageService.QueryEntityInfoById(Convert.ToInt32(entityId), user.logging_locale, user.time_zone);

            //根據主體ID查詢當前主體實際公司代碼關聯的其他主體名稱
            string entityName = _repository.QueryOtherEntityRelationCompany(entityId, entityManage.actual_company);
            //查詢其他啟用主體關聯的公司代碼僅只有要移除的公司代碼
            string onlyCompanyEntityName = _repository.QueryOtherEntityOnlyRelationCompany(entityId, entityManage.actual_company);

            //查詢其他申請單關聯人員部門和申請單

            //uat 165主體停用提示語處理
            //List<EntityRelationRole> entityRelationRoles = _repository.QueryOtherEntityRelation(entityId);
            //string deptids = string.Join(",", entityRelationRoles.Where(s => !string.IsNullOrEmpty(s.deptid)).Select(s => s.deptid));
            //string emp_infos = string.Join(",", entityRelationRoles.Where(s => !string.IsNullOrEmpty(s.emp_info)).Select(s => s.emp_info));
            //string apply_number = string.Join(",", entityRelationRoles.Where(s => !string.IsNullOrEmpty(s.apply_number)).Select(s => s.apply_number));
            List<string> list = new List<string>();

            //按順序拼接返回信息
            if (!string.IsNullOrEmpty(entityManage.actual_company))
            {
                list.Add(string.Format(ActionFilter.GetMultilingualValue("custom:messageContent:RemoveActualCompanyCode"), entityManage.actual_company) +
                         (string.IsNullOrEmpty(entityManage.company) ? "" : string.Format(ActionFilter.GetMultilingualValue("custom:messageContent:RemoveCompanyCode"), entityManage.company)));
            }
            if (!string.IsNullOrEmpty(entityName))
            {
                List<string> strings = entityName.Split(',').ToList();
                list.Add(string.Format(ActionFilter.GetMultilingualValue("custom:messageContent:entityIsUsedOtherEntity"), entityManage.actual_company, strings.Count));
                list.Add(string.IsNullOrEmpty(entityName)? "" : entityName);
            }
            if (!string.IsNullOrEmpty(onlyCompanyEntityName))
            {
                List<string> strings = onlyCompanyEntityName.Split(',').ToList();
                list.Add(string.Format(ActionFilter.GetMultilingualValue("custom:messageContent:entityIsUsedOnlyOtherEntity"), entityManage.actual_company, strings.Count));
                list.Add(string.IsNullOrEmpty(onlyCompanyEntityName) ? "" : onlyCompanyEntityName);
            }

            //uat 165主體停用提示語處理
            //if (!string.IsNullOrEmpty(emp_infos))
            //    list.Add(string.Format(ActionFilter.GetMultilingualValue("custom:messageContent:RemoveSpecialEntityUser"), emp_infos));
            //if (!string.IsNullOrEmpty(deptids))
            //    list.Add(string.Format(ActionFilter.GetMultilingualValue("custom:messageContent:RemoveSpecialEntityDept"), deptids));
            //if (!string.IsNullOrEmpty(apply_number))
            //    list.Add(string.Format(ActionFilter.GetMultilingualValue("custom:messageContent:RemoveSpecialEntityApply"), apply_number));
            apiResult.listData = "";
            //uat issue 165當未存在任何提示信息時返回空List
            List<string> wList = list.Where(x => !string.IsNullOrEmpty(x)).ToList();
            return wList.Any() ? list : wList;
        }
    }
}