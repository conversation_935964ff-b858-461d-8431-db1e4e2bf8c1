﻿using Elegal.Flow.Api.Services;
using Elegal.Flow.Api.Services.Paper;
using Elegal.Interface.Api.Common.Control.Web;
using Elegal.Interface.Api.Common.Model.ParaModel.FlowApi;
using Elegal.Interface.Api.Common.Model.ResultModel;
using Elegal.Interface.Api.Common.Model.ViewModel.FlowApi;
using Elegal.Orm.Dtos;
using Microsoft.AspNetCore.Mvc;
using System.Text;

namespace Elegal.Flow.Api.Controllers
{
    /// <summary>
    /// 特殊主體-人員設定
    /// </summary>
    [Route("[controller]")]
    [ApiController]
    public class FnpEntityUserController : BaseController
    {
        /// <summary>
        /// 特殊主體查詢
        /// </summary>
        [HttpPost]
        [Route("Query")]
        public async Task<ApiResultModelByObject> Query([FromBody]qryFnpEntityUser qry)
        {
            PageResult<FnpEntityUserModel> res = FnpEntityUserService.Query(qry);

            ApiResultModelByObject apiResult = new();
            apiResult.listData = res.Data;
            apiResult.totalCount = res.TotalRows;
            apiResult.rtnSuccess = true;

            InitLogRecord(qry, logRecord =>
            {
                StringBuilder stringBuilder = new();

                if (!string.IsNullOrEmpty(qry.emp)) stringBuilder.AppendLine($"員工:  {qry.emp}");
                if (!string.IsNullOrEmpty(qry.deptid)) stringBuilder.AppendLine($"部門代號:  {qry.deptid}");
                string entity = string.Empty;
                if (qry.entity != null && qry.entity.Count > 0)
                {
                    entity = PaperBasicDataService.GetEntityString(string.Join(",", qry.entity));
                }
                if (!string.IsNullOrEmpty(entity)) stringBuilder.AppendLine($"特殊主體:  {entity}");

                logRecord.Detail = stringBuilder.ToString();
            });

            return await Task.FromResult(apiResult);
        }

        /// <summary>
        /// 特殊主體查詢
        /// </summary>
        [HttpPost]
        [Route("ExportUserQuery")]
        public FileContentResult ExportUserQuery([FromBody] qryFnpEntityUser qry)
        {
            var excelBytes = FnpEntityUserService.ExportUserQuery(qry);
            string filleName = $"特殊主體設定_人員設定_{DateTime.Now.ToString("yyyyMMdd")}.xlsx";

            InitLogRecord(qry, logRecord =>
            {
                StringBuilder stringBuilder = new();

                if (!string.IsNullOrEmpty(qry.emp)) stringBuilder.AppendLine($"員工:  {qry.emp}");
                if (!string.IsNullOrEmpty(qry.deptid)) stringBuilder.AppendLine($"部門代號:  {qry.deptid}");
                string entity = string.Empty;
                if (qry.entity != null && qry.entity.Count > 0)
                {
                    entity = PaperBasicDataService.GetEntityString(string.Join(",", qry.entity));
                }
                if (!string.IsNullOrEmpty(entity)) stringBuilder.AppendLine($"特殊主體:  {entity}");

                logRecord.Detail = stringBuilder.ToString();
            });

            return File(excelBytes, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", filleName);
        }

        /// <summary>
        /// 特殊主體明細查詢
        /// </summary>
        [HttpGet]
        [Route("QueryDetails")]
        public async Task<ApiResultModelByObject> QueryDetails(string? emplid)
        {
            ApiResultModelByObject apiResult = new();
            apiResult.listData = FnpEntityUserService.QueryDetails(emplid);
            apiResult.rtnSuccess = true;


            InitLogRecord<object>(new(), logRecord =>
            {
                StringBuilder stringBuilder = new();
                if (!string.IsNullOrEmpty(emplid)) stringBuilder.AppendLine($"工號: {emplid}");

                logRecord.Detail = stringBuilder.ToString();
            });

            return await Task.FromResult(apiResult);
        }

        /// <summary>
        /// 更新特殊主體-人員設定
        /// </summary>
        [HttpPost]
        [Route("UpdateFnpEntityUser")]
        public async Task<ApiResultModelByObject> UpdateFnpEntityUser([FromBody]updateFnpEntity data)
        {
            List<FnpEntityUserDetailModel> oldData = FnpEntityUserService.QueryDetails(data.emplid);

            ApiResultModelByObject apiResult = new();
            apiResult.listData = FnpEntityUserService.UpdateFnpEntityUser(data.emplid, data.entity_id_list);
            apiResult.rtnSuccess = true;

            InitLogRecord(data, logRecord =>
            {
                StringBuilder stringBuilder = new();
                stringBuilder.AppendLine($"工號:  {data.emplid}");
                stringBuilder.AppendLine($"特殊主體:  {PaperBasicDataService.GetEntityString(string.Join(",", data.entity_id_list))}");

                logRecord.Detail = stringBuilder.ToString();

                if (oldData != null && oldData.Count > 0)
                {
                    string entityList = string.Join(", ", oldData.Select(x => x.user_entity));
                    if (!string.IsNullOrEmpty(entityList)) logRecord.DetailFormer = "特殊主體: " + entityList;
                }
            });

            return await Task.FromResult(apiResult);
        }
    }
}
