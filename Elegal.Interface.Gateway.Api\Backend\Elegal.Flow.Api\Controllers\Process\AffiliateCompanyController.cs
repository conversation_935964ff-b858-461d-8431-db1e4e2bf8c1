﻿using Elegal.Flow.Api.Services.Process;
using Elegal.Interface.Api.Common.Control.Filter;
using Elegal.Interface.Api.Common.Control.Web;
using Elegal.Interface.Api.Common.Model.ResultModel;
using Elegal.Interface.Api.Common.Model.ViewModel.FlowApi;
using Microsoft.AspNetCore.Mvc;
using System.Text;

namespace Elegal.Flow.Api.Controllers.Process
{
    /// <summary>
    /// 關企主體管理
    /// </summary>
    [Route("[controller]")]
    [ApiController]
    public class AffiliateCompanyController : BaseController
    {
        /// <summary>
        /// 關企主體列表查詢
        /// </summary>
        /// <param name="condition">查詢參數</param>
        /// <returns></returns>
        [HttpPost]
        [Route("GetDataList")]
        public async Task<ApiResultModelByObject> GetDataList(AffiliateCompanySearchModel condition)
        {
            ApiResultModelByObject apiResult = new()
            {
                listData = AffiliateCompanyService.GetDataList(condition),
                rtnSuccess = true
            };
            return await Task.FromResult(apiResult);
        }

        /// <summary>
        /// 獲取單筆關卡主體數據
        /// </summary>
        /// <param name="affCompanyCode">公司代碼</param>
        /// <returns></returns>
        [HttpGet]
        [Route("Find")]
        public async Task<ApiResultModelByObject> Find(string affCompanyCode)
        {
            return await Task.FromResult(AffiliateCompanyService.Find(affCompanyCode));
        }

        /// <summary>
        /// 新增關企主體
        /// </summary>
        /// <param name="affiliateCompany">數據模型</param>
        /// <returns></returns>
        [HttpPost]
        [Route("Insert")]
        public async Task<ApiResultModelByObject> Insert(AffiliateCompanyModel affiliateCompany)
        {
            return await Task.FromResult(AffiliateCompanyService.Insert(affiliateCompany));
        }

        /// <summary>
        /// 刪除關企主體
        /// </summary>
        /// <param name="affCompanyCode">公司代碼</param>
        /// <returns></returns>
        [HttpDelete]
        [Route("Delete")]
        public async Task<ApiResultModelByObject> Delete(string affCompanyCode)
        {
            return await Task.FromResult(AffiliateCompanyService.Delete(affCompanyCode));
        }

        /// <summary>
        /// 修改關企主體
        /// </summary>
        [HttpPost]
        [Route("Modify")]
        public async Task<ApiResultModelByObject> Modify(AffiliateCompanyModel affiliateCompany)
        {
            return await Task.FromResult(AffiliateCompanyService.Modify(affiliateCompany));
        }

        /// <summary>
        /// 匯出關企主體
        /// </summary>
        [HttpPost]
        [Route("Export")]
        public async Task<ApiResultModelByObject> Export(AffiliateCompanySearchModel affiliateCompany)
        {
            InitLogRecord(affiliateCompany, log =>
            {
                StringBuilder stringBuilder = new();
                if (!string.IsNullOrEmpty(affiliateCompany.Name))
                    stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("affCompany_affCompanyAbbName", true)}：{affiliateCompany.Name}");
                log.Detail = stringBuilder.ToString();
            });
            return await Task.FromResult(new ApiResultModelByObject() { rtnSuccess = true });
        }
    }
}
