﻿using Elegal.Interface.Api.Common.Control.Filter;
using Elegal.Interface.Api.Common.Model.ParaModel.FlowApi;
using Elegal.Interface.Api.Common.Model.ViewModel.FlowApi;
using Elegal.Interface.Api.Common.Repository;
using Elegal.Orm;
using Elegal.Orm.Dtos;
using System.Data;
using System.Text;
using PageParam = Elegal.Orm.Dtos.PageParam;

namespace Elegal.Flow.Api.Repository
{
    /// <summary>
    /// 特殊主體設定-人員設定
    /// </summary>
    public class FnpEntityUserRepository : BaseRepository
    {
        #region 查詢特殊主體
        /// <summary>
        /// 查詢特殊主体
        /// </summary>
        public PageResult<FnpEntityUserModel> QueryUser(qryFnpEntityUser qry)
        {
            PageResult<FnpEntityUserModel> result = new PageResult<FnpEntityUserModel>();

            StringBuilder sql = new StringBuilder();
            sql.Append(@"SELECT DISTINCT
                            ee.emplid,
                            LEN(ee.emplid) AS emplid_len, --Issue ：253 排序
                            og.company AS ee_company,--Issue：252 部門公司別使用OG表
                            ee.company AS hosted_company,--Issue：252 託管人員公司別使用EE表
							ee.deptid AS ee_deptid,
							  (SELECT STUFF((SELECT ', ' + fee.entity
                                FROM (SELECT company,entity_id FROM fnp_ec_relation UNION SELECT company,entity_id FROM fnp_ac_relation) AS fear
                                INNER JOIN fnp_entity fee ON fear.entity_id = fee.entity_id 
                                WHERE fear.company = og.company--Issue：252 部門公司別使用OG表
                                FOR XML PATH('')), 1, 1, '')) AS ee_entity,
								(SELECT STUFF((SELECT ';' + og1.company + '/' + cj1.deptid + '|' + STUFF((SELECT ', ' + fe.entity 
								FROM (SELECT company,entity_id FROM fnp_ec_relation UNION SELECT company,entity_id FROM fnp_ac_relation) AS fer
								INNER JOIN fnp_entity fe ON fer.entity_id = fe.entity_id 
								WHERE fer.company = og1.company
							FOR XML PATH('')), 1, 1, '')
                            FROM ps_cj_truck_vw_a cj1
                            INNER JOIN ps_sub_og_lgl_vw_a og1 ON cj1.deptid = og1.deptid
                            WHERE ee.emplid = cj1.emplid 
                                AND cj1.deptid != ee.deptid 
                                AND cj1.is_delete is null
                            FOR XML PATH('')), 1, 1, '')) AS cj_dept,
                            ee.name,
                            ee.name_a,
                            '0' is_aff,
                            CASE WHEN ee.termination is null THEN '1' ELSE '0' END AS emp_status,
							(SELECT STUFF((SELECT DISTINCT ', ' + CONCAT(fe.entity, '[user]', 
                                                CASE WHEN a.open_date_start IS NULL AND a.open_date_end IS NULL 
                                                THEN ''
                                                ELSE CONCAT('(', ISNULL(CONVERT(VARCHAR(10), a.open_date_start, 111), 'NA'), '~', ISNULL(CONVERT(VARCHAR(10), a.open_date_end, 111), 'NA'), ')')
                                            END)
                                FROM FNP_ENTITY_USER_SPECIAL A 
                                INNER JOIN (SELECT entity_id, entity, entity_namec, entity_namee FROM fnp_entity UNION SELECT aff_company_code AS entity_id, aff_company_abb AS entity, aff_company_cname AS entity_namec, aff_company_ename AS entity_namee FROM affiliate_company) fe on a.entity_id = fe.entity_id
                                WHERE A.EMPLID = ee.EMPLID AND A.is_delete = 0
                                FOR XML PATH('')), 1, 1, '')) AS user_entity,
                            (SELECT STUFF((SELECT DISTINCT ', ' + CONCAT(fe.entity, '[dept]', 
                                            CASE WHEN b.open_date_start IS NULL AND b.open_date_end IS NULL 
                                            THEN ''
                                            ELSE CONCAT('(', ISNULL(CONVERT(VARCHAR(10), b.open_date_start, 111), 'NA'), '~', ISNULL(CONVERT(VARCHAR(10), b.open_date_end, 111), 'NA'), ')')
                                            END)
                                FROM ps_sub_ee_lgl_vw_a ee1
                                LEFT JOIN ps_cj_truck_vw_a cj on ee.emplid = cj.emplid
                                LEFT JOIN fnp_entity_dept_special B ON (B.deptid = ee.deptid OR cj.deptid = b.deptid) AND b.is_delete = 0
                                INNER JOIN (SELECT entity_id, entity, entity_namec, entity_namee FROM fnp_entity UNION SELECT aff_company_code AS entity_id, aff_company_abb AS entity, aff_company_cname AS entity_namec, aff_company_ename AS entity_namee FROM affiliate_company) fe on B.entity_id = fe.entity_id
                                WHERE ee.emplid = ee1.emplid
                                FOR XML PATH('')), 1, 1, '')) AS dept_entity,
                                --Issue：232 OF單結案後，顯示的更新人員錯誤
                            eo.name AS operate_cuser,
                            eo.name_a AS operate_euser,
                            ISNULL(A.modify_time, A.create_time) AS operate_time
                          FROM ps_sub_ee_lgl_vw_a ee 
                          INNER JOIN ps_sub_og_lgl_vw_a og ON ee.deptid = og.deptid 
                            LEFT JOIN FNP_ENTITY_USER A on A.EMPLID = ee.EMPLID
                          LEFT JOIN ps_sub_ee_lgl_vw_a AS eo ON eo.emplid = ISNULL(A.modify_user,A.create_user)");

            StringBuilder csql = new StringBuilder();
            csql.Append(@"SELECT DISTINCT
                            count(1)
                          FROM 
                          ps_sub_ee_lgl_vw_a ee 
                          JOIN ps_sub_og_lgl_vw_a og ON ee.deptid = og.deptid 
						  left join FNP_ENTITY_USER A on A.EMPLID = ee.EMPLID ");

            StringBuilder condition = new StringBuilder();

            //特殊主体
            if (qry.entity != null && qry.entity.Count > 0)
            {
                //condition.Append(@$" AND entity_id IN ({string.Join(",", qry.entity.Select(s => $"'{s}'"))})");
                condition.Append($@" INNER JOIN fnp_entity_user_special fnps ON fnps.emplid = ee.emplid AND fnps.is_delete = 0 AND fnps.entity_id IN ({string.Join(",", qry.entity.Select(s => $"'{s}'"))})");
            }

            condition.Append(@" WHERE (1=1) ");

            //員工工號/姓名
            if (!string.IsNullOrEmpty(qry.emp))
            {
                condition.Append(@$"AND ( ee.emplid LIKE CONCAT(N'%', @emp, N'%') OR ee.name LIKE CONCAT(N'%', @emp, N'%') OR ee.name_a LIKE CONCAT(N'%', @emp, N'%'))");
            }
            //部門代號
            if (!string.IsNullOrEmpty(qry.deptid))
            {
                condition.AppendLine(@" AND ( ee.deptid LIKE CONCAT(@deptid, N'%') OR ee.emplid in (select emplid from ps_cj_truck_vw_a WHERE deptid LIKE CONCAT(@deptid, N'%') and is_delete is null))");
            }

            csql.Append(condition.ToString());
            int totalCount = DbAccess.Database.SqlQuery<int>(csql.ToString(), new
            {
                emp = qry.emp,
                deptid = qry.deptid
            }).FirstOrDefault();

            sql.Append(condition.ToString());
            if (qry.page == null || (qry.page.PageSize == 0 && qry.page.PageIndex == 0))
                qry.page = new PageParam() { PageIndex = 1, PageSize = 25 };

            string orderStr = string.Empty;
            if (qry.order == null || string.IsNullOrEmpty(qry.order.Field))
                //issue 253 特殊主體设定-人員設定，排序（简单排序，与员工权限管理排序不一致，员工权限管理里的排序是8位数字-》7位数字-》英文开头工号然后整体从大到小。这里是先按照在职状态排序，在职在前，离职在后，然后按照工号长度倒序排序，然后按照工号倒序排序）
                orderStr = "emp_status DESC,emplid_len DESC, ee.emplid DESC, ee_company ASC";
            else
                orderStr = qry.order.Field + " " + qry.order.Order.ToString();

            int offset = (qry.page.PageIndex - 1) * qry.page.PageSize;
            sql.Append(@$" ORDER BY {orderStr} OFFSET {offset} ROWS FETCH NEXT {qry.page.PageSize} ROWS ONLY");
            result.Data = DbAccess.Database.SqlQuery<FnpEntityUserModel>(sql.ToString(),
            new
            {
                emp = qry.emp,
                deptid = qry.deptid
            }).ToList();

            result.TotalRows = totalCount;
            return result;
        }


        #endregion

        public List<FnpEntityUserDetailModel> GetUserDetail(string emplid)
        {
            StringBuilder sql = new StringBuilder();
            sql.Append(@"SELECT DISTINCT 
                            ps.emplid, 
                            ps.name, 
                            ps.name_a, 
                            fe_u.entity_id as user_entity_id,
                            fe_u.entity as user_entity,
                            feu.open_date_start as user_open_date_start,
                            feu.open_date_end as user_open_date_end,
                            ISNULL(ee_modified.name, ee_create.name) AS operate_cuser,
                            ISNULL(ee_modified.name_a, ee_create.name_a) AS operate_euser,
                            ISNULL(feu.modify_time, feu.create_time) AS operate_time
                         FROM ps_sub_ee_lgl_vw_a ps
                         LEFT JOIN ps_cj_truck_vw_a cj ON ps.emplid = cj.emplid AND cj.is_delete is null
                         LEFT JOIN ps_sub_og_lgl_vw_a og ON ps.deptid = og.deptid OR cj.deptid = og.deptid
                         LEFT JOIN fnp_entity_user_special feu ON feu.emplid = ps.emplid AND feu.is_delete = 0
                         LEFT JOIN (SELECT entity_id, entity, entity_namec, entity_namee FROM fnp_entity UNION SELECT aff_company_code AS entity_id, aff_company_abb AS entity, aff_company_cname AS entity_namec, aff_company_ename AS entity_namee FROM affiliate_company) fe_u ON feu.entity_id = fe_u.entity_id
                         LEFT JOIN ps_sub_ee_lgl_vw_a ee_create on ee_create.emplid = feu.create_user
                         LEFT JOIN ps_sub_ee_lgl_vw_a ee_modified on ee_modified.emplid = feu.modify_user
                         WHERE ps.emplid = @emplid");

            return this.NpgsqlSearchByList<FnpEntityUserDetailModel>(sql.ToString(), new { emplid = emplid });
        }

        public List<FnpEntityDeptDetailModel> GetDeptDetail(string deptid)
        {
            StringBuilder sql = new StringBuilder();
            sql.Append(@"SELECT DISTINCT 
	                       	og.deptid,
							og.descr,
							og.descr_a,
                            fe_d.entity_id as dept_entity_id,
                            fe_d.entity as dept_entity,
                            fed.open_date_start as dept_open_date_start,
                            fed.open_date_end as dept_open_date_end,
                            ISNULL(ee_modified.name, ee_create.name) AS operate_cuser,
                            ISNULL(ee_modified.name_a, ee_create.name_a) AS operate_euser,
                            ISNULL(fed.modify_time, fed.create_time) AS operate_time
                         FROM ps_sub_og_lgl_vw_a og
                         LEFT JOIN fnp_entity_dept_special fed ON fed.deptid = og.deptid and fed.is_delete = 0
                         LEFT JOIN (SELECT entity_id, entity, entity_namec, entity_namee FROM fnp_entity UNION SELECT aff_company_code AS entity_id, aff_company_abb AS entity, aff_company_cname AS entity_namec, aff_company_ename AS entity_namee FROM affiliate_company) fe_d ON fed.entity_id = fe_d.entity_id
                         LEFT JOIN ps_sub_ee_lgl_vw_a ee_create on ee_create.emplid = fed.create_user
                         LEFT JOIN ps_sub_ee_lgl_vw_a ee_modified on ee_modified.emplid = fed.modify_user
                         WHERE og.deptid = @deptid");

            return this.NpgsqlSearchByList<FnpEntityDeptDetailModel>(sql.ToString(), new { deptid = deptid });
        }

        public bool UpdateFnpEntityUser(string emplid, List<string> entity_id)
        {
            string sql_check = $@"SELECT COUNT(1) FROM fnp_entity_user WHERE emplid = @emplid";
            bool isExist = this.NpgsqlSearchBySingle<int>(sql_check, new { emplid = emplid }) > 0;

            string entity_id_str = string.Join(",", entity_id);

            if (isExist)
            {
                string sql_update = $@"UPDATE fnp_entity_user SET 
                               entity_id = @entity_id_str,
                               modify_user = @modify_user,
                               modify_time = getutcdate()
                            WHERE emplid = @emplid";
                return this.ExecuteCommand(sql_update, new { emplid = emplid, entity_id_str = entity_id_str, modify_user = MvcContext.UserInfo.current_emp }) > 0;
            }
            else
            {
                string sql_insert = $@"INSERT INTO fnp_entity_user (
                               emplid,
                               entity_id,
                               create_user,
                               create_time)
                                VALUES ( 
                                @emplid,
                                @entity_id_str,
                                @create_user,
                                getutcdate())";
                return this.ExecuteCommand(sql_insert, new { emplid = emplid, entity_id_str = entity_id_str, create_user = MvcContext.UserInfo.current_emp }) > 0;
            }
        }

        public bool UpdateFnpEntityUserSpecial(string emplid, List<string> entity_id_lsit)
        {
            string sql_delete = $@"UPDATE fnp_entity_user_special SET 
                               is_delete = 1,
                               modify_user = @modify_user,
                               modify_time = getutcdate()
                            WHERE emplid = @emplid AND open_date_start is null AND open_date_end is null";
            this.ExecuteCommand(sql_delete, new { emplid = emplid, modify_user = MvcContext.UserInfo.current_emp });

            foreach (string entity_id in entity_id_lsit)
            {
                string sql_insert = $@"INSERT INTO fnp_entity_user_special (
                               emplid,
                               entity_id,
                               create_user,
                               create_time,
                               is_delete)
                                VALUES ( 
                                @emplid,
                                @entity_id,
                                @create_user,
                                getutcdate(),
                                0)";
                this.ExecuteCommand(sql_insert, new { emplid = emplid, entity_id = entity_id, create_user = MvcContext.UserInfo.current_emp });
            }

            return true;
        }

        public List<string> DeleteFnpEntityUser(string entity_id)
        {
            string sql_special = $@"SELECT DISTINCT emplid FROM fnp_entity_user_special WHERE is_delete = 0 AND entity_id = @entity_id ORDER BY emplid";
            List<string> empList = this.NpgsqlSearchByList<string>(sql_special, new { entity_id = entity_id });

            foreach (string emplid in empList)
            {
                string sql_entity = $@"SELECT entity_id FROM fnp_entity_user WHERE emplid = @emplid";
                string user_entity_id = this.NpgsqlSearchBySingle<string>(sql_entity, new { emplid = emplid });

                if (!string.IsNullOrEmpty(user_entity_id))
                {
                    string[] user_entity = user_entity_id.Split(',');
                    List<string> new_entity = new List<string>();
                    foreach (string entity in user_entity)
                    {
                        if (entity != entity_id)
                            new_entity.Add(entity);
                    }

                    string entity_id_str = string.Join(",", new_entity);

                    string sql_update = $@"UPDATE fnp_entity_user SET 
                               entity_id = @entity_id_str,
                               modify_user = @modify_user,
                               modify_time = getutcdate()
                            WHERE emplid = @emplid";
                    this.ExecuteCommand(sql_update, new { emplid = emplid, entity_id_str = entity_id_str, modify_user = MvcContext.UserInfo.current_emp });
                }
            }

            string sql_delete = $@"UPDATE fnp_entity_user_special SET 
                               is_delete = 1,
                               modify_user = @modify_user,
                               modify_time = getutcdate()
                            WHERE entity_id = @entity_id";
            this.ExecuteCommand(sql_delete, new { entity_id = entity_id, modify_user = MvcContext.UserInfo.current_emp });

            return empList;
        }
    }
}
