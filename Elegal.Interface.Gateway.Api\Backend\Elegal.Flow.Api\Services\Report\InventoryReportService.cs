﻿using Elegal.Flow.Api.Repository.Report;
using Elegal.Interface.Api.Common.Control.Filter;
using Elegal.Interface.Api.Common.FuncHelper;
using Elegal.Interface.Api.Common.Model.ParaModel.FlowApi.Report;
using Elegal.Orm.Utils;
using System.Text;

namespace Elegal.Flow.Api.Services.Report
{
    /// <summary>
    /// 纸本盘点统计表
    /// </summary>
    public static class InventoryReportService
    {
        private static readonly InventoryReportRepository _repository = new();
        /// <summary>
        /// 查询列表数据
        /// </summary>
        /// <param name="condition">查询参数</param>
        /// <returns></returns>
        internal static IEnumerable<object> GetListData(InventoryReportQueryCondition condition)
        {
            IEnumerable<object> result = new List<object>();
            switch (condition.Dimension.ToLower())
            {
                //区域维度查询
                case "area":
                    result = condition.PaperType.Any() ? _repository.GetInventoryReportDataAreaType(condition) : _repository.GetInventoryReportDataAreaNoType(condition);
                    break;
                //主体维度查询
                case "entity":
                    result = condition.PaperType.Any() ? _repository.GetInventoryReportDataEntityType(condition) : _repository.GetInventoryReportDataEntityNoType(condition);
                    break;
            }
            return result;
        }
        /// <summary>
        /// 查询列表详情数据
        /// </summary>
        /// <param name="condition"></param>
        /// <returns></returns>
        internal static IEnumerable<object> GetDetailListData(DetailListCondition condition)
        {
            return _repository.GetDetailListData(condition);
        }
        /// <summary>
        /// 获取日志记录详情内容
        /// </summary>
        /// <param name="condition"></param>
        /// <returns></returns>
        internal static string GetLogDetail(InventoryReportQueryCondition condition)
        {
            StringBuilder stringBuilder = new StringBuilder();
            string start = string.Empty, end = string.Empty;
            if (condition.FiledDateStart.HasValue)
                start = TimeZoneInfoConvertHelper.ConvertDateByTimeZoneByUtc(condition.FiledDateStart, MvcContext.UserInfo.time_zone).ToString("yyyy/MM/dd");
            if (condition.FiledDateEnd.HasValue)
                end = TimeZoneInfoConvertHelper.ConvertDateByTimeZoneByUtc(condition.FiledDateEnd, MvcContext.UserInfo.time_zone).ToString("yyyy/MM/dd");
            if (!string.IsNullOrEmpty(start) || !string.IsNullOrEmpty(end))
                stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("InventoryReport_FiledDate", true)}：{start} ~ {end}");
            if (condition.PaperConfidenLevel.Any())
                stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("InventoryReport_PaperConfidenLevel", true)}：{string.Join(',', condition.PaperConfidenLevel.Select(s => s.Value))}");
            if (condition.PaperEntryStatus.Any())
                stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("InventoryReport_PaperEntryStatus", true)}：{string.Join(',', condition.PaperEntryStatus.Select(s => s.Value))}");
            if (condition.PaperType.Any())
                stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("InventoryReport_PaperType", true)}：{string.Join(',', condition.PaperType.Select(s => s.Value))}");
            if (!string.IsNullOrEmpty(condition.ContractNumberStart) || !string.IsNullOrEmpty(condition.ContractNumberEnd))
                stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("InventoryReport_ContractNumber", true)}：{condition.ContractNumberStart} ~ {condition.ContractNumberEnd}");
            if (!string.IsNullOrEmpty(condition.PaperPositionStart) || !string.IsNullOrEmpty(condition.PaperPositionEnd))
                stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("InventoryReport_PaperPosition", true)}：{condition.PaperPositionStart} ~ {condition.PaperPositionEnd}");
            if (condition.Area.Any())
                stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("InventoryReport_Area", true)}：{string.Join(',', condition.Area.Select(s => s.Value))}");
            if (condition.Entity.Any())
                stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("InventoryReport_Entity", true)}：{string.Join(',', condition.Entity.Select(s => s.Value))}");
            if (condition.Filter.Any())
            {
                stringBuilder.AppendLine(CommonUtil.GetDivisionLine(ActionFilter.GetMultilingualValue("InventoryReport_Filter", true)));
                foreach (var filter in condition.Filter)
                {
                    stringBuilder.AppendLine($"{filter.Key}:{filter.Value}");
                }
            }
            return stringBuilder.ToString();
        }
    }
}
