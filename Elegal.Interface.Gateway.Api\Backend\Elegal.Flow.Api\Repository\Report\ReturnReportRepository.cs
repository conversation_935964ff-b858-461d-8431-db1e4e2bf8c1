﻿using Elegal.Interface.Api.Common.Control.Filter;
using Elegal.Interface.Api.Common.FuncHelper;
using Elegal.Interface.Api.Common.Model.ParaModel.FlowApi.Report;
using Elegal.Interface.ApiData.Service.Model.QueryCondition;
using Elegal.Interface.ApiData.Service.Repository;
using Elegal.Orm;
using Elegal.Orm.Dtos;

namespace Elegal.Flow.Api.Repository.Report
{
    /// <summary>
    /// 纸本借出统计表
    /// </summary>
    public class ReturnReportRepository
    {
        private readonly string unionSql = @"(select * from(select
                                    entity_id,entity,entity_namec,entity_namee,status,area_id
                                from
                                    fnp_entity
                                union 
                                select
                                    aff_company_code entity_id,
                                    aff_company_abb entity,
                                    aff_company_cname entity_namec,
                                    aff_company_ename entity_namee,
                                    case when aff_status = '1' then 0 else 1 end status,
                                    null area_id
                                from
                                    affiliate_company)tt)";
        private readonly string baseSql = @"SELECT
	                                                pld.detail_id,
	                                                pla.lend_number,
	                                                pla.create_time AS application_time,
	                                                CASE
		                                                WHEN pld.overdue_day > 0 THEN 1
		                                                ELSE 0
	                                                END AS isOverdue,
	                                                pbd.paper_confiden_level,
	                                                pbd.paper_type,
	                                                pld.lend_return_status,
	                                                pads.my_entity_id,
	                                                pla.lend_handler_emplid,
	                                                pla.lend_handler_deptid,
	                                                pla .lend_handler_bu,
	                                                pla.lend_handler_bg,
	                                                [value] AS returnCode
                                                FROM
	                                                paper_lending_detail AS pld
                                                INNER JOIN paper_lending_application AS pla ON
	                                                pla.lend_id = pld.paper_lend_id
                                                INNER JOIN paper_basic_data AS pbd ON
	                                                pbd.basic_id = pld.paper_basic_id
                                                INNER JOIN paper_application_data AS pads ON
	                                                pads.application_id = pbd.paper_applica_id
                                                CROSS APPLY STRING_SPLIT(lend_return_status,
	                                                ',')";

        #region 归还数据统计
        /// <summary>
        /// 主体维度纸本类型
        /// </summary>
        /// <param name="condition"></param>
        /// <returns></returns>
        internal IEnumerable<object> GetReturnReportDataEntityType(ReturnReportQueryCondition condition)
        {
            //纸本数据
            string sql = $@"select dt.EntityId,dt.PaperType,dt.ReturnCode from V_PaperReturnDetail dt 
                            where 1=1";
            SearchItemGroup searchItemGroup = GetBaseSearchQuery(condition);
            SearchItem searchItem = new();
            //自选主体
            if (condition.Entity.Any() && !(condition.Entity.Count.Equals(1) && condition.Entity[0].Key.Equals("-1")))
            {
                searchItemGroup.Items.Add(new SearchItem() { Field = "dt.EntityId", Compare = CompareOperator.IN, Value = string.Join(",", condition.Entity.Select(s => s.Key)), Logic = LogicOperator.And });
                searchItem = new SearchItem() { Field = "fe.entity_id", Compare = CompareOperator.IN, Value = string.Join(",", condition.Entity.Select(s => s.Key)), Logic = LogicOperator.And };
            }
            sql += Util.GetSearchItemGroup(searchItemGroup);
            //表视图数据
            string leftSql = $@"select fe.entity_id EntityId,fe.entity Entity,sp.PaperType,sp.PaperTypeName,sp.PaperTypeOrder from {unionSql} fe
                                cross join(select func_code PaperType,fun_name PaperTypeName,sort_order PaperTypeOrder 
			                                from sys_parameters where para_code = N'lib_paperType' and lang_type = N'{MvcContext.UserInfo.logging_locale}') sp
                                where 1=1";
            searchItemGroup = new() { Logic = LogicOperator.And, Items = new List<SearchItem>() };
            searchItemGroup.Items.Add(searchItem);
            searchItemGroup.Items.Add(new SearchItem() { Field = "sp.PaperType", Compare = CompareOperator.IN, Value = string.Join(",", condition.PaperType.Select(s => s.Key)), Logic = LogicOperator.And });
            leftSql += Util.GetSearchItemGroup(searchItemGroup);
            //纸本(归还现状)透视数据
            string pivotSql = $@"select EntityId,PaperType,{GetReturnStatusListString()} from
                                ({sql}) sourceTable
                                pivot (count(ReturnCode) for ReturnCode in({GetReturnStatusListString()})) as unpivotedTable";
            sql = $@"select a.EntityId,a.Entity ShowName,a.PaperType,a.PaperTypeName,
                    {GetReturnStatusNameListString()} 
                    from ({leftSql}) a
                    left join({pivotSql}) b
                    on a.EntityId = b.EntityId and a.PaperType = b.PaperType
                    order by Entity,PaperTypeOrder";
            return DbAccess.Database.SqlQuery<object>(sql);
        }
        /// <summary>
        /// 主体维度无纸本类型
        /// </summary>
        /// <param name="condition"></param>
        /// <returns></returns>
        internal IEnumerable<object> GetReturnReportDataEntityNoType(ReturnReportQueryCondition condition)
        {
            //纸本数据
            string sql = $@"select dt.EntityId,dt.ReturnCode from V_PaperReturnDetail dt 
                            where 1=1";
            SearchItemGroup searchItemGroup = GetBaseSearchQuery(condition);
            SearchItem searchItem = new();
            //自选主体
            if (condition.Entity.Any() && !(condition.Entity.Count.Equals(1) && condition.Entity[0].Key.Equals("-1")))
            {
                searchItemGroup.Items.Add(new SearchItem() { Field = "dt.EntityId", Compare = CompareOperator.IN, Value = string.Join(",", condition.Entity.Select(s => s.Key)), Logic = LogicOperator.And });
                searchItem = new SearchItem() { Field = "fe.entity_id", Compare = CompareOperator.IN, Value = string.Join(",", condition.Entity.Select(s => s.Key)), Logic = LogicOperator.And };
            }
            sql += Util.GetSearchItemGroup(searchItemGroup);
            //表视图数据
            searchItemGroup = new();
            searchItemGroup.Items.Add(searchItem);
            string leftSql = $@"select fe.entity_id EntityId,fe.entity Entity from {unionSql} fe where 1=1 {Util.GetSearchItemGroup(searchItemGroup)}";
            //纸本(归还现状)透视数据
            string pivotSql = $@"select EntityId,{GetReturnStatusListString()} from
                                ({sql}) sourceTable
                                pivot (count(ReturnCode) for ReturnCode in({GetReturnStatusListString()})) as unpivotedTable";
            sql = $@"select a.EntityId,a.Entity ShowName,{GetReturnStatusNameListString()} 
                    from ({leftSql}) a
                    left join({pivotSql}) b
                    on a.EntityId = b.EntityId
                    order by Entity";
            return DbAccess.Database.SqlQuery<object>(sql);
        }
        /// <summary>
        /// 经办人部门维度纸本类型
        /// </summary>
        /// <param name="condition"></param>
        /// <returns></returns>
        internal IEnumerable<object> GetReturnReportDataHandlerDeptType(ReturnReportQueryCondition condition)
        {
            //纸本数据
            string sql = $@"select dt.HandlerBg,dt.HandlerDeptid,dt.PaperType,dt.ReturnCode from V_PaperReturnDetail dt 
                            where 1=1";
            SearchItemGroup searchItemGroup = GetBaseSearchQuery(condition);
            searchItemGroup.Items.Add(new SearchItem() { Field = "dt.HandlerDeptid", Compare = CompareOperator.IN, Value = string.Join(",", condition.LendHandlerDeptid.Select(s => s.Key)), Logic = LogicOperator.And });
            sql += Util.GetSearchItemGroup(searchItemGroup);
            //表视图数据
            string leftSql = $@"select og.bg HandlerBg,og.deptid HandlerDeptid,og.descr HandlerDeptName,sp.PaperType,sp.PaperTypeName,sp.PaperTypeOrder from ps_sub_og_lgl_vw_a og cross join
                                (select func_code PaperType,fun_name PaperTypeName,sort_order PaperTypeOrder from sys_parameters where para_code = 'lib_paperType' and lang_type = '{MvcContext.UserInfo.logging_locale}') sp
                                where 1=1";
            searchItemGroup = new();
            searchItemGroup.Items.Add(new SearchItem() { Field = "sp.PaperType", Compare = CompareOperator.IN, Value = string.Join(",", condition.PaperType.Select(s => s.Key)), Logic = LogicOperator.And });
            searchItemGroup.Items.Add(new SearchItem() { Field = "og.deptid", Compare = CompareOperator.IN, Value = string.Join(",", condition.LendHandlerDeptid.Select(s => s.Key)), Logic = LogicOperator.And });
            leftSql += Util.GetSearchItemGroup(searchItemGroup);
            //纸本(归还现状)透视数据
            string pivotSql = $@"select HandlerBg,HandlerDeptid,PaperType,{GetReturnStatusListString()} from
                                ({sql}) sourceTable
                                pivot (count(ReturnCode) for ReturnCode in({GetReturnStatusListString()})) as unpivotedTable";
            sql = $@"select a.HandlerBg,a.HandlerDeptid,a.HandlerDeptName ShowName,a.PaperType,a.PaperTypeName,
                    {GetReturnStatusNameListString()} 
                    from ({leftSql}) a
                    left join({pivotSql}) b
                    on a.HandlerDeptid = b.HandlerDeptid and a.PaperType = b.PaperType
                    order by HandlerDeptid,PaperTypeOrder";
            return DbAccess.Database.SqlQuery<object>(sql);
        }
        /// <summary>
        /// 经办人部门维度无纸本类型
        /// </summary>
        /// <param name="condition"></param>
        /// <returns></returns>
        internal IEnumerable<object> GetReturnReportDataHandlerDeptNoType(ReturnReportQueryCondition condition)
        {
            //纸本数据
            string sql = $@"select dt.HandlerBg,dt.HandlerDeptid,dt.ReturnCode from V_PaperReturnDetail dt 
                            where 1=1";
            SearchItemGroup searchItemGroup = GetBaseSearchQuery(condition);
            searchItemGroup.Items.Add(new SearchItem() { Field = "dt.HandlerDeptid", Compare = CompareOperator.IN, Value = string.Join(",", condition.LendHandlerDeptid.Select(s => s.Key)), Logic = LogicOperator.And });
            sql += Util.GetSearchItemGroup(searchItemGroup);
            //表视图数据
            searchItemGroup = new();
            string leftSql = $@"select og.bg HandlerBg,og.deptid HandlerDeptid,og.descr HandlerDeptName from ps_sub_og_lgl_vw_a og 
                                where {Util.GetSearchItemSql(new SearchItem() { Field = "og.deptid", Compare = CompareOperator.IN, Value = string.Join(",", condition.LendHandlerDeptid.Select(s => s.Key)) })}";
            //纸本(归还现状)透视数据
            string pivotSql = $@"select HandlerBg,HandlerDeptid,{GetReturnStatusListString()} from
                                ({sql}) sourceTable
                                pivot (count(ReturnCode) for ReturnCode in({GetReturnStatusListString()})) as unpivotedTable";
            sql = $@"select a.HandlerBg,a.HandlerDeptid,a.HandlerDeptName ShowName,{GetReturnStatusNameListString()} 
                    from ({leftSql}) a
                    left join({pivotSql}) b
                    on a.HandlerDeptid = b.HandlerDeptid
                    order by HandlerDeptid";
            return DbAccess.Database.SqlQuery<object>(sql);
        }
        /// <summary>
        /// 经办人维度纸本类型
        /// </summary>
        /// <param name="condition"></param>
        /// <returns></returns>
        internal IEnumerable<object> GetReturnReportDataHandlerType(ReturnReportQueryCondition condition)
        {
            //纸本数据
            string sql = $@"select dt.HandlerEmplid,dt.PaperType,dt.ReturnCode from V_PaperReturnDetail dt 
                            where 1=1";
            SearchItemGroup searchItemGroup = GetBaseSearchQuery(condition);
            searchItemGroup.Items.Add(new SearchItem() { Field = "dt.HandlerEmplid", Compare = CompareOperator.IN, Value = string.Join(",", condition.LendHandler.Select(s => s.Key)), Logic = LogicOperator.And });
            sql += Util.GetSearchItemGroup(searchItemGroup);
            //表视图数据
            string leftSql = $@"select ee.emplid HandlerEmplid,ee.name Name,ee.name_a NameA,sp.PaperType,sp.PaperTypeName,sp.PaperTypeOrder from ps_sub_ee_lgl_vw_a ee cross join
                                (select func_code PaperType,fun_name PaperTypeName,sort_order PaperTypeOrder from sys_parameters where para_code = 'lib_paperType' and lang_type = '{MvcContext.UserInfo.logging_locale}') sp
                                where 1=1";
            searchItemGroup = new();
            searchItemGroup.Items.Add(new SearchItem() { Field = "sp.PaperType", Compare = CompareOperator.IN, Value = string.Join(",", condition.PaperType.Select(s => s.Key)), Logic = LogicOperator.And });
            searchItemGroup.Items.Add(new SearchItem() { Field = "ee.emplid", Compare = CompareOperator.IN, Value = string.Join(",", condition.LendHandler.Select(s => s.Key)), Logic = LogicOperator.And });
            leftSql += Util.GetSearchItemGroup(searchItemGroup);
            //纸本(归还现状)透视数据
            string pivotSql = $@"select HandlerEmplid,PaperType,{GetReturnStatusListString()} from
                                ({sql}) sourceTable
                                pivot (count(ReturnCode) for ReturnCode in({GetReturnStatusListString()})) as unpivotedTable";
            sql = $@"select a.HandlerEmplid,a.Name,a.NameA,a.NameA+'('+a.Name+')' ShowName,a.PaperType,a.PaperTypeName,
                    {GetReturnStatusNameListString()} 
                    from ({leftSql}) a
                    left join({pivotSql}) b
                    on a.HandlerEmplid = b.HandlerEmplid and a.PaperType = b.PaperType
                    order by stuff(a.HandlerEmplid,1,patindex('%[A-z]%',substring(a.HandlerEmplid,1,1))-1,'') asc, len(a.HandlerEmplid) desc,a.HandlerEmplid desc,PaperTypeOrder";
            return DbAccess.Database.SqlQuery<object>(sql);
        }
        /// <summary>
        /// 经办人维度无纸本类型
        /// </summary>
        /// <param name="condition"></param>
        /// <returns></returns>
        internal IEnumerable<object> GetReturnReportDataHandlerNoType(ReturnReportQueryCondition condition)
        {
            //纸本数据
            string sql = $@"select dt.HandlerEmplid,dt.ReturnCode from V_PaperReturnDetail dt 
                            where 1=1";
            SearchItemGroup searchItemGroup = GetBaseSearchQuery(condition);
            searchItemGroup.Items.Add(new SearchItem() { Field = "dt.HandlerEmplid", Compare = CompareOperator.IN, Value = string.Join(",", condition.LendHandler.Select(s => s.Key)), Logic = LogicOperator.And });
            sql += Util.GetSearchItemGroup(searchItemGroup);
            //表视图数据
            string leftSql = $@"select ee.emplid HandlerEmplid,ee.name Name,ee.name_a NameA from ps_sub_ee_lgl_vw_a ee
                                where {Util.GetSearchItemSql(new SearchItem() { Field = "ee.emplid", Compare = CompareOperator.IN, Value = string.Join(",", condition.LendHandler.Select(s => s.Key)) })}";
            //纸本(归还现状)透视数据
            string pivotSql = $@"select HandlerEmplid,{GetReturnStatusListString()} from
                                ({sql}) sourceTable
                                pivot (count(ReturnCode) for ReturnCode in({GetReturnStatusListString()})) as unpivotedTable";
            sql = $@"select a.HandlerEmplid,a.Name,a.NameA,a.NameA+'('+a.Name+')' ShowName,
                    {GetReturnStatusNameListString()} 
                    from ({leftSql}) a
                    left join({pivotSql}) b
                    on a.HandlerEmplid = b.HandlerEmplid
                    order by stuff(a.HandlerEmplid,1,patindex('%[A-z]%',substring(a.HandlerEmplid,1,1))-1,'') asc, len(a.HandlerEmplid) desc,a.HandlerEmplid desc";
            return DbAccess.Database.SqlQuery<object>(sql);
        }

        /// <summary>
        /// 基础查询条件
        /// </summary>
        /// <param name="condition"></param>
        /// <returns></returns>
        private static SearchItemGroup GetBaseSearchQuery(ReturnReportQueryCondition condition)
        {
            SearchItemGroup searchItemGroup = new() { Logic = LogicOperator.And, Items = new List<SearchItem>() };
            if (condition.ApplicationTimeStart.HasValue)
                searchItemGroup.Items.Add(new SearchItem() { Field = "dt.ApplicationTime", Compare = CompareOperator.GE, Value = TimeZoneInfoConvertHelper.ConvertDateByTimeZoneByUtc(condition.ApplicationTimeStart, MvcContext.UserInfo.time_zone).ToString("yyyy/MM/dd HH:mm:ss"), Logic = LogicOperator.And });
            if (condition.ApplicationTimeEnd.HasValue)
                searchItemGroup.Items.Add(new SearchItem() { Field = "dt.ApplicationTime", Compare = CompareOperator.LE, Value = TimeZoneInfoConvertHelper.ConvertDateByTimeZoneByUtc(condition.ApplicationTimeEnd, MvcContext.UserInfo.time_zone).ToString("yyyy/MM/dd HH:mm:ss"), Logic = LogicOperator.And });
            if (condition.IsOverdue.Any())
                searchItemGroup.Items.Add(new SearchItem() { Field = "dt.IsOverdue", Compare = CompareOperator.IN, Value = string.Join(',', condition.IsOverdue.Select(s => s.Key)), Logic = LogicOperator.And });
            if (condition.PaperConfidenLevel.Any())
                searchItemGroup.Items.Add(new SearchItem() { Field = "dt.PaperConfidenLevel", Compare = CompareOperator.IN, Value = string.Join(',', condition.PaperConfidenLevel.Select(s => s.Key)), Logic = LogicOperator.And });
            if (condition.PaperType.Any())
                searchItemGroup.Items.Add(new SearchItem() { Field = "dt.PaperType", Compare = CompareOperator.IN, Value = string.Join(',', condition.PaperType.Select(s => s.Key)), Logic = LogicOperator.And });
            return searchItemGroup;
        }
        /// <summary>
        /// 纸本归还现状
        /// </summary>
        /// <returns></returns>
        private static string GetReturnStatusListString()
        {
            SysParametersDataRepository repository = new SysParametersDataRepository();
            var status = repository.Query(new SysParametersQueryCondition()
            {
                ParaCode = "lib_returnStatus",
                LangType = MvcContext.UserInfo.logging_locale,
                OrderBys = new List<OrderByParam>() { new OrderByParam() { Field = "sort_order", Order = OrderBy.ASC } }
            }).Select(s => $"[{s.FuncCode}]");
            return string.Join(',', status);
        }
        /// <summary>
        /// 纸本归还现状名称
        /// </summary>
        /// <returns></returns>
        private static string GetReturnStatusNameListString()
        {
            SysParametersDataRepository repository = new SysParametersDataRepository();
            var status = repository.Query(new SysParametersQueryCondition()
            {
                ParaCode = "lib_returnStatus",
                LangType = MvcContext.UserInfo.logging_locale,
                OrderBys = new List<OrderByParam>() { new OrderByParam() { Field = "sort_order", Order = OrderBy.ASC } }
            }).Select(s => $"isnull([{s.FuncCode}],0) as 'lib_returnStatus_{s.FuncCode}'");
            return string.Join(',', status);
        }
        #endregion

        /// <summary>
        /// 查询列表详情数据
        /// </summary>
        /// <param name="condition"></param>
        /// <returns></returns>
        public IEnumerable<object> GetDetailListData(ReturnDetailListCondition condition)
        {
            string sql = $@"select 
	                            paper.PaperCode,paper.ContractNumber,paper.LendNumber,paper.RetrieveNumber,
	                            paper.FillEmplid,paper.FillCname,paper.FillEname,paper.HandlerEmplid,
	                            paper.HandlerDeptid,paper.HandleCname,paper.HandleEname,
	                            paper.ApplicationTime,paper.EntityId,paper.Entity,paper.PaperName,
	                            paper.ActualReturnTime,paper.ShouldReturnTime,paper.IsOverdue,paper.OverdueDay,detail.lend_return_status ReturnCode,
                                (
		                            select string_agg(tt.fun_name,',') 
		                            from (select fun_name from sys_parameters where para_code = N'lib_returnStatus' AND lang_type = N'ZH-TW' AND func_code in(select value from string_split(detail.lend_return_status,','))) tt
	                            ) ReturnCodeName,
                                (select fun_name from sys_parameters where lang_type = N'{MvcContext.UserInfo.logging_locale}' and para_code = 'lib_paperType' and func_code = paper.PaperType)PaperTypeName
                            from V_PaperReturnDetail paper
                            left join paper_lending_detail detail on paper.DetailId = detail.detail_id
                            where 1=1 ";
            SearchItemGroup searchItemGroup = new() { Logic = LogicOperator.And, Items = new List<SearchItem>() };
            if (condition.ApplicationTimeStart.HasValue)
                searchItemGroup.Items.Add(new SearchItem() { Field = "paper.ApplicationTime", Compare = CompareOperator.GE, Value = condition.ApplicationTimeStart.ToString("yyyy/MM/dd HH:mm:ss"), Logic = LogicOperator.And });
            if (condition.ApplicationTimeEnd.HasValue)
                searchItemGroup.Items.Add(new SearchItem() { Field = "paper.ApplicationTime", Compare = CompareOperator.LE, Value = condition.ApplicationTimeEnd.ToString("yyyy/MM/dd HH:mm:ss"), Logic = LogicOperator.And });
            if (condition.IsOverdue.Any()) searchItemGroup.Items.Add(new SearchItem() { Field = "paper.IsOverdue", Compare = CompareOperator.IN, Value = string.Join(",", condition.IsOverdue), Logic = LogicOperator.And });
            if (condition.PaperConfidenLevel.Any()) searchItemGroup.Items.Add(new SearchItem() { Field = "paper.PaperConfidenLevel", Compare = CompareOperator.IN, Value = string.Join(",", condition.PaperConfidenLevel), Logic = LogicOperator.And });
            if (condition.PaperType.Any()) searchItemGroup.Items.Add(new SearchItem() { Field = "paper.PaperType", Compare = CompareOperator.IN, Value = string.Join(",", condition.PaperType), Logic = LogicOperator.And });
            searchItemGroup.Items.Add(new SearchItem() { Field = "paper.ReturnCode", Compare = CompareOperator.IN, Value = string.Join(",", condition.ReturnCode), Logic = LogicOperator.And });
            switch (condition.Dimension.ToLower())
            {
                case "entity":
                    searchItemGroup.Items.Add(new SearchItem() { Field = "paper.EntityId", Compare = CompareOperator.IN, Value = string.Join(",", condition.Entity), Logic = LogicOperator.And });
                    break;
                case "dept":
                    searchItemGroup.Items.Add(new SearchItem() { Field = "paper.HandlerDeptid", Compare = CompareOperator.IN, Value = string.Join(",", condition.LendHandlerDeptid), Logic = LogicOperator.And });
                    break;
                case "handler":
                    searchItemGroup.Items.Add(new SearchItem() { Field = "paper.HandlerEmplid", Compare = CompareOperator.IN, Value = string.Join(",", condition.LendHandler), Logic = LogicOperator.And });
                    break;
            }
            sql += Util.GetSearchItemGroup(searchItemGroup);
            sql += "order by paper.LendNumber,paper.PaperCode";
            return DbAccess.Database.SqlQuery<object>(sql);
        }
    }
}
