﻿using Elegal.Flow.Api.Repository.Application;
using Elegal.Flow.Api.Services.Individual;
using Elegal.Flow.Common.Repository.FlowStep;
using Elegal.Flow.Common.Services;
using Elegal.Flow.Common.Services.FlowStep;
using Elegal.Flow.Common.Services.LiteratureApply;
using Elegal.Interface.Api.Common.Control.Filter;
using Elegal.Interface.Api.Common.FuncHelper;
using Elegal.Interface.Api.Common.FuncService;
using Elegal.Interface.Api.Common.Model.Enum;
using Elegal.Interface.Api.Common.Model.ParaModel.FlowApi;
using Elegal.Interface.Api.Common.Model.ResultModel;
using Elegal.Interface.Api.Common.Model.ResultModel.FlowApi.FlowStep;
using Elegal.Interface.Api.Common.Model.ViewModel.FlowApi;
using Elegal.Interface.Api.Common.Model.ViewModel.FlowApi.Application;
using Elegal.Interface.ApiData.Service.FuncService;
using Elegal.Interface.ApiData.Service.Model.Condition;
using Elegal.Interface.ApiData.Service.Model.DbModel;
using Elegal.Interface.ApiData.Service.Model.QueryCondition;
using Elegal.Orm;
using Elegal.Orm.Dtos;
using Elegal.Orm.Utils;
using System.Data;
using System.Text;
#nullable disable
namespace Elegal.Flow.Api.Services.Application
{
    /// <summary>
    /// 資料建檔 -> 合約管理作業
    /// </summary>
    public static class DocumentationOriginalArchiveService
    {
        private static string time_zone = AppSettingHelper.GetValue("TimeZone");
        private static DocumentationOriginalArchiveRepository _repository = new DocumentationOriginalArchiveRepository();
        private static readonly FlowStepRepository _flowRepository = new FlowStepRepository();
        //20250114二級菜單報錯問題提交，原先代碼邏輯有問題
        //資料建檔流程id固定為4
        private static readonly int flowId = 4;

        #region 基本資訊查詢
        /// <summary>
        /// 基本資訊查詢
        /// </summary>
        /// <param name="applyNumber"></param>
        /// <returns></returns>
        public static DocumentationContractViewModel GetBaseInformation(string applyNumber)
        {
            LiteratureApplication literatureApplication = LiteratureApplicationDataService.FindByKey(applyNumber);
            OriginalArchiveRecord originalArchiveRecord = OriginalArchiveRecordDataService.FindByKey(applyNumber);
            PaperApplicationData paperApplicationData = PaperApplicationDataDataService.Find(new PaperApplicationDataCondition() { ApplyNumber = applyNumber });
            var sysParams = SysParametersService.GetParameters(["dateOptions", "effDateOptions"]);
            var flowStepSigners = DbAccess.Query<FlowStepSigner>(new FlowStepSignerCondition { ApplyNumber = applyNumber });
            //当关签核人员
            var users = new List<PsSubEeLglVwA>();
            if (flowStepSigners.Length > 0)
                users = DbAccess.Query<PsSubEeLglVwA>(new PsSubEeLglVwAQueryCondition()
                {
                    SearchItemGroup = new SearchItemGroup()
                    {
                        Items = new List<SearchItem>()
                    {
                        new SearchItem()
                        {
                            Compare = CompareOperator.ARRAYIN,
                            Field = "emplid",
                            Values =  flowStepSigners.Select(s=>s.SignerEmplid)
                        }
                    }
                    }
                }).ToList();
            //加签签核人员
            var flowStepSignerInvitees = DbAccess.Query<FlowStepSignerInvitee>(new FlowStepSignerInviteeQueryCondition
            {
                ApplyNumber = applyNumber,
                OrderBys = [new OrderByParam() { Field = "sp_invite_level", Order = OrderBy.ASC }]
            });
            flowStepSignerInvitees = flowStepSignerInvitees.Where(w => w.SpInviteLevel.Equals(flowStepSignerInvitees.OrderBy(b => b.SpInviteLevel).FirstOrDefault().SpInviteLevel)).ToArray();
            var inviteeUsers = new List<PsSubEeLglVwA>();
            if (flowStepSignerInvitees.Length != 0)
                inviteeUsers = DbAccess.Query<PsSubEeLglVwA>(new PsSubEeLglVwAQueryCondition()
                {
                    SearchItemGroup = new SearchItemGroup()
                    {
                        Items =
                        [
                            new()
                        {
                            Compare = CompareOperator.ARRAYIN,
                            Field = "emplid",
                            Values =  flowStepSignerInvitees.Select(s=>s.InviteeEmplid)
                        }
                        ]
                    }
                }).ToList();
            var applicationUsers = PsSubEeLglVwADataService.Query(new PsSubEeLglVwAQueryCondition()
            {
                SearchItemGroup = new SearchItemGroup()
                {
                    Items = [
                        new()
                        {
                            Compare = CompareOperator.ARRAYIN,
                            Logic = LogicOperator.And,
                            Field = "emplid",
                            Values = [literatureApplication?.FillEmplid, literatureApplication?.PicEmplid, literatureApplication?.IncumbentEmplid, literatureApplication?.LegalAffairsEmplid]
                        }
                    ]
                }
            });
            var fill = applicationUsers.FirstOrDefault(f => f.Emplid.Equals(literatureApplication?.FillEmplid));//填单人
            var handler = applicationUsers.FirstOrDefault(f => f.Emplid.Equals(literatureApplication?.PicEmplid));//经办人
            var current = applicationUsers.FirstOrDefault(f => f.Emplid.Equals(literatureApplication?.IncumbentEmplid));//现任联络人
            var legal = applicationUsers.FirstOrDefault(f => f.Emplid.Equals(literatureApplication?.LegalAffairsEmplid));//承办法务
            DocumentationContractViewModel result = new()
            {
                ApplyNumber = applyNumber,
                ApplyDate = literatureApplication?.ApplyTime,
                IsFirstTime = originalArchiveRecord == null || (originalArchiveRecord != null && string.IsNullOrEmpty(originalArchiveRecord.OriginArchiveType)),
                //SIT：517 -> 起單後不應該給定默認值
                HasPaper = paperApplicationData?.HavingPaper.ToString(),
                //HasPaper = paperApplicationData?.HavingPaper.ToString() ?? "0",
                HasPaperDetail = paperApplicationData?.IsPaperDetail.ToString(),
                MyEntity = literatureApplication?.EntityId,
                PartyA = literatureApplication?.OtherParty,
                ContractName = literatureApplication?.ContractName,
                ContractNumber = literatureApplication?.OtherPartyNumber,
                PicDeptid = literatureApplication?.PicDeptid,
                FormType = literatureApplication?.FormType,
                DataArchiveType = literatureApplication?.DataArchiveType,//資料建檔類型
                ConfidenLevel = literatureApplication?.ConfidenLevel,
                FillEmplid = literatureApplication?.FillEmplid,
                FillCname = fill?.Name,
                FillEname = fill?.NameA,
                FillMail = fill?.EmailAddressA,
                fillTermination = fill?.Termination,//CR：385
                HandlerEmplid = literatureApplication?.PicEmplid,
                HandlerCname = handler?.Name,
                HandlerEname = handler?.NameA,
                HandlerMail = handler?.EmailAddressA,
                handlerTermination = handler?.Termination,//CR：385
                CurrentContactPerson = literatureApplication?.IncumbentEmplid,
                CurrentContactPersonCname = current?.Name,
                CurrentContactPersonEname = current?.NameA,
                currentContactPersonTermination = current?.Termination,//CR：385
                LegalEmplid = literatureApplication?.LegalAffairsEmplid,
                LegalCname = legal?.Name,
                LegalEname = legal?.NameA,
                legalTermination = legal?.Termination,//CR：385
                ApplicationState = literatureApplication?.ApplicationState,
                EffDate = literatureApplication?.EffDate,
                ConfirmEffDate = literatureApplication?.EffDate,
                ConfirmEffDateType = literatureApplication?.EffType,
                ConfirmEffDateTypeName = sysParams.FirstOrDefault(f => f.ParaCode.Equals("effDateOptions") && f.FuncCode.Equals(literatureApplication?.EffType))?.FunName,
                OtherEffDate = literatureApplication?.OtherEffDate,
                SignDate = literatureApplication?.SignDate,
                ConfirmSignDate = literatureApplication?.SignDate,
                ConfirmSignDateType = literatureApplication?.SignType,
                ConfirmSignDateTypeName = sysParams.FirstOrDefault(f => f.ParaCode.Equals("dateOptions") && f.FuncCode.Equals(literatureApplication?.SignType))?.FunName,
                OtherSignDate = literatureApplication?.OtherSignDate,
                ExpDate = literatureApplication?.ExpDate,
                ConfirmExpDate = literatureApplication?.ExpDate,
                ConfirmExpDateType = literatureApplication?.ExpType,
                ConfirmExpDateTypeName = sysParams.FirstOrDefault(f => f.ParaCode.Equals("dateOptions") && f.FuncCode.Equals(literatureApplication?.ExpType))?.FunName,
                HasExpExtend = literatureApplication?.HasExpExtend,
                OtherExpDate = literatureApplication?.OtherExpDate,
                StepId = flowStepSigners.FirstOrDefault()?.StepId.ToString(),
                ShouldSigners = string.Join(", ", users.Select(u => u.NameA)),
                SpInviteLevel = flowStepSignerInvitees.FirstOrDefault()?.SpInviteLevel,
                SpecialInviteeUser = string.Join(", ", inviteeUsers.Select(u => u.NameA)),
                PaperBasicDatas = paperApplicationData == null ? [] : PaperBasicDataDataService.Query(new PaperBasicDataQueryCondition() { PaperApplicaId = paperApplicationData?.ApplicationId }).Select(CommonUtil.Map<PaperBasicData, Interface.ApiData.Service.Model.ViewModel.PaperBasicDataViewModel>).ToList(),
                SysUploadFiles = GetPaperFile(applyNumber),
                FlowContractHistorys = ContractOriginalArchiveService.GetContractHistoryData(applyNumber),
                ArchiveRecordsResults = new ContractOriginalArchiveRepository().ArchiveRecords(applyNumber).ToList(),
                //0407 確認到期日 是否延展
                confirmHasExpExtend = literatureApplication?.HasExpExtend,
            };
            if (originalArchiveRecord != null)
            {
                result.OriginArchiveRemark = originalArchiveRecord?.OriginArchiveRemark;
                result.OriginArchiveType = originalArchiveRecord?.OriginArchiveType;
                result.OriginArchiveTime = originalArchiveRecord.OriginArchiveDate;
                result.confirmHasExpExtend = originalArchiveRecord?.ConfirmHasExpExtend;
            }
            #region 第1层级签核(申請人/經辦人/現任聯絡人)
            FlowStepSignerInvitee flowStepSignerInvitee = flowStepSignerInvitees.FirstOrDefault(f => f.SpInviteLevel.Equals(1));
            if (flowStepSignerInvitee != null)
            {
                result.InviteeTask = flowStepSignerInvitee.PicSpReason;
                result.InviteeReason = flowStepSignerInvitee.PicSpReamrk;
            }
            else
            {
                #region 第2层级签核(法務)
                flowStepSignerInvitee = flowStepSignerInvitees.FirstOrDefault(f => f.SpInviteLevel.Equals(2));
                if (flowStepSignerInvitee != null)
                {
                    result.InviteeTask = flowStepSignerInvitee.LegalSpReason;
                    result.InviteeReason = flowStepSignerInvitee.LegalSpReamrk;
                }
                #endregion
            }
            #endregion
            #region 效期确认
            var obj = _repository.GetContractValidityInformationRecord(applyNumber);
            if (obj != null)
            {
                result.ConfirmEffDate = obj?.ConfirmEffDate;
                result.ConfirmSignDate = obj?.ConfirmSignDate;
                result.ConfirmExpDate = obj?.ConfirmExpDate;
            }
            #endregion
            #region 其他单绑定时提示信息
            result.OtherNotice = new ContractOriginalArchiveRepository().GetOtherNotice(applyNumber);
            #endregion
            TimeZoneInfoConvertHelper.ConvertObjectDateByUtc(result);
            return result;
        }
        #endregion

        #region 提交歸檔資訊
        /// <summary>
        /// 提交歸檔資訊
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public static ApiResultModelByObject SetOriginalArchive(DocumentationContractViewModel model)
        {
            ApiResultModelByObject result = new ApiResultModelByObject();
            DbAccess.PerformInTransaction(dbContext =>
            {
                result.rtnSuccess = LiteratureApplyService.HandleLiteratureOriginalArchiveToTransaction(dbContext, model);
            });

            #region 发送邮件
            if (result.rtnSuccess)
            {
                int step = _flowRepository.GetContractMaTaskStepid(flowId);
                SendMailService.SendMail("A", model.ApplyNumber, MvcContext.UserInfo.current_emp, step, [], new List<MailTypeUtils> { MailTypeUtils.F5 });
            }
            #endregion

            ActionFilter.InitLogRecord(logRecord =>
            {
                StringBuilder stringBuilder = new();
                if (!string.IsNullOrEmpty(model.ApplyNumber)) stringBuilder.AppendLine($"申請單號: {model.ApplyNumber}");
                logRecord.Detail = stringBuilder.ToString();
            });
            result.rtnSuccess = true;
            return result;
        }
        #endregion

        #region 獲取紙本資訊附檔
        /// <summary>
        /// 獲取紙本資訊附檔
        /// </summary>
        /// <param name="applyNumber"></param>
        /// <returns></returns>
        public static List<SysUploadFile> GetPaperFile(string applyNumber)
        {
            return [.. SysUploadFileDataService.Query(new SysUploadFileQueryCondition()
            {
                UploadType = 4,
                UploadKey = applyNumber,
                ArchivePurposes = 3,
                IsWatermake = (int)YesOrNoUtils.No
            })];
        }
        #endregion

        #region 保存特殊加簽設定
        /// <summary>
        /// 保存特殊加簽設定
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public static ApiResultModelByObject AddSpecialSignature(SpecialSignatureModel model)
        {
            ApiResultModelByObject result = new ApiResultModelByObject() { rtnSuccess = true };
            var user = MvcContext.UserInfo;
            LiteratureApplication literatureApplication = LiteratureApplicationDataService.FindByKey(model.ApplyNumber);//申请单信息
            if (literatureApplication == null)
                return new ApiResultModelByObject()
                {
                    listData = false,
                    rtnSuccess = false,
                    messageTitle = ActionFilter.GetMultilingualValue("Common_notFound", true),
                    messageType = MessageTypeUtils.Warning.ToString(),
                };
            var currentUser = PsSubEeLglVwADataService.FindByKey(user.current_emp);//操作者
            var loggingUser = PsSubEeLglVwADataService.FindByKey(user.logging_emp);//登录者
            var fill = PsSubEeLglVwADataService.FindByKey(literatureApplication.FillEmplid);//填单人
            var handler = PsSubEeLglVwADataService.FindByKey(literatureApplication.PicEmplid);//经办人
            var legal = PsSubEeLglVwADataService.FindByKey(literatureApplication.LegalAffairsEmplid);//承办法务
            var incumbent = PsSubEeLglVwADataService.FindByKey(literatureApplication.IncumbentEmplid);//现任联络人
            var sysParameters = SysParametersDataService.Query(new SysParametersQueryCondition()
            {
                LangType = user.logging_locale,
                SearchItemGroup = new SearchItemGroup()
                {
                    Items = new List<SearchItem>() { new SearchItem() { Field = "para_code", Values = ["spInviteSetting", "spInviteByPic", "spInviteByLegal"], Compare = CompareOperator.ARRAYIN } }
                }
            });//参数
            var flowStepSignerList = DbAccess.Query<FlowStepSigner>(new FlowStepSignerQueryCondition() { ApplyNumber = model.ApplyNumber });//当关签核人
            if (flowStepSignerList.Any(a => a.IsInvitee.Equals((int)YesOrNoUtils.Yes)))
                return new ApiResultModelByObject()
                {
                    listData = false,
                    rtnSuccess = false,
                    messageTitle = ActionFilter.GetMultilingualValue("Common_repetAction", true),
                    messageType = MessageTypeUtils.Warning.ToString(),
                };
            var flowStepSigner = flowStepSignerList.FirstOrDefault(f => f.SignerEmplid.Equals(currentUser.Emplid));//当前操作者是否是当关者，若不是，则为代理人
            var shouldSigner = currentUser;//应签核人
            if (flowStepSigner == null)//代理人
            {
                var agentList = SysAgentService.GetBeAgentListByEmplid(user.current_emp);
                var list = agentList.Where(w => flowStepSignerList.FirstOrDefault(f => f.SignerEmplid.Equals(w.AgentEmpid)) != null);
                shouldSigner = PsSubEeLglVwADataService.FindByKey(flowStepSignerList.FirstOrDefault()?.SignerEmplid);
            }
            //Issue：207 建檔特殊加簽通知 必要參數
            bool isSendMail = false;
            List<MailRecipientResultModel> inviteSigner = new List<MailRecipientResultModel>();
            StringBuilder stepOpinion = new StringBuilder();
            DbAccess.PerformInTransaction(dbContext =>
            {
                FlowStepSignerInvitee entity = new FlowStepSignerInvitee();
                //合约管理作业历程
                FlowContractHistory flowContractHistory = new FlowContractHistory()
                {
                    FlowId = flowId,
                    ApplyNumber = model.ApplyNumber,
                    StepId = flowStepSignerList.FirstOrDefault()?.StepId,
                    ApplySequence = 1,
                    StepAction = 18,
                    ActualSignerEmplid = currentUser.Emplid,
                    ActualSignerName = currentUser.Name,
                    ActualSignerNameA = currentUser.NameA,
                    ActualSignerDeptid = currentUser.Deptid,
                    ShouldSignerEmplid = shouldSigner.Emplid,
                    ShouldSignerName = shouldSigner.Name,
                    ShouldSignerNameA = shouldSigner.NameA,
                    ShouldSignerDeptid = shouldSigner.Deptid,
                    ContractOptions = "特殊加簽",
                    CreateTime = DateTime.UtcNow
                };
                
                stepOpinion.AppendLine($"{ActionFilter.GetMultilingualValue("Common_order", true)}：{sysParameters.Where(w => w.ParaCode.Equals("spInviteSetting")).FirstOrDefault(f => f.FuncCode.Equals(model.SignatureSetting))?.FunName}");
                //特殊加签设定类型区分
                switch (model.SignatureSetting)
                {
                    //申請人/經辦人/現任聯絡人
                    case "01":
                        entity = new FlowStepSignerInvitee
                        {
                            ApplyNumber = model.ApplyNumber,
                            FlowId = flowId,
                            StepId = flowStepSignerList.FirstOrDefault()?.StepId,
                            CreateUser = user.current_emp,
                            InviteeType = 2,//1：一般加簽；2：特殊加簽
                            PicSpReason = string.Join(",", model.SignatureResonHandler),
                            PicSpReamrk = model.SignatureRemarkHandler,
                            SpInviteLevel = 1,
                            SpInviteeSetting = "01"
                        };
                        if (!string.IsNullOrEmpty(literatureApplication.IncumbentEmplid))
                        {
                            entity.InviteeEmplid = incumbent.Emplid;
                            entity.InviteeDeptid = incumbent.Deptid;
                            dbContext.Create(entity);
                            //將加簽人員添加到特定集合中 Issue：207
                            inviteSigner.Add(new MailRecipientResultModel
                            {
                                recipient_emplid = incumbent.Emplid,
                                recipient_deptid = incumbent.Deptid
                            });
                            stepOpinion.AppendLine($"申請人/經辦人/現任聯絡人:{string.Join("/", incumbent.NameA)}"); 
                        }
                        else
                        {
                            entity.InviteeEmplid = fill.Emplid;
                            entity.InviteeDeptid = fill.Deptid;
                            dbContext.Create(entity);
                            //將加簽人員添加到特定集合中 Issue：207
                            inviteSigner.Add(new MailRecipientResultModel
                            {
                                recipient_emplid = fill.Emplid,
                                recipient_deptid = fill.Deptid
                            });
                            if (!literatureApplication.FillEmplid.Equals(literatureApplication.PicEmplid))
                            {
                                entity.InviteeEmplid = handler.Emplid;
                                entity.InviteeDeptid = handler.Deptid;
                                dbContext.Create(entity);
                                //將加簽人員添加到特定集合中 Issue：207
                                inviteSigner.Add(new MailRecipientResultModel
                                {
                                    recipient_emplid = handler.Emplid,
                                    recipient_deptid = handler.Deptid
                                });
                            }
                            stepOpinion.AppendLine($"申請人/經辦人/現任聯絡人:{string.Join("/", handler.NameA)}");
                        }
                        if (model.SignatureResonHandler.Count > 0)
                            stepOpinion.AppendLine($"{ActionFilter.GetMultilingualValue("Common_specialWork1", true)} ：{string.Join("、", model.SignatureResonHandler.Select(s => sysParameters.Where(w => w.ParaCode.Equals("spInviteByPic")).FirstOrDefault(f => f.FuncCode.Equals(s))?.FunName))}");
                        if (!string.IsNullOrEmpty(model.SignatureRemarkHandler)) stepOpinion.AppendLine($"{ActionFilter.GetMultilingualValue("Common_specialInviteRemark1", true)}：{model.SignatureRemarkHandler}");
                        break;
                    //法務
                    case "02":
                        entity = new FlowStepSignerInvitee
                        {
                            ApplyNumber = model.ApplyNumber,
                            FlowId = flowId,
                            StepId = flowStepSignerList.FirstOrDefault()?.StepId,
                            CreateUser = user.current_emp,
                            InviteeType = 2,//1：一般加簽；2：特殊加簽
                            LegalSpReason = string.Join(",", model.SignatureResonLegal),
                            LegalSpReamrk = model.SignatureRemarkLegal,
                            InviteeEmplid = legal?.Emplid,
                            InviteeDeptid = legal?.Deptid,
                            SpInviteLevel = 2,
                            SpInviteeSetting = "02"
                        };
                        dbContext.Create(entity);
                        //將加簽人員添加到特定集合中 Issue：207
                        inviteSigner.Add(new MailRecipientResultModel
                        {
                            recipient_emplid = legal?.Emplid,
                            recipient_deptid = legal?.Deptid
                        });
                        if (legal != null)
                            stepOpinion.AppendLine($"承辦法務: {legal.NameA}");
                        if (model.SignatureResonLegal.Count > 0)
                            stepOpinion.AppendLine($"{ActionFilter.GetMultilingualValue("Common_specialWork2", true)}：{string.Join("、", model.SignatureResonLegal.Select(s => sysParameters.Where(w => w.ParaCode.Equals("spInviteByLegal")).FirstOrDefault(f => f.FuncCode.Equals(s))?.FunName))}");
                        if (!string.IsNullOrEmpty(model.SignatureRemarkLegal)) stepOpinion.AppendLine($"{ActionFilter.GetMultilingualValue("Common_specialInviteRemark2", true)}：{model.SignatureRemarkLegal}");
                        break;
                    //申請人/經辦人/現任聯絡人 > 法務
                    case "03":
                        entity = new FlowStepSignerInvitee
                        {
                            ApplyNumber = model.ApplyNumber,
                            FlowId = flowId,
                            StepId = flowStepSignerList.FirstOrDefault()?.StepId,
                            CreateUser = user.current_emp,
                            InviteeType = 2,//1：一般加簽；2：特殊加簽
                            PicSpReason = string.Join(",", model.SignatureResonHandler),
                            PicSpReamrk = model.SignatureRemarkHandler,
                            SpInviteLevel = 1,
                            SpInviteeSetting = "03"
                        };
                        if (!string.IsNullOrEmpty(literatureApplication.IncumbentEmplid))
                        {
                            entity.InviteeEmplid = incumbent.Emplid;
                            entity.InviteeDeptid = incumbent.Deptid;
                            dbContext.Create(entity);
                            //將加簽人員添加到特定集合中 Issue：207
                            inviteSigner.Add(new MailRecipientResultModel
                            {
                                recipient_emplid = incumbent.Emplid,
                                recipient_deptid = incumbent.Deptid
                            });
                            stepOpinion.AppendLine($"申請人/經辦人/現任聯絡人:{string.Join("/", incumbent.NameA)}");
                        }
                        else
                        {
                            entity.InviteeEmplid = fill.Emplid;
                            entity.InviteeDeptid = fill.Deptid;
                            dbContext.Create(entity);
                            //將加簽人員添加到特定集合中 Issue：207
                            inviteSigner.Add(new MailRecipientResultModel
                            {
                                recipient_emplid = fill.Emplid,
                                recipient_deptid = fill.Deptid
                            });
                            if (!literatureApplication.FillEmplid.Equals(literatureApplication.PicEmplid))
                            {
                                entity.InviteeEmplid = handler.Emplid;
                                entity.InviteeDeptid = handler.Deptid;
                                dbContext.Create(entity);
                                //將加簽人員添加到特定集合中 Issue：207
                                inviteSigner.Add(new MailRecipientResultModel
                                {
                                    recipient_emplid = handler.Emplid,
                                    recipient_deptid = handler.Deptid
                                });
                            }
                            stepOpinion.AppendLine($"申請人/經辦人/現任聯絡人:{string.Join("/", handler.NameA)}");
                        }
                        entity = new FlowStepSignerInvitee
                        {
                            ApplyNumber = model.ApplyNumber,
                            FlowId = flowId,
                            StepId = flowStepSignerList.FirstOrDefault()?.StepId,
                            CreateUser = user.current_emp,
                            InviteeType = 2,//1：一般加簽；2：特殊加簽
                            LegalSpReason = string.Join(",", model.SignatureResonLegal),
                            LegalSpReamrk = model.SignatureRemarkLegal,
                            InviteeEmplid = legal?.Emplid,
                            InviteeDeptid = legal?.Deptid,
                            SpInviteLevel = 2,
                            SpInviteeSetting = "03"
                        };
                        dbContext.Create(entity);
                        //將加簽人員添加到特定集合中 Issue：207
                        inviteSigner.Add(new MailRecipientResultModel
                        {
                            recipient_emplid = legal?.Emplid,
                            recipient_deptid = legal?.Deptid
                        });
                        if (model.SignatureResonHandler.Count > 0)
                            stepOpinion.AppendLine($"{ActionFilter.GetMultilingualValue("Common_specialWork1", true)}：{string.Join("、", model.SignatureResonHandler.Select(s => sysParameters.Where(w => w.ParaCode.Equals("spInviteByPic")).FirstOrDefault(f => f.FuncCode.Equals(s))?.FunName))}");
                        if (!string.IsNullOrEmpty(model.SignatureRemarkHandler)) stepOpinion.AppendLine($"{ActionFilter.GetMultilingualValue("Common_specialInviteRemark1", true)}：{model.SignatureRemarkHandler}");
                        if (legal != null)
                            stepOpinion.AppendLine($"承辦法務: {legal.NameA}");
                        if (model.SignatureResonLegal.Count > 0)
                            stepOpinion.AppendLine($"{ActionFilter.GetMultilingualValue("Common_specialWork2", true)}：{string.Join("、", model.SignatureResonLegal.Select(s => sysParameters.Where(w => w.ParaCode.Equals("spInviteByLegal")).FirstOrDefault(f => f.FuncCode.Equals(s))?.FunName))}");
                        if (!string.IsNullOrEmpty(model.SignatureRemarkLegal)) stepOpinion.AppendLine($"{ActionFilter.GetMultilingualValue("Common_specialInviteRemark2", true)}：{model.SignatureRemarkLegal}");
                        break;
                }
                flowContractHistory.StepOpinion = stepOpinion.ToString();//特殊加签时签核意见
                dbContext.Create(flowContractHistory);
                var flowStepSigners = dbContext.Query<FlowStepSigner>(new FlowStepSignerQueryCondition() { ApplyNumber = model.ApplyNumber, StepId = flowStepSignerList.FirstOrDefault()?.StepId });
                dbContext.BatchUpdate(flowStepSigners.Select(s => { s.IsInvitee = 1; return s; }).ToArray());
                //加簽者工號
                dbContext.Update(new FormApplication() { ApplyNumber = model.ApplyNumber, Signatory = user.current_emp });
                isSendMail = true;
            });
            //Issue：207 -> 建檔特殊加簽通知
            if (isSendMail)
            {
                SendMailService.SendMail("A", model.ApplyNumber, MvcContext.UserInfo.current_emp, 402, [], new List<MailTypeUtils> { MailTypeUtils.F3 }, inviteSigner);
            }
            ActionFilter.InitLogRecord(log =>
            {
                log.Detail = $"申請單號:{model.ApplyNumber}\r\n" + stepOpinion.ToString();
                log.ControlName = log.ControlName.Replace("SpecialInvite", ActionFilter.GetMultilingualValue("Common_specialInvite", true));
            });
            return result;
        }
        #endregion

        #region 刪除特殊加簽人員
        /// <summary>
        /// 刪除特殊加簽人員
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public static ApiResultModel DeleteSpecialSignature(DeleteSpecialSignatureModel model)
        {
            var user = MvcContext.UserInfo;
            LiteratureApplication literatureApplication = LiteratureApplicationDataService.FindByKey(model.ApplyNumber);//申请单信息
            var currentUser = PsSubEeLglVwADataService.FindByKey(user.current_emp);//操作者
            var flowStepSignerList = DbAccess.Query<FlowStepSigner>(new FlowStepSignerQueryCondition() { ApplyNumber = model.ApplyNumber });//当关签核人
            var flowStepSigner = flowStepSignerList.FirstOrDefault(f => f.SignerEmplid.Equals(currentUser.Emplid));//当前操作者是否是当关者，若不是，则为代理人
            var shouldSigner = currentUser;//应签核人
            if (flowStepSigner == null)//代理人
            {
                var agentList = SysAgentService.GetBeAgentListByEmplid(user.current_emp);
                var list = agentList.Where(w => flowStepSignerList.FirstOrDefault(f => f.SignerEmplid.Equals(w.AgentEmpid)) != null);
                shouldSigner = PsSubEeLglVwADataService.FindByKey(flowStepSignerList.FirstOrDefault()?.SignerEmplid);
            }
            //Issue：208 -> 取消加簽必要參數
            bool isSendMail = false;
            List<MailRecipientResultModel> inviteSigner = new List<MailRecipientResultModel>();
            //合约管理作业历程(簽核人只顯示英文名)
            StringBuilder stringBuilder = new StringBuilder();
            DbAccess.PerformInTransaction(dbContext =>
            {
                var oldDatas = dbContext.Query<FlowStepSignerInvitee>(new FlowStepSignerInviteeQueryCondition()
                {
                    SearchItemGroup = new SearchItemGroup()
                    {
                        Items = new List<SearchItem>()
                        {
                            new SearchItem()
                            {
                                Compare = CompareOperator.ARRAYIN,
                                Values =model.Rowids.Select(s=>s.ToString()).ToArray(),
                                Field = "rowid"
                            }
                        }
                    }
                });
                var handle = oldDatas.Where(w => w.SpInviteLevel == 1);
                var legal = oldDatas.Where(w => w.SpInviteLevel == 2);
                dbContext.BatchDeleteSql<FlowStepSignerInvitee, int?>([.. model.Rowids]);
                
                if (handle.Any())
                {
                    //將刪除的加簽人員添加到特定集合中  Issue：208
                    foreach (FlowStepSignerInvitee dsi in handle)
                    {
                        inviteSigner.Add(new MailRecipientResultModel
                        {
                            recipient_emplid = dsi.InviteeEmplid,
                            recipient_deptid = dsi.InviteeDeptid
                        });
                    }
                    stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("Common_spInvite1", true)}：{string.Join(",", handle.Select(s => dbContext.FindByKey<PsSubEeLglVwA, string>(s.InviteeEmplid)).Select(s => $"{s.NameA}"))}");
                }
                if (legal.Any())
                {
                    //將刪除的加簽人員添加到特定集合中  Issue：208
                    foreach (FlowStepSignerInvitee dsl in legal)
                    {
                        inviteSigner.Add(new MailRecipientResultModel
                        {
                            recipient_emplid = dsl.InviteeEmplid,
                            recipient_deptid = dsl.InviteeDeptid
                        });
                    }
                    stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("Common_spInvite2", true)}：{string.Join(",", legal.Select(s => dbContext.FindByKey<PsSubEeLglVwA, string>(s.InviteeEmplid)).Select(s => $"{s.NameA}"))}");
                }

                FlowContractHistory flowContractHistory = new FlowContractHistory()
                {
                    FlowId = flowId,
                    ApplyNumber = model.ApplyNumber,
                    StepId = flowStepSignerList.FirstOrDefault()?.StepId,
                    ApplySequence = 1,
                    StepAction = 15,
                    ActualSignerEmplid = currentUser.Emplid,
                    ActualSignerName = currentUser.Name,
                    ActualSignerNameA = currentUser.NameA,
                    ActualSignerDeptid = currentUser.Deptid,
                    ShouldSignerEmplid = shouldSigner.Emplid,
                    ShouldSignerName = shouldSigner.Name,
                    ShouldSignerNameA = shouldSigner.NameA,
                    ShouldSignerDeptid = shouldSigner.Deptid,
                    ContractOptions = "取消特殊加簽",
                    CreateTime = DateTime.UtcNow,
                    StepOpinion = stringBuilder.ToString()
                };
                dbContext.Create(flowContractHistory);
                isSendMail = true;
            });
            if (DbAccess.Query<FlowStepSignerInvitee>(new FlowStepSignerInviteeQueryCondition() { ApplyNumber = model.ApplyNumber }).Length == 0)
            {
                var flowStepSigners = DbAccess.Query<FlowStepSigner>(new FlowStepSignerQueryCondition() { ApplyNumber = model.ApplyNumber, StepId = flowStepSignerList.FirstOrDefault()?.StepId, });
                DbAccess.BatchUpdate(flowStepSigners.Select(s => { s.IsInvitee = 0; return s; }).ToArray());
                //加簽者工號
                DbAccess.Update(new FormApplication() { ApplyNumber = model.ApplyNumber, Signatory = "" });
            }
            //Issue：208 -> 建檔取消特殊加簽
            if (isSendMail)
            {
                SendMailService.SendMail("A", model.ApplyNumber, MvcContext.UserInfo.current_emp, 402, [], new List<MailTypeUtils> { MailTypeUtils.F4 }, inviteSigner);
            }
            ActionFilter.InitLogRecord(log =>
            {
                log.Detail = $"申請單號:{model.ApplyNumber}\r\n" + stringBuilder.ToString();
                log.ControlName = log.ControlName.Replace("CancelSpecialInvite", ActionFilter.GetMultilingualValue("Common_cancelSpecialInvite", true));
            });
            return new ApiResultModel() { rtnSuccess = true };
        }
        #endregion

        #region 保存效期確認
        /// <summary>
        /// 保存效期確認
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public static ApiResultModelByObject SaveDate(ContractValidityDate model)
        {
            ApiResultModelByObject result = new ApiResultModelByObject();

            DbAccess.PerformInTransaction(dbContext =>
            {
                // 此方法只用与特殊加签处理，saveData 
                result.listData = FlowStepService.SaveDateToTransaction(dbContext, model, "R", "saveData").ToString();
                result.rtnSuccess = true;
            });

            return result;
        }
        #endregion
    }
}
