﻿using System.Text;
using Elegal.Interface.Api.Common.Control;
using Elegal.Interface.Api.Common.Control.Filter;
using Elegal.Interface.Api.Common.FuncHelper;
using Elegal.Interface.Api.Common.FuncService;
using Elegal.Interface.Api.Common.FuncService.ApplicationPermission;
using Elegal.Interface.Api.Common.Model;
using Elegal.Interface.Api.Common.Model.ParaModel.FlowApi.ApplicationSearch;
using Elegal.Interface.Api.Common.Model.SqlSugarModels;
using Elegal.Interface.Api.Common.Model.ViewModel.FlowApi;
using Elegal.Interface.Api.Common.Model.ViewModel.FlowApi.ApplicationSearch;
using Elegal.Interface.Api.Common.Model.ViewModel.PermissionApi;
using Elegal.Interface.Api.Common.Model.ViewModel.PermissionApi.EmpManage;
using Elegal.Interface.Api.Common.Repository;
using NPOI.SS.UserModel;
using NPOI.Util;
using NPOI.XSSF.UserModel;

namespace Elegal.Flow.Api.Services.ApplicationSearch;

/// <summary>
/// 資料查詢 -> 申請單查詢
/// </summary>
public class ApplicationSearchRefactorService(
    HiddenCodeDictionaryRefactorRepository hiddenCodeDictionaryRefactorRepository,
    SysParametersRefactorService sysParametersRefactorService,
    ApplicationBaseRefactorService applicationBaseRefactorService,
    IHttpContextAccessor httpContextAccessor)
{
    /// <summary>
    /// 申請單簽核關卡匯總
    /// </summary>
    private static readonly List<string> signOffStep = AppSettingHelper.GetValue("signOffStep").Split(",").ToList();


    #region 申請單查詢 - 案件申請查詢

    /// <summary>
    /// 通過SP獲取申請單數據 ISSUE：468
    /// </summary>
    /// <param name="asp"></param>
    /// <param name="empID"></param>
    /// <param name="userRole"></param>
    /// <param name="langType"></param>
    /// <returns></returns>
    public async Task<(int, List<ApplicationSearchViewModel>)> GetApplicationFormInquiry(ApplicationSearchParaModel asp,
        string empID, List<int> userRole, string langType = "ZH-TW")
    {
        var listData = new List<ApplicationSearchViewModel>();
        int totalCount = 0;
        //當無主體數據時，不用查詢數據(UAT NO：145)
        //改為使用前端傳遞的參數 20250625
        if (asp.defaultEntity.Any())
        {
            var listHiddenData = await hiddenCodeDictionaryRefactorRepository.GetHiddenCode();
            //根據主體分別獲取單據主體+角色主體數據 -> Issue：323
            string applyNumberSql = string.Empty;

            #region 拼接排序語句

            string orderBySql = @"ORDER BY fa.apply_number ASC";
            //根據排序欄位拼接排序語句
            if (!string.IsNullOrEmpty(asp.orderField))
            {
                List<string> file = asp.orderField.Split(',').ToList();
                string specialOrder = @"";
                //金額做特殊處理
                if (file.Contains("amount"))
                {
                    specialOrder += string.Format(@" amount_convert {0}", asp.orderType.ToUpper());
                    file.Remove("amount");
                }

                if (file.Contains("exchange_amount")) //換算後台幣無小數點
                {
                    specialOrder += string.Format(@" exchange_amount_convert {0}", asp.orderType.ToUpper());
                    file.Remove("exchange_amount");
                }

                orderBySql = @"ORDER BY " + specialOrder + string.Join(',',
                    file.Select(s => string.Format(@"{0} {1}", s.ToLower().Replace("ult_", "").Replace("_pfn", ""),
                        asp.orderType.ToUpper())));
            }

            #endregion

            #region 拼接分頁語句

            string pageSql = @"OFFSET {0} ROW FETCH NEXT {1} ROW ONLY";
            if (asp.pageCount == 0 && asp.pageSize == 0)
            {
                pageSql = string.Empty;
            }
            else
            {
                pageSql = string.Format(pageSql, asp.startSize, asp.pageSize);
            }

            #endregion

            #region 將user需要顯示拼接欄位進行匯總

            List<string> showField = new List<string>();
            showField.AddRange(asp.otherField);
            showField.AddRange(asp.financeField);

            #endregion

            #region 獲取查詢參數

            object searchPara = new
            {
                langType = langType,
                caseStatus = asp.caseStatus,
                defaultEntity = string.Join(',', asp.defaultEntity.Select(s => s)),
                empID = empID,
                userRole = string.Join(',', userRole.Select(s => s)),
                menuCode = asp.menuCode,
                defaultApplicationState = string.Join(',', asp.defaultApplicationState.Select(s => s)),
                applyType = string.Join(',', asp.applyType.Select(s => s)),
                applyNumber = asp.applyNumber,
                applicationState = string.Join(',', asp.applicationState.Select(s => s)),
                applyTimeStart = asp.applyTimeStart,
                applyTimeEnd = asp.applyTimeEnd,
                entityID = string.Join(',', asp.entityID.Select(s => s)),
                groupEntity = string.Join(',', asp.groupEntity.Select(s => s)),
                otherParty = string.Join(',', asp.otherParty.Select(s => s)), //otherPartySql,
                contractName = asp.contractName,
                empData = string.Join(',', asp.empData.Select(s => s)),
                deptID = string.Join(',', asp.deptID.Select(s => s)),
                contractType = string.Join(',', asp.contractType.Select(s => s)),
                contractObj = string.Join(',', asp.contractObj.Select(s => s)),
                contractNumber = asp.contractNumber,
                hasRelation = asp.hasRelation,
                mainContractNumber = asp.mainContractNumber,
                signType = string.Join(',', asp.signType.Select(s => s)),
                signDateStart = asp.signDateStart,
                signDateEnd = asp.signDateEnd,
                confirmSignDateStart = asp.confirmSignDateStart,
                confirmSignDateEnd = asp.confirmSignDateEnd,
                effType = string.Join(',', asp.effType.Select(s => s)),
                effDateStart = asp.effDateStart,
                effDateEnd = asp.effDateEnd,
                confirmEffDateStart = asp.confirmEffDateStart,
                confirmEffDateEnd = asp.confirmEffDateEnd,
                expType = string.Join(',', asp.expType.Select(s => s)),
                hasExpExtend = asp.hasExpExtend,
                expDateStart = asp.expDateStart,
                expDateEnd = asp.expDateEnd,
                expExtDateStart = asp.expExtDateStart,
                expExtDateEnd = asp.expExtDateEnd,
                confirmExpDateStart = asp.confirmExpDateStart,
                confirmExpDateEnd = asp.confirmExpDateEnd,
                confirmHasExpExtend = asp.confirmHasExpExtend,
                approvedDateStart = asp.approvedDateStart,
                approvedDateEnd = asp.approvedDateEnd,
                originArchiveDateStart = asp.originArchiveDateStart,
                originArchiveDateEnd = asp.originArchiveDateEnd,
                closedDateStart = asp.closedDateStart,
                closedDateEnd = asp.closedDateEnd,
                localContractNumber = asp.localContractNumber,
                othePrartyNumber = asp.othePrartyNumber,
                earlyCeaseDateStrat = asp.earlyCeaseDateStrat,
                earlyCeaseDateEnd = asp.earlyCeaseDateEnd,
                endorsement = string.Join(',', asp.endorsement.Select(s => s)),
                originArchiveType = string.Join(',', asp.originArchiveType.Select(s => s)),
                isHavingMoney = string.Join(',', asp.isHavingMoney.Select(s => s)),
                currency = string.Join(',', asp.currency.Select(s => s)),
                taxesType = string.Join(',', asp.taxesType.Select(s => s)),
                accountType = string.Join(',', asp.accountType.Select(s => s)),
                accountDeptid = string.Join(',', asp.accountDeptid.Select(s => s)),
                codeName = string.Join(',', asp.codeName.Select(s => s)),
                confidenLevel = string.Join(',', asp.confidenLevel.Select(s => s)),
                acknowledgeType = string.Join(',', asp.acknowledgeType.Select(s => s)),
                currentLevels = string.Join(',', asp.currentLevels.Select(s => s)),
                currentSigner = string.Join(',', asp.currentSigner.Select(s => s)),
                signerData = string.Join(',', asp.signerData.Select(s => s)),
                signerDept = string.Join(',', asp.signerDept.Select(s => s)),
                showField = string.Join(',', showField.Select(s => s)),
                orderBySql = orderBySql,
                pageSql = pageSql
            };

            #endregion

            await InsertLog("GetApplicationFormInquiryToSql：" + DateTime.Now);
            //查詢參數
            var result = await SqlSugarHelper.Db.Ado.SqlQueryAsync<ApplicationSearchViewModel>(@"
            EXEC SP_GetApplicationFormInquiry @langType,@caseStatus,@defaultEntity,@empID,@userRole,@menuCode,
            @defaultApplicationState,@applyType,@applyNumber,@applicationState,@applyTimeStart,@applyTimeEnd,
            @entityID,@groupEntity,@otherParty,@contractName,@empData,@deptID,
            @contractType,@contractObj,@contractNumber,@hasRelation,@mainContractNumber,@signType,
            @signDateStart,@signDateEnd,@confirmSignDateStart,@confirmSignDateEnd,@effType,@effDateStart,
            @effDateEnd,@confirmEffDateStart,@confirmEffDateEnd,@expType,@hasExpExtend,@expDateStart,
            @expDateEnd,@expExtDateStart,@expExtDateEnd,@confirmExpDateStart,@confirmExpDateEnd,@confirmHasExpExtend,
            @approvedDateStart,@approvedDateEnd,@originArchiveDateStart,@originArchiveDateEnd,@closedDateStart,@closedDateEnd,
            @localContractNumber,@othePrartyNumber,@earlyCeaseDateStrat,@earlyCeaseDateEnd,@endorsement,@originArchiveType,
            @isHavingMoney,@currency,@taxesType,@accountType,@accountDeptid,@codeName,
            @confidenLevel,@acknowledgeType,@currentLevels,@currentSigner,@signerData,@signerDept,
            @showField,@orderBySql,@pageSql;", searchPara);
            await InsertLog("GetApplicationFormInquiryToHidden：" + DateTime.Now);
            //返回總筆數
            totalCount = listData.Any() ? listData[0].totalCount : 0;

            foreach (var item in result)
            {
                //極機密案件且當前案件狀態不為暫存(T)且不為進行中(I)，需要做隱碼作業
                if (item.confiden_level.ToUpper() == "01".ToUpper() &&
                    item.application_state.ToUpper() != "I".ToUpper() &&
                    item.application_state.ToUpper() != "T".ToUpper())
                {
                    listData.Add(HiddenValueConvertHelper.ConvertToHiddenBySingleV2(item, listHiddenData));
                }
                else
                {
                    listData.Add(item);
                }
            }
        }

        await InsertLog("GetApplicationFormInquiryToReturn：" + DateTime.Now);
        return (totalCount, listData);
    }

    /// <summary>
    /// 匯出
    /// </summary>
    /// <param name="asp">查詢欄位</param>
    /// <param name="empID">當前登陸者工號</param>
    /// <param name="userRole">當前登陸者工號</param>>
    /// <param name="langType">語係</param>
    /// <returns></returns>
    public async Task<byte[]> GetApplicationDataExport(ApplicationExportParaModel asp, string empID, List<int> userRole,
        string langType = "ZH-TW")
    {
        var userInfoModel = AppStaticServices.GetUserInfoFromHttpContext(httpContextAccessor.HttpContext);
        await InsertLog("GetApplicationDataExport_Export_Begin");
        // 小綜合查詢匯出改為使用存儲過程匯出結果  ISSUE：468 by SpringJiang 20250708
        var (total, applicationSearchViewModels) = await GetApplicationFormInquiry(asp, empID, userRole, langType);
        //获取时区，仅仅获取一次，仅仅调用一次当前线程的 （Accessor.HttpContext会从线程池拿到当前请求的httpcontext；不适合放在一个超多数据的循环里）
        var timeZone = userInfoModel.time_zone;
        string yes = ActionFilter.GetMultilingualValue("ApplicationSearch_Export:yes", true); //異動時間
        string no = ActionFilter.GetMultilingualValue("ApplicationSearch_Export:no", true); //異動時間
        string signType1 = ActionFilter.GetMultilingualValue("ApplicationSearch_Export:signType1", true); //異動時間

        await InsertLog("GetApplicationDataExport_seach_end");

        #region sheet

        //创建工作表
        IWorkbook workbook = new XSSFWorkbook();
        ISheet sheet = workbook.CreateSheet("Sheet1");

        //定义一个字体
        IFont font = workbook.CreateFont().configFont(f =>
        {
            f.FontHeightInPoints = 12;
            f.FontName = "Calibri";
        });
        //定义一个样式
        ICellStyle defaultstyle = sheet.Workbook.CreateCellStyle().SetDefaultTitleBorder().AddFont(font).SetwrapTextOn()
            .VerticalCenter();

        //設置表頭
        sheet.SetHeader(asp.ExportColumns.Select(e => e.header).ToList(), style =>
        {
            var font = sheet.Workbook.CreateFont();
            font.IsBold = true; // 设置加粗
            style.AddFont(font).SetDefaultTitleBorder();
        });

        int columnLength = asp.ExportColumns.Count;
        //設置列寬
        sheet.SetColumnAutoWidth(0, columnLength);

        //写入数据
        sheet.WriteData(applicationSearchViewModels, (item, row) =>
        {
            for (int i = 0; i < columnLength; i++)
            {
                var column = asp.ExportColumns[i];

                if (column.field == "currentSigner")
                {
                    var data1 = item.current_signer.EmptyDefault(string.Empty).Split(",")
                        .Where(e => !string.IsNullOrEmpty(e))
                        .Select(e => e + (item.is_invitee == 1 ? signType1 : string.Empty));
                    var data2 = item.current_signer_over.EmptyDefault(string.Empty).EmptyDefault(string.Empty)
                        .Split(",").Where(e => !string.IsNullOrEmpty(e))
                        .Select(e => e + (item.is_invitee == 1 ? signType1 : string.Empty));
                    item["currentSigner", timeZone] = string.Join("\r\n", data1.Concat(data2));
                }

                if (column.field == "hasStampDuty")
                {
                    item["hasStampDuty", timeZone] = item.has_stamp_duty == "1" ? yes :
                        item.has_stamp_duty == "0" ? no : item.has_stamp_duty;
                }

                row.CreateElegalDefaultCell(i, defaultstyle).SetCellValue(item[column.field, timeZone]);
            }
        });

        #endregion

        await InsertLog("GetApplicationDataExport_Export_End");
        return workbook.ToBytes();
    }

    #endregion

    #region 申請單查詢 - 其他申請查詢

    /// <summary>
    /// 根據查詢參數獲取其他申請單集合
    /// </summary>
    /// <param name="asp"></param>
    /// <param name="empID"></param>
    /// <param name="totalCount"></param>
    /// <param name="langType"></param>
    /// <returns></returns>
    public async Task<(int, List<OtherApplicationSearchViewModel>)> GetOtherApplicationData(
        OtherApplicationSearchParaModel asp, string empID, List<int> userRole, string langType = "ZH-TW")
    {
        var listResult = new List<OtherApplicationSearchViewModel>();

        #region 根據查詢結果分組顯示數據

        var fnpEntityRepository = new FnpEntityRepository();
        var publicHelperRepository = new PublicHelperRepository();

        #region sql 语句

        #region SQL語句處理

        //根據查詢條件獲取申請單的臨時表
        //{0}排序字段
        //{1}特殊查詢語句
        //{2}一般查詢條件
        //{3}分頁
        //{4}動態欄位語句
        //{5}動態欄位需要連表查詢SQL語句
        string getViewResultSql = @"
                         --獲取查詢結果 -> 單純的申請單號
                         WITH sort_data AS (
                         SELECT
                         ROW_NUMBER() OVER(ORDER BY {0}) AS sort_id_p,
                         ROW_NUMBER() OVER(PARTITION BY apply_number ORDER BY {0}) AS sort_id_r,
                         oa.apply_number
                         FROM (
                         SELECT oa.apply_number, apply_time, pic_deptid, pic_emplid, fill_emplid, form_type, application_state, legal_emplid, current_levels, current_signer, current_signer_over, is_invitee, dept_type, expose_emplid, expose_other_person, retrieve_apply_number, estimate_apply_number, voided_apply_number, seal_apply_number, entity_id, other_party, contract_number, group_contract_number, history_entity_id, special_entity_id, doc_type, actual_date_start, actual_date_end, closed_date FROM dbo.v_getoapplicaion AS oa
                         {1}
                         WHERE 1 = 1
                         {2}
                         ) AS oa
                         ),
                         sort_data_fina AS (
                         SELECT apply_number
                         FROM sort_data WHERE sort_id_r = 1
                         {3})
                         --根據獲取到的單號數據進行最終顯示結果查詢
                         SELECT DISTINCT
                         --默認顯示欄位
                         goa.apply_number,--申請單號
                         goa.apply_time,--申請日期
                         goa.pic_deptid,--經辦人部門代碼
                         goa.pic_emplid,--經辦人
                         pee.name AS pic_cname,--經辦人中文名稱
                         pee.name_a AS pic_ename,--經辦人中文名稱
                         goa.form_type,--申請類別
                         CONCAT(
                         (SELECT fun_name FROM dbo.sys_parameters WHERE lang_type = @langType AND para_code = N'applicationType' AND func_code = N'O'),
                         N' - ',
                         N'(',goa.form_type,N')',
                         (SELECT fun_name FROM dbo.sys_parameters WHERE lang_type = @langType AND para_code = N'formType_O' AND func_code = CONVERT(NVARCHAR,goa.form_type))
                         ) AS form_type_pfn,--申請類別名稱
                         goa.application_state,--案件狀態
                         (SELECT fun_name FROM dbo.sys_parameters WHERE lang_type = @langType AND para_code = N'applicationState' AND func_code = CONVERT(NVARCHAR,goa.application_state)) AS application_state_pfn,--案件狀態名稱
                         goa.legal_emplid,--承辦法務人員工號
                         lee.name AS legal_cname,--承辦法務人員中文名稱
                         lee.name_a AS legal_ename,--承辦法務人員中文名稱
                         goa.current_levels,--目前關卡
                         (SELECT fun_name FROM dbo.sys_parameters AS sp WHERE lang_type = @langType AND EXISTS(SELECT 1 FROM (SELECT value AS para_code FROM STRING_SPLIT(@signOffStep, ',')) AS fpc WHERE fpc.para_code = sp.para_code) AND func_code = CONVERT(NVARCHAR,goa.current_levels)) AS current_levels_pfn,--目前關卡名稱
                         goa.current_signer,goa.current_signer_over,goa.is_invitee--目前待審人員
                         {4}
                         FROM (SELECT goa.apply_number, apply_time, pic_deptid, pic_emplid, fill_emplid, form_type, application_state, legal_emplid, current_levels, current_signer, current_signer_over, is_invitee, dept_type, expose_emplid, expose_other_person, retrieve_apply_number, estimate_apply_number, voided_apply_number, seal_apply_number, entity_id, other_party, contract_number, history_entity_id, special_entity_id, doc_type, actual_date_start, actual_date_end, closed_date FROM dbo.v_getoapplicaion AS goa
                         INNER JOIN sort_data_fina AS tempData ON tempData.apply_number = goa.apply_number
                         ) AS goa
                         --經辦人
                         LEFT JOIN (SELECT emplid,name,name_a FROM dbo.ps_sub_ee_lgl_vw_a) AS pee ON pee.emplid = goa.pic_emplid
                         --承辦法務人員
                         LEFT JOIN (SELECT emplid,name,name_a FROM dbo.ps_sub_ee_lgl_vw_a) AS lee ON lee.emplid = goa.legal_emplid
                         {5}
                         ORDER BY {0};";
        //獲取總筆數SQL語句
        string getTotlaCountSql = @"
                         SELECT COUNT(goa.apply_number)
                         FROM (SELECT DISTINCT oa.apply_number FROM dbo.v_getoapplicaion AS oa
                         {1}
                         WHERE 1 = 1
                         --查詢條件
                         {0}
                         ) AS goa;";
        //默認排序
        string defaultOrderBy = @"apply_number ASC";
        //分頁字段
        string pageSql = (asp.pageCount != 0 && asp.pageSize != 0)
            ? string.Format(@"ORDER BY sort_id_p ASC OFFSET {0} ROWS FETCH NEXT {1} ROWS ONLY", asp.startSize,
                asp.pageSize)
            : string.Empty;
        //獲取查詢條件
        StringBuilder getAppParaSql = new StringBuilder();
        //動態欄位語句
        StringBuilder getDynamicField = new StringBuilder();
        //動態欄位需要連表查詢SQL語句
        StringBuilder getDynamicTable = new StringBuilder();
        //特殊查詢欄位
        StringBuilder getAppSpecialParaSql = new StringBuilder();

        #endregion

        #region 拼接查詢條件

        #region 當前登陸者申請過，簽核過的單據

        if (!string.IsNullOrEmpty(empID))
        {
            //Issue：323 查詢當前登錄人做過處理的單據改為使用視圖
            getAppSpecialParaSql.AppendLine(
                @"INNER JOIN (SELECT DISTINCT apply_number FROM dbo.V_GetApplicaionEntityRole WHERE emplid = @empID) AS ap ON ap.apply_number = oa.apply_number");
        }

        #endregion

        #region 一般查詢條件

        #region 申請單號：不區分大小寫，模糊查詢

        if (!string.IsNullOrEmpty(asp.applyNumber))
        {
            getAppParaSql.AppendLine(@"AND oa.apply_number LIKE CONCAT(N'%', @applyNumber, N'%')");
        }

        #endregion

        #region 申請日期：區間查詢，拆開成起始日期和截止日期查詢

        //開始
        if (asp.applyTimeStart.HasValue)
        {
            getAppParaSql.AppendLine(@"AND oa.apply_time >= @applyTimeStart");
        }

        //結束
        if (asp.applyTimeEnd.HasValue)
        {
            getAppParaSql.AppendLine(@"AND oa.apply_time <= @applyTimeEnd");
        }

        #endregion

        #region 申請類別：(A)案件調閱，(B)案件清單調閱，(C)作廢，(D)重新用印，(E)檢視歷史單權限，(F)特殊主體申請，。默認全選

        if (asp.formType.Any())
        {
            getAppParaSql.AppendLine(
                @"AND EXISTS(SELECT 1 FROM (SELECT value AS form_type FROM STRING_SPLIT(@formType, ',')) AS ft WHERE ft.form_type = oa.form_type)");
        }

        #endregion

        #region 申請人/經辦人：所有在職+離職員工，可通過工號中英文名模糊查詢申請人、經辦人。可多選。

        if (asp.empData.Any())
        {
            getAppParaSql.AppendLine(@"AND (
                 EXISTS(SELECT 1 FROM (SELECT value AS pic_emplid FROM STRING_SPLIT(@empData, ',')) AS pice WHERE pice.pic_emplid = oa.pic_emplid)
                 OR
                 EXISTS(SELECT 1 FROM (SELECT value AS fill_emplid FROM STRING_SPLIT(@empData, ',')) AS fille WHERE fille.fill_emplid = oa.fill_emplid)
                 )");
        }

        #endregion

        #region 經辦人部門：所有部門。可通過部門編碼模糊查詢。可多選

        if (asp.deptID.Any())
        {
            getAppParaSql.AppendLine(
                @"AND EXISTS(SELECT 1 FROM (SELECT value AS pic_deptid FROM STRING_SPLIT(@deptID, ',')) AS pd WHERE pd.pic_deptid = oa.pic_deptid)");
        }

        #endregion

        #region 必要揭露人員名單(ABEF)

        string getExposPersonSql = @"";
        //內部人員
        if (asp.exposeEmplid.Any())
        {
            getExposPersonSql =
                @"EXISTS(SELECT 1 FROM (SELECT value AS expose_emplid FROM STRING_SPLIT(@exposeEmplid, ',')) AS oaee WHERE oaee.expose_emplid = oa.expose_emplid)";
        }

        //其他人員
        if (!string.IsNullOrEmpty(asp.exposeOtherEmplid))
        {
            getExposPersonSql += string.Format(
                @" {0} oa.expose_other_person LIKE CONCAT(N'%', @exposeOtherEmplid, N'%')",
                !string.IsNullOrEmpty(getExposPersonSql) ? "OR" : "");
        }

        //當不為空時，需要進行查詢
        if (!string.IsNullOrEmpty(getExposPersonSql))
        {
            getAppParaSql.AppendFormat(@"AND ({0})", getExposPersonSql).AppendLine();
            //需要限定特殊類型
            getAppParaSql.AppendLine(
                @"AND EXISTS(SELECT 1 FROM (SELECT value AS form_type FROM STRING_SPLIT(N'A,B,E,F', ',')) AS ft WHERE ft.form_type = oa.form_type)");
        }

        #endregion

        #region 承辦法務(ABCD)：所有在職+離職員工，可通過工號中英文名模糊查詢，可多選

        if (asp.legalAdmin.Any())
        {
            getAppParaSql.AppendLine(
                @"AND EXISTS(SELECT 1 FROM (SELECT value AS legal_emplid FROM STRING_SPLIT(@legalAdmin, ',')) AS le WHERE le.legal_emplid = oa.legal_emplid)");
            //需要限定特殊類型
            getAppParaSql.AppendLine(
                @"AND EXISTS(SELECT 1 FROM (SELECT value AS form_type FROM STRING_SPLIT(N'A,B,C,D', ',')) AS ft WHERE ft.form_type = oa.form_type)");
        }

        #endregion

        #region 實際開放期間(ABEF)

        #region 是否要查詢在有效期內的數據

        if (asp.isValidityPeriod == 1)
        {
            getAppParaSql.AppendLine(
                @"AND CONVERT(VARCHAR(100),getutcdate(),23) >= CONVERT(VARCHAR(100),oa.actual_date_start,23) AND CONVERT(VARCHAR(100),getutcdate(),23) <= CONVERT(VARCHAR(100),oa.actual_date_end,23)");
        }

        #endregion

        #region 開始區間

        //開始
        if (asp.actualDateStartBegin.HasValue)
        {
            getAppParaSql.AppendLine(@"AND oa.actual_date_start >= @actualDateStartBegin");
        }

        //結束
        if (asp.actualDateStartDone.HasValue)
        {
            getAppParaSql.AppendLine(@"AND oa.actual_date_start <= @actualDateStartDone");
        }

        #endregion

        #region 結束區間

        //開始
        if (asp.actualDateEndBegin.HasValue)
        {
            getAppParaSql.AppendLine(@"AND oa.actual_date_end >= @actualDateEndBegin");
        }

        //結束
        if (asp.actualDateEndDone.HasValue)
        {
            getAppParaSql.AppendLine(@"AND oa.actual_date_end <= @actualDateEndDone");
        }

        #endregion

        if (asp.actualDateStartBegin.HasValue || asp.actualDateStartDone.HasValue
                                              || asp.actualDateEndBegin.HasValue || asp.actualDateEndDone.HasValue)
        {
            //需要限定特殊類型
            getAppParaSql.AppendLine(
                @"AND EXISTS(SELECT 1 FROM (SELECT value AS form_type FROM STRING_SPLIT(N'A,B,E,F', ',')) AS ft WHERE ft.form_type = oa.form_type)");
        }

        #endregion

        #region 調閱/作廢/重新用印單號(ACD)：不區分大小寫，模糊查詢

        if (!string.IsNullOrEmpty(asp.retrieveApplyNumber))
        {
            getAppParaSql.AppendLine(@"AND (
                 oa.retrieve_apply_number LIKE CONCAT(N'%', @retrieveApplyNumber, N'%')
                 OR oa.estimate_apply_number LIKE CONCAT(N'%', @retrieveApplyNumber, N'%')
                 OR oa.voided_apply_number LIKE CONCAT(N'%', @retrieveApplyNumber, N'%')
                 OR oa.seal_apply_number LIKE CONCAT(N'%', @retrieveApplyNumber, N'%')
                 )");
            //需要限定特殊類型
            getAppParaSql.AppendLine(
                @"AND EXISTS(SELECT 1 FROM (SELECT value AS form_type FROM STRING_SPLIT(N'A,C,D', ',')) AS ft WHERE ft.form_type = oa.form_type)");
        }

        #endregion

        #region 調閱類型(A)：電子檔、正本紙本。可多選

        if (asp.docType.Any())
        {
            getAppParaSql.AppendLine(
                @"AND EXISTS(SELECT 1 FROM (SELECT value AS doc_type FROM STRING_SPLIT(@docType, ',')) AS dt WHERE dt.doc_type = oa.doc_type)");
        }

        #endregion

        #region 我方(ACD)：下拉選擇，可按主體簡稱，中英文名稱查詢選擇。可多選。可選主體的範圍同案件查詢

        if (asp.entityID.Any())
        {
            getAppParaSql.AppendLine(
                @"AND EXISTS(SELECT 1 FROM (SELECT value AS entity_id FROM STRING_SPLIT(@entityID, ',')) AS ei WHERE ei.entity_id = oa.entity_id)");
            //需要限定特殊類型
            getAppParaSql.AppendLine(
                @"AND EXISTS(SELECT 1 FROM (SELECT value AS form_type FROM STRING_SPLIT(N'A,C,D', ',')) AS ft WHERE ft.form_type = oa.form_type)");
        }

        #endregion

        #region 他方(ACD)：關鍵字模糊搜尋，不區分大小寫，關鍵字之間用空格分隔，雙引號內的作為完整的一個關鍵字

        List<string> listOtherPrartyApplyNumber = new List<string>();
        if (asp.otherParty.Any())
        {
            var (otherSql, otherPara) = publicHelperRepository.GetOtherPartSql(asp.otherParty);
            listOtherPrartyApplyNumber =
                await SqlSugarHelper.Db.Ado.SqlQueryAsync<string>(
                    $"SELECT DISTINCT apply_number FROM ({otherSql}) AS oap", otherPara);
            getAppParaSql.AppendLine(@"AND (
                EXISTS(SELECT 1 FROM (SELECT value AS apply_number FROM STRING_SPLIT(@otherPrartyApplyNumber, ',')) AS oan WHERE oan.apply_number = oa.retrieve_apply_number)
                OR EXISTS(SELECT 1 FROM (SELECT value AS apply_number FROM STRING_SPLIT(@otherPrartyApplyNumber, ',')) AS oan WHERE oan.apply_number = oa.estimate_apply_number)
                OR EXISTS(SELECT 1 FROM (SELECT value AS apply_number FROM STRING_SPLIT(@otherPrartyApplyNumber, ',')) AS oan WHERE oan.apply_number = oa.voided_apply_number)
                OR EXISTS(SELECT 1 FROM (SELECT value AS apply_number FROM STRING_SPLIT(@otherPrartyApplyNumber, ',')) AS oan WHERE oan.apply_number = oa.seal_apply_number)
                )");

            //需要限定特殊類型
            getAppParaSql.AppendLine(
                @"AND EXISTS(SELECT 1 FROM (SELECT value AS form_type FROM STRING_SPLIT(N'A,C,D', ',')) AS ft WHERE ft.form_type = oa.form_type)");
        }

        #endregion

        #region 合約編號(ACD)

        //不區分大小寫，模糊查詢；勾選項「關聯合約查詢」，若勾選擇將「合約編號」欄位變更為下拉查詢，單選
        if (!string.IsNullOrEmpty(asp.contractNumber))
        {
            if (asp.hasRelation == 1)
            {
                getAppParaSql.AppendLine(@"AND oa.group_contract_number = @contractNumber");
            }
            else
            {
                getAppParaSql.AppendLine(@"AND oa.contract_number LIKE CONCAT(N'%', @contractNumber, N'%')");
            }

            //需要限定特殊類型
            getAppParaSql.AppendLine(
                @"AND EXISTS(SELECT 1 FROM (SELECT value AS form_type FROM STRING_SPLIT(N'A,C,D', ',')) AS ft WHERE ft.form_type = oa.form_type)");
        }

        #endregion

        #region 檢視/特殊主體權限(EF)：列出所有E類申請單主體，可多選

        if (asp.viewEntityID.Any())
        {
            getAppParaSql.AppendLine(@"AND (
                 EXISTS(SELECT 1 FROM (SELECT value AS history_entity_id FROM STRING_SPLIT(@viewEntityID, ',')) AS hei WHERE hei.history_entity_id = oa.history_entity_id)
                 OR 
                 EXISTS(SELECT 1 FROM (SELECT value AS special_entity_id FROM STRING_SPLIT(@viewEntityID, ',')) AS sei WHERE sei.special_entity_id = oa.special_entity_id)
                 )");
            //需要限定特殊類型
            getAppParaSql.AppendLine(
                @"AND EXISTS(SELECT 1 FROM (SELECT value AS form_type FROM STRING_SPLIT(N'E,F', ',')) AS ft WHERE ft.form_type = oa.form_type)");
        }

        #endregion

        #region 案件狀態：簽核中、結案、作廢。可多選

        if (asp.applicationState.Any())
        {
            getAppParaSql.AppendLine(
                @"AND EXISTS (SELECT 1 FROM (SELECT value AS application_state FROM STRING_SPLIT(@applicationState, ',')) AS ass WHERE ass.application_state = oa.application_state)");
        }
        else
        {
            getAppParaSql.AppendLine(
                @"AND EXISTS (SELECT 1 FROM (SELECT value AS application_state FROM STRING_SPLIT(@defaultApplicationState, ',')) AS ass WHERE ass.application_state = oa.application_state)");
        }

        #endregion

        #endregion

        #region 進階查詢條件

        #region 結案日：區間查詢，拆開成起始日期和截止日期查詢

        //開始
        if (asp.closedDateStart.HasValue)
        {
            getAppParaSql.AppendLine(@"AND oa.closed_date >= @closedDateStart");
        }

        //結束
        if (asp.closedDateEnd.HasValue)
        {
            getAppParaSql.AppendLine(@"AND oa.closed_date <= @closedDateEnd");
        }

        #endregion

        #region 目前關卡：下拉可多選。顯示所有申請單關卡並集

        if (asp.currentLevels.Any())
        {
            getAppParaSql.AppendLine(
                @"AND EXISTS(SELECT 1 FROM (SELECT value AS current_levels FROM STRING_SPLIT(@currentLevels, ',')) AS cl WHERE cl.current_levels = oa.current_levels)");
        }

        #endregion

        #region 目前待審人員：可通過工號中英文名模糊查詢員工，可多選

        if (asp.currentSigner.Any())
        {
            getAppSpecialParaSql.AppendLine(@"
                         INNER JOIN (
                         SELECT DISTINCT apply_number FROM (
                         --查詢當前關卡簽核者時，需要排除被加簽過的申請單號
                         SELECT apply_number,signer_emplid FROM dbo.flow_step_signer WHERE is_invitee = 0
                         UNION
                         SELECT apply_number,invitee_emplid AS signer_emplid FROM dbo.flow_step_signer_invitee
                         ) AS sid
                         WHERE EXISTS(SELECT 1 FROM (SELECT value AS signer_emplid FROM STRING_SPLIT(@currentSigner, ',')) AS cs WHERE cs.signer_emplid = sid.signer_emplid)
                         ) AS cs ON cs.apply_number = oa.apply_number");
        }

        #endregion

        #region 簽核人：可通過工號中英文名模糊查詢員工，可多選

        if (asp.signerData.Any())
        {
            //eidt by SpringJiang 20250620 -> 添加 flow_step_history_from_wilegal 查詢範圍
            getAppSpecialParaSql.AppendLine(@"
                         INNER JOIN (
                         SELECT DISTINCT fss.apply_number FROM (
                         SELECT actual_signer_emplid AS signer_emplid,apply_number FROM dbo.flow_step_history
                         UNION ALL
                         SELECT signer_emplid,apply_number FROM dbo.flow_step_signer
                         UNION ALL
                         SELECT actual_signer_emplid AS signer_emplid,apply_number FROM dbo.flow_step_history_from_wilegal
                         ) AS fss
                         WHERE EXISTS(SELECT 1 FROM (SELECT value AS signer_emplid FROM STRING_SPLIT(@signerData, ',')) AS se WHERE se.signer_emplid = fss.signer_emplid)
                         ) AS ss ON ss.apply_number = oa.apply_number");
        }

        #endregion

        #region 簽核人部門：所有部門。可通過部門編碼模糊查詢。可多選

        if (asp.signerDept.Any())
        {
            //eidt by SpringJiang 20250620 -> 添加 flow_step_history_from_wilegal 查詢範圍
            getAppSpecialParaSql.AppendLine(@"
                         INNER JOIN (
                         SELECT DISTINCT fss.apply_number FROM (
                         SELECT actual_signer_deptid AS signer_deptid,apply_number FROM dbo.flow_step_history
                         UNION ALL
                         SELECT signer_deptid,apply_number FROM dbo.flow_step_signer
                         UNION ALL
                         SELECT actual_signer_deptid AS signer_deptid,apply_number FROM dbo.flow_step_history_from_wilegal
                         ) AS fss WHERE EXISTS(SELECT 1 FROM (SELECT value AS signer_deptid FROM STRING_SPLIT(@signerDept, ',')) AS sd WHERE sd.signer_deptid = fss.signer_deptid)
                         ) AS sd ON sd.apply_number = oa.apply_number");
        }

        #endregion

        #endregion

        #endregion

        #region 拼接其他欄位

        foreach (string of in asp.otherField)
        {
            if (of.ToLower() != "actual_date" && of.ToLower() != "expose_personnel")
            {
                getDynamicField.AppendFormat(@",goa.{0}", of.ToLower().Replace("ult_", "").Replace("_pfn", ""))
                    .AppendLine();
            }

            switch (of.ToLower())
            {
                case "dept_type_pfn": //申請類型
                    getDynamicField
                        .AppendFormat(
                            @",(SELECT fun_name FROM dbo.sys_parameters WHERE lang_type = @langType AND para_code = N'otherDeptType' AND func_code = CONVERT(NVARCHAR,goa.dept_type)) AS {0}",
                            of.ToLower()).AppendLine();
                    break;
                case "retrieve_apply_number": //調閱單號
                    getDynamicField.AppendLine(@",goa.estimate_apply_number");
                    break;
                case "expose_personnel": //必要揭露人員名單(內部同仁/其他人員)
                    getDynamicField.AppendLine(
                        @",goa.expose_emplid,ee.name AS expose_cname,ee.name_a AS expose_ename,goa.expose_other_person");
                    getDynamicTable.AppendLine(
                        @"LEFT JOIN (SELECT emplid,name,name_a FROM dbo.ps_sub_ee_lgl_vw_a) AS ee ON ee.emplid = goa.expose_emplid");
                    break;
                case "entity_id": //我方主體
                    getDynamicField.AppendLine(@",mfe.entity,mfe.entity_namec,mfe.entity_namee");
                    getDynamicTable.AppendFormat(@"LEFT JOIN ({0}) AS mfe ON mfe.entity_id = goa.entity_id",
                        fnpEntityRepository.GetAllEntity()).AppendLine();
                    break;
                case "history_entity_id": //申請主體
                    getDynamicField.AppendLine(
                        @",hfe.entity AS h_entity,hfe.entity_namec AS h_entity_namec,hfe.entity_namee AS h_entity_namee");
                    getDynamicTable.AppendFormat(@"LEFT JOIN ({0}) AS hfe ON hfe.entity_id = goa.history_entity_id",
                        fnpEntityRepository.GetAllEntity()).AppendLine();
                    break;
                case "special_entity_id": //特殊主體權限
                    getDynamicField.AppendLine(
                        @",sfe.entity AS s_entity,sfe.entity_namec AS s_entity_namec,sfe.entity_namee AS s_entity_namee");
                    getDynamicTable.AppendFormat(@"LEFT JOIN ({0}) AS sfe ON sfe.entity_id = goa.special_entity_id",
                        fnpEntityRepository.GetAllEntity()).AppendLine();
                    break;
                case "doc_type_pfn": //調閱類型
                    getDynamicField
                        .AppendFormat(
                            @",(SELECT fun_name FROM dbo.sys_parameters WHERE lang_type = @langType AND para_code = N'otherDocType' AND func_code = CONVERT(NVARCHAR,goa.doc_type)) AS {0}",
                            of.ToLower()).AppendLine();
                    break;
                case "actual_date": //實際開放期間
                    getDynamicField.AppendLine(@",goa.actual_date_start,goa.actual_date_end");
                    break;
            }
        }

        #endregion

        #region 拼接排序語句

        //根據排序欄位拼接排序語句
        if (!string.IsNullOrEmpty(asp.orderField))
        {
            List<string> file = asp.orderField.Split(',').ToList();
            defaultOrderBy = string.Join(',',
                file.Select(s =>
                    string.Format(@"{0} {1}", s.ToLower().Replace("ult_", "").Replace("_pfn", ""), asp.orderType)));
        }

        #endregion


        #region 拼接查詢參數

        object searchPara = new
        {
            empID = empID,
            defaultApplicationState = string.Join(',', asp.defaultApplicationState.Select(s => s)),
            applyNumber = asp.applyNumber,
            applyTimeStart = asp.applyTimeStart,
            applyTimeEnd = asp.applyTimeEnd,
            formType = string.Join(',', asp.formType.Select(s => s)),
            empData = string.Join(',', asp.empData.Select(s => s)),
            deptID = string.Join(',', asp.deptID.Select(s => s)),
            legalAdmin = string.Join(',', asp.legalAdmin.Select(s => s)),
            exposeEmplid = string.Join(',', asp.exposeEmplid.Select(s => s)),
            exposeOtherEmplid = asp.exposeOtherEmplid,
            actualDateStartBegin = asp.actualDateStartBegin,
            actualDateStartDone = asp.actualDateStartDone,
            actualDateEndBegin = asp.actualDateEndBegin,
            actualDateEndDone = asp.actualDateEndDone,
            retrieveApplyNumber = asp.retrieveApplyNumber,
            docType = string.Join(',', asp.docType.Select(s => s)),
            entityID = string.Join(',', asp.entityID.Select(s => s)),
            otherPrartyApplyNumber = string.Join(',', listOtherPrartyApplyNumber.Select(s => s)),
            contractNumber = asp.contractNumber,
            viewEntityID = string.Join(',', asp.viewEntityID.Select(s => s)),
            applicationState = string.Join(',', asp.applicationState.Select(s => s)),
            closedDateStart = asp.closedDateStart,
            closedDateEnd = asp.closedDateEnd,
            currentLevels = string.Join(',', asp.currentLevels.Select(s => s)),
            currentSigner = string.Join(',', asp.currentSigner.Select(s => s)),
            signerData = string.Join(',', asp.signerData.Select(s => s)),
            signerDept = string.Join(',', asp.signerDept.Select(s => s)),
            langType = langType,
            signOffStep = string.Join(',', signOffStep.Select(s => s))
        };

        #endregion

        #region 拼接終版SQL語句

        //數據查詢
        string getFinalParaSql = string.Format(getViewResultSql,
            defaultOrderBy, //排序字段
            getAppSpecialParaSql.ToString(), //特殊查詢語句
            getAppParaSql.ToString(), //查詢條件
            pageSql, //分頁字段
            getDynamicField.ToString(), //動態欄位語句
            getDynamicTable.ToString() //動態欄位需要連表查詢SQL語句
        );
        //總筆數查詢
        string getFinalTotalCountSql = string.Format(getTotlaCountSql,
            getAppParaSql.ToString(), //查詢條件
            getAppSpecialParaSql.ToString() //特殊查詢語句
        );

        #endregion

        #endregion

        SqlSugarHelper.Db.Ado.IsClearParameters = false;
        var searchParaCount = searchPara.Copy();
        var totalCount = await SqlSugarHelper.Db.Ado.SqlQuerySingleAsync<int>(getFinalTotalCountSql, searchParaCount);
        var listData =
            await SqlSugarHelper.Db.Ado.SqlQueryAsync<OtherApplicationSearchResultModel>(getFinalParaSql,
                searchPara);

        if (listData.Any())
        {
            //Issue：215 其他申請單顯示界面也需要隱碼
            listData = await hiddenCodeDictionaryRefactorRepository.ConvertToHiddenByList<OtherApplicationSearchResultModel>(listData, "od");
            OtherApplicationSearchViewModel oasv = new OtherApplicationSearchViewModel();
            //根據申請單號將集合分組
            var groupApplication = listData.GroupBy(ga => ga.apply_number);
            foreach (var ga in groupApplication)
            {
                oasv = new OtherApplicationSearchViewModel();
                List<OtherApplicationSearchResultModel> listGroupData =
                    listData.Where(lga => lga.apply_number.ToLower() == ga.Key.ToLower()).ToList();
                if (listGroupData.Any())
                {
                    #region 默認欄位

                    oasv.apply_number = listGroupData[0].apply_number;
                    oasv.apply_time = listGroupData[0].apply_time;
                    oasv.pic_deptid = listGroupData[0].pic_deptid;
                    oasv.pic_emplid = listGroupData[0].pic_emplid;
                    oasv.pic_cname = listGroupData[0].pic_cname;
                    oasv.pic_ename = listGroupData[0].pic_ename;
                    oasv.form_type = listGroupData[0].form_type;
                    oasv.form_type_pfn = listGroupData[0].form_type_pfn;
                    oasv.application_state = listGroupData[0].application_state;
                    oasv.application_state_pfn = listGroupData[0].application_state_pfn;

                    #region 循環承辦法務人員

                    foreach (OtherApplicationSearchResultModel oasvm in listGroupData)
                    {
                        if (!string.IsNullOrEmpty(oasvm.legal_emplid))
                        {
                            oasv.legal_emplid.Add(new EmpManagementModel
                            {
                                name_a = oasvm.legal_ename,
                                name = oasvm.legal_cname,
                                emplid = oasvm.legal_emplid
                            });
                        }
                    }

                    //去除重複數據
                    oasv.legal_emplid =
                        new List<EmpManagementModel>(new HashSet<EmpManagementModel>(oasv.legal_emplid));

                    #endregion

                    oasv.current_levels = listGroupData[0].current_levels;
                    oasv.current_levels_pfn = listGroupData[0].current_levels_pfn;
                    oasv.current_signer = listGroupData[0].current_signer;
                    oasv.current_signer_over = listGroupData[0].current_signer_over;
                    oasv.is_invitee = listGroupData[0].is_invitee;

                    #endregion

                    #region 其他欄位

                    oasv.dept_type = listGroupData[0].dept_type;
                    oasv.dept_type_pfn = listGroupData[0].dept_type_pfn;

                    #region 循環必要揭露人員(內部同仁)

                    foreach (OtherApplicationSearchResultModel oasvm in listGroupData)
                    {
                        if (!string.IsNullOrEmpty(oasvm.expose_emplid))
                        {
                            oasv.expose_emplid.Add(new EmpManagementModel
                            {
                                name_a = oasvm.expose_ename,
                                name = oasvm.expose_cname,
                                emplid = oasvm.expose_emplid
                            });
                        }
                    }

                    //去除重複數據
                    oasv.expose_emplid =
                        new List<EmpManagementModel>(new HashSet<EmpManagementModel>(oasv.expose_emplid));

                    #endregion

                    oasv.expose_other_person = string.Join(", ",
                        listGroupData.Where(s => !string.IsNullOrEmpty(s.expose_other_person))
                            .Select(eo => eo.expose_other_person).Distinct().ToList());
                    oasv.retrieve_apply_number = string.Join(", ",
                        listGroupData.Where(s => !string.IsNullOrEmpty(s.retrieve_apply_number))
                            .Select(eo => eo.retrieve_apply_number).Distinct().ToList());
                    oasv.estimate_apply_number = string.Join(", ",
                        listGroupData.Where(s => !string.IsNullOrEmpty(s.estimate_apply_number))
                            .Select(eo => eo.estimate_apply_number).Distinct().ToList());
                    oasv.voided_apply_number = listGroupData[0].voided_apply_number;
                    oasv.seal_apply_number = listGroupData[0].seal_apply_number;

                    #region 我方主體

                    foreach (OtherApplicationSearchResultModel fem in listGroupData)
                    {
                        if (!string.IsNullOrEmpty(fem.entity_id))
                        {
                            oasv.entity_id.Add(new FnpEntityModel
                            {
                                EntityId = fem.entity_id,
                                EntityNamec = fem.entity_namec,
                                EntityNamee = fem.entity_namee,
                                Entity = fem.entity
                            });
                        }
                    }

                    //去除重複數據
                    oasv.entity_id = new List<FnpEntityModel>(new HashSet<FnpEntityModel>(oasv.entity_id));

                    #endregion

                    #region 他方

                    foreach (OtherApplicationSearchResultModel op in listGroupData)
                    {
                        if (!string.IsNullOrEmpty(op.other_party))
                        {
                            oasv.other_party.Add(op.other_party);
                        }
                    }

                    //去除重複數據
                    oasv.other_party = oasv.other_party.Distinct().ToList();

                    #endregion

                    #region 合約編號

                    foreach (OtherApplicationSearchResultModel op in listGroupData)
                    {
                        if (!string.IsNullOrEmpty(op.contract_number))
                        {
                            oasv.contract_number.Add(op.contract_number);
                        }
                    }

                    //去除重複數據
                    oasv.contract_number = oasv.contract_number.Distinct().ToList();

                    #endregion

                    #region 申請主體

                    foreach (OtherApplicationSearchResultModel fem in listGroupData)
                    {
                        if (!string.IsNullOrEmpty(fem.history_entity_id))
                        {
                            oasv.history_entity_id.Add(new FnpEntityModel
                            {
                                EntityId = fem.history_entity_id,
                                EntityNamec = fem.h_entity_namec,
                                EntityNamee = fem.h_entity_namee,
                                Entity = fem.h_entity
                            });
                        }
                    }

                    //去除重複數據
                    oasv.history_entity_id =
                        new List<FnpEntityModel>(new HashSet<FnpEntityModel>(oasv.history_entity_id));

                    #endregion

                    #region 特殊主體權限

                    foreach (OtherApplicationSearchResultModel fem in listGroupData)
                    {
                        if (!string.IsNullOrEmpty(fem.special_entity_id))
                        {
                            oasv.special_entity_id.Add(new FnpEntityModel
                            {
                                EntityId = fem.special_entity_id,
                                EntityNamec = fem.s_entity_namec,
                                EntityNamee = fem.s_entity_namee,
                                Entity = fem.s_entity
                            });
                        }
                    }

                    //去除重複數據
                    oasv.special_entity_id =
                        new List<FnpEntityModel>(new HashSet<FnpEntityModel>(oasv.special_entity_id));

                    #endregion

                    oasv.doc_type = listGroupData[0].doc_type;
                    oasv.doc_type_pfn = listGroupData[0].doc_type_pfn;
                    oasv.actual_date_start = listGroupData[0].actual_date_start;
                    oasv.actual_date_end = listGroupData[0].actual_date_end;
                    oasv.closed_date = listGroupData[0].closed_date;

                    #endregion
                }

                listResult.Add(oasv);
            }
        }

        #endregion

        return (totalCount, listResult);
    }

    #endregion


    #region 獲取目前關卡，關卡並集(合約申請，資料建檔，關企建檔)

    /// <summary>
    ///  獲取目前關卡，關卡並集(合約申請，資料建檔，關企建檔)
    /// </summary>
    /// <param name="langType">語係</param>
    /// <returns></returns>
    public async Task<List<DropDownListModel>> GetFlowStepByParaCode(string langType = "ZH-TW")
    {
        return await sysParametersRefactorService.GetFlowStepByParaCode(
            new List<string>
                { "generalContractStep", "capitalContractStep", "hrContractStep", "rdContractStep", "geContractStep" },
            langType);
    }

    #endregion


    #region 根據案件狀態查詢不同的主體

    /// <summary>
    /// 根據案件狀態查詢不同的主體
    /// </summary>
    /// <param name="caseStatus">案件狀態</param>
    /// <param name="menuCode">菜單欄</param>
    /// <param name="empID">當前登陸者工號</param>
    /// <param name="userRole">當前登陸者的角色信息</param>
    /// <returns></returns>
    public async Task<List<FnpEntityModel>> GetFnpEntityByCaseStatus(int caseStatus, int menuCode, string empID,
        List<int> userRole)
    {
        var listEntity = new List<FnpEntityModel>();
        // 根據人員工號獲取當前人員的單據角色
        var applyRole = (await applicationBaseRefactorService.GetFixedRolesByEmplID(empID, string.Empty))
            .Where(s => s < 0).ToList();
        await InsertLog("根據人員工號獲取當前人員的單據角色");

        //根據角色+菜單欄信息驗證角色是否存在全部主體類型
        userRole.AddRange(applyRole);

        var contractViews = new List<ContractViewModel>();

        #region 資料檢視權限進行中案件主體查詢特殊處理

        //小綜合查詢進行中案件需要進行特殊處理，這個狀態只能為小綜合查詢使用，後期如查進行中，請確認好要求後看看是否可以複用
        if (caseStatus == 0)
        {
            contractViews = await applicationBaseRefactorService.GetSelectTypeByContractView(userRole, menuCode, 38);
            await InsertLog("資料檢視權限進行中案件主體查詢特殊處理 case 0");
        }
        else
        {
            contractViews = await applicationBaseRefactorService.GetSelectTypeByContractView(userRole, menuCode);
            await InsertLog("資料檢視權限進行中案件主體查詢特殊處理 not 0");
        }

        #endregion

        //是否存在全部主體權限
        caseStatus = contractViews.Any(s => s.selectType == 0) ? -1 : caseStatus;

        //根據檢查後的數據進行處理
        switch (caseStatus)
        {
            case -1: //根據用戶角色發現存在全部主體權限角色
                listEntity = await applicationBaseRefactorService.GetAllEntity();
                await InsertLog("根據用戶角色發現存在全部主體權限角色");
                break;
            case 0: //簽核中案件
                listEntity = await applicationBaseRefactorService.GetSignerEntity(empID, contractViews);
                await InsertLog("簽核中案件");
                break;
            case 1: //歷史案件
                listEntity = await applicationBaseRefactorService.GetOldCaseEntity(empID, contractViews);
                await InsertLog("歷史案件");
                break;
            case 2: //歷史案件(大綜合查詢使用)
                listEntity = await applicationBaseRefactorService.GetOldCaseEntityBySynthesis(empID, contractViews);
                await InsertLog("歷史案件(大綜合查詢使用)");
                break;
            case 3: //其他申請查詢中ACD類型申請單主體
                listEntity = await applicationBaseRefactorService.GetFnpEntityByOtherACD(empID);
                await InsertLog("其他申請查詢中ACD類型申請單主體");
                break;
            case 4: //其他申請查詢中EF類型申請單主體
                listEntity = await applicationBaseRefactorService.GetFnpEntityByOtherEF(empID);
                await InsertLog("其他申請查詢中EF類型申請單主體");
                break;
        }

        return listEntity;
    }

    #endregion

    #region 獲取關聯合約編號
    /// <summary>
    /// 獲取關聯合約編號
    /// </summary>
    /// <returns></returns>
    public async Task<List<string>> GetGroupContractNumber()
    {
        return await SqlSugarHelper.Db.Ado.SqlQueryAsync<string>(
            @"SELECT DISTINCT group_contract_number AS contract_number FROM dbo.v_getallcontractnumber WHERE ISNULL(group_contract_number,N'') <> N'' ORDER BY group_contract_number ASC;");
    }
    #endregion

    private async Task InsertLog(string step, string log = null)
    {
        var userInfoModel = AppStaticServices.GetUserInfoFromHttpContext(httpContextAccessor.HttpContext);
        await SqlSugarHelper.Db.Insertable(new upload_log
        {
            upload_key = "history query",
            upload_type = "history query",
            upload_step = step,
            create_user = userInfoModel.current_emp,
            log_detail = log
        }).ExecuteCommandAsync();
    }
}