﻿using Elegal.Interface.Api.Common.Model.ViewModel.FlowApi.Application;
using Elegal.Orm;
#nullable disable
namespace Elegal.Flow.Api.Repository.Application
{
    /// <summary>
    /// 合约管理作业(关企建档)
    /// </summary>
    public class EnterpriseOriginalArchiveRepository
    {
        /// <summary>
        /// 合约效期资讯
        /// </summary>
        /// <param name="applyNumber"></param>
        /// <returns></returns>
        public DocumentationContractViewModel GetContractValidityInformationRecord(string applyNumber)
        {
            string sql = $@"select
	                            confirm_eff_date ConfirmEffDate,--生效日
	                            confirm_sign_date ConfirmSignDate,--簽約日
                                confirm_exp_date ConfirmExpDate--到期日
                            from
	                            original_archive_record
                            where
	                            apply_number = {DbAccess.Database.GetParameterPrefix()}applyNumber";
            return DbAccess.Database.SqlQuery<DocumentationContractViewModel>(sql, new { applyNumber }).FirstOrDefault();
        }
    }
}
