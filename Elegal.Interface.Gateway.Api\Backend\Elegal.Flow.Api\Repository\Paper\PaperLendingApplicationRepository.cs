﻿using Elegal.Interface.Api.Common.Control.Filter;
using Elegal.Interface.Api.Common.FuncHelper;
using Elegal.Interface.Api.Common.Model.DBModel;
using Elegal.Interface.Api.Common.Model.ParaModel.FlowApi;
using Elegal.Interface.Api.Common.Model.ResultModel.FlowApi;
using Elegal.Interface.Api.Common.Model.ViewModel.FlowApi;
using Elegal.Interface.Api.Common.Repository;
using Elegal.Orm;
using Elegal.Orm.Dtos;
using System.Text;

namespace Elegal.Flow.Api.Repository.Paper
{
    /// <summary>
    /// 紙本借出申請Repository
    /// </summary>
    public class PaperLendingApplicationRepository : BaseRepository
    {
        #region 查詢借出申請資料
        /// <summary>
        /// 查詢借出申請資料
        /// </summary>
        /// <param name="qry"></param>
        /// <returns></returns>
        public PageResult<PaperLendingApplicationViewModel> QueryPaperLendingApplication(qryPaperLendingApplication qry)
        {
            PageResult<PaperLendingApplicationViewModel> result = new PageResult<PaperLendingApplicationViewModel>();
            StringBuilder countSql = new StringBuilder();
            countSql.Append(@"SELECT
                           COUNT(*) 
                         FROM (
                            SELECT DISTINCT
                                pla.lend_id,--主鍵id
                                pla.lend_number,--借出單號
                                pldd.retrieve_number,--調閱單號
                                pla.application_time,--申請日期
                                pla.create_time, 
                                pla.lend_handler_emplid,--經辦人
                                pla.lend_fill_emplid,--填單人
                                (SELECT COUNT(detail_id) FROM paper_lending_detail WHERE paper_lend_id = pla.lend_id AND is_pickup_lend = 1) AS lendCount,--共多少紙本
                                (SELECT COUNT(detail_id) FROM paper_lending_detail WHERE paper_lend_id = pla.lend_id AND is_return = 1) AS returnCount,--已還多少紙本
				                (SELECT COUNT(detail_id) FROM paper_lending_detail WHERE paper_lend_id = pla.lend_id AND pickup_time IS NULL AND CONVERT(DATE, should_return_time) < CONVERT(DATE, GETUTCDATE()) AND pla.lend_status = '02') AS notCollectCount,
								(SELECT COUNT(detail_id) FROM paper_lending_detail WHERE paper_lend_id = pla.lend_id AND pickup_time IS NOT NULL AND actual_return_time IS NULL AND CONVERT(DATE, should_return_time) < CONVERT(DATE, GETUTCDATE())) AS overdueCount,
                                pla.lend_status,--申請單狀態
                                (SELECT fun_name FROM sys_parameters WHERE para_code = N'lib_lendStatus' AND lang_type = @lang_type AND func_code = pla.lend_status) AS lendStatusName--s申請單中文名
                            FROM paper_lending_application AS pla
                            --明細資料
                            LEFT JOIN (SELECT paper_lend_id,paper_basic_id,paper_code,paper_name,pickup_time FROM paper_lending_detail) AS pld ON pld.paper_lend_id = pla.lend_id
                            --需求資訊
                            LEFT JOIN (SELECT retrieve_number,paper_lend_id FROM paper_lending_demand) AS pldd ON pldd.paper_lend_id = pla.lend_id
                            LEFT JOIN paper_basic_data pbd on pbd.basic_id = pld.paper_basic_id
                            LEFT JOIN paper_application_data pad ON pad.application_id = pbd.paper_applica_id
                            LEFT JOIN (SELECT apply_number,contract_number,contract_name FROM V_GetApplicationSearch) AS vg ON vg.apply_number = pad.apply_number
                            WHERE 1 = 1");
            StringBuilder sql = new StringBuilder();
            sql.Append(@"SELECT
                            lend_id,--主鍵id
                            lend_number,--借出單號
                            retrieve_number,--調閱單號
                            application_time,--申請日期
                            lend_handler_emplid,--經辦人工號
                            ee.name,--經辦人中文名稱
                            ee.name_a,--經辦人英文名稱
                            lend_fill_emplid,
							ee_fill.name AS fill_name,--填單人中文名稱
                            ee_fill.name_a AS fill_name_a,--填單人英文名稱
                            lendCount,--共多少紙本
                            returnCount,--已還多少紙本
                            notCollectCount,
                            overdueCount,
                            lend_status,--申請單狀態
                            lendStatusName,--s申請單中文名
                            create_time 
                         FROM (
                            SELECT DISTINCT
                                pla.lend_id,--主鍵id
                                pla.lend_number,--借出單號
                                pldd.retrieve_number,--調閱單號
                                pla.application_time,--申請日期
                                pla.create_time, 
                                pla.lend_handler_emplid,--經辦人
                                pla.lend_fill_emplid,--填單人
                                (SELECT COUNT(detail_id) FROM paper_lending_detail WHERE paper_lend_id = pla.lend_id AND is_pickup_lend = 1) AS lendCount,--共多少紙本
                                (SELECT COUNT(detail_id) FROM paper_lending_detail WHERE paper_lend_id = pla.lend_id AND is_return = 1) AS returnCount,--已還多少紙本
                                (SELECT COUNT(detail_id) FROM paper_lending_detail WHERE paper_lend_id = pla.lend_id AND pickup_time IS NULL AND CONVERT(DATE, should_return_time) < CONVERT(DATE, GETUTCDATE()) AND pla.lend_status = '02') AS notCollectCount,
								(SELECT COUNT(detail_id) FROM paper_lending_detail WHERE paper_lend_id = pla.lend_id AND ((pickup_time IS NOT NULL AND actual_return_time IS NULL AND CONVERT(DATE, should_return_time) < CONVERT(DATE, GETUTCDATE()))
								OR (actual_return_time is not null AND CONVERT(DATE, should_return_time) < CONVERT(DATE, actual_return_time)))) AS overdueCount, 
                                pla.lend_status,--申請單狀態
                                (SELECT fun_name FROM sys_parameters WHERE para_code = N'lib_lendStatus' AND lang_type = @lang_type AND func_code = pla.lend_status) AS lendStatusName--s申請單中文名
                            FROM paper_lending_application AS pla
                            --明細資料
                            LEFT JOIN (SELECT paper_lend_id,paper_basic_id,paper_code,paper_name,pickup_time FROM paper_lending_detail) AS pld ON pld.paper_lend_id = pla.lend_id
                            --需求資訊
                            LEFT JOIN (SELECT retrieve_number,paper_lend_id FROM paper_lending_demand) AS pldd ON pldd.paper_lend_id = pla.lend_id
                            LEFT JOIN paper_basic_data pbd on pbd.basic_id = pld.paper_basic_id
                            LEFT JOIN paper_application_data pad ON pad.application_id = pbd.paper_applica_id
                            LEFT JOIN (SELECT apply_number,contract_number,contract_name FROM V_GetApplicationSearch) AS vg ON vg.apply_number = pad.apply_number
                            WHERE 1 = 1");
            //原有邏輯逾期未歸還需紅底改為歸還之後還是紅底，故SQL overdueCount欄位加入逾期歸還判斷
            //20240325 Eric

            StringBuilder condition = new StringBuilder();

            //紙本/合約編號
            if (!string.IsNullOrEmpty(qry.paper_code))
            {
                condition.Append(@$" AND ( pld.paper_code LIKE CONCAT(N'%', @paper_code, N'%') OR vg.contract_number LIKE CONCAT(N'%', @paper_code, N'%'))");
            }
            //紙本/合約名稱
            if (!string.IsNullOrEmpty(qry.paper_name))
            {
                condition.Append(@$" AND ( pld.paper_name LIKE CONCAT(N'%', @paper_name, N'%') OR vg.contract_name LIKE CONCAT(N'%', @paper_name, N'%'))");
            }
            //借出單號
            if (!string.IsNullOrEmpty(qry.lend_number))
            {
                condition.Append(@$" AND pla.lend_number LIKE CONCAT(N'%', @lend_number, N'%')");
            }
            //其他申請單號
            if (!string.IsNullOrEmpty(qry.retrieve_number))
            {
                condition.Append(@$" AND pldd.retrieve_number LIKE CONCAT(N'%', @retrieve_number, N'%')");
            }
            //申請日期開始
            if (qry.create_time_start != null)
            {
                condition.Append(@$" AND pla.application_time >= @create_time_start");
            }
            //申請日期開始
            if (qry.create_time_end != null)
            {
                condition.Append(@$" AND pla.application_time <= @create_time_end");
            }
            //取件日期開始
            if (qry.pickup_time_start != null)
            {
                condition.Append(@$" AND pld.pickup_time >= @pickup_time_start");
            }
            //取件日期結束
            if (qry.pickup_time_end != null)
            {
                condition.Append(@$" AND pld.pickup_time <= @pickup_time_end");
            }
            //經辦人
            if (!string.IsNullOrEmpty(qry.lend_handler_emplid))
            {
                condition.Append(@$" AND pla.lend_handler_emplid LIKE CONCAT(N'%', @lend_handler_emplid, N'%')");
            }
            //填單人
            if (qry.lend_fill_emplid != null && qry.lend_fill_emplid.Count > 0)
            {
                string[] lend_fill_emplid = qry.lend_fill_emplid.ToArray();
                condition.Append(@$" AND pla.lend_fill_emplid IN ({string.Join(",", lend_fill_emplid.Select(s => $"N'{s}'"))})");
            }
            //申請單狀態
            if (qry.lend_status != null && qry.lend_status.Count > 0)
            {
                string[] lend_status = qry.lend_status.ToArray();
                condition.Append(@$" AND pla.lend_status IN ({string.Join(",", lend_status.Select(s => $"N'{s}'"))})");
            }

            condition.Append($@") AS plaData");
            condition.Append($@" LEFT JOIN (SELECT emplid,name,name_a FROM ps_sub_ee_lgl_vw_a) AS ee ON ee.emplid = plaData.lend_handler_emplid ");
            condition.Append($@" LEFT JOIN (SELECT emplid,name,name_a FROM ps_sub_ee_lgl_vw_a) AS ee_fill on ee_fill.emplid = plaData.lend_fill_emplid");
            countSql.Append(condition.ToString());
            int totalCount = DbAccess.Database.SqlQuery<int>(countSql.ToString(), new
            {
                qry.paper_code,
                qry.paper_name,
                qry.lend_number,
                qry.retrieve_number,
                qry.create_time_start,
                qry.create_time_end,
                qry.pickup_time_start,
                qry.pickup_time_end,
                qry.lend_handler_emplid,
                lang_type = MvcContext.UserInfo.logging_locale
            }).FirstOrDefault();

            sql.Append(condition.ToString());
            if (qry.page == null || qry.page.PageSize == 0 && qry.page.PageIndex == 0)
                qry.page = new PageParam() { PageIndex = 1, PageSize = 25 };

            if (qry.order == null || string.IsNullOrEmpty(qry.order.Field))
                qry.order = new OrderByParam() { Field = "create_time", Order = OrderBy.DESC };

            int offset = (qry.page.PageIndex - 1) * qry.page.PageSize;
            string orderStr = qry.order.Field + " " + qry.order.Order.ToString();
            sql.Append(@$" ORDER BY {orderStr} OFFSET {offset} ROWS FETCH NEXT {qry.page.PageSize} ROWS ONLY");
            result.Data = DbAccess.Database.SqlQuery<PaperLendingApplicationViewModel>(sql.ToString(),
            new
            {
                qry.paper_code,
                qry.paper_name,
                qry.lend_number,
                qry.retrieve_number,
                qry.create_time_start,
                qry.create_time_end,
                qry.pickup_time_start,
                qry.pickup_time_end,
                qry.lend_handler_emplid,
                lang_type = MvcContext.UserInfo.logging_locale
            }).ToList();

            foreach (var item in result.Data)
            {
                if (item.create_time != null)
                    item.create_time_local = item.create_time.ConvertDateByTimeZoneByUtc(MvcContext.UserInfo.time_zone);
                if (item.application_time != null)
                    item.application_time_local = item.application_time.ConvertDateByTimeZoneByUtc(MvcContext.UserInfo.time_zone);
            }

            result.TotalRows = totalCount;
            return result;
        }
        #endregion

        #region 借出明細
        /// <summary>
        /// 借出明細
        /// </summary>
        /// <param name="lend_id"></param>
        /// <param name="lang_type"></param>
        /// <returns></returns>
        public List<PaperLendingApplicationDetailViewModel> GetPaperLendingDetails(int lend_id, string lang_type = "ZH-TW")
        {
            //原有邏輯逾期未歸還需紅底改為歸還之後還是紅底，故SQL is_overdue欄位加入逾期歸還判斷
            //20240325 Eric
            string sql = $@"SELECT
                                vg.contract_number,--合約編號
                                pld.paper_code,--紙本編號
                                pld.paper_name,--紙本名稱
                                pbd.paper_entry_status,--入庫狀態
                                (SELECT STRING_AGG(fun_name, ', ') FROM sys_parameters WHERE para_code = N'lib_paperEntryStatus' AND lang_type = @lang_type AND func_code IN(SELECT value FROM STRING_SPLIT(pbd.paper_entry_status, ','))) AS entryStatusName,--入庫狀態中文名稱(SELECT STRING_AGG(fun_name, ', ') FROM sys_parameters WHERE para_code = N'lib_paperEntryStatus' AND lang_type = @lang_type AND func_code IN(SELECT value  FROM STRING_SPLIT(pbd.paper_entry_status, ','))) AS entryStatusName,--入庫狀態中文名稱
                                pbd.paper_return_status,--紙本歸還現狀
                                (SELECT STRING_AGG(fun_name, ', ') FROM sys_parameters WHERE para_code = N'lib_returnStatus' AND lang_type = @lang_type AND func_code IN(SELECT value FROM STRING_SPLIT(pbd.paper_return_status, ','))) AS returnStatusName,--紙本歸還現狀中文名稱
                                pbd.paper_position,--紙本存放位置
                                pbd.parer_borrow_days,--借閱日上線
                                pld.is_return,--歸還狀態1：已歸還；0：未歸還
                                CASE
                                    WHEN pld.pickup_time IS NULL AND CONVERT(DATE, pld.should_return_time) < CONVERT(DATE, GETUTCDATE()) THEN 1 -- 未領取
                                    ELSE 0
                                END AS is_notCollect, -- 未領取
                                CASE
                                    WHEN pld.pickup_time IS NOT NULL AND actual_return_time IS NULL AND CONVERT(DATE, pld.should_return_time) < CONVERT(DATE, GETUTCDATE()) THEN 1 -- 逾期未歸還
                                    WHEN pld.pickup_time IS NOT NULL AND actual_return_time IS NOT NULL AND CONVERT(DATE, pld.should_return_time) < CONVERT(DATE, actual_return_time) THEN 1 -- 逾期歸還
                                    ELSE 0
                                END AS is_overdue -- 逾期未歸還
                            FROM paper_lending_detail AS pld
                            INNER JOIN paper_basic_data AS pbd ON pld.paper_basic_id = pbd.basic_id
		                    LEFT JOIN paper_application_data pad ON pbd.paper_applica_id = pad.application_id
		                    LEFT JOIN V_GetApplicationSearch vg ON pad.apply_number = vg.apply_number
                            WHERE pld.paper_lend_id = @lend_id AND (pld.is_pickup_lend is null OR pld.is_pickup_lend = 1)";
            return DbAccess.Database.SqlQuery<PaperLendingApplicationDetailViewModel>(sql, new { lend_id, lang_type }).ToList();
        }
        #endregion

        #region 調閱申請單資訊(新增-需求資訊)
        /// <summary>
        /// 調閱申請單資訊(新增-需求資訊)
        /// </summary>
        /// <param name="retrieve_number"></param>
        /// <returns></returns>
        public List<OtherApplicationDetail> GetOtherApplicationDetail(string retrieve_number)
        {
            //auth_date_start、auth_date_end在oo3版需要转时区时间
            string sql = $@"SELECT DISTINCT TOP(200) 
                                retrieve_number,
                                applynumber,
                                vg.contract_number,
                                vg.contract_name,
                                discloseperson,--顯示內部同仁/其他同仁：00：無；01：其他同仁；10：內部同仁；11：內部同仁+其他同仁
                                ename,--英文名(內部同仁)
                                empid,--工號(內部同仁)
                                depid,--部門代碼(內部同仁)
                                company,--部門公司別(內部同仁)
                                otherperson,--其他人員(直接顯示)
                                other,--其他說明
                                auth_date_start,
                                auth_date_end,
                                pba.paper_code, 
                                retrieve_handler, --經辦人工號
                                applycause1,
                                applyopentime,
                                sort_order  
                            FROM V_GetOtherRetrieveNumber vg
                            LEFT JOIN paper_application_data pad on vg.applynumber = pad.apply_number
							LEFT JOIN paper_basic_data pba on pad.application_id = pba.paper_applica_id 
                            WHERE retrieve_number LIKE CONCAT(N'%', @retrieve_number, N'%')";
            var res = NpgsqlSearchByList<OtherApplicationDetail>(sql, new { retrieve_number });
            if (res != null && res.Count > 0)
            {
                foreach (OtherApplicationDetail item in res)
                {
                    if (item.auth_date_start != null) item.auth_date_start = item.auth_date_start.ConvertDateByTimeZoneByUtc(MvcContext.UserInfo.time_zone);
                    if (item.auth_date_end != null) item.auth_date_end = item.auth_date_end.ConvertDateByTimeZoneByUtc(MvcContext.UserInfo.time_zone);
                }
            }

            return res;
        }
        #endregion

        #region 獲取其他申請單明細
        /// <summary>
        /// 獲取其他申請單明細
        /// </summary>
        /// <param name="retrieve_number"></param>
        /// <returns></returns>
        public List<paper_basic_data> GetRetrievePaper(string retrieve_number)
        {
            string sql = $@"SELECT DISTINCT
                              pba.*
                            FROM V_GetOtherRetrieveNumber vg
                            inner JOIN paper_application_data pad on vg.applynumber = pad.apply_number
							inner JOIN paper_basic_data pba on pad.application_id = pba.paper_applica_id
                            WHERE retrieve_number = @retrieve_number";
            return NpgsqlSearchByList<paper_basic_data>(sql, new { retrieve_number });
        }
        #endregion

        #region 借出明細查詢(新增-借出明細)
        /// <summary>
        /// 借出明細查詢(新增-借出明細)
        /// 20240415 改為只撈已入庫紙本 Eric
        /// </summary>
        /// <param name="contract_number"></param>
        /// <param name="paper_code"></param>
        /// <param name="detail_limit"></param>
        /// <returns></returns>
        public List<GetLendingDetailViewMode> GetLendingDetail(string? contract_number, string? paper_code, int? detail_limit = null)
        {
            string sql;
            if (detail_limit != null && detail_limit > 0)
                sql = $@"SELECT TOP({detail_limit}) ";
            else
                sql = $@"SELECT ";
            sql += $@"      pbd.basic_id,--紙本基本資料明細id
                                vg.contract_number,--合約編號
                                pbd.paper_code,--紙本編號
                                pbd.paper_name,--紙本名稱
                                pbd.paper_entry_status,--入庫狀態
                                (SELECT STRING_AGG(fun_name, ', ') FROM sys_parameters WHERE para_code = N'lib_paperEntryStatus' AND lang_type = @lang_type AND func_code IN(SELECT value  FROM STRING_SPLIT(pbd.paper_entry_status, ','))) AS entryStatusName,--入庫狀態中文名稱
                                pbd.paper_return_status,--紙本現狀
                                (SELECT STRING_AGG(fun_name, ', ') FROM sys_parameters WHERE para_code = N'lib_returnStatus' AND lang_type = @lang_type AND func_code IN(SELECT value  FROM STRING_SPLIT(pbd.paper_return_status, ','))) AS returnStatusName,--歸還現狀中文名稱
                                pbd.paper_position,--存放位置
                                pbd.parer_borrow_days,--借閱日上線
                                pbd.create_user--填單人/代理人 --應歸還日期=申請日期+借出天數-1
                                FROM paper_application_data AS pads
                                INNER JOIN paper_basic_data AS pbd ON pads.application_id = pbd.paper_applica_id AND pbd.paper_entry_status = '05'
                                LEFT JOIN V_GetApplicationSearch vg ON pads.apply_number = vg.apply_number";
            if (!string.IsNullOrEmpty(contract_number))
                sql += $@" WHERE vg.contract_number LIKE CONCAT(N'%', @contract_number, N'%')";
            if (!string.IsNullOrEmpty(paper_code))
                sql += $@" WHERE pbd.paper_code LIKE CONCAT(N'%', @paper_code, N'%')";
            List<GetLendingDetailViewMode> res = NpgsqlSearchByList<GetLendingDetailViewMode>(sql, new { lang_type = MvcContext.UserInfo.logging_locale, contract_number, paper_code });
            foreach (var item in res)
            {
                if (!string.IsNullOrEmpty(item.contract_number)) item.contract_number = item.contract_number.Replace("\u001f", "");
            }
            return res;
        }
        #endregion

        #region 借出明細批次查詢(新增-借出明細)
        /// <summary>
        /// 借出明細批次查詢(新增-借出明細)
        /// </summary>
        /// <param name="contract_number"></param>
        /// <param name="paper_code"></param>
        /// <returns></returns>
        public List<GetLendingDetailViewMode> BatchGetLendingDetail(List<string>? contract_number, List<string>? paper_code)
        {
            string sql = $@"SELECT TOP(200) 
                                pbd.basic_id,--紙本基本資料明細id
                                vg.contract_number,--合約編號
                                pbd.paper_code,--紙本編號
                                pbd.paper_name,--紙本名稱
                                pbd.paper_entry_status,--入庫狀態
                                (SELECT STRING_AGG(fun_name, ', ') FROM sys_parameters WHERE para_code = N'lib_paperEntryStatus' AND lang_type = @lang_type AND func_code IN(SELECT value  FROM STRING_SPLIT(pbd.paper_entry_status, ','))) AS entryStatusName,--入庫狀態中文名稱
                                pbd.paper_return_status,--紙本現狀
                                (SELECT STRING_AGG(fun_name, ', ') FROM sys_parameters WHERE para_code = N'lib_returnStatus' AND lang_type = @lang_type AND func_code IN(SELECT value  FROM STRING_SPLIT(pbd.paper_return_status, ','))) AS returnStatusName,--歸還現狀中文名稱
                                pbd.paper_position,--存放位置
                                pbd.parer_borrow_days,--借閱日上線
                                pbd.create_user--填單人/代理人 --應歸還日期=申請日期+借出天數-1
                                FROM paper_application_data AS pads
                                INNER JOIN paper_basic_data AS pbd ON pads.application_id = pbd.paper_applica_id
	                            LEFT JOIN V_GetApplicationSearch vg ON pads.apply_number = vg.apply_number";
            if (contract_number != null && contract_number.Count > 0)
            {
                string[] contract_number_list = contract_number.ToArray();
                sql += $@" WHERE vg.contract_number IN ({string.Join(",", contract_number_list.Select(s => $"N'{s}'"))})";
            }
            if (paper_code != null && paper_code.Count > 0)
            {
                string[] paper_code_list = paper_code.ToArray();
                sql += $@" WHERE pbd.paper_code IN ({string.Join(",", paper_code_list.Select(s => $"N'{s}'"))})";
            }

            List<GetLendingDetailViewMode> res = NpgsqlSearchByList<GetLendingDetailViewMode>(sql, new { lang_type = MvcContext.UserInfo.logging_locale, contract_number, paper_code });
            foreach (var item in res)
            {
                if (!string.IsNullOrEmpty(item.contract_number)) item.contract_number = item.contract_number.Replace("\u001f", "");
            }
            return res;
        }
        #endregion

        #region 獲取借出明細需求資訊
        /// <summary>
        /// 獲取借出明細需求資訊
        /// </summary>
        /// <param name="paper_lend_id"></param>
        /// <returns></returns>
        public paper_lending_demand GetPaperLendingDemand(int paper_lend_id)
        {
            string sql = $@"SELECT * FROM paper_lending_demand WHERE paper_lend_id = @paper_lend_id";
            return NpgsqlSearchBySingle<paper_lending_demand>(sql, new { paper_lend_id });
        }
        #endregion

        #region 獲取借出主表資訊
        /// <summary>
        /// 獲取借出主表資訊
        /// </summary>
        /// <param name="lend_id"></param>
        /// <returns></returns>
        public paper_lending_application GetPaperLendingApplicationByLendID(int lend_id)
        {
            string sql = $@"SELECT * FROM paper_lending_application WHERE lend_id = @lend_id";
            return NpgsqlSearchBySingle<paper_lending_application>(sql, new { lend_id });
        }
        #endregion

        #region 獲取已借出明細
        /// <summary>
        /// 獲取已借出明細
        /// </summary>
        /// <param name="paper_lend_id"></param>
        /// <returns></returns>
        public List<paper_lending_detail> GetPaperLendingDetail(int paper_lend_id)
        {
            string sql = $@"SELECT pld.detail_id
                                    ,pld.paper_lend_id
                                    ,pld.paper_basic_id
                                    ,pld.paper_demand_id
                                    ,vg.contract_number
                                    ,vg.contract_name
                                    ,pld.paper_code
                                    ,pld.paper_name
                                    ,pld.is_return
                                    ,pld.should_return_time
                                    ,pld.actual_return_time
                                    ,pld.receive_status
                                    ,pld.pickup_status
                                    ,pld.pickup_time
                                    ,pld.consignment_number
                                    ,pld.pickup_emplid
                                    ,pld.actual_pickup_time
                                    ,pld.overdue_day
                                    ,pld.actual_borrow_days
                                    ,pld.loan_due_date
                                    ,pld.create_user
                                    ,pld.create_time
                                    ,pld.modify_user
                                    ,pld.modify_time
                                    ,pld.is_pickup_lend
                                    ,pld.lend_return_status 
                            FROM paper_lending_detail pld
                            LEFT JOIN paper_basic_data pbd ON pld.paper_basic_id = pbd.basic_id
							LEFT JOIN paper_application_data pad ON pad.application_id = pbd.paper_applica_id
                            LEFT JOIN V_GetApplicationSearch vg ON vg.apply_number = pad.apply_number
                            WHERE pld.paper_lend_id = @paper_lend_id";
            return NpgsqlSearchByList<paper_lending_detail>(sql, new { paper_lend_id });
        }
        #endregion

        #region 獲取未借出明細
        /// <summary>
        /// 獲取未借出明細
        /// </summary>
        /// <param name="paper_lend_id"></param>
        /// <returns></returns>
        public List<paper_lending_detail> GetPaperUnlendingDetail(int paper_lend_id)
        {
            string sql = $@"SELECT detail_id
                                  ,paper_lend_id
                                  ,paper_basic_id
                                  ,paper_demand_id
                                  ,vg.contract_number
                                  ,vg.contract_name
                                  ,pud.paper_code
                                  ,pud.paper_name
                                  ,is_return
                                  ,is_pickup_lend
                                  ,should_return_time
                                  ,actual_return_time
                                  ,receive_status
                                  ,pickup_status
                                  ,pickup_time
                                  ,consignment_number
                                  ,pickup_emplid
                                  ,actual_pickup_time
                                  ,overdue_day
                                  ,actual_borrow_days
                                  ,loan_due_date
                                  ,lend_return_status
                                  ,pud.create_user
                                  ,pud.create_time
                                  ,pud.modify_user
                                  ,pud.modify_time
                                  ,pud.paper_entry_status
                                  ,lend_number
                              FROM dbo.paper_unlending_detail pud
                              LEFT JOIN paper_basic_data pbd ON pud.paper_basic_id = pbd.basic_id
                              LEFT JOIN paper_application_data pad ON pad.application_id = pbd.paper_applica_id
                              LEFT JOIN V_GetApplicationSearch vg ON vg.apply_number = pad.apply_number 
                              WHERE paper_lend_id = @paper_lend_id";
            return NpgsqlSearchByList<paper_lending_detail>(sql, new { paper_lend_id });
        }
        #endregion

        #region 獲取借出單申請人明細
        /// <summary>
        /// 獲取借出單申請人明細
        /// </summary>
        /// <param name="lend_id"></param>
        /// <returns></returns>
        public LendFillEmpInfoViewMode GetLendFillEmpInfo(int lend_id)
        {
            string sql = $@"SELECT
                                lend_id,
                                lend_number,--借出單號
                                create_time,--申請日期
                                ee.name,--中文名稱
                                ee.name_a,--英文名稱
                                ee.deptid,--部門代號
                                ee.phone_a,--分機
                                ee.email_address_a--郵箱地址
                            FROM (SELECT lend_id,lend_number,create_time,lend_fill_emplid FROM paper_lending_application WHERE lend_id = @lend_id) AS pla
                            INNER JOIN (SELECT emplid,name,name_a,deptid,phone_a,email_address_a FROM ps_sub_ee_lgl_vw_a) AS ee ON ee.emplid = pla.lend_fill_emplid";
            return NpgsqlSearchBySingle<LendFillEmpInfoViewMode>(sql, new { lend_id });
        }
        #endregion

        #region 顯示借出明細
        /// <summary>
        /// 顯示借出明細
        /// </summary>
        /// <param name="lend_id"></param>
        /// <returns></returns>
        public List<LendingPaperInfo> GetLendingPaperInfo(int lend_id)
        {
            string sql = $@"SELECT
                                pld.detail_id,--主鍵id
                                vg.contract_number,--合約編號
                                pld.paper_code,--紙本編號
                                pld.paper_name,--紙本名稱
                                pba.paper_entry_status,--入庫狀態
                                (SELECT fun_name FROM sys_parameters WHERE para_code = N'' AND lang_type = @lang_type AND func_code = pba.paper_entry_status) AS entryStatusName--入庫狀態中文名稱
                             FROM paper_lending_detail AS pld
                             INNER JOIN paper_basic_data AS pba ON pld.paper_basic_id = pba.basic_id
                             LEFT JOIN paper_basic_data pbd ON pld.paper_basic_id = pbd.basic_id
							 LEFT JOIN paper_application_data pad ON pad.application_id = pbd.paper_applica_id
							 LEFT JOIN V_GetApplicationSearch vg ON pad.apply_number = vg.apply_number
                             WHERE pld.paper_lend_id = @lend_id";
            List<LendingPaperInfo> res = NpgsqlSearchByList<LendingPaperInfo>(sql, new { lend_id, lang_type = MvcContext.UserInfo.logging_locale });
            foreach (var item in res)
            {
                if (!string.IsNullOrEmpty(item.contract_number)) item.contract_number = item.contract_number.Replace("\u001f", "");
            }
            return res;
        }
        #endregion

        #region 是否存在調閱單人員
        /// <summary>
        /// 是否存在調閱單人員
        /// </summary>
        /// <param name="retrieve_number"></param>
        /// <param name="empid"></param>
        /// <returns></returns>
        public bool IsRetrieveEmp(string retrieve_number, string empid)
        {
            string sql = $@"SELECT COUNT(empid)  FROM V_GetOtherRetrieveNumber WHERE retrieve_number = @retrieve_number AND (empid = @empid OR retrieve_handler = @empid)";
            return NpgsqlSearchBySingle<int>(sql, new { retrieve_number, empid }) > 0;
        }
        #endregion

        #region 是否為申請人或經辦人
        /// <summary>
        /// 是否為申請人或經辦人
        /// </summary>
        /// <param name="lend_id"></param>
        /// <param name="empid"></param>
        /// <returns></returns>
        public bool IsFillOrhandlerEmp(int lend_id, string empid)
        {
            string sql = $@"SELECT COUNT(lend_id) FROM paper_lending_application WHERE lend_id = @lend_id AND (lend_fill_emplid = @empid OR lend_handler_emplid = @empid)";
            return NpgsqlSearchBySingle<int>(sql, new { lend_id, empid }) > 0;
        }
        #endregion

        #region 獲取嗶咔機數據
        /// <summary>
        /// 獲取嗶咔機數據
        /// </summary>
        /// <param name="badge_id"></param>
        /// <returns></returns>
        public string GetUserByBadgeId(string badge_id)
        {
            string sql = $@"SELECT emplid FROM employeeinfo_jobs_g1 WHERE badge_id = @badge_id";
            return NpgsqlSearchBySingle<string>(sql, new { badge_id });
        }
        #endregion

        #region 檢查是否存在借出明細
        /// <summary>
        /// 檢查是否存在借出明細
        /// </summary>
        /// <param name="paper_code"></param>
        /// <returns></returns>
        public bool CheckPaperLendingDetail(string paper_code)
        {
            string sql = $@"select count(detail_id) from paper_lending_detail where paper_code = @paper_code";
            return NpgsqlSearchBySingle<int>(sql, new { paper_code }) > 0;
        }
        #endregion

        #region 獲取BU/BG數據
        /// <summary>
        /// type=0:bu;type=1:bg
        /// </summary>
        /// <param name="type"></param>
        /// <param name="emplid"></param>
        /// <returns></returns>
        public string GetUserBuBg(int type, string emplid)
        {
            string sql;
            if (type == 0)
                sql = $@"SELECT bu FROM ps_sub_og_lgl_vw_a WHERE deptid = (SELECT deptid FROM ps_sub_ee_lgl_vw_a WHERE emplid = @emplid)";
            else
                sql = $@"SELECT bg FROM ps_sub_og_lgl_vw_a WHERE deptid = (SELECT deptid FROM ps_sub_ee_lgl_vw_a WHERE emplid = @emplid)";
            return NpgsqlSearchBySingle<string>(sql, new { emplid });
        }
        #endregion

        #region 暫存

        #region 獲取暫存單
        /// <summary>
        /// 獲取暫存單
        /// </summary>
        /// <returns></returns>
        public int GetTempApplication()
        {
            string sql = $@"SELECT lend_id FROM paper_lending_application WHERE temp_lend_number = @temp_lend_number";
            return NpgsqlSearchBySingle<int>(sql, new { temp_lend_number = MvcContext.UserInfo.current_emp });
        }
        #endregion

        #region 獲取暫存的明細
        /// <summary>
        /// 獲取暫存的明細
        /// </summary>
        /// <param name="temp_lend_number"></param>
        /// <returns></returns>
        public List<int> GetPersonTempApplication(string temp_lend_number)
        {
            StringBuilder sql = new StringBuilder();
            sql.Append(@"SELECT lend_id FROM paper_lending_application pla ");
            sql.Append(@" INNER JOIN paper_lending_demand pld ON pla.lend_id = pld.paper_lend_id and (pld.retrieve_number is not null and pld.retrieve_number = '')");
            sql.Append(@"WHERE pla.temp_lend_number = @temp_lend_number ");

            return NpgsqlSearchByList<int>(sql.ToString(), new { temp_lend_number });
        }
        #endregion

        #region 獲取調閱單暫存
        /// <summary>
        /// 獲取調閱單暫存
        /// </summary>
        /// <param name="retrieve_number"></param>
        /// <returns></returns>
        public List<int> GetOtherTempApplication(string retrieve_number)
        {
            StringBuilder sql = new StringBuilder();
            sql.Append(@"SELECT lend_id FROM paper_lending_application pla ");
            sql.Append(@" INNER JOIN paper_lending_demand pld ON pla.lend_id = pld.paper_lend_id and pld.retrieve_number = @retrieve_number ");
            sql.Append(@"WHERE pla.temp_lend_number is not null ");

            return NpgsqlSearchByList<int>(sql.ToString(), new { retrieve_number });
        }
        #endregion

        #endregion

        #region 郵件(後期未使用，是否剔除待整理)

        #region 發送人員

        #region 根據 lendid 獲取需要發送揭露人員(多個人員)
        /// <summary>
        /// 根據 lendid 獲取需要發送揭露人員(多個人員)
        /// </summary>
        /// <param name="lendID">借出申請單id</param>
        /// <param name="lend_status">借出申請單狀態</param>
        /// <returns></returns>
        public List<LendClosedMailModel> GetLendExposeData(int lendID, string lend_status)
        {
            string getLendClosedExposeSql = @"
                         SELECT DISTINCT oee.name AS oCname,oee.name_a AS oEname,oee.email_address_a AS oEmail FROM (
                         SELECT retrieve_number FROM paper_lending_demand pld
						 join paper_lending_application pla on pla.lend_id = pld.paper_lend_id
                         WHERE pla.lend_status = @lend_status AND pla.lend_id = @lendID
                         ) AS plh
                         INNER JOIN (SELECT retrieve_number,empid FROM v_getotherretrievenumber) AS orv ON orv.retrieve_number = plh.retrieve_number
                         LEFT JOIN (SELECT emplid,name,name_a,email_address_a FROM ps_sub_ee_lgl_vw_a) AS oee ON oee.emplid = orv.empid;";
            return NpgsqlSearchByList<LendClosedMailModel>(getLendClosedExposeSql, new
            {
                lendID,
                lend_status
            });
        }
        #endregion

        #region 根據 lendid 獲取需要發送揭露人員(單一經辦人)
        /// <summary>
        /// 根據 lendid 獲取需要發送揭露人員(單一經辦人)
        /// </summary>
        /// <param name="lendID"></param>
        /// <param name="lend_status">借出申請單狀態</param>
        /// <returns></returns>
        public List<LendClosedMailModel> GetLendHandleData(int lendID, string lend_status)
        {
            string getLendClosedHandleSql = @"
                         SELECT DISTINCT hee.name AS oCname,hee.name_a AS oEname,hee.email_address_a AS oEmail FROM (
                         SELECT lend_handler_emplid FROM paper_lending_application
                         WHERE lend_status = @lend_status AND lend_id = @lendID
                         ) AS plh
                         LEFT JOIN (SELECT emplid,name,name_a,email_address_a FROM ps_sub_ee_lgl_vw_a) AS hee ON hee.emplid = plh.lend_handler_emplid";
            //不檢查經辦人是否有email 20240305

            return NpgsqlSearchByList<LendClosedMailModel>(getLendClosedHandleSql, new
            {
                lendID,
                lend_status
            });
        }
        #endregion

        #endregion

        #region 根據 lendid 獲取 填單人、填單人代理人、經辦人郵箱(抄送人員)
        /// <summary>
        /// 根據 lendid 獲取 填單人、填單人代理人、經辦人郵箱
        /// </summary>
        /// <param name="lendID">借出申請單id</param>
        /// <returns></returns>
        public string GetLendCc(int lendID, string lend_status)
        {
            string getCcSql = @"
                         SELECT STRING_AGG(cemail, ';') AS ccEmail FROM (
                         --填單人郵箱
                         SELECT (SELECT email_address_a FROM ps_sub_ee_lgl_vw_a AS fee WHERE fee.emplid = plh.lend_fill_emplid) AS cemail FROM paper_lending_application AS plh
                         WHERE lend_status = @lend_status AND lend_id = @lendID
                         UNION
                         --填單人代理人郵箱
                         SELECT (SELECT email_address_a FROM ps_sub_ee_lgl_vw_a AS faee WHERE faee.emplid = sa.agent_empid) AS cemail FROM (SELECT agent_empid,auth_empid FROM sys_agent WHERE CONVERT(date,start_time) <= CONVERT(date,getutcdate()) AND CONVERT(date,end_time) >= CONVERT(date,getutcdate())) AS sa
                         INNER JOIN (
                         SELECT lend_fill_emplid FROM paper_lending_application AS plh
                         WHERE lend_status = @lend_status AND lend_id = @lendID                        
                         ) plh ON sa.auth_empid = plh.lend_fill_emplid
                         UNION
                         --經辦人郵箱
                         SELECT (SELECT email_address_a FROM ps_sub_ee_lgl_vw_a AS hee WHERE hee.emplid = plh.lend_handler_emplid) AS cemail FROM paper_lending_application AS plh
                         WHERE lend_status = @lend_status AND lend_id = @lendID
                         ) AS ccData
                         WHERE cemail <> N'NULL';";
            return NpgsqlSearchBySingle<string>(getCcSql, new
            {
                lendID,
                @lend_status
            });
        }
        #endregion

        #region 根據 lendid 獲取 借出單號，申請日期，其他申請單號，經辦人(總數據)
        /// <summary>
        /// 根據 lendid 獲取 借出單號，申請日期，其他申請單號，經辦人
        /// </summary>
        /// <param name="lendID">借出申請單id</param>
        /// <returns></returns>
        public LendClosedMailModel GetLendData(int lendID, string lend_status)
        {
            string getLendClosedSql = @"
                         SELECT lend_number,application_time,retrieve_number,hee.name AS hCName,hee.name_a AS hEName,hee.email_address_a AS hEmail FROM (
                         SELECT DISTINCT lend_number,application_time,retrieve_number,lend_handler_emplid FROM paper_lending_history
                         WHERE lend_status = @lend_status
                         AND lend_number = (SELECT lend_number FROM paper_lending_application WHERE lend_status = @lend_status AND lend_id = @lendID)
                         ) AS plh
                         LEFT JOIN (SELECT emplid,name,name_a,email_address_a FROM ps_sub_ee_lgl_vw_a) AS hee ON hee.emplid = plh.lend_handler_emplid;";
            return NpgsqlSearchBySingle<LendClosedMailModel>(getLendClosedSql, new
            {
                lendID,
                lend_status
            });
        }
        #endregion

        #region 根據 lendid 獲取 借出單號對應的數據集合(借出明細數據)
        /// <summary>
        /// 根據 lendid 獲取 借出單號對應的數據集合
        /// </summary>
        /// <param name="lendID">借出申請單id</param>
        /// <returns></returns>
        public List<LendClosedMailModel> GetLendDetailData(int lendID, string lend_status)
        {
            string getLendClosedDetailSql = @"
                         SELECT DISTINCT history_id,vg.contract_number,plh.paper_code,plh.paper_name,(SELECT STRING_AGG(fun_name, ', ') FROM sys_parameters WHERE para_code = N'lib_returnStatus' AND lang_type = @lang_type AND func_code IN(SELECT value  FROM STRING_SPLIT(lend_return_status, ','))) AS returnStatusName,actual_return_time 
                         FROM paper_lending_history plh
		                 LEFT JOIN paper_basic_data pbd ON plh.paper_basic_id = pbd.basic_id
		                 LEFT JOIN paper_application_data pad ON pbd.paper_applica_id = pad.application_id
		                 LEFT JOIN V_GetApplicationSearch vg ON vg.apply_number = pad.apply_number
                         WHERE lend_status = @lend_status
                         AND lend_number = (SELECT lend_number FROM paper_lending_application WHERE lend_status = @lend_status AND lend_id = @lendID);";
            return NpgsqlSearchByList<LendClosedMailModel>(getLendClosedDetailSql, new
            {
                lendID,
                lend_status,
                lang_type = MvcContext.UserInfo.logging_locale
            });
        }
        #endregion

        #endregion

        #region 新增借出申請(事務處理)

        #region 新增借出申請
        /// <summary>
        /// 新增借出申請
        /// </summary>
        /// <param name="data"></param>
        /// <param name="dbContext"></param>
        /// <returns></returns>
        public bool InsertPaperLendingApplicationToTransaction(paper_lending_application data, IDbContext dbContext)
        {
            string sql = $@"INSERT INTO paper_lending_application(lend_number, lend_fill_emplid, lend_fill_deptid, lend_handler_emplid, lend_handler_deptid, lend_status, void_reason, create_user, lend_fill_bu ,lend_fill_bg ,lend_handler_bu ,lend_handler_bg, temp_lend_number)
                            VALUES(@lend_number, @lend_fill_emplid, @lend_fill_deptid, @lend_handler_emplid, @lend_handler_deptid, @lend_status, @void_reason, @create_user ,@lend_fill_bu ,@lend_fill_bg ,@lend_handler_bu ,@lend_handler_bg, @temp_lend_number)";
            return this.ExecuteCommandToTransaction(sql, dbContext, data) > 0;
        }
        #endregion

        #region 新增成功後，獲取最新一筆單據 暫存單
        /// <summary>
        /// 新增成功後，獲取最新一筆單據
        /// </summary>
        /// <param name="temp_lend_number"></param>
        /// <param name="type"></param>
        /// <param name="context"></param>
        /// <returns></returns>
        public paper_lending_application GetNewTempPaperLendingApplicationToTransaction(string temp_lend_number, string type, IDbContext context)
        {
            string sql;
            if (type == "INSERT")
            {
                sql = $@"SELECT TOP(1)* FROM paper_lending_application WHERE temp_lend_number = @temp_lend_number ORDER BY create_time DESC";
            }
            else
            {
                sql = $@"SELECT TOP(1)* FROM paper_lending_application WHERE temp_lend_number = @temp_lend_number AND modify_time is not null ORDER BY modify_time DESC";
            }
            return this.NpgsqlSearchBySingleToTransaction<paper_lending_application>(sql, context, new { temp_lend_number });
        }
        #endregion

        #region 新增成功後，獲取最新一筆單據 正式單
        /// <summary>
        /// 新增成功後，獲取最新一筆單據
        /// </summary>
        /// <param name="lend_number"></param>
        /// <param name="context"></param>
        /// <returns></returns>
        public paper_lending_application GetPaperLendingApplicationToTransaction(string lend_number, IDbContext context)
        {
            string sql = $@"SELECT * FROM paper_lending_application WHERE lend_number = @lend_number";
            return this.NpgsqlSearchBySingleToTransaction<paper_lending_application>(sql, context, new { lend_number });
        }
        #endregion

        #region 新增需求資訊
        /// <summary>
        /// 新增需求資訊
        /// </summary>
        /// <param name="data"></param>
        /// <param name="dbContext"></param>
        /// <returns></returns>
        public bool InsertPaperLendingDemandToTransaction(paper_lending_demand data, IDbContext dbContext)
        {
            string sql = $@"INSERT INTO paper_lending_demand(paper_lend_id, retrieve_number, borrow_days, demand_reason, retrieve_reason, create_user)
                                VALUES(@paper_lend_id, @retrieve_number, @borrow_days, @demand_reason, @retrieve_reason, @create_user)";
            return this.ExecuteCommandToTransaction(sql, dbContext, data) > 0;
        }
        #endregion

        #region 查詢數據需求資訊
        /// <summary>
        /// 查詢數據需求資訊
        /// </summary>
        /// <param name="paper_lend_id"></param>
        /// <param name="context"></param>
        /// <returns></returns>
        public paper_lending_demand GetPaperLendingDemandToTransaction(int paper_lend_id, IDbContext context)
        {
            string sql = $@"SELECT * FROM paper_lending_demand WHERE paper_lend_id = @paper_lend_id";
            return this.NpgsqlSearchBySingleToTransaction<paper_lending_demand>(sql, context, new { paper_lend_id });
        }
        #endregion

        #region 新增借出明細
        /// <summary>
        /// 新增借出明細
        /// </summary>
        /// <param name="data"></param>
        /// <param name="context"></param>
        /// <returns></returns>
        public bool InsertPaperLendingDetailToTransaction(paper_lending_detail data, IDbContext context)
        {
            string sql = $@"INSERT INTO paper_lending_detail(paper_lend_id, paper_basic_id, paper_demand_id, contract_number, contract_name, paper_code, paper_name, is_return, should_return_time, create_user, is_pickup_lend)
                            VALUES(@paper_lend_id, @paper_basic_id, @paper_demand_id, @contract_number, @contract_name, @paper_code, @paper_name, @is_return, @should_return_time, @create_user, @is_pickup_lend)";
            return this.ExecuteCommandToTransaction(sql, context, data) > 0;
        }
        #endregion

        #region 刪除未借出明細
        /// <summary>
        /// 刪除未借出明細
        /// </summary>
        /// <param name="paper_lend_id"></param>
        /// <param name="context"></param>
        /// <returns></returns>
        public bool DeletePaperUnLendingDetailToTransaction(int paper_lend_id, IDbContext context)
        {
            string sql = $@"DELETE FROM paper_unlending_detail WHERE paper_lend_id = @paper_lend_id";

            return this.ExecuteCommandToTransaction(sql, context, new { paper_lend_id }) > 0;
        }
        #endregion

        #region 新增未借出明細
        /// <summary>
        /// 新增未借出明細
        /// </summary>
        /// <param name="data"></param>
        /// <param name="context"></param>
        /// <returns></returns>
        public bool InsertPaperUnlendingDetailToTransaction(paper_unlending_detail data, IDbContext context)
        {
            string sql = $@"INSERT INTO paper_unlending_detail(paper_lend_id, paper_basic_id, paper_demand_id, contract_number, contract_name, paper_code, paper_name, is_return, should_return_time, create_user, is_pickup_lend, paper_entry_status, lend_number)
                            VALUES(@paper_lend_id, @paper_basic_id, @paper_demand_id, @contract_number, @contract_name, @paper_code, @paper_name, @is_return, @should_return_time, @create_user, @is_pickup_lend, @paper_entry_status, @lend_number)";
            return this.ExecuteCommandToTransaction(sql, context, data) > 0;
        }
        #endregion

        #region 修改申請單號
        /// <summary>
        /// 修改申請單號
        /// </summary>
        /// <param name="lend_id"></param>
        /// <param name="lend_number"></param>
        /// <param name="context"></param>
        /// <returns></returns>
        public bool UpdateLendNumberToTransaction(int lend_id, string lend_number, IDbContext context)
        {
            string sql = $@"UPDATE paper_lending_application
                            SET 
                                lend_number = @lend_number
                            WHERE
                                lend_id = @lend_id;";
            return this.ExecuteCommandToTransaction(sql, context, new { lend_id, lend_number }) > 0;
        }
        #endregion

        #region 修改主表資訊
        /// <summary>
        /// 修改主表資訊
        /// </summary>
        /// <param name="data"></param>
        /// <param name="context"></param>
        /// <returns></returns>
        public bool UpdatePaperLendingApplicationToTransaction(paper_lending_application data, IDbContext context)
        {
            string sql = $@"UPDATE paper_lending_application
                            SET 
                                lend_fill_emplid = @lend_fill_emplid,
                                lend_fill_deptid = @lend_fill_deptid,
                                lend_handler_emplid = @lend_handler_emplid,
                                lend_handler_deptid = @lend_handler_deptid,
                                lend_status = @lend_status,
                                void_reason = @void_reason,
                                modify_user = @modify_user,
                                modify_time = @modify_time,
                                lend_fill_bu = @lend_fill_bu, 
                                lend_fill_bg = @lend_fill_bg, 
                                lend_handler_bu = @lend_handler_bu,
                                lend_handler_bg = @lend_handler_bg, 
                                temp_lend_number = @temp_lend_number 
                            WHERE
                                lend_id = @lend_id;";
            return this.ExecuteCommandToTransaction(sql, context, data) > 0;
        }
        #endregion

        #region 修改主表時間
        /// <summary>
        /// 修改主表時間
        /// </summary>
        /// <param name="lend_id"></param>
        /// <param name="context"></param>
        /// <returns></returns>
        public bool UpdatePaperLendingApplicationTimeToTransaction(int lend_id, IDbContext context)
        {
            string sql = $@"UPDATE paper_lending_application
                            SET 
                                application_time = GETUTCDATE()
                             WHERE
                                lend_id = @lend_id;";
            return this.ExecuteCommandToTransaction(sql, context, new { lend_id }) > 0;
        }
        #endregion

        #region 修改需求資訊
        /// <summary>
        /// 修改需求資訊
        /// </summary>
        /// <param name="data"></param>
        /// <param name="context"></param>
        /// <returns></returns>
        public bool UpdatePaperLendingDemandToTransaction(paper_lending_demand data, IDbContext context)
        {
            string sql = $@"UPDATE paper_lending_demand 
                            SET 
                                retrieve_number = @retrieve_number, 
                                borrow_days = @borrow_days, 
                                demand_reason = @demand_reason, 
                                retrieve_reason = @retrieve_reason, 
                                modify_user = @modify_user,
                                modify_time = @modify_time
                            WHERE paper_lend_id = @paper_lend_id AND demand_id = @demand_id";
            return this.ExecuteCommandToTransaction(sql, context, data) > 0;
        }
        #endregion

        #region 獲取借出明細
        /// <summary>
        /// 獲取借出明細
        /// </summary>
        /// <param name="paper_lend_id"></param>
        /// <param name="context"></param>
        /// <returns></returns>
        public List<paper_lending_detail> GetPaperLendingDetailToTransaction(int paper_lend_id, IDbContext context)
        {
            string sql = $@"SELECT pld.detail_id
                                    ,pld.paper_lend_id
                                    ,pld.paper_basic_id
                                    ,pld.paper_demand_id
                                    ,vg.contract_number
                                    ,vg.contract_name
                                    ,pld.paper_code
                                    ,pld.paper_name
                                    ,pld.is_return
                                    ,pld.should_return_time
                                    ,pld.actual_return_time
                                    ,pld.receive_status
                                    ,pld.pickup_status
                                    ,pld.pickup_time
                                    ,pld.consignment_number
                                    ,pld.pickup_emplid
                                    ,pld.actual_pickup_time
                                    ,pld.overdue_day
                                    ,pld.actual_borrow_days
                                    ,pld.loan_due_date
                                    ,pld.create_user
                                    ,pld.create_time
                                    ,pld.modify_user
                                    ,pld.modify_time
                                    ,pld.is_pickup_lend
                                    ,pld.lend_return_status 
                            FROM paper_lending_detail pld
                            LEFT JOIN paper_basic_data pbd ON pld.paper_basic_id = pbd.basic_id
							LEFT JOIN paper_application_data pad ON pad.application_id = pbd.paper_applica_id
                            LEFT JOIN V_GetApplicationSearch vg ON vg.apply_number = pad.apply_number
                            WHERE pld.paper_lend_id = @paper_lend_id";
            return this.NpgsqlSearchByListToTransaction<paper_lending_detail>(sql, context, new { paper_lend_id });
        }
        #endregion

        #region 刪除明細
        /// <summary>
        /// 刪除明細
        /// </summary>
        /// <param name="paper_lend_id"></param>
        /// <param name="paper_demand_id"></param>
        /// <param name="detail_id"></param>
        /// <param name="context"></param>
        /// <returns></returns>
        public bool DeletePaperLendingDetailToTransaction(int paper_lend_id, int paper_demand_id, int detail_id, IDbContext context)
        {
            string sql = $@"DELETE FROM paper_lending_detail WHERE paper_lend_id = @paper_lend_id AND paper_demand_id = @paper_demand_id AND detail_id = @detail_id";

            return this.ExecuteCommandToTransaction(sql, context, new { paper_lend_id, paper_demand_id, detail_id }) > 0;
        }
        #endregion

        #region 獲取舊數據
        /// <summary>
        /// 獲取舊數據
        /// </summary>
        /// <param name="paper_lend_id"></param>
        /// <param name="paper_demand_id"></param>
        /// <param name="detail_id"></param>
        /// <param name="context"></param>
        /// <returns></returns>
        public paper_lending_detail GetPaperLendingDetailToTransaction(int paper_lend_id, int paper_demand_id, int detail_id, IDbContext context)
        {
            string sql = $@"SELECT * FROM paper_lending_detail WHERE paper_lend_id = @paper_lend_id AND paper_demand_id = @paper_demand_id AND detail_id = @detail_id";
            return NpgsqlSearchBySingleToTransaction<paper_lending_detail>(sql, context, new { paper_lend_id, paper_demand_id, detail_id });
        }
        #endregion

        #region 修改借出明細
        /// <summary>
        /// 修改借出明細
        /// </summary>
        /// <param name="data"></param>
        /// <param name="context"></param>
        /// <returns></returns>
        public bool UpdatePaperLendingDetailToTransaction(paper_lending_detail data, IDbContext context)
        {
            string sql = $@"UPDATE paper_lending_detail 
                            SET 
                                paper_basic_id = @paper_basic_id, 
                                paper_demand_id = @paper_demand_id, 
                                contract_number = @contract_number, 
                                contract_name = @contract_name, 
                                paper_code = @paper_code, 
                                paper_name = @paper_name, 
                                is_return = @is_return, 
                                should_return_time = @should_return_time, 
                                receive_status = @receive_status, 
                                pickup_status = @pickup_status, 
                                pickup_time = @pickup_time, 
                                consignment_number = @consignment_number, 
                                pickup_emplid = @pickup_emplid, 
                                actual_pickup_time = @actual_pickup_time, 
                                overdue_day = @overdue_day, 
                                actual_borrow_days = @actual_borrow_days, 
                                loan_due_date = @loan_due_date, 
                                modify_user = @modify_user,
                                modify_time = @modify_time,
                                is_pickup_lend = @is_pickup_lend
                            WHERE paper_lend_id = @paper_lend_id AND paper_demand_id = @paper_demand_id AND detail_id = @detail_id";
            return this.ExecuteCommandToTransaction(sql, context, data) > 0;
        }
        #endregion

        #region 修改申請單為出借中
        /// <summary>
        /// 修改申請單為出借中
        /// </summary>
        /// <param name="lend_id"></param>
        /// <param name="lend_status"></param>
        /// <param name="context"></param>
        /// <returns></returns>
        public bool UpdateLendStatusToTransaction(int lend_id, string lend_status, IDbContext context)
        {
            string sql = $@"UPDATE paper_lending_application
                            SET 
                                lend_status = @lend_status
                            WHERE
                                lend_id = @lend_id;";
            return this.ExecuteCommandToTransaction(sql, context, new { lend_id, lend_status }) > 0;
        }
        #endregion

        #region 刪除借出單主表
        /// <summary>
        /// 刪除借出單主表
        /// </summary>
        /// <param name="lend_id"></param>
        /// <param name="context"></param>
        /// <returns></returns>
        public bool DeletePaperLendingApplicationToTransaction(int lend_id, IDbContext context)
        {
            string sql = $@"DELETE FROM paper_lending_application WHERE lend_id = @lend_id";
            return this.ExecuteCommandToTransaction(sql, context, new { lend_id }) > 0;
        }
        #endregion

        #region 刪除借出單調閱清單
        /// <summary>
        /// 刪除借出單調閱清單
        /// </summary>
        /// <param name="paper_lend_id"></param>
        /// <param name="context"></param>
        /// <returns></returns>
        public bool DeletePaperLendingDemandToTransaction(int paper_lend_id, IDbContext context)
        {
            string sql = $@"DELETE FROM paper_lending_demand WHERE paper_lend_id = @paper_lend_id";
            return this.ExecuteCommandToTransaction(sql, context, new { paper_lend_id }) > 0;
        }
        #endregion

        #region 刪除借出單明細
        /// <summary>
        /// 刪除借出單明細
        /// </summary>
        /// <param name="paper_lend_id"></param>
        /// <param name="context"></param>
        /// <returns></returns>
        public bool DeletePaperLendingDetailToTransaction(int paper_lend_id, IDbContext context)
        {
            string sql = $@"DELETE FROM paper_lending_detail WHERE paper_lend_id = @paper_lend_id";

            return this.ExecuteCommandToTransaction(sql, context, new { paper_lend_id }) > 0;
        }
        #endregion

        #region 修改主表為作廢
        /// <summary>
        /// 修改主表為作廢
        /// </summary>
        /// <param name="lend_id"></param>
        /// <param name="void_reason"></param>
        /// <param name="context"></param>
        /// <returns></returns>
        public bool VoidLendingToTransaction(int lend_id, string void_reason, IDbContext context)
        {
            string sql = $@"UPDATE paper_lending_application
                            SET 
                                lend_status = @lend_status,
                                void_reason = @void_reason
                            WHERE
                                lend_id = @lend_id;";
            return this.ExecuteCommandToTransaction(sql, context, new { lend_id, lend_status = "05", void_reason }) > 0;
        }
        #endregion

        #region 新增借出歷史明細(已結案和已作廢)
        /// <summary>
        /// 新增借出歷史明細(已結案和已作廢)
        /// </summary>
        /// <param name="data"></param>
        /// <param name="context"></param>
        /// <returns></returns>
        public bool InsertPaperLendingHistoryToTransaction(paper_lending_history data, IDbContext context)
        {
            string sql = @"
        INSERT INTO paper_lending_history (
            lend_number,
            lend_fill_emplid,
            lend_fill_deptid,
            lend_fill_bu,
            lend_fill_bg,
            lend_handler_emplid,
            lend_handler_deptid,
            lend_handler_bu,
            lend_handler_bg,
            application_time,
            lend_status,
            void_reason,
            retrieve_number,
            borrow_days,
            demand_reason,
            retrieve_reason,
            paper_basic_id,
            paper_code,
            paper_name,
            contract_number,
            contract_name,
            is_return,
            should_return_time,
            actual_return_time,
            pickup_status,
            pickup_time,
            consignment_number,
            pickup_emplid,
            actual_pickup_time,
            overdue_day,
            actual_borrow_days,
            loan_due_date,
            create_user,
            create_time,
            is_pickup_lend,
            lend_return_status 
        ) VALUES (
            @lend_number,
            @lend_fill_emplid,
            @lend_fill_deptid,
            @lend_fill_bu,
            @lend_fill_bg,
            @lend_handler_emplid,
            @lend_handler_deptid,
            @lend_handler_bu,
            @lend_handler_bg,
            @application_time,
            @lend_status,
            @void_reason,
            @retrieve_number,
            @borrow_days,
            @demand_reason,
            @retrieve_reason,
            @paper_basic_id,
            @paper_code,
            @paper_name,
            @contract_number,
            @contract_name,
            @is_return,
            @should_return_time,
            @actual_return_time,
            @pickup_status,
            @pickup_time,
            @consignment_number,
            @pickup_emplid,
            @actual_pickup_time,
            @overdue_day,
            @actual_borrow_days,
            @loan_due_date,
            @create_user,
            @create_time,
            @is_pickup_lend,
            @lend_return_status 
        )";
            return this.ExecuteCommandToTransaction(sql, context, data) > 0;
        }
        #endregion

        #endregion
    }
}
