﻿using System.Reflection;
using Elegal.Interface.Api.Common.Control.SetupService;
using Elegal.Interface.Api.Common.Model;
using Microsoft.AspNetCore.Http.Features;
using Microsoft.AspNetCore.Mvc.Formatters;
using Microsoft.AspNetCore.Server.Kestrel.Core;
using Minio;
using System.Text;
using Elegal.Interface.Api.Common.Control;
using Elegal.Interface.Api.Common.Services;

namespace Elegal.Flow.Api
{
    /// <summary>
    /// Startup
    /// </summary>
    public class Startup
    {
        /// <summary>
        /// Startup
        /// </summary>
        public Startup(IConfiguration config)
        {
            configuration = config;
        }

        /// <summary>
        /// Configuration
        /// </summary>
        private IConfiguration configuration { get; }

        /// <summary>
        /// ConfigureServices
        /// </summary>
        public void ConfigureServices(IServiceCollection services)
        {
            // 注册日志服务
            services.AddScoped<ILogService, LogService>();
            services.AddHttpContextAccessor();

            #region Mini注入

            MinIOConfig minIOConfig = new();
            configuration.Bind("MinIO", minIOConfig);
            services.AddMinio(configureClient => configureClient
                .WithEndpoint(minIOConfig.Endpoint)
                .WithCredentials(minIOConfig.AccessKey, minIOConfig.SecretKey));
            services.AddControllers();
            services.AddSingleton(minIOConfig);
            services.AddSingleton(sp =>
            {
                // 進行MinioClient的配置
                return (MinioClient)new MinioClient()
                    .WithEndpoint(minIOConfig.Endpoint)
                    .WithCredentials(minIOConfig.AccessKey, minIOConfig.SecretKey)
                    .WithSSL()
                    .Build();
            });

            #endregion

            #region 註冊PlainTextI

            //注册PlainTextI
            services.AddControllers(options =>
            {
                options.InputFormatters.Add(new PlainTextInputFormatter());
            });
            #endregion

            //設置請求體大小
            services.Configure<FormOptions>(x => x.MultipartBodyLengthLimit = 100 * 1024 * 1024);
            services.Configure<KestrelServerOptions>(options =>
            {
                options.Limits.MaxRequestBodySize = 100 * 1024 * 1024; // 设置最大请求体大小为100MB
            });

            SetupConfiguration.SetupConfigureServices(services, configuration);
            SetupSwagger.AddSwaggerSetup(services, "Flow");
        }

        /// <summary>
        /// Configure
        /// </summary>
        public void Configure(IApplicationBuilder app, IWebHostEnvironment env, IHttpContextAccessor accessor)
        {
            AppStaticServices.Initialize(app.ApplicationServices);
            SetupConfiguration.SetupConfigure(app, env, accessor);
        }
    }
}

/// <summary>
/// MCP簽核所需
/// </summary>
public sealed class PlainTextInputFormatter : TextInputFormatter
{
    public PlainTextInputFormatter()
    {
        SupportedMediaTypes.Add("text/plain");
        SupportedEncodings.Add(Encoding.UTF8);
    }
    public override async Task<InputFormatterResult> ReadRequestBodyAsync(InputFormatterContext context, Encoding encoding)
    {
        string content;
        using (var reader = context.ReaderFactory(context.HttpContext.Request.Body, encoding))
        {
            content = await reader.ReadToEndAsync();
        }
        return await InputFormatterResult.SuccessAsync(content);
    }
}