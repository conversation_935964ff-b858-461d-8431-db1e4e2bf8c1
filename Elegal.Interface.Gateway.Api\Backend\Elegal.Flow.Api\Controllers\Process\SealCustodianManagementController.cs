﻿using Elegal.Flow.Api.Services.Process;
using Elegal.Interface.Api.Common.Control.Filter;
using Elegal.Interface.Api.Common.Control.Web;
using Elegal.Interface.Api.Common.Model.ParaModel.FlowApi;
using Elegal.Interface.Api.Common.Model.ResultModel;
using Elegal.Interface.Api.Common.Model.ResultModel.FlowApi;
using Elegal.Interface.Api.Common.Model.ViewModel;
using Elegal.Interface.ApiData.Service.FuncService;
using Elegal.Interface.ApiData.Service.Model.QueryCondition;
using Elegal.Orm.Utils;
using Microsoft.AspNetCore.Mvc;
using System.Text;

namespace Elegal.Flow.Api.Controllers.Process
{

    /// <summary>
    /// 印鑑保管人
    /// </summary>
    [Route("[controller]")]
    [ApiController]
    public class SealCustodianManagementController : BaseController
    {
        /// <summary>
        /// 印鑑保管人管理頁面查詢
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [Route("GetDataList")]
        public async Task<ApiResultModelByObject> GetDataList([FromBody] SealCustodianSearchModel sealCustodian)
        {
            ApiResultModelByObject apiResult = new();
            UserInfoModel user = MvcContext.UserInfo;

            apiResult.listData = SealCustodianManagementService.QuerySealCustodian(sealCustodian, user.logging_locale, user.time_zone);
            apiResult.rtnSuccess = true;
            return await Task.FromResult(apiResult);
        }

        /// <summary>
        /// 印鑑保管人管理頁面新增数据
        /// </summary>
        /// <param name="model">新增参数</param>
        /// <returns></returns>
        [HttpPost]
        [Route("AddData")]
        public async Task<ApiResultModelByObject> AddData([FromBody] SealCustodianSearchModel model)
        {
            model.CreateUser = MvcContext.UserInfo.current_emp;
            return await Task.FromResult(SealCustodianManagementService.AddData(model));
        }

        /// <summary>
        /// 印鑑保管人管理頁面刪除数据
        /// </summary>
        /// <param name="model"> 刪除数据</param>
        /// <returns></returns>
        [HttpPut]
        [Route("DeleteData")]
        public async Task<ApiResultModelByObject> DeleteData([FromBody] SealCustodianResult model)
        {
            return await Task.FromResult(SealCustodianManagementService.DeleteData(model));
        }

        /// <summary>
        /// 获取用印下拉数据源
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("GetSealSelectData")]
        public async Task<ApiResultModelByObject> GetSealSelectData()
        {
            ApiResultModelByObject apiResult = new();
            SysParametersQueryCondition sysParametersQueryCondition = new SysParametersQueryCondition()
            {
                LangType = MvcContext.UserInfo.logging_locale,
                ParaCode = "sealType",
                OrderBys = new List<Orm.Dtos.OrderByParam>()
                {
                    new Orm.Dtos.OrderByParam(){Field="SortOrder",Order=Orm.Dtos.OrderBy.ASC}
                }
            };
            apiResult.listData = SysParametersDataService.Query(sysParametersQueryCondition);
            apiResult.rtnSuccess = true;

            return await Task.FromResult(apiResult);
        }

        /// <summary>
        /// 列表数据导出日志记录
        /// </summary>
        /// <param name="sealCustodian"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("ExportLog")]
        public async Task<ApiResultModelByObject> ExportLog(SealCustodianSearchModel sealCustodian)
        {
            ApiResultModelByObject apiResult = new ApiResultModelByObject()
            {
                listData = true,
                rtnSuccess = true,
            };
            InitLogRecord(log =>
            {
                StringBuilder stringBuilder = new StringBuilder();
                if (sealCustodian.seal_types != null && sealCustodian.seal_types.Count > 0)
                    stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("DirectorsSupervisorsManagement_titletype", true)}：{string.Join(",", sealCustodian.seal_types.Select(kvp => kvp.Value))}");
                if (!string.IsNullOrEmpty(sealCustodian.area.Value))
                    stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("SealCustodianManagement_area", true)}：{sealCustodian.area.Value}");
                if (!string.IsNullOrEmpty(sealCustodian.entity.Value))
                    stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("SealCustodianManagement_fnpEntity", true)}：{sealCustodian.entity.Value}");
                if (!string.IsNullOrEmpty(sealCustodian.emp_id))
                    stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("SealCustodianManagement_name", true)}：{sealCustodian.emp_id}");
                if (sealCustodian.filterList.Any())
                {
                    stringBuilder.AppendLine(CommonUtil.GetDivisionLine(ActionFilter.GetMultilingualValue("SealCustodianManagement_filter", true)));
                    stringBuilder.AppendLine(string.Join("\r\n", sealCustodian.filterList.Select(s => $"{ActionFilter.GetMultilingualValue($"SealCustodianManagement_export:{Nomenclature.ToCamel(s.Key)}", true)}：{s.Value}")));
                }
                log.Detail = stringBuilder.ToString();
            });
            return await Task.FromResult(apiResult);
        }
    }
}
