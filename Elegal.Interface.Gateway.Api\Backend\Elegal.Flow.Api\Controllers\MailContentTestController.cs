﻿using Elegal.Flow.Common.Services;
using Elegal.Flow.Common.Services.FlowStep;
using Elegal.Flow.Common.Services.OtherApply;
using Elegal.Interface.Api.Common.Model.Enum;
using Elegal.Interface.Api.Common.Model.ViewModel.FlowApi.OtherApply;
using Elegal.Interface.ApiData.Service.Model.DbModel;
using Microsoft.AspNetCore.Mvc;
using System.ComponentModel;

namespace Elegal.Flow.Api.Controllers
{
    public class _MailContentTestController : ControllerBase
    {
        [HttpGet]
        [Route("GetMailContent")]
        public UserEmailContent GetMailContent([FromQuery] string apply_number)
        {
            return SendMailService.GetMailContent(apply_number, MailTypeUtils.C1, "Wits.EricHuang", 105);
        }

        [HttpGet]
        [Route("SendMail")]
        public void SendMail([FromQuery] string apply_number)
        {
            List<MailTypeUtils> typeList = new List<MailTypeUtils>();
            if (apply_number.StartsWith("O"))
            {
                OtherApplyViewModel other_Application = OtherApplyService.GetOtherApplication(apply_number);
                if (other_Application.otherApplication.form_type == "A")
                    typeList = new List<MailTypeUtils>() { MailTypeUtils.OA };
                else if (other_Application.otherApplication.form_type == "B")
                    typeList = new List<MailTypeUtils>() { MailTypeUtils.OB };
                else if (other_Application.otherApplication.form_type == "C")
                    typeList = new List<MailTypeUtils>() { MailTypeUtils.OC };
                else if (other_Application.otherApplication.form_type == "D")
                    typeList = new List<MailTypeUtils>() { MailTypeUtils.OD };
                else if (other_Application.otherApplication.form_type == "E")
                    typeList = new List<MailTypeUtils>() { MailTypeUtils.OE };
                else if (other_Application.otherApplication.form_type == "F")
                    typeList = new List<MailTypeUtils>() { MailTypeUtils.OF };
                else
                    typeList = new List<MailTypeUtils>();
            }

            SendMailService.SendMail("O", apply_number, "Wits.EricHuang", 508, null, typeList);
        }

        [HttpGet]
        [Route("SendLendingMail")]
        public void SendLendingMail([FromQuery] string apply_number)
        {
            List<MailTypeUtils> typeList = new List<MailTypeUtils>();

            typeList.Add(MailTypeUtils.LD);
            typeList.Add(MailTypeUtils.LN);
            typeList.Add(MailTypeUtils.LM);

            SendMailService.SendLendingMail(apply_number, typeList);
        }
    }
}
