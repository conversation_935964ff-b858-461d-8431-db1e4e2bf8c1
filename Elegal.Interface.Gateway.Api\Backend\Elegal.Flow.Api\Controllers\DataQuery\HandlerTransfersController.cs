using Elegal.Flow.Api.Services.DataQuery;
using Elegal.Interface.Api.Common.Control.Filter;
using Elegal.Interface.Api.Common.Control.Web;
using Elegal.Interface.Api.Common.FuncHelper;
using Elegal.Interface.Api.Common.Model.Enum;
using Elegal.Interface.Api.Common.Model.ParaModel.FlowApi;
using Elegal.Interface.Api.Common.Model.ResultModel;
using Elegal.Interface.Api.Common.Model.ResultModel.FlowApi;
using Elegal.Interface.Api.Common.Model.ViewModel;
using Elegal.Interface.Api.Common.Model.ViewModel.PermissionApi.Home;
using Elegal.Interface.ApiData.Service.FuncService;
using Elegal.Interface.ApiData.Service.Model.Condition;
using Elegal.Interface.ApiData.Service.Model.DbModel;
using Elegal.Orm.Utils;
using MathNet.Numerics;
using Microsoft.AspNetCore.Mvc;
using System.Text;

namespace Elegal.Flow.Api.Controllers.DataQuery
{

    /// <summary>
    /// 经办人转单
    /// </summary>
    [Route("[controller]")]
    [ApiController]
    public class HandlerTransfersController : BaseController
    {
        /// <summary>
        /// 列表数据
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("ChildListData")]
        public async Task<ApiResultModelByObject> ChildListData(string transfer_id)
        {
            UserInfoModel user = MvcContext.UserInfo;

            var listAllData = HandlerTransfersService.ChildListData(transfer_id);
            List<ApplyDetail> list = new();
            foreach (ApplyDetail resultModel in listAllData)
            {
                //極機密案件且當前案件狀態為已核准狀態需要進行隱碼處理
                if (resultModel.confiden_level.ToUpper() == "01".ToUpper() && (resultModel.application_state.ToUpper() != "I".ToUpper() && resultModel.application_state.ToUpper() != "T".ToUpper()))
                {
                    ApplyDetail applyDetail = HiddenValueConvertHelper.ConvertToHiddenBySingle<ApplyDetail>(resultModel);
                    list.Add(applyDetail);
                }
                else { list.Add(resultModel); }
            }
            ApiResultModelByObject apiResult = new()
            {
                listData = listAllData,
                rtnSuccess = true
            };
            return await Task.FromResult(apiResult);
        }

        /// <summary>
        /// 根據轉單單號查詢數據
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("GetDataByApply")]
        public async Task<ApiResultModelByObject> GetDataByApply(string transfer_pic_number)
        {
            UserInfoModel user = MvcContext.UserInfo;

            HandlerTransferResultModel listAllData = HandlerTransfersService.GetDataByApply(transfer_pic_number);
            List<ApplyDetail> list = new();
            foreach (ApplyDetail resultModel in listAllData.applyDetail)
            {
                //極機密案件且當前案件狀態為已核准狀態需要進行隱碼處理
                if (resultModel.confiden_level.ToUpper() == "01".ToUpper() && (resultModel.application_state.ToUpper() != "I".ToUpper() && resultModel.application_state.ToUpper() != "T".ToUpper()))
                {
                    ApplyDetail applyDetail = HiddenValueConvertHelper.ConvertToHiddenBySingle<ApplyDetail>(resultModel);
                    list.Add(applyDetail);
                }
                else { list.Add(resultModel); }
            }
            listAllData.applyDetail = list;
            ApiResultModelByObject apiResult = new()
            {
                listData = listAllData,
                rtnSuccess = true
            };
            ActionFilter.InitLogRecord(transfer_pic_number, log =>
            {
                StringBuilder stringBuilder = new();
                if (!string.IsNullOrEmpty(transfer_pic_number))
                    stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("HandlerTransfersManagement_applyNumber", true)}：{transfer_pic_number}");
                log.Detail = stringBuilder.ToString();
            });
            return await Task.FromResult(apiResult);
        }

        /// <summary>
        /// 列表数据
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [Route("ListData")]
        public async Task<ApiResultModelByObject> ListData([FromBody] TransferSearchModel model)
        {
            UserInfoModel user = MvcContext.UserInfo;
            if (model == null) {
                return new ApiResultModelByObject()
                {
                    messageType = MessageTypeUtils.Warning.ToString(),
                    messageTitle = ActionFilter.GetMultilingualValue("custom:messageTitle:systemError"),
                    rtnSuccess = false
                };
            }
            var listAllData = HandlerTransfersService.ListData(model);
            ApiResultModelByObject apiResult = new()
            {
                listData = listAllData,
                rtnSuccess = true
            };
            ActionFilter.InitLogRecord(model, log =>
            {
                StringBuilder stringBuilder = new();
                if (!string.IsNullOrEmpty(model.apply_number))
                    stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("HandlerTransfersManagement_applyNumber", true)}：{model.apply_number}");
                if (model.emp != null)
                    stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("HandlerTransfersManagement_emp", true)}：{model.emp.NameA}/{model.emp.Name}({model.emp.Emplid})");
                log.Detail = stringBuilder.ToString();
            });
            return await Task.FromResult(apiResult);
        }

        /// <summary>
        /// 查詢原經辦部門數據
        /// </summary>
        /// <param name="emplid">工号</param>
        /// <returns></returns>
        [HttpGet]
        [Route("DeptList")]
        public async Task<ApiResultModelByObject> DeptList(string emplid)
        {
            ApiResultModelByObject apiResult = new()
            {
                listData = HandlerTransfersService.GetFormerDept(emplid),
                rtnSuccess = true
            };
            return await Task.FromResult(apiResult);
        }

        /// <summary>
        /// 案件清单
        /// </summary>
        /// <param name="emplid">工号</param>
        /// <param name="deptid">部門</param>
        /// <param name="transfer_pic_number">轉單單號</param>
        /// <returns></returns>
        [HttpGet]
        [Route("CaseData")]
        public async Task<ApiResultModelByObject> CaseData(string emplid,string deptid,string transfer_pic_number = "")
        {
            var listAllData = HandlerTransfersService.CaseData(emplid, deptid, transfer_pic_number);
            List<ApplyDetail> list = new();
            foreach (ApplyDetail model in listAllData)
            {
                //極機密案件且當前案件狀態為已核准狀態需要進行隱碼處理
                if (model.confiden_level.ToUpper() == "01".ToUpper() && (model.application_state.ToUpper() != "I".ToUpper() || model.application_state.ToUpper() != "T".ToUpper()))
                {
                    list.Add(HiddenValueConvertHelper.ConvertToHiddenBySingle<ApplyDetail>(model));
                }
                else { list.Add(model); }
            }
            ApiResultModelByObject apiResult = new()
            {
                listData = listAllData,
                rtnSuccess = true
            };
            return await Task.FromResult(apiResult);
        }

        /// <summary>
        /// 转单提交
        /// </summary>
        /// <param name="model">新增数据</param>
        /// <returns></returns>
        [HttpPut]
        [Route("Submit")]
        public async Task<ApiResultModelByObject> Submit([FromBody] ModifyHandlerTransferModel model)
        {
            //转单业务
            model.create_user = MvcContext.UserInfo.current_emp;
            ApiResultModelByObject apiResult = HandlerTransfersService.Submit(model);
            return await Task.FromResult(apiResult);
        }

        /// <summary>
        /// Approve
        /// </summary>
        /// <param name="model">審批/提交</param>
        /// <returns></returns>
        [HttpPut]
        [Route("Approve")]
        public async Task<ApiResultModelByObject> Approve([FromBody]ModifyHandlerTransferModel model)
        {
            //转单业务
            model.create_user = MvcContext.UserInfo.current_emp;
            ApiResultModelByObject apiResult = HandlerTransfersService.Approve(model);
            return await Task.FromResult(apiResult);
        }

        /// <summary>
        /// Reject
        /// </summary>
        /// <param name="model">駁回</param>
        /// <returns></returns>
        [HttpPut]
        [Route("Reject")]
        public async Task<ApiResultModelByObject> Reject([FromBody] ModifyHandlerTransferModel model)
        {
            model.create_user = MvcContext.UserInfo.current_emp;
            ApiResultModelByObject apiResult = HandlerTransfersService.Reject(model);
            return await Task.FromResult(apiResult);
        }

        /// <summary>
        /// Cancellation
        /// </summary>
        /// <param name="model">作廢</param>
        /// <returns></returns>
        [HttpPut]
        [Route("Cancellation")]
        public async Task<ApiResultModelByObject> Cancellation([FromBody] ModifyHandlerTransferModel model)
        {
            //转单业务
            model.create_user = MvcContext.UserInfo.current_emp;
            ApiResultModelByObject apiResult = HandlerTransfersService.Cancellation(model);
            return await Task.FromResult(apiResult);
        }

        /// <summary>
        /// WithDraw
        /// </summary>
        /// <param name="model">撤回</param>
        /// <returns></returns>
        [HttpPut]
        [Route("WithDraw")]
        public async Task<ApiResultModelByObject> WithDraw([FromBody] ModifyHandlerTransferModel model)
        {
            //转单业务
            model.create_user = MvcContext.UserInfo.current_emp;
            ApiResultModelByObject apiResult = HandlerTransfersService.WithDraw(model);
            return await Task.FromResult(apiResult);
        }



        /// <summary>
        /// 汇出记录日志
        /// </summary>
        /// <param name="model">新增数据</param>
        /// <returns></returns>
        [HttpPost]
        [Route("ExportLog")]
        public async Task<ApiResultModelByObject> ExportLog([FromBody] TransferSearchModel model)
        {
            ApiResultModelByObject apiResult = new();
            if (model == null) {
                apiResult.messageTitle = ActionFilter.GetMultilingualValue("custom:messageTitle:exportError");
                apiResult.messageType = MessageTypeUtils.Warning.ToString();
                apiResult.rtnSuccess = false;
                return apiResult;
            }
            var listAllData = HandlerTransfersService.QueryExportList(model.transfer_pic_numbers);
            apiResult.listData = listAllData;
            ActionFilter.InitLogRecord(model, log =>
            {
                StringBuilder stringBuilder = new();
                if (!string.IsNullOrEmpty(model.apply_number))
                    stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("HandlerTransfersManagement_applyNumber", true)}：{model.apply_number}");
                if (model.emp != null)
                    stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("HandlerTransfersManagement_emp", true)}：{model.emp.NameA}/{model.emp.Name}({model.emp.Emplid})");
                log.Detail = stringBuilder.ToString();
            });
            apiResult.rtnSuccess = true;
            return await Task.FromResult(apiResult);
        }
    }
}
