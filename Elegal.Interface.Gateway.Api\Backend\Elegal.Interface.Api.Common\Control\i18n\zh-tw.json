{
  "error": {
    "400": "",
    "401": "未授權，請登錄",
    "403": "拒絕訪問",
    "404": "請求資源出錯",
    "405": "HTTP方法不被允許",
    "408": "請求超時",
    "500": "服務器內部錯誤",
    "501": "服務未實現",
    "502": "網關錯誤",
    "503": "服務不可用",
    "504": "網關超時",
    "505": "HTTP版本不受支持",
    "601": "郵件發送失敗",
    "602": "IT測試帳號"
  },
  "commonWord": {
    "information": "提示訊息",
    "add": "新增",
    "all": "全部",
    "addedAgentDepartment": "以下部門已新增代理人",
    "existingAgentDepartment": "以下部門已授權其他人員",
    "repeat": "重複資料",
    "incumbent_1": "在職",
    "incumbent_0": "離職",
    "fileName": "文件名",
    "DepartmentCode": "部門代碼",
    "DepartmentAndCompany": "部門公司別",
    "retrieve": "調閱",
    "count": "共 {0} 筆",
    "status_1": "啟用",
    "status_0": "停用",
    "newCase": "新案件",
    "oldCase": "舊案件",
    "isReturn": "已被Return",
    "isReject": "已被Reject",
    "isInvitee": "加簽中",
    "isAcknowledge": "會簽中"
  },
  "custom": {
    "messageTitle": {
      "fileExist": "已存在該檔案用途文件", //已存在该档案用途文件
      "uploadSuccess": "上傳成功",
      "systemError": "系統錯誤",
      "doNotUpdate": "不允許修改",
      "doNotDelete": "不允許刪除",
      "createSuccess": "新增成功",
      "createFail": "新增失敗",
      "updateFail": "修改失敗",
      "dataExists": "資料已經存在",
      "dataNotexist": "資料不存在",
      "dataUsed": "資料已被使用",
      "fileNotexist": "文件不存在",
      "enableFail": "主體啟用失敗",
      "fileUploadFail": "文件上傳失敗",
      "fileDeleteFail": "文件刪除失敗",
      "fileDownloadFail": "文件下載失敗",
      "selectData": "請勾選數據", //請勾選數據
      "approveFail": "審批失敗",
      "editFail": "審批失敗",
      "toPdfFail": "轉檔失敗",
      "contractNumberError": "無法生成唯一的 contract_number",
      "previewFail": "預覽失敗",
      "noAuthorizedRecipient": "非有權收件者",
      "exportError": "匯出異常"
    },
    "messageContent": {
      "repeatUpload": "檔案【{0}】已存在，請重新上傳！",
      "fileUploadFiltered": "文件上傳已完成，系統已過濾以下空文件",
      "fileUpload": "文件上傳已完成",
      "validateError": "參數校驗轉換錯誤，請聯絡系統管理員(請查看首頁的承辦窗口 - 聯絡資訊)",
      "notFindByDB": "檔案【{0}】資料不存在",
      "notToPdf": "檔案【{0}】不支援線上閱讀，請確認是否為壓縮檔或檔案受密碼保護",
      "notToPdfAndWatermark": "檔案【{0}】不支援下載浮水印檔",
      "selectDownloadFiles": "請選擇要下載的文件",
      "selectDeleteFiles": "請選擇要删除的文件",
      "fileSizeMax": "檔案大小超過總限制，最大可上傳80MB",
      "repeatFiles": "以下文件重複上传，是否選擇覆盖？",
      "dataExists": "重複資料：{0}",
      "searchreQuired": "查詢條件必填:{0}",
      "repeatAll": "項目新增失敗，所選資料已存在",
      "noTemplateFile": "您正在試圖下載不存在的文件",
      "repeatMessage": "以下資料已存在，請重新輸入",
      "dataChurn": "此筆資料可能已經發生變動，建議檢查資料更新時間或重新載入頁面，以獲取資料最新狀態",
      "dataUsed": "該資料已被使用，不允許刪除",
      "AgentsExist": "所選部門已授權其他人員，無法再次授權！",
      "mailContentEmpty": "郵件模板缺失，請聯繫管理人員！",
      "approverLevelNotEdit": "該主體已停用，簽核層級不可修改",
      "roleNameNotEdit": "此角色的'角色名稱'不可修改",
      "roleNotAdd": "該角色名稱不允許新增",
      "levelRange": "簽核層級範圍[-1,9]",
      "endLessStart": "起始簽核層級不可小於最高簽核層級",
      "notZero": "簽核層級不可為0",
      "noRecordDaysSetting": "未找到系統記錄保留天數配置信息",
      "mailFail": "郵件發送失敗",
      "mailSuccess": "郵件發送成功",
      "publicRoleNameExists": "公版角色名稱：{0}",
      "systemError": "錯誤原因：{0}",
      "RoleNameExists": "角色名稱已存在，請重新輸入。",
      "createSuccess": "已成功新增項目，系統已自動過濾已存在資料",
      "modifySuccess": "已成功修改項目，系統已自動過濾異動資料",
      "keepOne": "管理員角色至少保留一個人員",
      "keepOneContract": "以下合約理員角色至少保留一個人員,{0}",
      "roleDeleted": "角色已被刪除",
      "nullArea": "區域不允許為空",
      "empidIsEmpty": "工號為空",
      "keepOneApproverManagement": "同一主體下的總部法務行政必須至少有一人",
      "entityDeleted": "主體已被刪除",
      "contractDeleted": "合約性質已被刪除",
      "contractViewSelectType2": "自訂主體，請選擇主體數據",
      "approverManagementDeleted": "關卡已被刪除",
      "directorsSupervisorsDeleted": "董監事職稱已被刪除",
      "sealCustodianDeleted": "用印類型已被刪除",
      "accountTypeDeleted": "類別已被刪除",
      "doNotDeleteRole": "內置角色不允許刪除",
      "doNotUpdateRole": "內置角色不允許修改名稱", //內置角色不允許修改名称
      "StartTimeLargerThanEndTime": "結束時間應該大於開始時間",
      "AgentExist": "此部門已授權其他人員，無法再次授權！",
      "uploadMessageTitle": "文件上傳異常",
      "uploadMessageContent": "文件上傳異常請重試",
      "downloadMessageTitle": "文件下載異常",
      "downloadMessageContent": "文件下載異常請重試",
      "bulletinsNewSubjectLength": "公告主題需小於200個字",
      "bulletinsTabCannotModify": "印信保管(蓋印)窗口(01)不可被修改",
      "bulletinsTabColumnLength": "欄位超長中文15字，英文30字",
      "globalContentMaxLength": "全局提示標題不應超過 {0} 字",
      "webSuspendStartDate": "請輸入開始日期！",
      "webSuspendStartTime": "請輸入開始時間！",
      "webSuspendCloseMessage": "請輸入關站提示語！",
      "webSuspendGlobalTitle": "請輸入全局提示標題！",
      "webSuspendEndDateTime": "請將結束日期和結束時間輸入完整！",
      "entityEnableMessage": "請先設置總部法務行政人員",
      "contractManagerMessage": "請設置合約管理員至少為一人",
      "areaNameExist": "區域名稱已存在，請重新輸入",
      "areaUsed": "所選區域已有設定主體資訊，請確認！",
      "shutdownContentMaxLength": "關站提示語不應超過 {0} 字",
      "shutdownNowDateLargerStartDate": "關站開始時間不能小於當前時間！",
      "shutdownHasValue": "還有待公告/待執行設定，請等待執行完成後再新增作業！",
      "cancelSuspendStatus": "該筆關站數據狀態已變動，請刷新頁面！",
      "keepOneApprover": "主體：{0} 在總部法務行政必須至少有一人",
      "sameUnderAgent": "項目：{0} 承辦人和代理人不能為同一個人",
      "sameUnderItem": "項目：{0} 有相同數據",
      "sameDirectorsSupervisors": "職稱：{0} ；主體：{1} 存在相同數據",
      "sameSealCustodianManage": "用印種類：{0} ；主體：{1} 存在相同數據",
      "areaDataNotExists": "區域數據不存在",
      "entityNotInNews": "公告中不存在該主體數據",
      "paraRequired": "請檢查是否有選擇查詢類型以及輸入關鍵字！",
      "paraMaxLength": "參數中/英文不得超過 {0} 字！",
      "nullPara": "參數中/英文名稱必填！",
      "nullParaValue": "請填寫參數值！",
      "paraValueMaxLength": "參數值中/英文不得超過 {0} 字！",
      "emptyParaValue": "參數值中/英文名稱必填！",
      "sameParaValue": "參數值：{0} 已存在！",
      "bulletinsNewTime": "公告起迄開始、結束時間需大於或等於公告日期",
      "nikeNameFileError": "請確認文檔是否為空文件或文件中存在特定欄位(Code Name)，如不確定，可參考範例文件！",
      "nikeMaxWord": "暱稱：{0} 超過最大長度值( {1} 字)，請修改！",
      "nikeMissPara": "必要參數缺失，請聯繫開發人員！",
      "empRole": "角色：{0} 可能已經發生變動，建議重新載入頁面，以獲取該人員最新角色信息",
      "empApprove": "關卡：{0} 可能已經發生變動，建議重新載入頁面，以獲取該人員最新關卡信息",
      "empUndertak": "項目：{0} 可能已經發生變動，建議重新載入頁面，以獲取該人員最新承辦窗口信息",
      "empSpecialEntity": "主體：{0} 可能已經發生變動，建議重新載入頁面，以獲取該人員最新主體信息",
      //Issue：309
      "deleteApproveType": "關卡：{0}，存在未完成單號 {1}，請確認是否仍需刪除。",
      "deleteApproveEntityID": "關卡：{0}；主體：{1}，存在未完成單號 {2}，請全部完成後再進行刪除作業！",
      "empDirectorsSupervisors": "職稱：{0} ；主體：{1} 可能已經發生變動，建議重新載入頁面，以獲取該人員最新職稱信息",
      "empSealCustodianManage": "用印種類：{0} ；主體：{1} 可能已經發生變動，建議重新載入頁面，以獲取該人員最新用印種類信息",
      "authLoginError_AuthCodeMissed": "使用者: {0} 未設置授權碼",
      "authLoginError_AuthCodeError": "授權碼錯誤",
      "authLoginError_AuthCodeDisable": "授權碼未啟用",
      "scheduleJobNameExist": "自動化批次排程任務名重複",
      "AutoTimeEarly": "設置時間不能早於07:20",
      "AutoWeeklyJob": "每週作業的作業運行間隔無效",
      "AutoMonthlyJob": "每月作業的作業運行間隔無效",
      "AutoInvaildType": "作業運行類型無效",
      "authLoginError_EmplidMissed": "取得人事資料失敗",
      "authLoginError_EmplidTermination": "人員已離職",
      "actualCompanyHavaBeenBound": "存在實際公司代碼已經綁定[{0}]主體",
      "RemoveCompanyCode": "將移除當前主體的授權公司代碼({0})",
      "RemoveSpecialEntityUser": "移除特殊主體人員設定({0})",
      "RemoveSpecialEntityDept": "移除特殊主體部門設定({0})",
      "RemoveSpecialEntityApply": "特殊主體關聯申請單({0})",
      //SIT Issue:485
      "nofeSendMail": "主體郵件通知被停用",
      //UAT Issue:165  Start
      "entityIsUsedOtherEntity": "並將移除以下主體的授權公司代碼({0})，共{1}筆",
      "entityIsUsedOnlyOtherEntity": "以下賦予啟用主體公司代碼僅有({0})將無法移除，共{1}筆，請確認",
      //UAT Issue:165  End
      "getPaperDataFail": "當前單號不存在於資料表中，請確認後再操作",
      "paperLending": "當前單號借出中，不可修改",
      "batchPaperWork_LostOrDestroyed": "預約中、出借中或已銷毀單號不可修改",
      "batchPaperWork_LostOrDestroyed_ApplyNumber": "以下單號為預約中、出借中或已銷毀單號，不可修改 : {0}",
      "OldPaperData_repeatData": "已成功新增{0}個紙本資料，系統已自動過濾已存在資料 : {1}",
      "OldPaperData_repeat": "以下紙本系統中已存在，不可新增 : {0}",
      "LendingError": "以下紙本狀態非已入庫，不可出借: ",
      "Inbound_Status": "入庫狀態: ",
      "Storage_Location": "存放位置: ",
      "lendReturnCheck": "歸還資料可能已經發生變動，建議重新載入頁面，以獲取最新數據",
      "paperDeletedError": "已有借出申請單的紙本不可刪除",
      "paperModifiedError": "已有借出申請單的紙本不可修改紙本編號",
      "TempApplicationExist": "暫存單已存在",
      "ApplicationChanged": "紙本資料有異動，請再次確認",
      "AuthCodeNull": "授權碼不可為空",
      "AuthCodeError": "授權碼長度為8~20，且必須是英文大寫、小寫、數字、特殊符號中的3種組合!",
      //SIT：604 -> 更改提示信息
      "LoginError": "提醒：正在執行HR資料更新，請稍後重試。",
      "RemoveFnpEntityUser": "已移除當前主體的特殊主體設定-人員設定: ",
      "RemoveFnpEntityDept": "已移除當前主體的特殊主體設定-部門設定: ",
      "SelectData": "請選擇數據後操作",
      "Form_FileNoExist": "請至少上傳一份合約及附件後再提交",
      "FileNoExist": "請至少上傳一個附檔後再提交",
      "FileNoExist_References": "請至少上傳一個參考資料後再提交",
      "FormEnd_Validate_Date": "請填寫確認生效日/確認簽約日/確認到期日",
      "FormEnd_Validate_ArchiveStatus": "請填寫正本歸檔狀態",
      "FormEnd_Validate_File": "請至少上傳一份文件至歸檔區",
      "FormEnd_Validate_Paper": "請建立紙本清單",
      "FormEnd_Validate_ApplyNumber": "請確認申請單號是否存在",
      "FormEnd_Validate_ContractNumber": "執行此操作前請必填「合約管理作業」之【合約編號】欄位",
      "FormEnd_Validate_Same_ContractNumber": "「合約管理作業」之【合約編號】欄位存在相同資訊",
      "RefNumberError": "找不到REF主約對應的主體合約編號",
      "BeforeEntityConsistency": "修改前主體簡稱不可與主體簡稱一致",
      "StepSignerNull": "[{0}]關卡未設置關卡人員，請聯絡系統管理員(請查看首頁的承辦窗口 - 聯絡資訊)",
      //Issue：367 -> 簽核人員提示信息修改
      "SignerIsNull": " [{0}]關卡未設置關卡人員，請聯絡系統管理員(請查看首頁的承辦窗口 - 聯絡資訊)",
      "StepSignerError": "簽核關卡錯誤",
      "McpSigning": "已由MCP簽核",
      "Bypass_CEO": "因 {0}，需後送總經理關卡",
      "Bypass_President": "因 {0}，需後送董事長關卡",
      "EntityDifferent": "主體簡稱與主約編號不一致",
      "EntityExistApplication": "主體存在申請單，{0}",
      "NoApproveAuth": "無單據審批權限",
      "ApplyInProcess": "申請單在流程中",
      "OtherFormTypeError": "其他申請單類型錯誤",
      "OtherInProcess": "需求案件 {0} 已有進行中的 {1} 申請單 {2}，請再次確認。",
      "TypeBNoData": "指定的條件查無資料，請重新選擇",
      "StartDate": "起日",
      "EndDate": "迄日",
      "StartEndDate": "起訖日",
      "ActualDateError": "實際開放期間({0})小於當前日期，系統自動改為結案日期",
      "AddPaperApplicationError": "申請單已確認無紙本，新增失敗。",
      "NoToPdfWatermark": "所選檔案無法轉檔加水印",
      "repeatPaper": "紙本重複，請重新填寫",
      "SigntoryNotChange": "簽核人員未發送改變",
      "SigntoryCurrentSignerLessOne": "當關待審人員少於一人",
      "StepStartNotAddSinger": "申請/經辦人階段不能增加被加簽人員",
      "noAuthorizedRecipient": "此人員非有權收件者",
      "readError": "讀取信息失敗，請嘗試重新操作或聯係管理員。",
      "builtinRoleNotUpdate": "内置角色不允許修改名稱",
      "CeoOrPresidentStepNull": "原 {0} 關卡沒有設置關卡人員，現系統確認該關卡已設置 {1}，請確定要簽核至 {0} 關卡嗎？",
      //Issue：92
      "CeoOrPresidentStepNullByNoPass": "原 {0} 關卡沒有設置關卡人員",
      "HanderNotSamePic": "交接人、經辦人/現任聯絡人不能為同一人",
      "EntityExits": "主體公司簡稱重複，請重新輸入！",
      "UnderTakeWindowAgentExits": "代理人：{0} {1}；承辦窗口聯絡人：{2} {3}不能是同一人",
      "UnderTakeWindowDataExits": "項目：{0}；承辦窗口聯絡人：{1} {2}",
      //Issue：500
      "InvateNotSign": "仍有加簽人員未簽核！",
      //Issue：200
      "supplementary_reason": "因Return後符合會簽規則，加簽相關主管以利知悉本案：{0}",
      "hasInviteeSigner": "尚有加簽人員名單，請先至【invite】確認是否加簽",
      //在線預覽相關
      "againLater": "本次轉檔失敗，請稍後重試",
      //附件下載
      "fileNull": "文件（{0}）不存在",
      //Issue：381
      "MCPApprovalInProgress": "MCP簽核中",
      //SIT：638
      "InviterIsNull": "請選擇加簽人員"
    }
  },
  "export": {
    "lendId": "借出單號",
    "accessNumber": "其他申請單號",
    "filler": "填單人",
    "handler": "經辦人",
    "applyDate": "申請日期",
    "applyStatus": "申請單狀態",
    "contractNum": "合約編號",
    "paperId": "紙本編號",
    "paperName": "紙本名稱",
    "inboundStatus": "入庫狀態",
    "copiesStatus": "紙本現狀",
    "saveLocation": "存放位置",
    "maxBorrowDay": "借閱日上限",
    "shouldReturnDate": "應歸還日期",
    "returnDate": "歸還日期",
    "pickUp": "是否已取件",
    "returnStatus": "歸還狀態",
    "overdueDays": "逾期天數",
    "returnPaperStatus": "歸還現狀",
    "hasPickUp": "已取件",
    "noPickUp": "未取件",
    "hasReturn": "已歸還",
    "noReturn": "未歸還",
    "export_fnpEntityUser": {
      "employeeId": "員工工號",
      "name": "員工姓名",
      "employeeStatus": "任職狀態",
      "companyCodeDept": "部門公司別/部門代碼",
      "deptEntity": "部門所屬主體",
      "custodianCompany": "託管人員公司別",
      "custodyDeptEntity": "託管部門主體",
      "specialEntitys": "特殊主體",
      "updateTime": "更新時間"
    },
    "export_fnpEntityDept": {
      "deptid": "部門代號",
      "descr": "部門名稱",
      "ogCompany": "部門公司別",
      "ogEntity": "部門所屬主體",
      "hostedCompany": "託管人員公司別",
      "hostedEntity": "託管部門主體",
      "specialEntitys": "特殊主體",
      "updateTime": "更新時間"
    },
    "export_ExportPaperBasicData": {
      "paper_code": "紙本編號",
      "contract_number": "合約編號",
      "paper_name": "紙本名稱",
      "paper_entry_status_name": "入庫狀態",
      "paper_position": "存放位置",
      "paper_return_status_name": "紙本現狀",
      "my_entity": "我方主體",
      "party_a": "他方主體",
      "paper_type_name": "紙本類型",
      "confiden_level_name": "機密等級"
    }
  },
  "ByPassReason": {
    "Evacuation": "當前關卡簽核人將於總經理/董事長關卡做簽核，故System by pass此關卡",
    "Signed": "當前關卡簽核人已於前關卡簽核，故System by pass此關卡",
    "EqualAgent": "當前關卡簽核人等於申請人/經辦人，故System by pass此關卡",
    "EqualPresident": "當前關卡簽核人將於董事長關卡做簽核，故System by pass此關卡",
    "Same": "因 {0} 關卡人員相同滑關後送 {1}",
    "Assign": "依核決權限表需要後送 {0}",
    "None": "系統判斷無需後送",
    "SiteGM": "因非所屬主體且無SiteGM需要後送 {0}",
    "NoneCeo": "依核決權限表需要後送 總經理，但主體關卡人員未設定故後送 董事長",
    //Issue：283
    "NoneCeoByPresident": "依核決權限表需要後送 總經理>董事長，但主體關卡人員未設定故後送 董事長"
  },
  //項目中其他需要多語言清單匯總
  "otherLang": {
    //檔案附件
    "fileLang": {
      "ArchivePurposes1": "合約及附件",
      "ArchivePurposes2": "參考資料",
      "ArchivePurposes3": "歸檔"
    },
    //紙本進度追蹤
    "paperTrackingBathJob": {
      "paperTracking01": "通知經辦，提供紙本給行政",
      "paperTracking02": "通知法務，找行政領紙本",
      "paperTracking05": "通知經辦，已送簽",
      "paperTracking07": "通知經辦，找行政自取",
      "paperTracking09": "通知經辦，已託運",
      "paperTracking00": "無需發送郵件通知"
    }
  },
  //XML文檔多語言匯總
  "XmlLang": {
    //XML頭部
    "xmlTitles": {
      "applicationType": "流程類別",
      "stepName": "當前關卡名稱",
      "normalSigner": "目前待審人員",
      "inviteSigner": "被加簽人員",
      "applyInfo": "申請單位資訊",
      "financeInfo": "財務資訊",
      "undertakeInfo": "總部承辦法務資訊",
      "undfinanceInfo": "承辦財務資訊",
      "acknowledgeInfo": "會簽類型",
      "legalAdminInfo": "總部法務行政資訊",
      "originalSignerInfo": "授權正本簽署人",
      "inviterInfo": "加簽人資訊",
      "flowHistoryInfo": "簽核歷程",
      "inviteeInfo": "指定加簽人員名單",
      "otherACDCaseInfo": "案件",
      "otherEEntityInfo": "申請主體",
      "otherFEntityInfo": "特殊主體權限",
      "otherInterPersonInfo": "必要揭露人員名單 - 內部同仁",
      "otherOuterPersonInfo": "其他人員"
    },
    //XML區塊
    "xmlBlock": {
      "applyNumber": "申請單號",
      "picEmplid": "經辦人",
      "picDeptid": "經辦人 部門代碼",
      "myEntity": "我方主體",
      "otherParty": "他方(含簡稱)",
      "isSchool": "他方為學校、研究或學術機構",
      "contractName": "合約/文件名稱",
      "contractBgRemark": "背景摘要",
      "confidenLevel": "機密等級",
      "havingMoney": "金額",
      "exchangeAmount": "換算台幣(參考匯率：{0})",
      "accountType": "應收/付款",
      "accountDeptid": "掛帳部門",
      "isPrForm": "是否有PMCS已核准之PR單",
      "prNo": "PR單號",
      "prDeptid": "PR Charge Department",
      "prType": "PR Type",
      "prRemark": "PR Remark / Purchase Reason",
      "contractType": "合約性質",
      "fileCategory": "文件類別",
      "contractObj": "標的物",
      "isBg": "簽核當下權利義務是否與其他已知BG相關",
      "bgDeptid": "BG部門",
      "undertakeRemark": "總部承辦法務備註",
      "hasStampDuty": "是否有印花稅",
      "taxCurrency": "稅種幣別",
      "taxType": "稅種",
      "taxRate": "稅率",
      "estimateTaxAmount": "估計稅金金額",
      "undfinanceRemark": "承辦財務備註",
      "acknowledgeType": "會簽類型",
      "originaStatus": "正本簽署人授權方式",
      "finalSignLevel": "最終簽署人簽核關卡",
      "byPassReason": "後送判斷原因",
      "originalRemark": "建議指定原因",
      "docType": "調閱類型",
      "hasDownload": "特殊下載權限",
      "applyReason": "申請原因",
      "applyDate": "申請開放期間",
      "otherBSearchPara": "清單查詢條件",
      "otherBSearchList": "清單查詢列表",
      "otherBSearchCount": "清單查詢條件資料筆數",
      "otherBExportPara": "清單匯出需求欄位",
      "oldPaperStatus": "原合約紙本狀態"
    },
    //XML表格
    "xmlTable": {
      //簽核歷程
      "flowHistoryTable": {
        "stepName": "關卡名",
        "shouldSignerName": "應簽核人",
        "signerName": "實際簽核人",
        "signerResult": "簽核結果",
        "signerTime": "簽核時間",
        "signerComment": "簽核意見"
      },
      //加簽人員資訊
      "inviterTable": {
        "inviterEmplid": "工號",
        "inviterEName": "英文全名",
        "inviterDeptCode": "部門代碼",
        "inviterRemark": "加簽原因"
      },
      //其他申請案件清單列表
      "otherACDCaseTable": {
        "serialNumber": "序號",
        "applyNumber": "申請單號",
        "myEntity": "我方",
        "otherParty": "他方",
        "contractName": "合約(文件)名稱",
        "picDeptid": "經辦人部門代碼",
        "contractNumber": "合約編號",
        "confidenLevel": "機密等級"
      },
      //其他申請主體清單列表
      "otherEFEntityTable": {
        "serialNumber": "序號",
        "entity": "主體簡稱",
        "entityName": "主體名稱"
      },
      //其他申請必要揭露人員列表
      "otherInterPersonTable": {
        "empNo": "工號",
        "empEName": "英文全名",
        "deptCode": "部門代碼",
        "deptCompany": "部門公司別"
      }
    },
    //XML其他
    "xmlOther": {
      "Yes": "是",
      "No": "否",
      "otherTitle": "其他類申請",
      "formTitle": "合約申請",
      "inviteeEmp": "加簽人員",
      "entityIsLegal": "(此主體將啟動法定代表人簽核關卡)",
      "oldData": "舊資料",
      "originSuperTypeReturn": "因本合約需多位指定人員簽署，若需授權其他簽署人請return法務行政協調",
      "originalComment": "指定正本簽署主管，若選擇“其他”，務必輸入“姓名”後送出申請單",
      "originalType": "授權正本簽署人",
      "other": "其他",
      "commentOther": "完整調閱清單請至eLegal查看。",
      "invitee": "加簽",
      "acknowledge": "會簽",
      "picWithdraw": "經辦人撤回",
      "inviteMessage": "若要進行人員加簽，則於加簽人員欄位指定特定人員後，請務必選擇“invite”後送出申請單。"
    },
    //XML附件處理
    "xmlArchivePurposes": {
      "1": "合約及附件",
      "2": "參考資料",
      "3": "歸檔",
      "4": "法務行政交接備註檔案附件"
    }
  },
  "ApplicationPageType": {
    //"0": "任意頁面",
    "0": "未结案",
    "1": "待處理案件--當關案件",
    "2": "待處理案件--代理案件",
    "3": "待處理案件--被加簽案件",
    "4": "未结案",
    "5": "O類申請單調閱案件檢視"
  }
}