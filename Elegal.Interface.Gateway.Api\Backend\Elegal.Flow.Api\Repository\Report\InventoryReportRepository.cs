﻿using Elegal.Interface.Api.Common.Control.Filter;
using Elegal.Interface.Api.Common.FuncHelper;
using Elegal.Interface.Api.Common.Model.ParaModel.FlowApi.Report;
using Elegal.Interface.Api.Common.Repository;
using Elegal.Interface.ApiData.Service.Model.QueryCondition;
using Elegal.Interface.ApiData.Service.Repository;
using Elegal.Orm;
using Elegal.Orm.Dtos;

namespace Elegal.Flow.Api.Repository.Report
{
    /// <summary>
    /// 纸本盘点统计表
    /// </summary>
    public class InventoryReportRepository : BaseRepository
    {

        private readonly string unionSql = @"(select * from(select
                                    entity_id,entity,entity_namec,entity_namee,status,area_id
                                from
                                    fnp_entity
                                union 
                                select
                                    aff_company_code entity_id,
                                    aff_company_abb entity,
                                    aff_company_cname entity_namec,
                                    aff_company_ename entity_namee,
                                    case when aff_status = '1' then 0 else 1 end status,
                                    null area_id
                                from
                                    affiliate_company)tt)";

        #region 获取纸本盘点统计数据
        /// <summary>
        /// 区域维度无纸本类型
        /// </summary>
        /// <param name="condition"></param>
        /// <returns></returns>
        public IEnumerable<object> GetInventoryReportDataAreaNoType(InventoryReportQueryCondition condition)
        {
            string sql = $@"select
	                            area.area_id AreaId,
	                            area.area_name AreaName,
	                            basic.paper_entry_status PaperEntryStatus
                            from paper_basic_data basic--筛选条件：机密等级、入库状态、纸本类型、纸本位置
                            inner join paper_application_data application on basic.paper_applica_id = application.application_id--筛选条件：合约编号、主体
                            left join V_GetUnConfirmedApplication vg on	vg.apply_number = application.apply_number--筛选条件：归档日期筛选
                            inner join {unionSql} entity on entity.entity_id = application.my_entity_id
                            left join sys_area area on area.area_id = entity.area_id
                            left join sys_parameters paper_entry on basic.paper_entry_status = paper_entry.func_code 
                            and paper_entry.lang_type='{MvcContext.UserInfo.logging_locale}' and paper_entry.para_code = 'lib_paperEntryStatus'
                            where 1=1 ";
            SearchItemGroup searchItemGroup = GetBaseSearchQuery(condition);
            searchItemGroup.Items.Add(new SearchItem() { Field = "area.area_id", Compare = CompareOperator.IN, Value = string.Join(",", condition.Area.Select(s => s.Key)), Logic = LogicOperator.And });
            sql += Util.GetSearchItemGroup(searchItemGroup);
            sql = $@"select a.area_id AreaId,a.area_name AreaName,{GetPaperEntryStatusNameListString()} from sys_area a
                        left join (select AreaId,AreaName,{GetPaperEntryStatusListString()} from ({sql}) as sourceTable
                        pivot (
                        count(PaperEntryStatus) for PaperEntryStatus in({GetPaperEntryStatusListString()})
                        ) as unpivotedTable) b
                        on a.area_id = b.AreaId
                        where {Util.GetSearchItemSql(new SearchItem() { Field = "a.area_id", Compare = CompareOperator.IN, Value = string.Join(",", condition.Area.Select(s => s.Key)) })}
                        order by AreaName";
            return DbAccess.Database.SqlQuery<object>(sql);
        }

        /// <summary>
        /// 主体维度无纸本类型
        /// </summary>
        /// <param name="condition"></param>
        /// <returns></returns>
        internal IEnumerable<object> GetInventoryReportDataEntityNoType(InventoryReportQueryCondition condition)
        {
            string sql = $@"select
		                        area.area_id AreaId,
		                        area.area_name AreaName,
	                            entity.entity_id EntityId,
	                            entity.entity Entity,
	                            basic.paper_entry_status PaperEntryStatus
                            from paper_basic_data basic--筛选条件：机密等级、入库状态、纸本类型、纸本位置
                            inner join paper_application_data application on	basic.paper_applica_id = application.application_id--筛选条件：合约编号、主体
                            left join V_GetUnConfirmedApplication vg on	vg.apply_number = application.apply_number--筛选条件：归档日期筛选
                            inner join {unionSql} entity on entity.entity_id = application.my_entity_id
                            left join sys_area area on area.area_id = entity.area_id
                            left join sys_parameters paper_entry on basic.paper_entry_status = paper_entry.func_code 
                            and paper_entry.lang_type='{MvcContext.UserInfo.logging_locale}' and paper_entry.para_code = 'lib_paperEntryStatus'
                            where 1=1 ";
            SearchItemGroup searchItemGroup = GetBaseSearchQuery(condition);
            SearchItem searchItem = new();
            //自选主体
            if (condition.Entity.Any() && !(condition.Entity.Count.Equals(1) && condition.Entity[0].Key.Equals("-1")))
            {
                searchItemGroup.Items.Add(new SearchItem() { Field = "application.my_entity_id", Compare = CompareOperator.IN, Value = string.Join(",", condition.Entity.Select(s => s.Key)), Logic = LogicOperator.And });
                searchItem = new SearchItem() { Field = "a.entity_id", Compare = CompareOperator.IN, Value = string.Join(",", condition.Entity.Select(s => s.Key)) };
            }
            //全部主体
            else if (condition.Area.Any())
            {
                searchItemGroup.Items.Add(new SearchItem() { Field = "area.area_id", Compare = CompareOperator.IN, Value = string.Join(",", condition.Area.Select(s => s.Key)), Logic = LogicOperator.And });
                searchItem = new SearchItem() { Field = "c.area_id", Compare = CompareOperator.IN, Value = string.Join(",", condition.Area.Select(s => s.Key)) };
            }
            sql += Util.GetSearchItemGroup(searchItemGroup);
            sql = $@"select c.area_id AreaId, c.area_name AreaName,a.entity_id EntityId,a.entity Entity,{GetPaperEntryStatusNameListString()} from {unionSql} a
                        left join (select AreaId,AreaName,EntityId,Entity,{GetPaperEntryStatusListString()} from ({sql}) as sourceTable
                        pivot (
                        count(PaperEntryStatus) for PaperEntryStatus in({GetPaperEntryStatusListString()})
                        ) as unpivotedTable) b
                        on a.entity_id = b.EntityId
                        left join sys_area c on a.area_id = c.area_id
                        where {Util.GetSearchItemSql(searchItem)}
                        order by AreaName,Entity";
            return DbAccess.Database.SqlQuery<object>(sql);
        }

        /// <summary>
        /// 区域维度有纸本类型
        /// </summary>
        /// <param name="condition"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        internal IEnumerable<object> GetInventoryReportDataAreaType(InventoryReportQueryCondition condition)
        {
            string sql = $@"select
	                            area.area_id AreaId,
	                            area.area_name AreaName,
	                            basic.paper_type PaperType,
	                            paper_type.fun_name PaperTypeName,
	                            basic.paper_entry_status PaperEntryStatus
                            from paper_basic_data basic--筛选条件：机密等级、入库状态、纸本类型、纸本位置
                            left join paper_application_data application on	basic.paper_applica_id = application.application_id--筛选条件：合约编号、主体
                            left join V_GetUnConfirmedApplication vg on	vg.apply_number = application.apply_number--筛选条件：归档日期筛选
                            inner join {unionSql} entity on entity.entity_id = application.my_entity_id
                            left join sys_area area on area.area_id = entity.area_id
                            left join sys_parameters paper_entry on basic.paper_entry_status = paper_entry.func_code 
                            and paper_entry.lang_type='{MvcContext.UserInfo.logging_locale}' and paper_entry.para_code = 'lib_paperEntryStatus'
                            left join sys_parameters paper_type on basic.paper_type = paper_type.func_code 
                            and paper_type.lang_type='{MvcContext.UserInfo.logging_locale}' and paper_type.para_code = 'lib_paperType'
                            where 1=1 ";
            SearchItemGroup searchItemGroup = GetBaseSearchQuery(condition);
            searchItemGroup.Items.Add(new SearchItem() { Field = "area.area_id", Compare = CompareOperator.IN, Value = string.Join(",", condition.Area.Select(s => s.Key)), Logic = LogicOperator.And });
            searchItemGroup.Items.Add(new SearchItem() { Field = "basic.paper_type", Compare = CompareOperator.IN, Value = string.Join(",", condition.PaperType.Select(s => s.Key)), Logic = LogicOperator.And });
            sql += Util.GetSearchItemGroup(searchItemGroup);
            searchItemGroup = new() { Logic = LogicOperator.And, Items = new List<SearchItem>() };
            searchItemGroup.Items.Add(new SearchItem() { Field = "a.AreaId", Compare = CompareOperator.IN, Value = string.Join(",", condition.Area.Select(s => s.Key)), Logic = LogicOperator.And });
            searchItemGroup.Items.Add(new SearchItem() { Field = "a.PaperType", Compare = CompareOperator.IN, Value = string.Join(",", condition.PaperType.Select(s => s.Key)), Logic = LogicOperator.And });
            string leftSql = $@"select
	                                sa.area_id AreaId,
	                                sa.area_name AreaName,
	                                sp.func_code PaperType,
	                                sp.fun_name PaperTypeName,
	                                sp.sort_order PaperTypeOrder
                                from
	                                sys_area sa
                                cross join (
	                                select
		                                func_code,
		                                fun_name,
		                                sort_order
	                                from
		                                sys_parameters
	                                where
		                                para_code = N'lib_paperType'
		                                and lang_type = N'{MvcContext.UserInfo.logging_locale}') sp";
            sql = $@"select a.AreaId,a.AreaName,a.PaperType,a.PaperTypeName,{GetPaperEntryStatusNameListString()} 
                        from ({leftSql}) a
                        left join (select AreaId,AreaName,PaperType,PaperTypeName,{GetPaperEntryStatusListString()} from ({sql}) as sourceTable
                        pivot (
                        count(PaperEntryStatus) for PaperEntryStatus in({GetPaperEntryStatusListString()})
                        ) as unpivotedTable) b
                        on a.AreaId=b.AreaId and a.PaperType=b.PaperType
                        where 1=1 {Util.GetSearchItemGroup(searchItemGroup)}
                        order by AreaName,a.PaperTypeOrder";
            return DbAccess.Database.SqlQuery<object>(sql);
        }

        /// <summary>
        /// 主体维度有纸本类型
        /// </summary>
        /// <param name="condition"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        internal IEnumerable<object> GetInventoryReportDataEntityType(InventoryReportQueryCondition condition)
        {
            string sql = $@"select area.area_id AreaId,area.area_name AreaName,entity.entity_id EntityId,entity.entity Entity,
                                basic.paper_type PaperType,paper_type.fun_name PaperTypeName,basic.paper_entry_status PaperEntryStatus
                            from paper_basic_data basic--筛选条件：机密等级、入库状态、纸本类型、纸本位置
                            inner join paper_application_data application on	basic.paper_applica_id = application.application_id--筛选条件：合约编号、主体
                            left join V_GetUnConfirmedApplication vg on	vg.apply_number = application.apply_number--筛选条件：归档日期筛选
                            inner join {unionSql} entity on entity.entity_id = application.my_entity_id
                            left join sys_area area on area.area_id = entity.area_id
                            left join sys_parameters paper_entry on basic.paper_entry_status = paper_entry.func_code 
                            and paper_entry.lang_type='{MvcContext.UserInfo.logging_locale}' and paper_entry.para_code = 'lib_paperEntryStatus'
                            left join sys_parameters paper_type on basic.paper_type = paper_type.func_code 
                            and paper_type.lang_type='{MvcContext.UserInfo.logging_locale}' and paper_type.para_code = 'lib_paperType'
                            where 1=1";
            SearchItemGroup searchItemGroup = GetBaseSearchQuery(condition);
            searchItemGroup.Items.Add(new SearchItem() { Field = "basic.paper_type", Compare = CompareOperator.IN, Value = string.Join(",", condition.PaperType.Select(s => s.Key)), Logic = LogicOperator.And });
            SearchItem searchItem = new();
            //自选主体
            if (condition.Entity.Any() && !(condition.Entity.Count.Equals(1) && condition.Entity[0].Key.Equals("-1")))
            {
                searchItemGroup.Items.Add(new SearchItem() { Field = "application.my_entity_id", Compare = CompareOperator.IN, Value = string.Join(",", condition.Entity.Select(s => s.Key)), Logic = LogicOperator.And });
                searchItem = new SearchItem() { Field = "a.EntityId", Compare = CompareOperator.IN, Value = string.Join(",", condition.Entity.Select(s => s.Key)), Logic = LogicOperator.And };
            }
            //全部主体
            else if (condition.Area.Any())
            {
                searchItemGroup.Items.Add(new SearchItem() { Field = "area.area_id", Compare = CompareOperator.IN, Value = string.Join(",", condition.Area.Select(s => s.Key)), Logic = LogicOperator.And });
                searchItem = new SearchItem() { Field = "a.AreaId", Compare = CompareOperator.IN, Value = string.Join(",", condition.Area.Select(s => s.Key)), Logic = LogicOperator.And };
            }
            sql += Util.GetSearchItemGroup(searchItemGroup);
            searchItemGroup = new() { Logic = LogicOperator.And, Items = new List<SearchItem>() };
            searchItemGroup.Items.Add(searchItem);
            searchItemGroup.Items.Add(new SearchItem() { Field = "a.PaperType", Compare = CompareOperator.IN, Value = string.Join(",", condition.PaperType.Select(s => s.Key)), Logic = LogicOperator.And });
            string leftSql = $@"select fe.area_id AreaId,fe.entity_id EntityId,fe.entity Entity,sp.func_code PaperType,sp.fun_name PaperTypeName,sp.sort_order PaperTypeOrder from {unionSql} fe
                                cross join(select func_code,fun_name,sort_order from sys_parameters where para_code = N'lib_paperType' and lang_type = N'{MvcContext.UserInfo.logging_locale}') sp";
            sql = $@"select c.area_id AreaId,c.area_name AreaName,a.EntityId,a.Entity,a.PaperType,a.PaperTypeName,
                        {GetPaperEntryStatusNameListString()}
                        from ({leftSql}) a
                        left join (select AreaId,AreaName,EntityId,Entity,PaperType,PaperTypeName,{GetPaperEntryStatusListString()} 
                                    from ({sql}) as sourceTable
                        pivot (count(PaperEntryStatus) for PaperEntryStatus in({GetPaperEntryStatusListString()})) as unpivotedTable) b
                        on a.EntityId = b.EntityId and a.PaperType = b.PaperType
                        left join sys_area c on a.AreaId = c.area_id
                        where 1=1 {Util.GetSearchItemGroup(searchItemGroup)}
                        order by AreaName,Entity,PaperTypeOrder";
            return DbAccess.Database.SqlQuery<object>(sql);
        }

        /// <summary>
        /// 基础查询条件
        /// </summary>
        /// <param name="condition"></param>
        /// <returns></returns>
        private static SearchItemGroup GetBaseSearchQuery(InventoryReportQueryCondition condition)
        {
            SearchItemGroup searchItemGroup = new() { Logic = LogicOperator.And, Items = new List<SearchItem>() };
            if (condition.FiledDateStart.HasValue)
                searchItemGroup.Items.Add(new SearchItem() { Field = "vg.filed_date", Compare = CompareOperator.GE, Value = TimeZoneInfoConvertHelper.ConvertDateByTimeZoneByUtc(condition.FiledDateStart, MvcContext.UserInfo.time_zone).ToString("yyyy/MM/dd HH:mm:ss"), Logic = LogicOperator.And });
            if (condition.FiledDateEnd.HasValue)
                searchItemGroup.Items.Add(new SearchItem() { Field = "vg.filed_date", Compare = CompareOperator.LE, Value = TimeZoneInfoConvertHelper.ConvertDateByTimeZoneByUtc(condition.FiledDateEnd, MvcContext.UserInfo.time_zone).ToString("yyyy/MM/dd HH:mm:ss"), Logic = LogicOperator.And });
            if (condition.PaperConfidenLevel.Any()) searchItemGroup.Items.Add(new SearchItem() { Field = "basic.paper_confiden_level", Compare = CompareOperator.IN, Value = string.Join(",", condition.PaperConfidenLevel.Select(s => s.Key)), Logic = LogicOperator.And });
            if (!string.IsNullOrEmpty(condition.ContractNumberStart)) searchItemGroup.Items.Add(new SearchItem() { Field = "vg.contract_number", Logic = LogicOperator.And, Compare = CompareOperator.GE, Value = condition.ContractNumberStart });
            if (!string.IsNullOrEmpty(condition.ContractNumberEnd)) searchItemGroup.Items.Add(new SearchItem() { Field = "vg.contract_number", Logic = LogicOperator.And, Compare = CompareOperator.LE, Value = condition.ContractNumberEnd });
            if (!string.IsNullOrEmpty(condition.PaperPositionStart)) searchItemGroup.Items.Add(new SearchItem() { Field = "basic.paper_position", Logic = LogicOperator.And, Compare = CompareOperator.GE, Value = condition.PaperPositionStart });
            if (!string.IsNullOrEmpty(condition.PaperPositionEnd)) searchItemGroup.Items.Add(new SearchItem() { Field = "basic.paper_position", Logic = LogicOperator.And, Compare = CompareOperator.LE, Value = condition.PaperPositionEnd });
            return searchItemGroup;
        }

        /// <summary>
        /// 纸本入库状态
        /// </summary>
        /// <returns></returns>
        private static string GetPaperEntryStatusListString()
        {
            SysParametersDataRepository repository = new SysParametersDataRepository();
            var status = repository.Query(new SysParametersQueryCondition()
            {
                ParaCode = "lib_paperEntryStatus",
                LangType = MvcContext.UserInfo.logging_locale,
                OrderBys = new List<OrderByParam>() { new OrderByParam() { Field = "sort_order", Order = OrderBy.ASC } }
            }).Select(s => $"[{s.FuncCode}]");
            return string.Join(',', status);
        }

        /// <summary>
        /// 纸本入库状态名称
        /// </summary>
        /// <returns></returns>
        private static string GetPaperEntryStatusNameListString()
        {
            SysParametersDataRepository repository = new SysParametersDataRepository();
            var status = repository.Query(new SysParametersQueryCondition()
            {
                ParaCode = "lib_paperEntryStatus",
                LangType = MvcContext.UserInfo.logging_locale,
                OrderBys = new List<OrderByParam>() { new OrderByParam() { Field = "sort_order", Order = OrderBy.ASC } }
            }).Select(s => $"isnull([{s.FuncCode}],0) as 'lib_paperEntryStatus_{s.FuncCode}'");
            return string.Join(',', status);
        }
        #endregion

        /// <summary>
        /// 查询详情数据
        /// </summary>
        /// <param name="condition"></param>
        /// <returns></returns>
        public IEnumerable<object> GetDetailListData(DetailListCondition condition)
        {
            string sql = $@"select
	                            basic.paper_code PaperCode,
	                            basic.paper_name PaperName,
	                            vg.contract_number ContractNumber,
                                area.area_name AreaName,
	                            entity.entity EntityName,
	                            basic.paper_type PaperType,
	                            (select fun_name from sys_parameters where para_code = 'lib_paperType' and func_code=basic.paper_type and lang_type='{MvcContext.UserInfo.logging_locale}') PaperTypeName,
	                            basic.paper_confiden_level PaperConfidenLevel,
	                            (select fun_name from sys_parameters where para_code = 'confidentStatus' and func_code=basic.paper_confiden_level and lang_type='{MvcContext.UserInfo.logging_locale}') PaperConfidenLevelName,
	                            basic.paper_entry_status,
	                            (select fun_name from sys_parameters where para_code = 'lib_paperEntryStatus' and func_code=basic.paper_entry_status and lang_type='{MvcContext.UserInfo.logging_locale}') PaperEntryStatusName,
	                            basic.paper_position
                            from paper_basic_data basic--筛选条件：机密等级、入库状态、纸本类型、纸本位置
                            left join paper_application_data application on	basic.paper_applica_id = application.application_id--筛选条件：合约编号、主体
                            left join V_GetUnConfirmedApplication vg on vg.apply_number = application.apply_number--筛选条件：归档日期筛选
                            inner join {unionSql} entity on entity.entity_id = application.my_entity_id
                            left join sys_area area on area.area_id = entity.area_id
                            where 1=1 ";
            SearchItemGroup searchItemGroup = new() { Logic = LogicOperator.And, Items = new List<SearchItem>() };
            if (condition.FiledDateStart.HasValue)
                searchItemGroup.Items.Add(new SearchItem() { Field = "vg.filed_date", Compare = CompareOperator.GE, Value = condition.FiledDateStart.ToString("yyyy/MM/dd HH:mm:ss"), Logic = LogicOperator.And });
            if (condition.FiledDateEnd.HasValue)
                searchItemGroup.Items.Add(new SearchItem() { Field = "vg.filed_date", Compare = CompareOperator.LE, Value = condition.FiledDateEnd.ToString("yyyy/MM/dd HH:mm:ss"), Logic = LogicOperator.And });
            if (condition.PaperConfidenLevel.Any()) searchItemGroup.Items.Add(new SearchItem() { Field = "basic.paper_confiden_level", Compare = CompareOperator.IN, Value = string.Join(",", condition.PaperConfidenLevel), Logic = LogicOperator.And });
            searchItemGroup.Items.Add(new SearchItem() { Field = "basic.paper_entry_status", Compare = CompareOperator.IN, Value = string.Join(",", condition.PaperEntryStatus), Logic = LogicOperator.And });
            if (condition.PaperType.Any()) searchItemGroup.Items.Add(new SearchItem() { Field = "basic.paper_type", Compare = CompareOperator.IN, Value = string.Join(",", condition.PaperType), Logic = LogicOperator.And });
            if (!string.IsNullOrEmpty(condition.ContractNumberStart)) searchItemGroup.Items.Add(new SearchItem() { Field = "vg.contract_number", Logic = LogicOperator.And, Compare = CompareOperator.GE, Value = condition.ContractNumberStart });
            if (!string.IsNullOrEmpty(condition.ContractNumberEnd)) searchItemGroup.Items.Add(new SearchItem() { Field = "vg.contract_number", Logic = LogicOperator.And, Compare = CompareOperator.LE, Value = condition.ContractNumberEnd });
            if (!string.IsNullOrEmpty(condition.PaperPositionStart)) searchItemGroup.Items.Add(new SearchItem() { Field = "basic.paper_position", Logic = LogicOperator.And, Compare = CompareOperator.GE, Value = condition.PaperPositionStart });
            if (!string.IsNullOrEmpty(condition.PaperPositionEnd)) searchItemGroup.Items.Add(new SearchItem() { Field = "basic.paper_position", Logic = LogicOperator.And, Compare = CompareOperator.LE, Value = condition.PaperPositionEnd });
            switch (condition.Dimension)
            {
                case "area":
                    searchItemGroup.Items.Add(new SearchItem() { Field = "area.area_id", Compare = CompareOperator.IN, Value = string.Join(",", condition.AreaId), Logic = LogicOperator.And });
                    break;
                case "entity":
                    searchItemGroup.Items.Add(new SearchItem() { Field = "entity.entity_id", Compare = CompareOperator.IN, Value = string.Join(",", condition.EntityId), Logic = LogicOperator.And });
                    break;
            }
            sql += Util.GetSearchItemGroup(searchItemGroup);
            sql += "order by basic.paper_code";
            return DbAccess.Database.SqlQuery<object>(sql);
        }
    }
}
