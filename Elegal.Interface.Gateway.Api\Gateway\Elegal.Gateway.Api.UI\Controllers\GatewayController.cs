using Elegal.Gateway.Api.UI.Application.ActionFilters;
using Elegal.Gateway.Api.UI.Application.ExceptionFilters;
using Elegal.Gateway.Api.UI.Application.ResultFilters;
using Elegal.Gateway.Api.UI.Authorization;
using Elegal.Interface.Api.Common.FuncHelper;
using Elegal.Interface.Api.Common.Model.ResultModel;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.SignalR.Client;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Primitives;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.IdentityModel.Tokens.Jwt;
using System.Linq;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Threading.Tasks;
using System.Web;

namespace Elegal.Gateway.Api.UI.Controllers
{
    [Route("[controller]")]
    [ApiController]
    [ServiceFilter(typeof(GatewayAuthorizeAttribute))]
    [ServiceFilter(typeof(GatewayAsyncActionFilterAttribute))]
    [ServiceFilter(typeof(GatewayAsyncExceptionFilterAttribute))]
    [ServiceFilter(typeof(GatewayAsyncResultFilterAttribute))]

    public class GatewayController : ControllerBase
    {
        readonly double Timeout = 30; // 30 seconds - reasonable timeout for API gateway
        readonly IApiOrchestrator _apiOrchestrator;
        readonly ILogger<ApiGatewayLog> _logger;
        readonly IHttpService _httpService;


        public GatewayController(IApiOrchestrator apiOrchestrator, ILogger<ApiGatewayLog> logger, IHttpService httpService)
        {
            _apiOrchestrator = apiOrchestrator;
            _logger = logger;
            _httpService = httpService;
        }

        [HttpGetOrHead]
        [Route("{api}/{key}")]
        [ServiceFilter(typeof(GatewayGetOrHeadAuthorizeAttribute))]
        [ServiceFilter(typeof(GatewayGetOrHeadAsyncActionFilterAttribute))]
        [ServiceFilter(typeof(GatewayGetOrHeadAsyncExceptionFilterAttribute))]
        [ServiceFilter(typeof(GatewayGetOrHeadAsyncResultFilterAttribute))]
        public async Task<IActionResult> Get(string api, string key, string parameters = null)
        {
            parameters = parameters != null ? HttpUtility.UrlDecode(parameters) : string.Empty;

            _logger.LogApiInfo(api, key, parameters);

            var apiInfo = _apiOrchestrator.GetApi(api, true);

            var gwRouteInfo = apiInfo.Mediator.GetRoute(key);

            var routeInfo = gwRouteInfo.Route;

            var (isValid, errorMsg) = await validateTokenAsync(_httpService);
            if (!isValid) return Unauthorized(errorMsg);

            if (routeInfo.Exec != null)
            {
                await refreshTokenAsync();
                return Ok(await routeInfo.Exec(apiInfo, this.Request));
            }
            else
            {
                using (var client = routeInfo.HttpClientConfig?.HttpClient())
                {
                    _httpService.Client.DefaultRequestHeaders.Host = AppSettingHelper.Configuration[$"ApiHost:{api}"];

                    this.Request.Headers?.AddRequestHeaders((client ?? _httpService.Client).DefaultRequestHeaders);

                    if (client == null)
                    {
                        routeInfo.HttpClientConfig?.CustomizeDefaultHttpClient?.Invoke(_httpService.Client, this.Request);
                    }
                    _logger.LogApiInfo($"日志 response 參數 {apiInfo.BaseUrl}{routeInfo.Path}{parameters}");

                    _logger.LogApiInfo($"日志 參數 {routeInfo.Path + parameters}");

                    _logger.LogApiInfo($"日志 參數 {_httpService.Client.DefaultRequestHeaders.Host}");

                    if (client != null) client.Timeout = TimeSpan.FromSeconds(Timeout);
                    _httpService.Client.Timeout = TimeSpan.FromSeconds(Timeout);
                    var response = await (client ?? _httpService.Client).GetAsync($"{apiInfo.BaseUrl}{(routeInfo.IsParameterizedRoute ? routeInfo.GetPath(this.Request) : routeInfo.Path + parameters)}");

                    response.EnsureSuccessStatusCode();

                    _logger.LogApiInfo($"{apiInfo.BaseUrl}{routeInfo.Path}{parameters}", false);

                    await refreshTokenAsync();

                    if (routeInfo.ResponseType != null && routeInfo.ResponseType == typeof(FileContentResult))
                    {
                        return File(await response.Content.ReadAsByteArrayAsync(), routeInfo.FileContentType, routeInfo.FileName);
                    }
                    else
                    {
                        return Ok(routeInfo.ResponseType != null
                        ? JsonConvert.DeserializeObject(await response.Content.ReadAsStringAsync(), routeInfo.ResponseType)
                        : await response.Content.ReadAsStringAsync());
                    }
                }
            }
        }

        [HttpPost]
        [Route("{api}/{key}")]
        [ServiceFilter(typeof(GatewayPostAuthorizeAttribute))]
        [ServiceFilter(typeof(GatewayPostAsyncActionFilterAttribute))]
        [ServiceFilter(typeof(GatewayPostAsyncExceptionFilterAttribute))]
        [ServiceFilter(typeof(GatewayPostAsyncResultFilterAttribute))]
        public async Task<IActionResult> Post(string api, string key, object request, string parameters = null)
        {
            parameters = parameters != null ? HttpUtility.UrlDecode(parameters) : string.Empty;

            _logger.LogApiInfo(api, key, parameters, request);

            var apiInfo = _apiOrchestrator.GetApi(api, true);

            var gwRouteInfo = apiInfo.Mediator.GetRoute(key);

            var routeInfo = gwRouteInfo.Route;

            var (isValid, errorMsg) = await validateTokenAsync(_httpService);
            if (!isValid) return Unauthorized(errorMsg);

            if (routeInfo.Exec != null)
            {
                await refreshTokenAsync();
                return Ok(await routeInfo.Exec(apiInfo, this.Request));
            }
            else
            {
                using (var client = routeInfo.HttpClientConfig?.HttpClient())
                {
                    HttpContent content = null;
                    _httpService.Client.DefaultRequestHeaders.Host = AppSettingHelper.Configuration[$"ApiHost:{api}"];


                    if (routeInfo.HttpClientConfig?.HttpContent != null)
                    {
                        content = routeInfo.HttpClientConfig.HttpContent();
                    }
                    else
                    {
                        content = new StringContent(request.ToString(), Encoding.UTF8, "application/json");

                        content.Headers.ContentType = new MediaTypeHeaderValue("application/json");
                    }

                    this.Request.Headers?.AddRequestHeaders((client ?? _httpService.Client).DefaultRequestHeaders);

                    if (client == null)
                    {
                        routeInfo.HttpClientConfig?.CustomizeDefaultHttpClient?.Invoke(_httpService.Client, this.Request);
                    }

                    _logger.LogApiInfo($"{apiInfo.BaseUrl}{routeInfo.Path}{parameters}");

                    if (client != null) client.Timeout = TimeSpan.FromSeconds(Timeout);
                    _httpService.Client.Timeout = TimeSpan.FromSeconds(Timeout);
                    var response = await (client ?? _httpService.Client).PostAsync($"{apiInfo.BaseUrl}{(routeInfo.IsParameterizedRoute ? routeInfo.GetPath(this.Request) : routeInfo.Path + parameters)}", content);

                    _logger.LogApiInfo($"{apiInfo.BaseUrl}{routeInfo.Path}{parameters}", false);

                    response.EnsureSuccessStatusCode();

                    await refreshTokenAsync();

                    if (routeInfo.ResponseType != null && routeInfo.ResponseType == typeof(FileContentResult))
                    {
                        return File(await response.Content.ReadAsByteArrayAsync(), routeInfo.FileContentType, routeInfo.FileName);
                    }
                    else
                    {
                        return Ok(routeInfo.ResponseType != null
                     ? JsonConvert.DeserializeObject(await response.Content.ReadAsStringAsync(), routeInfo.ResponseType)
                     : await response.Content.ReadAsStringAsync());
                    }
                }
            }
        }

        [HttpPost]
        [Route("Form/{api}/{key}")]
        [ServiceFilter(typeof(GatewayPostAuthorizeAttribute))]
        [ServiceFilter(typeof(GatewayPostAsyncActionFilterAttribute))]
        [ServiceFilter(typeof(GatewayPostAsyncExceptionFilterAttribute))]
        [ServiceFilter(typeof(GatewayPostAsyncResultFilterAttribute))]
        public async Task<IActionResult> Post(string api, string key)
        {
            var apiInfo = _apiOrchestrator.GetApi(api, true);
            var gwRouteInfo = apiInfo.Mediator.GetRoute(key);
            var routeInfo = gwRouteInfo.Route;
            var (isValid, errorMsg) = await validateTokenAsync(_httpService);
            if (!isValid) return Unauthorized(errorMsg);
            if (routeInfo.Exec != null)
            {
                await refreshTokenAsync();
                return Ok(await routeInfo.Exec(apiInfo, this.Request));
            }
            else
            {
                using (var client = routeInfo.HttpClientConfig?.HttpClient())
                {
                    _httpService.Client.DefaultRequestHeaders.Host = AppSettingHelper.Configuration[$"ApiHost:{api}"];
                    this.Request.Headers?.AddRequestHeaders((client ?? _httpService.Client).DefaultRequestHeaders);
                    if (client == null)
                    {
                        routeInfo.HttpClientConfig?.CustomizeDefaultHttpClient?.Invoke(_httpService.Client, this.Request);
                    }
                    var formData = new MultipartFormDataContent();
                    foreach (var keyName in this.Request.Form.Keys)
                    {
                        if (this.Request.Form.TryGetValue(keyName, out StringValues _v))
                            formData.Add(new StringContent(_v.ToString()), keyName);
                    }
                    if (Request.Form.Files.Any())
                    {
                        foreach (var file in Request.Form.Files)
                        {
                            var fileContent = new StreamContent(file.OpenReadStream());
                            var fileHeader = new ContentDispositionHeaderValue("form-data")
                            {
                                Name = file.Name ?? "file", // 文件的字段名，与服务端期望的对应  
                                FileName = file.FileName // 文件名  
                            };
                            fileContent.Headers.ContentDisposition = fileHeader;
                            formData.Add(fileContent, file.Name ?? "file", file.FileName);
                        }
                    }

                    if (client != null) client.Timeout = TimeSpan.FromSeconds(Timeout);
                    _httpService.Client.Timeout = TimeSpan.FromSeconds(Timeout);
                    var response = await (client ?? _httpService.Client).PostAsync($"{apiInfo.BaseUrl}{routeInfo.Path}", formData);
                    response.EnsureSuccessStatusCode();
                    formData.Dispose();
                    await refreshTokenAsync();
                    if (routeInfo.ResponseType != null && routeInfo.ResponseType == typeof(FileContentResult))
                    {
                        return File(await response.Content.ReadAsByteArrayAsync(), routeInfo.FileContentType, routeInfo.FileName);
                    }
                    else
                    {
                        return Ok(routeInfo.ResponseType != null
                     ? JsonConvert.DeserializeObject(await response.Content.ReadAsStringAsync(), routeInfo.ResponseType)
                     : await response.Content.ReadAsStringAsync());
                    }
                }
            }
        }

        [HttpPost]
        [Route("hub/{api}/{key}")]
        [ServiceFilter(typeof(GatewayHubPostAuthorizeAttribute))]
        [ServiceFilter(typeof(GatewayHubPostAsyncActionFilterAttribute))]
        [ServiceFilter(typeof(GatewayHubPostAsyncExceptionFilterAttribute))]
        [ServiceFilter(typeof(GatewayHubPostAsyncResultFilterAttribute))]
        public async Task PostHub(string api, string key, params object[] request)
        {
            _logger.LogApiInfo(api, key, "", request);

            var hubInfo = _apiOrchestrator.GetHub(api);

            var gwRouteInfo = hubInfo.Mediator.GetRoute(key);

            var connection = hubInfo.Connection;

            if (connection.State != HubConnectionState.Connected)
            {
                await connection.StartAsync();
            }

            await connection.InvokeCoreAsync(gwRouteInfo.HubRoute.InvokeMethod, request);
        }

        [HttpPut]
        [Route("{api}/{key}")]
        [ServiceFilter(typeof(GatewayPutAuthorizeAttribute))]
        [ServiceFilter(typeof(GatewayPutAsyncActionFilterAttribute))]
        [ServiceFilter(typeof(GatewayPutAsyncExceptionFilterAttribute))]
        [ServiceFilter(typeof(GatewayPutAsyncResultFilterAttribute))]
        public async Task<IActionResult> Put(string api, string key, object request, string parameters = null)
        {
            parameters = parameters != null ? HttpUtility.UrlDecode(parameters) : string.Empty;

            _logger.LogApiInfo(api, key, parameters, request);

            var apiInfo = _apiOrchestrator.GetApi(api, true);

            var gwRouteInfo = apiInfo.Mediator.GetRoute(key);

            var routeInfo = gwRouteInfo.Route;

            var (isValid, errorMsg) = await validateTokenAsync(_httpService);
            if (!isValid) return Unauthorized(errorMsg);

            if (routeInfo.Exec != null)
            {
                await refreshTokenAsync();
                return Ok(await routeInfo.Exec(apiInfo, this.Request));
            }
            else
            {
                using (var client = routeInfo.HttpClientConfig?.HttpClient())
                {
                    HttpContent content = null;

                    if (routeInfo.HttpClientConfig?.HttpContent != null)
                    {
                        content = routeInfo.HttpClientConfig.HttpContent();
                    }
                    else
                    {
                        content = new StringContent(request.ToString(), Encoding.UTF8, "application/json");

                        content.Headers.ContentType = new MediaTypeHeaderValue("application/json");
                    }

                    _httpService.Client.DefaultRequestHeaders.Host = AppSettingHelper.Configuration[$"ApiHost:{api}"];

                    this.Request.Headers?.AddRequestHeaders((client ?? _httpService.Client).DefaultRequestHeaders);

                    if (client == null)
                    {
                        routeInfo.HttpClientConfig?.CustomizeDefaultHttpClient?.Invoke(_httpService.Client, this.Request);
                    }

                    _logger.LogApiInfo($"{apiInfo.BaseUrl}{routeInfo.Path}{parameters}");

                    var response = await (client ?? _httpService.Client).PutAsync($"{apiInfo.BaseUrl}{(routeInfo.IsParameterizedRoute ? routeInfo.GetPath(this.Request) : routeInfo.Path + parameters)}", content);

                    _logger.LogApiInfo($"{apiInfo.BaseUrl}{routeInfo.Path}{parameters}", false);

                    response.EnsureSuccessStatusCode();

                    await refreshTokenAsync();

                    return Ok(routeInfo.ResponseType != null
                        ? JsonConvert.DeserializeObject(await response.Content.ReadAsStringAsync(), routeInfo.ResponseType)
                        : await response.Content.ReadAsStringAsync());
                }
            }
        }

        [HttpPatch]
        [Route("{api}/{key}")]
        [ServiceFilter(typeof(GatewayPatchAuthorizeAttribute))]
        [ServiceFilter(typeof(GatewayPatchAsyncActionFilterAttribute))]
        [ServiceFilter(typeof(GatewayPatchAsyncExceptionFilterAttribute))]
        [ServiceFilter(typeof(GatewayPatchAsyncResultFilterAttribute))]
        public async Task<IActionResult> Patch(string api, string key, [FromBody] JsonPatchDocument<object> patch, string parameters = null)
        {
            parameters = parameters != null ? HttpUtility.UrlDecode(parameters) : string.Empty;

            _logger.LogApiInfo(api, key, parameters, patch.ToString());

            var apiInfo = _apiOrchestrator.GetApi(api, true);

            var gwRouteInfo = apiInfo.Mediator.GetRoute(key);

            var routeInfo = gwRouteInfo.Route;

            var (isValid, errorMsg) = await validateTokenAsync(_httpService);
            if (!isValid) return Unauthorized(errorMsg);

            if (routeInfo.Exec != null)
            {
                await refreshTokenAsync();
                return Ok(await routeInfo.Exec(apiInfo, this.Request));
            }
            else
            {
                using (var client = routeInfo.HttpClientConfig?.HttpClient())
                {
                    HttpContent content = null;

                    if (routeInfo.HttpClientConfig?.HttpContent != null)
                    {
                        content = routeInfo.HttpClientConfig.HttpContent();
                    }
                    else
                    {
                        var p = JsonConvert.SerializeObject(patch);

                        content = new StringContent(p, Encoding.UTF8, "application/json-patch+json");
                    }

                    _httpService.Client.DefaultRequestHeaders.Host = AppSettingHelper.Configuration[$"ApiHost:{api}"];

                    this.Request.Headers?.AddRequestHeaders((client ?? _httpService.Client).DefaultRequestHeaders);

                    if (client == null)
                    {
                        routeInfo.HttpClientConfig?.CustomizeDefaultHttpClient?.Invoke(_httpService.Client, this.Request);
                    }

                    _logger.LogApiInfo($"{apiInfo.BaseUrl}{routeInfo.Path}{parameters}");

                    var response = await (client ?? _httpService.Client).PatchAsync($"{apiInfo.BaseUrl}{(routeInfo.IsParameterizedRoute ? routeInfo.GetPath(this.Request) : routeInfo.Path + parameters)}", content);

                    _logger.LogApiInfo($"{apiInfo.BaseUrl}{routeInfo.Path}{parameters}", false);

                    response.EnsureSuccessStatusCode();

                    await refreshTokenAsync();

                    return Ok(routeInfo.ResponseType != null
                        ? JsonConvert.DeserializeObject(await response.Content.ReadAsStringAsync(), routeInfo.ResponseType)
                        : await response.Content.ReadAsStringAsync());
                }
            }
        }

        [HttpDelete]
        [Route("{api}/{key}")]
        [ServiceFilter(typeof(GatewayDeleteAuthorizeAttribute))]
        [ServiceFilter(typeof(GatewayDeleteAsyncActionFilterAttribute))]
        [ServiceFilter(typeof(GatewayDeleteAsyncExceptionFilterAttribute))]
        [ServiceFilter(typeof(GatewayDeleteAsyncResultFilterAttribute))]
        public async Task<IActionResult> Delete(string api, string key, string parameters = null)
        {
            parameters = parameters != null ? HttpUtility.UrlDecode(parameters) : string.Empty;

            _logger.LogApiInfo(api, key, parameters);

            var apiInfo = _apiOrchestrator.GetApi(api, true);

            var gwRouteInfo = apiInfo.Mediator.GetRoute(key);

            var routeInfo = gwRouteInfo.Route;

            var (isValid, errorMsg) = await validateTokenAsync(_httpService);
            if (!isValid) return Unauthorized(errorMsg);

            if (routeInfo.Exec != null)
            {
                await refreshTokenAsync();
                return Ok(await routeInfo.Exec(apiInfo, this.Request));
            }
            else
            {
                using (var client = routeInfo.HttpClientConfig?.HttpClient())
                {
                    _httpService.Client.DefaultRequestHeaders.Host = AppSettingHelper.Configuration[$"ApiHost:{api}"];

                    this.Request.Headers?.AddRequestHeaders((client ?? _httpService.Client).DefaultRequestHeaders);

                    if (client == null)
                    {
                        routeInfo.HttpClientConfig?.CustomizeDefaultHttpClient?.Invoke(_httpService.Client, this.Request);
                    }

                    _logger.LogApiInfo($"{apiInfo.BaseUrl}{routeInfo.Path}{parameters}");

                    var response = await (client ?? _httpService.Client).DeleteAsync($"{apiInfo.BaseUrl}{(routeInfo.IsParameterizedRoute ? routeInfo.GetPath(this.Request) : routeInfo.Path + parameters)}");

                    _logger.LogApiInfo($"{apiInfo.BaseUrl}{routeInfo.Path}{parameters}", false);

                    response.EnsureSuccessStatusCode();

                    await refreshTokenAsync();

                    return Ok(routeInfo.ResponseType != null
                        ? JsonConvert.DeserializeObject(await response.Content.ReadAsStringAsync(), routeInfo.ResponseType)
                        : await response.Content.ReadAsStringAsync());
                }
            }
        }

        [HttpGet]
        [Route("orchestration")]
        [ServiceFilter(typeof(GatewayGetOrchestrationAuthorizeAttribute))]
        [ServiceFilter(typeof(GatewayGetOrchestrationAsyncActionFilterAttribute))]
        [ServiceFilter(typeof(GatewayGetOrchestrationAsyncExceptionFilterAttribute))]
        [ServiceFilter(typeof(GatewayGetOrchestrationAsyncResultFilterAttribute))]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(IEnumerable<Orchestration>))]
        public async Task<IActionResult> GetOrchestration(string api = null, string key = null)
        {
            api = api?.ToLower();
            key = key?.ToLower();

            var orchestrations = await Task.FromResult(string.IsNullOrEmpty(api) && string.IsNullOrEmpty(key)
                                                ? _apiOrchestrator.Orchestration
                                                : (!string.IsNullOrEmpty(api) && string.IsNullOrEmpty(key)
                                                ? _apiOrchestrator.Orchestration?.Where(x => x.Api.Contains(api.Trim()))
                                                : (string.IsNullOrEmpty(api) && !string.IsNullOrEmpty(key)
                                                ? _apiOrchestrator.Orchestration?.Where(x => x.Routes.Any(y => y.Key.Contains(key.Trim())))
                                                                                 .Select(x => x.FilterRoutes(key))
                                                : _apiOrchestrator.Orchestration?.Where(x => x.Api.Contains(api.Trim()))
                                                                                 .Select(x => x.FilterRoutes(key)))));

            return Ok(orchestrations);
        }

        #region token驗證及刷新
        private async Task<(bool isValid, string errorMsg)> validateTokenAsync(IHttpService _httpService)
        {
            //需排除驗證的網址
            List<string> skipPath = new List<string> { "/Gateway/permissionapi/Login",
                "/Gateway/permissionapi/RefreshToken","/Gateway/permissionapi/RefreshToken1",
                "/Gateway/permissionapi/ValidateToken","/Gateway/permissionapi/ValidateToken1" ,
                "/Gateway/permissionapi/ITLogin","/Gateway/permissionapi/GetCurrentPsInfo"};
            try
            {
                //排除驗證
                foreach (string path in skipPath)
                {
                    if (this.Request.HttpContext.Request.Path.ToString().ToLower() == path.ToLower()) return (true, "");
                }

                //無處理過的token
                if (!this.Request.HttpContext.Request.Headers.ContainsKey("Authorization"))
                {
                    return (false, "Invalid ID Token: No ID Token");
                }

                string idToken = this.Request.HttpContext.Request.Headers["Authorization"].ToString();

                JwtSecurityTokenHandler handler = new JwtSecurityTokenHandler();
                JwtSecurityToken decodedToken = handler.ReadJwtToken(idToken);

                //如果為IT測試帳號登入，則不需驗證
                if (decodedToken.Payload.TryGetValue("it_user", out object it_user)) return (true, "");

                string clientID = AppSettingHelper.Configuration.GetSection("AADClientID").Value;

                // 擷取 ID Token 中的相關資訊
                string tokenClientID = decodedToken.Payload["aud"].ToString();

                // 驗證 ClientID 是否有效
                if (tokenClientID == null || tokenClientID != clientID)
                {
                    return (false, "Invalid ID Token: Invalid AAD Client ID");
                }

                object tokenObj = await GetUserToken(decodedToken.Payload["logging_emp"].ToString());

                if (tokenObj != null)
                {
                    ApiResultModelByObject apiRes = JsonConvert.DeserializeObject<ApiResultModelByObject>(JsonConvert.SerializeObject(tokenObj));

                    if (apiRes.listData != null)
                    {
                        string userToken = apiRes.listData.ToString();

                        JwtSecurityToken decodedDBToken = handler.ReadJwtToken(userToken);

                        double tokenExpTimeStamp = Convert.ToDouble(decodedDBToken.Payload["exp"].ToString());
                        //double tokenExpTimeStamp = Convert.ToDouble(handler.ReadJwtToken(userToken).Payload["exp"].ToString());

                        // 驗證 Expire Time 是否有效
                        if (tokenExpTimeStamp == null || tokenExpTimeStamp == 0)
                        {
                            return (false, "Invalid ID Token: Invalid Expire Time");
                        }

                        //驗證 Expire Time 是否已過期
                        if (tokenExpTimeStamp < DateTimeOffset.Now.ToUnixTimeSeconds())
                        {
                            return (false, "Invalid ID Token: Token Expired");
                        }

                        //返回AAD登入者不檢查授權碼
                        if (this.Request.HttpContext.Request.Path.ToString().ToLower() != "/Gateway/permissionapi/ConvertLoginUser".ToLower())
                        {
                            if (!ValidAuthLogin(decodedToken, decodedDBToken))
                            {
                                this.Request.HttpContext.Response.Headers.Add("Access-Control-Expose-Headers", "auth_code_changed");
                                this.Request.HttpContext.Response.Headers.Add("auth_code_changed", "1");
                                return (false, "授權訊息已變動，請重新登入");
                            }
                        }
                    }
                }

                //#region 加載頭部，為各個微服務提供驗證參數
                this.Request.HttpContext.Request.Headers.Add("Access-Control-Expose-Headers", "Cache-Control, Content-Language, Content-Type, Expires, Last-Modified, Date, logging_emp, logging_name, current_emp, current_name, refresh_token, token_expire_time, maintain_message,is_resignation");
                //#endregion

                return (true, "");
            }
            catch (Exception ex)
            {
                return (false, "Invalid ID Token: " + ex.Message);
            }
        }

        private async Task<object> GetUserToken(string emplid)
        {
            var apiInfo = _apiOrchestrator.GetApi("permissionapi", true);

            var gwRouteInfo = apiInfo.Mediator.GetRoute("GetUserToken");

            var routeInfo = gwRouteInfo.Route;

            // Use injected HttpClient instead of creating new one
            var response = await _httpService.Client.GetAsync($"{apiInfo.BaseUrl}{(routeInfo.IsParameterizedRoute ? routeInfo.GetPath(this.Request) : routeInfo.Path + "?emplid=" + emplid)}");
            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                return content != null ? JsonConvert.DeserializeObject(content, routeInfo.ResponseType) : null;
            }
            return null;
        }

        private bool ValidAuthLogin(JwtSecurityToken idToken, JwtSecurityToken dbToken)
        {

            string login_type = string.Empty;
            string auth_code = string.Empty;
            string db_auth_code = string.Empty;

            //驗證登入方式
            if (idToken.Payload.TryGetValue("login_type", out var loginTypeValue))
            {
                login_type = loginTypeValue?.ToString();

                //如不是授權碼登入，則不需驗證
                if (login_type != "2") return true;

                //取得client端授權碼
                if (idToken.Payload.TryGetValue("auth_code", out var authCodeValue))
                {
                    auth_code = authCodeValue?.ToString();

                    //取得db端授權碼
                    if (dbToken.Payload.TryGetValue("auth_code", out var dbAuthCodeValue))
                    {
                        db_auth_code = dbAuthCodeValue?.ToString();

                        //if (db_auth_code != "2") return true;

                        if (auth_code != db_auth_code) return false;
                    }

                }
            }

            return true;
        }

        private async Task refreshTokenAsync()
        {
            //跳過清單
            List<string> skipPath = new List<string> { "/Gateway/permissionapi/Login",
                "/Gateway/permissionapi/RefreshToken","/Gateway/permissionapi/RefreshToken1",
                "/Gateway/permissionapi/ValidateToken" , "/Gateway/permissionapi/ValidateToken1",
                "/Gateway/permissionapi/Logout", "/Gateway/permissionapi/ITLogin","/Gateway/permissionapi/GetCurrentPsInfo"};
            try
            {
                long expireTime = long.Parse(AppSettingHelper.Configuration.GetSection("AADExpireTime").Value);

                if (this.Request.HttpContext.Request.Path.ToString().ToLower() == "/Gateway/permissionapi/ITLogin".ToLower()) expireTime = long.MaxValue;

                foreach (string path in skipPath)
                {
                    if (this.Request.HttpContext.Request.Path.ToString().ToLower() == path.ToLower())
                    {
                        if (path.ToLower() != ("/Gateway/permissionapi/ValidateToken").ToLower() && path.ToLower() != ("/Gateway/permissionapi/ValidateToken1").ToLower())
                        {
                            //在Header中加入token過期時間，前端需顯示
                            this.Request.HttpContext.Response.Headers.Add("token_expire_time", expireTime.ToString());
                            this.Request.HttpContext.Response.Headers.Add("maintain_message", "");
                            this.Request.HttpContext.Response.Headers.Add("is_resignation", "");
                            this.Request.HttpContext.Response.Headers.Add("Access-Control-Expose-Headers", "Cache-Control, Content-Language, Content-Type, Expires, Last-Modified, Date, refresh_token, token_expire_time, maintain_message,is_resignation");
                        }
                        return;
                    }
                }

                string idToken = this.Request.HttpContext.Request.Headers["Authorization"].ToString();

                JwtSecurityTokenHandler handler = new JwtSecurityTokenHandler();
                JwtSecurityToken decodedToken = handler.ReadJwtToken(idToken);

                //如果為IT測試帳號登入，則不需驗證
                if (decodedToken.Payload.TryGetValue("it_user", out object it_user))
                {
                    this.Request.HttpContext.Response.Headers.Add("refresh_token", idToken);
                    this.Request.HttpContext.Response.Headers.Add("token_expire_time", long.MaxValue.ToString());
                    this.Request.HttpContext.Response.Headers.Add("maintain_message", "");
                    this.Request.HttpContext.Response.Headers.Add("is_resignation", "1");
                    this.Request.HttpContext.Response.Headers.Add("Access-Control-Expose-Headers", "Cache-Control, Content-Language, Content-Type, Expires, Last-Modified, Date, refresh_token, token_expire_time, maintain_message,is_resignation");
                    return;
                }

                var apiInfo = _apiOrchestrator.GetApi("permissionapi", true);

                var gwRouteInfo = apiInfo.Mediator.GetRoute("RefreshTokenAuto");

                var routeInfo = gwRouteInfo.Route;

                ApiResultModelByObject res;
                // Use injected HttpClient instead of creating new one
                var response = await _httpService.Client.PostAsync($"{apiInfo.BaseUrl}{(routeInfo.IsParameterizedRoute ? routeInfo.GetPath(this.Request) : routeInfo.Path + "?idToken=" + idToken)}", null);
                var content = await response.Content.ReadAsStringAsync();
                res = JsonConvert.DeserializeObject<ApiResultModelByObject>(content);

                if (res != null && res.listData != null && !string.IsNullOrEmpty(res.listData.ToString()))
                {
                    JwtSecurityToken decodedDBToken = handler.ReadJwtToken(res.listData.ToString());
                    //非call更換登入方式時，如其他瀏覽器更換使用者，需告訴前端
                    if (this.Request.HttpContext.Request.Path.ToString().ToLower() != ("/Gateway/permissionapi/ConvertUser").ToLower() &&
                        this.Request.HttpContext.Request.Path.ToString().ToLower() != ("/Gateway/permissionapi/ConvertAuthUser").ToLower() &&
                        this.Request.HttpContext.Request.Path.ToString().ToLower() != ("/Gateway/permissionapi/ConvertLoginUser").ToLower())
                    {
                        if (decodedDBToken.Payload.ContainsKey("userinfo_changed")) this.Request.HttpContext.Response.Headers.Add("userinfo_changed", decodedDBToken.Payload["userinfo_changed"].ToString());
                    }

                    this.Request.HttpContext.Response.Headers.Add("refresh_token", handler.WriteToken(decodedDBToken));
                    this.Request.HttpContext.Response.Headers.Add("token_expire_time", expireTime.ToString());
                    //獲取關站提示語
                    this.Request.HttpContext.Response.Headers.Add("maintain_message", decodedDBToken.Payload["maintain_message"] != null ? decodedDBToken.Payload["maintain_message"].ToString() : "");
                    //驗證登陸者是否離職
                    this.Request.HttpContext.Response.Headers.Add("is_resignation", decodedDBToken.Payload["is_resignation"] != null ? decodedDBToken.Payload["is_resignation"].ToString() : "");
                }
                else
                {
                    this.Request.HttpContext.Response.Headers.Add("refresh_token", handler.WriteToken(decodedToken));
                    this.Request.HttpContext.Response.Headers.Add("token_expire_time", "0");
                }

                this.Request.HttpContext.Response.Headers.Add("Access-Control-Expose-Headers", "Cache-Control, Content-Language, Content-Type, Expires, Last-Modified, Date, refresh_token, token_expire_time, userinfo_changed, error, maintain_message");
            }
            catch (Exception ex)
            {
                this.Request.HttpContext.Response.Headers.Add("error", ex.Message);
                this.Request.HttpContext.Response.Headers.Add("Access-Control-Expose-Headers", "error");
            }
        }
        #endregion
    }
}