﻿using Elegal.Flow.Api.Services;
using Elegal.Interface.Api.Common.Control.Filter;
using Elegal.Interface.Api.Common.Control.Web;
using Elegal.Interface.Api.Common.Model;
using Elegal.Interface.Api.Common.Model.ResultModel;
using Elegal.Interface.Api.Common.Model.ResultModel.FlowApi;
using Elegal.Orm.Utils;
using Microsoft.AspNetCore.Mvc;
using System.Text;

namespace Elegal.Flow.Api.Controllers
{
    /// <summary>
    /// 承辦窗口服務
    /// </summary>
    [Route("[controller]")]
    [ApiController]
    public class UndertakeWindowController : BaseController
    {
        /// <summary>
        /// 承辦窗口頁面查詢
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [Route("QueryUndertakeWindow")]
        public async Task<ApiResultModelByObject> QueryUndertakeWindow([FromBody] UndertakeWindow undertake)
        {
            ApiResultModelByObject apiResult = new();

            List<UndertakeWindow> undertakeWindowList = UndertakeWindowService.QueryUndertakeWindow(undertake, MvcContext.UserInfo.logging_locale, MvcContext.UserInfo.time_zone);
            InitLogRecord(undertake, log =>
            {
                StringBuilder stringBuilder = new();
                if (!string.IsNullOrEmpty(undertake.empid))
                    stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("UndertakeWindow_log:QueryParam", true)}：{undertake.empid}");
                log.Detail = stringBuilder.ToString();

            });
            apiResult.listData = undertakeWindowList;
            apiResult.rtnSuccess = true;

            return await Task.FromResult(apiResult);
        }

        /// <summary>
        /// 根據newid查詢承辦窗口數據
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("QueryUndertakeWindowByFnid")]
        public async Task<ApiResultModelByObject> QueryUndertakeWindowByNewid(int fnid)
        {
            ApiResultModelByObject apiResult = new();

            UndertakeWindow undertakeWindow = UndertakeWindowService.QueryUndertakeWindowByFnid(fnid, MvcContext.UserInfo.logging_locale, MvcContext.UserInfo.time_zone);
            if (undertakeWindow != null && undertakeWindow.fnid > 0)
            {
                InitLogRecord(fnid, log =>
                {
                    StringBuilder stringBuilder = new();
                    InitInsertLog(undertakeWindow, stringBuilder);
                    log.Detail = stringBuilder.ToString();
                });
                apiResult.listData = undertakeWindow;
                apiResult.rtnSuccess = true;
            }
            else
            {
                apiResult.messageTitle = ActionFilter.GetMultilingualValue("custom:messageTitle:dataNotexist");
                apiResult.messageContent = new List<string> { string.Format(ActionFilter.GetMultilingualValue("custom:messageContent:dataChurn")) };
            }

            return await Task.FromResult(apiResult);
        }

        /// <summary>
        /// 查詢項目下拉欄
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("QueryContact")]
        public async Task<ApiResultModelByObject> QueryContact()
        {
            ApiResultModelByObject apiResult = new();

            List<DropDownListModel> parameter = UndertakeWindowService.QueryContact();
            apiResult.listData = parameter;
            apiResult.rtnSuccess = true;

            return await Task.FromResult(apiResult);
        }

        /// <summary>
        /// 根據newid刪除承辦窗口數據
        /// </summary>
        /// <returns></returns>
        [HttpDelete]
        [Route("DeleteUndertakeWindow")]
        public async Task<ApiResultModelByObject> DeleteUndertakeWindow(int fnid)
        {
            ApiResultModelByObject apiResult = new();

            if (fnid > 0)
            {
                UndertakeWindow undertakeWindow = UndertakeWindowService.QueryUndertakeWindowByFnid(fnid, MvcContext.UserInfo.logging_locale, MvcContext.UserInfo.time_zone);
                if (undertakeWindow != null && undertakeWindow.fnid > 0)
                {
                    int count = UndertakeWindowService.DeleteUndertakeWindow(fnid);
                    InitLogRecord(fnid, log =>
                    {
                        StringBuilder stringBuilder = new();
                        InitInsertLog(undertakeWindow, stringBuilder);
                        log.Detail = stringBuilder.ToString();
                    });
                    apiResult.listData = count;
                    apiResult.rtnSuccess = true;
                }
                else
                {
                    apiResult.messageTitle = ActionFilter.GetMultilingualValue("custom:messageTitle:dataNotexist");
                    apiResult.messageContent = new List<string> { string.Format(ActionFilter.GetMultilingualValue("custom:messageContent:dataChurn")) };
                }
            }

            return await Task.FromResult(apiResult);
        }

        /// <summary>
        /// 新增承辦窗口數據
        /// </summary>
        /// <returns></returns>
        [HttpPut]
        [Route("InsertUndertakeWindow")]
        public async Task<ApiResultModelByObject> InsertUndertakeWindow(UndertakeWindow undertake)
        {
            ApiResultModelByObject apiResult = new();

            if (undertake != null)
            {
                undertake.operate_cuser = MvcContext.UserInfo.current_emp;
                undertake.operate_time = DateTime.Now;
                if (UndertakeWindowService.DuplicateVerificationUndertakeWindow(undertake))
                {
                    if (!undertake.empid.Equals(undertake.contact_agent))
                    {
                        int count = UndertakeWindowService.InsertUndertakeWindow(undertake);
                        InitLogRecord(undertake, log =>
                        {
                            StringBuilder stringBuilder = new();
                            InitInsertLog(undertake, stringBuilder);
                            log.Detail = stringBuilder.ToString();
                        });
                        apiResult.listData = count;
                        apiResult.rtnSuccess = true;
                    }
                    else
                    {
                        apiResult.messageTitle = ActionFilter.GetMultilingualValue("custom:messageTitle:dataExists");
                        apiResult.messageContent = new List<string> { string.Format(ActionFilter.GetMultilingualValue("custom:messageContent:repeatMessage")),
                            string.Format(ActionFilter.GetMultilingualValue("custom:messageContent:UnderTakeWindowAgentExits"),undertake.agent_euser, undertake.agent_cuser,undertake.contact_euser, undertake.contact_cuser)};
                    }
                }
                else
                {
                    apiResult.messageTitle = ActionFilter.GetMultilingualValue("custom:messageTitle:dataExists");
                    apiResult.messageContent = new List<string> { string.Format(ActionFilter.GetMultilingualValue("custom:messageContent:repeatMessage")), string.Format(ActionFilter.GetMultilingualValue("custom:messageContent:UnderTakeWindowDataExits"), undertake.item_name, undertake.contact_cuser, undertake.contact_euser) };
                }
            }

            return await Task.FromResult(apiResult);
        }

        private static void InitInsertLog(UndertakeWindow undertake, StringBuilder stringBuilder)
        {
            if (!string.IsNullOrEmpty(undertake.item))
                stringBuilder.AppendLine($"{AttributeUtil.GetParameterDescribe<UndertakeWindow>("item_name")}：{undertake.item_name}");
            if (!string.IsNullOrEmpty(undertake.contact_user))
                stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("UndertakeWindow_log:UndertakeWindowName", true)}：{undertake.contact_euser}/{undertake.contact_cuser}({undertake.contact_user})");
            if (!string.IsNullOrEmpty(undertake.agent_user))
                stringBuilder.AppendLine($"{AttributeUtil.GetParameterDescribe<UndertakeWindow>("agent_user")}：{undertake.agent_euser}/{undertake.agent_cuser}({undertake.agent_user})");
            if (!string.IsNullOrEmpty(undertake.contact_notes))
                stringBuilder.AppendLine($"{AttributeUtil.GetParameterDescribe<UndertakeWindow>("contact_notes")}：{undertake.contact_notes}");
            if (undertake.is_show_home != null)
            {
                string is_show_home = undertake.is_show_home.Equals(0) ? $"{AttributeUtil.GetParameterDescribe<UndertakeWindow>("is_show_home")}：否" : $"{AttributeUtil.GetParameterDescribe<UndertakeWindow>("is_show_home")}：是";
                stringBuilder.AppendLine(is_show_home);
            }
        }

        /// <summary>
        /// 修改承辦窗口數據
        /// </summary>
        /// <returns></returns>
        [HttpPut]
        [Route("UpdateUndertakeWindow")]
        public async Task<ApiResultModelByObject> UpdateUndertakeWindow(UndertakeWindow undertake)
        {
            ApiResultModelByObject apiResult = new();

            if (undertake != null)
            {
                undertake.operate_euser = MvcContext.UserInfo.current_emp;
                undertake.operate_time = DateTime.Now;
                UndertakeWindow undertakeWindow = UndertakeWindowService.QueryUndertakeWindowByFnid(undertake.fnid, MvcContext.UserInfo.logging_locale, MvcContext.UserInfo.time_zone);
                if (null != undertakeWindow && undertakeWindow.fnid > 0)
                {
                    if (UndertakeWindowService.DuplicateVerificationUndertakeWindow(undertake))
                    {
                        if (!undertake.empid.Equals(undertake.contact_agent))
                        {
                            int count = UndertakeWindowService.UpdateUndertakeWindow(undertake);
                            InitLogRecord(undertake, log =>
                            {
                                StringBuilder stringBuilder = new();
                                InitInsertLog(undertake, stringBuilder);
                                log.Detail = stringBuilder.ToString();

                                StringBuilder stringBuilderFormer = new();
                                InitInsertLog(undertakeWindow, stringBuilderFormer);
                                log.DetailFormer = stringBuilderFormer.ToString();
                            });
                            apiResult.listData = count;
                            apiResult.rtnSuccess = true;
                        }
                        else
                        {
                            apiResult.messageTitle = ActionFilter.GetMultilingualValue("custom:messageTitle:dataExists");
                            apiResult.messageContent = new List<string> { string.Format(ActionFilter.GetMultilingualValue("custom:messageContent:repeatMessage")), string.Format(ActionFilter.GetMultilingualValue("custom:messageContent:UnderTakeWindowAgentExits"),undertake.agent_euser, undertake.agent_cuser,undertake.contact_euser, undertake.contact_cuser) };
                        }
                    }
                    else
                    {
                        apiResult.messageTitle = ActionFilter.GetMultilingualValue("custom:messageTitle:dataExists");
                        apiResult.messageContent = new List<string> { string.Format(ActionFilter.GetMultilingualValue("custom:messageContent:repeatMessage")), string.Format(ActionFilter.GetMultilingualValue("custom:messageContent:UnderTakeWindowDataExits"), undertake.item_name, undertake.contact_cuser, undertake.contact_euser) };
                    }
                }
                else
                {
                    apiResult.messageTitle = ActionFilter.GetMultilingualValue("custom:messageTitle:dataNotexist");
                    apiResult.messageContent = new List<string> { string.Format(ActionFilter.GetMultilingualValue("custom:messageContent:dataChurn")) };
                }
            }

            return await Task.FromResult(apiResult);
        }
    }
}
