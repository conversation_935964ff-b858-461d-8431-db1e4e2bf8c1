﻿
using Elegal.Flow.Api.Repository;
using Elegal.Flow.Api.Services.Process;
using Elegal.Interface.Api.Common.Control.Filter;
using Elegal.Interface.Api.Common.Model.ParaModel.FlowApi;
using Elegal.Interface.Api.Common.Model.ResultModel;
using Elegal.Interface.ApiData.Service.FuncService;
using Elegal.Interface.ApiData.Service.Model.DbModel;

namespace Elegal.Flow.Api.Services
{
    /// <summary>
    /// 下載記錄日誌
    /// </summary>
    public static class DownloadLogService
    {
        private static DownloadLogRepository downloadLogRepository = new DownloadLogRepository();

        /// <summary>
        /// 插入下載記錄日誌
        /// </summary>
        /// <param name="downloadLog">數據模型</param>
        /// <returns></returns>
        internal static ApiResultModelByObject Insert(DownloadLogModel downloadLog)
        {
            return new ApiResultModelByObject()
            {
                listData = downloadLogRepository.Insert(downloadLog),
                rtnSuccess = true
            };
        }
    }
}
