using Elegal.Flow.Api.Controllers;
using Elegal.Flow.Api.Repository.Process;
using Elegal.Flow.Common;
using Elegal.Flow.Common.Repository.FlowStep;
using Elegal.Flow.Common.Services;
using Elegal.Interface.Api.Common.Control.Filter;
using Elegal.Interface.Api.Common.FuncHelper;
using Elegal.Interface.Api.Common.FuncService;
using Elegal.Interface.Api.Common.Model.DBModel.FlowStep;
using Elegal.Interface.Api.Common.Model.Enum;
using Elegal.Interface.Api.Common.Model.ParaModel.FlowApi.ApplicationSearch;
using Elegal.Interface.Api.Common.Model.ParaModel.FlowApi.FlowStep;
using Elegal.Interface.Api.Common.Model.ResultModel;
using Elegal.Interface.Api.Common.Model.ResultModel.FlowApi.FlowStep;
using Elegal.Interface.Api.Common.Model.ResultModel.FlowApi.Process;
using Elegal.Interface.Api.Common.Repository;
using Elegal.Interface.ApiData.Service.FuncService;
using Elegal.Interface.ApiData.Service.Model.DbModel;
using Elegal.Interface.ApiData.Service.Model.QueryCondition;
using Elegal.Orm;
using Elegal.Orm.Dtos;
using System.Text;
using static Elegal.Flow.Common.CommonUtil;

namespace Elegal.Flow.Api.Services.Process
{
    /// <summary>
    /// 簽核人員管理
    /// </summary>
    public static class SignatoryManagementService
    {
        private static readonly SignatoryManagementRepository _repository = new();
        private static readonly ApplicationPermissionRepository _applyPermissionRepository = new ApplicationPermissionRepository();
        private static readonly FlowStepProcessRepository _processRepository = new FlowStepProcessRepository();
        private static readonly FlowStepRepository _flowRepository = new FlowStepRepository();

        /// <summary>
        /// 申請單詳情查詢
        /// </summary>
        /// <returns></returns>
        public static List<SignatoryManagerResultModel> GetDataList(SignatoryManagementSearchModel condition)
        {
            List<SignatoryManagerResultModel> signatoryManagerResults = _repository.GetDataList(condition);
            //修改簽核人員隱碼問題改為進行中案件極機密案件隱碼，同經辦人轉單20250219 pike
            List<SignatoryManagerResultModel> list = new();
            foreach (SignatoryManagerResultModel resultModel in signatoryManagerResults)
            {
                //極機密案件且當前案件狀態為已核准狀態需要進行隱碼處理
                if (resultModel.confiden_level.ToUpper() == "01".ToUpper() && (resultModel.application_state.ToUpper() != "I".ToUpper() && resultModel.application_state.ToUpper() != "T".ToUpper()))
                {
                    SignatoryManagerResultModel applyDetail = HiddenValueConvertHelper.ConvertToHiddenBySingle<SignatoryManagerResultModel>(resultModel);
                    list.Add(applyDetail);
                }
                else { list.Add(resultModel); }
            }
            ActionFilter.InitLogRecord(condition, log =>
            {
                StringBuilder stringBuilder = new();
                //修改簽核人員改為三大申請單類型查詢20250217 pike
                if (condition.applyType.Any())
                {
                    string type = _repository.getFormTypeName(condition.applyType);
                    stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("Signatory_formType", true)}：{type}");
                }
                if (!string.IsNullOrEmpty(condition.applyNumber))
                    stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("Signatory_applyNumber", true)}：{condition.applyNumber}");
                if (condition.applyTimeStart != null)
                {
                    stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("Signatory_applyTimeStart", true)}：{BulletinsController.GetLocalTimeToString(condition.applyTimeStart)}");
                }
                if (condition.applyTimeEnd != null)
                    stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("Signatory_applyTimeEnd", true)}：{BulletinsController.GetLocalTimeToString(condition.applyTimeEnd)}");
                if (!string.IsNullOrEmpty(condition.contractNumber))
                    stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("Signatory_contractNumber", true)}：{condition.contractNumber}");

                if (condition.empData.Any())
                {
                    stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("Signatory_empData", true)}：{_repository.QueryEEInfoByEmplids(string.Join(",", condition.empData))}");
                }

                if (condition.deptID.Any())
                    stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("Signatory_deptID", true)}：{string.Join(",",condition.deptID)}");

                if (condition.entityID.Any())
                    stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("Signatory_entityID", true)}：{_repository.GetEntityByEntityID(string.Join(",", condition.entityID))}");

                if (condition.otherParty.Any())
                    stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("Signatory_otherParty", true)}：{string.Join(",",condition.otherParty)}");

                if (condition.currentSigner.Any())
                {
                    stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("Signatory_currentSigner", true)}：{_repository.QueryEEInfoByEmplids(string.Join(",", condition.currentSigner))}");
                }
                if (condition.is_abnormal.Any())
                    stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("Signatory_isAbnormal", true)}：{string.Join(",", condition.is_abnormal).Replace("Y", "是").Replace("N", "否")}");

                if (condition.currentLevelNames.Any())
                {
                    stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("Signatory_currentLevels", true)}：{string.Join(",", condition.currentLevelNames)}");
                }

                log.Detail = stringBuilder.ToString();
            });
            return list;
        }

        private static string getSysParaters(List<string> list, string para_code)
        {
            List<SysParameters> sysParameters = SysParametersDataService.Query(new SysParametersQueryCondition()
            {
                LangType = MvcContext.UserInfo.logging_locale,
                SearchItemGroup = new SearchItemGroup()
                {
                    Items =
                         [
                             new()
                        {
                            Field = "para_code",
                            Values = [para_code],
                            Compare = CompareOperator.ARRAYIN
                        }
                         ]
                }
            });
            string type = string.Join(",", sysParameters.Where(s => list.Contains(s.FuncCode)).Select(s => s.FunName).ToArray());
            return type;
        }

        /// <summary>
        /// 申請單案件列表查詢
        /// </summary>
        internal static ApplySignatoryDetailResultModel GetApplySignatoryDetail(string apply_number)
        {
            //查詢申請單詳情
            ApplySignatoryDetailResultModel applySignatoryDetails = _repository.GetApplySignatoryDetail(apply_number);

            //修改簽核人員隱碼問題改為進行中案件極機密案件隱碼，同經辦人轉單20250219 pike
            if (applySignatoryDetails.confiden_level.ToUpper() == "01".ToUpper() && (applySignatoryDetails.application_state.ToUpper() != "I".ToUpper() && applySignatoryDetails.application_state.ToUpper() != "T".ToUpper()))
            {
                applySignatoryDetails = HiddenValueConvertHelper.ConvertToHiddenBySingle<ApplySignatoryDetailResultModel>(applySignatoryDetails);
            }

            //查詢申請單案件列表
            List<SignatoryCaseList> signatoryCaseLists = _repository.GetApplySignatoryCaseList(apply_number);

            if (signatoryCaseLists != null)
                signatoryCaseLists = HiddenValueConvertHelper.ConvertToHiddenByList<SignatoryCaseList>(signatoryCaseLists, "od");

            //申請單詳情、案件列表綁定
            applySignatoryDetails.SignatoryCaseList = signatoryCaseLists;
            ActionFilter.InitLogRecord(apply_number, log =>
            {
                StringBuilder stringBuilder = new();
                if (!string.IsNullOrEmpty(apply_number))
                    stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("Signatory_applyNumber", true)}：{apply_number}");
                log.Detail = stringBuilder.ToString();
            });
            return applySignatoryDetails;
        }

        /// <summary>
        /// 簽核人員查詢業務
        /// </summary>
        internal static ApplySignatoryEmpModel GetSignatiryEmp(string apply_number)
        {
            ApplySignatoryEmpModel applySignatoryEmp = new ApplySignatoryEmpModel();
            //簽核人
            applySignatoryEmp.singerList = _repository.GetApplySingerList(apply_number);
            //加簽人
            applySignatoryEmp.inviteeList = _repository.GetApplyInviteeList(apply_number);
            //特殊加簽人
            applySignatoryEmp.specialInviteeList = _repository.GetApplySpecialInviteeList(apply_number);
            return applySignatoryEmp;
        }

        /// <summary>
        /// 修改簽核人員
        /// </summary>
        internal static ApiResultModelByObject UpdateSignatiryEmp(ModifySignatoryEmpModel modifySignatoryEmpModel)
        {

            string apply_number = modifySignatoryEmpModel.apply_number;

            //查詢數據庫最新的簽核人員信息
            ApplySignatoryEmpModel oldapplySignatoryEmp = GetSignatiryEmp(apply_number);
            //頁面數據
            ApplySignatoryEmpModel applySignatoryEmp = modifySignatoryEmpModel.applySignatoryEmp;
            //頁面修改前數據
            ApplySignatoryEmpModel oldPageApplySignatoryEmp = modifySignatoryEmpModel.oldApplySignatoryEmp;
            //查詢申請單當前關卡
            int current_level = _repository.FindCurrentLevelByApplyNumber(apply_number);


            #region 數據處理
            HashSet<string> singerEmpList = new HashSet<string>(applySignatoryEmp.singerEmpList);
            HashSet<string> oldSingerEmpList = new HashSet<string>(oldapplySignatoryEmp.singerEmpList);
            HashSet<string> oldPageSingerEmpList = new HashSet<string>(oldPageApplySignatoryEmp.singerEmpList);
            HashSet<string> inviteeEmpList = new HashSet<string>(applySignatoryEmp.inviteeEmpList);
            HashSet<string> oldInviteeEmpList = new HashSet<string>(oldapplySignatoryEmp.inviteeEmpList);
            HashSet<string> oldPageInviteeEmpList = new HashSet<string>(oldPageApplySignatoryEmp.inviteeEmpList);
            HashSet<string> specialInviteeEmpList = new HashSet<string>(applySignatoryEmp.specialInviteeEmpList);
            HashSet<string> oldSpecialInviteeEmpList = new HashSet<string>(oldapplySignatoryEmp.specialInviteeEmpList);
            HashSet<string> oldPageSpecialInviteeEmpList = new HashSet<string>(oldPageApplySignatoryEmp.specialInviteeEmpList);

            //新增簽核人員
            List<string> InsertSingerEmpList = singerEmpList.Except(oldSingerEmpList).ToList();
            //刪除簽核人員
            List<string> DelSingerEmpList = oldSingerEmpList.Except(singerEmpList).ToList();
            //新增加簽人員
            List<string> InsertInviteeEmpList = inviteeEmpList.Except(oldInviteeEmpList).ToList();
            //刪除加簽人員
            List<string> DelInviteeEmpList = oldInviteeEmpList.Except(inviteeEmpList).ToList();
            //新增特殊加簽人員
            List<string> InsertSpecialInviteeEmpList = specialInviteeEmpList.Except(oldSpecialInviteeEmpList).ToList();
            //刪除特殊加簽人員
            List<string> DelSpecialInviteeEmpList = oldSpecialInviteeEmpList.Except(specialInviteeEmpList).ToList();
            #endregion

            #region 校驗數據是否最新
            if (current_level != modifySignatoryEmpModel.current_levels)
            {
                //數據異動提醒
                return new ApiResultModelByObject()
                {
                    messageType = MessageTypeUtils.Warning.ToString(),
                    rtnSuccess = false,
                    messageTitle = ActionFilter.GetMultilingualValue("custom:messageTitle:dataNotexist"),
                    messageContent = new List<string> { string.Format(ActionFilter.GetMultilingualValue("custom:messageContent:dataChurn")) }
                };
            }
            #endregion

            #region 校驗數據是否最新
            if (!oldSingerEmpList.SetEquals(oldPageSingerEmpList) ||
                !oldInviteeEmpList.SetEquals(oldPageInviteeEmpList) ||
                !oldSpecialInviteeEmpList.SetEquals(oldPageSpecialInviteeEmpList))
            {
                //數據異動提醒
                return new ApiResultModelByObject()
                {
                    messageType = MessageTypeUtils.Warning.ToString(),
                    rtnSuccess = false,
                    messageTitle = ActionFilter.GetMultilingualValue("custom:messageTitle:dataNotexist"),
                    messageContent = new List<string> { string.Format(ActionFilter.GetMultilingualValue("custom:messageContent:dataChurn")) }
                };
            }
            #endregion

            #region 校驗是否在MCP簽核中
            if (_repository.CheckMcpSign(apply_number)) {
                //MCP簽核中
                return new ApiResultModelByObject()
                {
                    messageType = MessageTypeUtils.Warning.ToString(),
                    rtnSuccess = false,
                    messageTitle = ActionFilter.GetMultilingualValue("custom:messageTitle:updateFail"),
                    messageContent = new List<string> { string.Format(ActionFilter.GetMultilingualValue("custom:messageContent:MCPApprovalInProgress")) }
                };
            }
            #endregion

            #region 校驗簽核人員是否重複
            //20250324特殊加簽不卡與當官者重複
            List<string> signatoryEmps = applySignatoryEmp.singerEmpList
                                                            .Concat(applySignatoryEmp.inviteeEmpList)
                                                            .ToList();
            //去重
            List<string> signatoryEmpDistinct = signatoryEmps.Distinct().ToList();

            if (applySignatoryEmp != null && !signatoryEmps.Count.Equals(signatoryEmpDistinct.Count))
            {
                var duplicateElements = signatoryEmps.GroupBy(x => x).Where(group => group.Count() > 1)
                                                 .Select(group => group.Key);
                //取得重複人員工號列表
                string emplids = string.Join(",", duplicateElements.ToList());
                //人員出現重複
                
                return new ApiResultModelByObject()
                {
                    messageType = MessageTypeUtils.Warning.ToString(),
                    rtnSuccess = false,
                    messageTitle = ActionFilter.GetMultilingualValue("custom:messageTitle:updateFail"),
                    messageContent = new List<string> { $"{string.Format(ActionFilter.GetMultilingualValue("custom:messageContent:dataExists"), _repository.getEENameAByEmplids(emplids))}" }
                };
            }
            #endregion

            #region 校驗簽核人員和修改之前是否一樣
            if (singerEmpList.SetEquals(oldSingerEmpList) &&
                inviteeEmpList.SetEquals(oldInviteeEmpList) &&
                specialInviteeEmpList.SetEquals(oldSpecialInviteeEmpList))
            {
                //簽核人員和修改之前一樣
                return new ApiResultModelByObject()
                {
                    messageType = MessageTypeUtils.Warning.ToString(),
                    rtnSuccess = false,
                    messageTitle = ActionFilter.GetMultilingualValue("custom:messageTitle:updateFail"),
                    messageContent = new List<string> { $" {ActionFilter.GetMultilingualValue("custom:messageContent:SigntoryNotChange")}" }
                };
            }
            #endregion

            #region 校驗當關待審人員數量
            if (_repository.GetEffectiveSigner(string.Join(",", applySignatoryEmp.singerEmpList)) < 1)
            {
                //當關待審人員少於一人
                return new ApiResultModelByObject()
                {
                    messageType = MessageTypeUtils.Warning.ToString(),
                    rtnSuccess = false,
                    messageTitle = ActionFilter.GetMultilingualValue("custom:messageTitle:updateFail"),
                    messageContent = new List<string> { $" {ActionFilter.GetMultilingualValue("custom:messageContent:SigntoryCurrentSignerLessOne")}" }
                };
            }
            #endregion

            #region 申請/經辦人階段不能增加被加簽人員
            //
            if ("4".Equals(_repository.CheckApplyState(apply_number)))
            {
                HashSet<string> result = new HashSet<string>(inviteeEmpList.Except(oldInviteeEmpList));
                if (result.Count > 0)
                    //申請/經辦人階段不能增加被加簽人員
                    return new ApiResultModelByObject()
                    {
                        messageType = MessageTypeUtils.Warning.ToString(),
                        rtnSuccess = false,
                        messageTitle = ActionFilter.GetMultilingualValue("custom:messageTitle:updateFail"),
                        messageContent = new List<string> { $" {ActionFilter.GetMultilingualValue("custom:messageContent:StepStartNotAddSinger")}" }
                    };
            }
            #endregion

            #region 校驗通過執行修改
            //插入簽核人員
            //20250303新增歷程記錄和事務
            string insertSingerEmpList = string.Join(",", InsertSingerEmpList);
            DbAccess.PerformInTransaction(context =>
            {
                if (!string.IsNullOrEmpty(insertSingerEmpList))
                {
                    _repository.InsertSignEmp(insertSingerEmpList, apply_number, current_level, context);
                }

                //插入加簽人員
                string insertInviteeEmpList = string.Join(",", InsertInviteeEmpList);
                if (!string.IsNullOrEmpty(insertInviteeEmpList))
                {
                    _repository.InsertInviteeEmp(insertInviteeEmpList, apply_number, InsertInviteeEmpList.Count > 0 ? true : false, current_level, context);
                }


                //刪除簽核人員
                string delInviteeEmpList = string.Join(",", DelSingerEmpList);
                if (!string.IsNullOrEmpty(delInviteeEmpList))
                {
                    _repository.DeleteSignEmp(delInviteeEmpList, apply_number, current_level, context);
                }

                //刪除加簽人員
                string deleteInviteeSignEmp = string.Join(",", DelInviteeEmpList);
                if (!string.IsNullOrEmpty(deleteInviteeSignEmp))
                {
                    //20250219修改簽核人員當加簽人員為0
                    _repository.DeleteInviteeSignEmp(deleteInviteeSignEmp, apply_number, InsertInviteeEmpList.Count == 0 ? true : false, current_level, context);
                }
                SysChangeRecordService.AddSysChangeRecordsToTransaction(modifySignatoryEmpModel.sysChangeRecordParaModel, context);
            });
            #endregion

            #region 郵件
            //郵件
            //如有新增的當關者，則寄送郵件
            List<MailRecipientResultModel> newSigners = applySignatoryEmp.singerList.Concat(applySignatoryEmp.inviteeList)
                                        .ToList()
                                        .Where(s => insertSingerEmpList.Contains(s.emplid))
                                        .Select(s => new MailRecipientResultModel()
                                        {
                                            recipient_emplid = s.emplid,
                                            recipient_deptid = s.deptid
                                        })
                                        .ToList();
            //如有新增的加簽者，則寄送郵件
            List<MailRecipientResultModel> newInvitees = applySignatoryEmp.singerList.Concat(applySignatoryEmp.inviteeList)
                                        .ToList()
                                        .Where(s => InsertInviteeEmpList.Contains(s.emplid))
                                        .Select(s => new MailRecipientResultModel()
                                        {
                                            recipient_emplid = s.emplid,
                                            recipient_deptid = s.deptid,
                                            recipient_type = s.invitee_type.ToString()
                                        })
                                        .ToList();
            ApplyStepDataModel para = _repository.GetFlowStepData(apply_number);
            List<InviteeParaModel> inviteeList = newInvitees.Select(s => new InviteeParaModel()
            {
                invitee_emplid = s.recipient_emplid,
                invitee_deptid = s.recipient_deptid
            }).ToList();

            List<ApplicationApproveProcess> stepList = _processRepository.GetApplicationApprove(para.apply_type, para.form_type, para.apply_number, para.step_id);

            #region 新增當關簽核者
            //當關者新增簽核人流程數據
            FlowStepParaModel flowData = new FlowStepParaModel()
            {
                apply_type = para.apply_type,
                apply_number = apply_number,
                action = _flowRepository.GetAction(para.flow_id, para.step_id, -1)
            };

            if (para.step_id == _flowRepository.GetOrganizationStepid(flowData.flow_id))
            {
                List<MailTypeUtils> organizationMailTypes = new List<MailTypeUtils>();
                if (flowData.apply_type == "C")
                {
                    organizationMailTypes.Add(MailTypeUtils.C1);
                    organizationMailTypes.Add(MailTypeUtils.C2);
                }
                else if (flowData.apply_type == "O")
                {
                    organizationMailTypes.Add(MailTypeUtils.O1);
                    organizationMailTypes.Add(MailTypeUtils.O2);
                }
            }
            else {
                List<MailTypeUtils> acknowledgeMailTypes = new List<MailTypeUtils>();
                if (flowData.apply_type == "C")
                {
                    acknowledgeMailTypes.Add(MailTypeUtils.C1);
                    acknowledgeMailTypes.Add(MailTypeUtils.C2);
                }
                else if (flowData.apply_type == "O")
                {
                    acknowledgeMailTypes.Add(MailTypeUtils.O1);
                    acknowledgeMailTypes.Add(MailTypeUtils.O2);
                }
                else if (flowData.apply_type == "A")
                {
                    acknowledgeMailTypes.Add(MailTypeUtils.F1);
                }
                if (newSigners.Count > 0)
                //發送郵件調整為實時查詢Pike20250303
                SendMailService.SendMail(PublicHelper.GetApplicationType(flowData.apply_type), flowData.apply_number, "", -1, stepList, acknowledgeMailTypes, newSigners);
            }

            //判斷關卡是否是會簽
            EnumContractSendType? sendType = null;
            if (para.step_id == _flowRepository.GetAcknowledgeStepid(para.flow_id))
            {
                sendType = EnumContractSendType.Acknowledge;
            }
            else {
                sendType = EnumContractSendType.FormApply;
            }

            //加簽者需要取加簽流程是不是要發MCP20250325
            if (flowData.action.is_send_mcp) {
                foreach (MailRecipientResultModel next in newSigners)
                {
                    //如果簽核人為空
                    if (string.IsNullOrEmpty(next.recipient_emplid)) continue;
                    try
                    {
                        SendHelper.SendMcpForApply(para.apply_type, sendType.Value, para.apply_number, next.recipient_emplid, next.recipient_deptid, para.step_id, 0);

                        string agentEmp = _applyPermissionRepository.GetAgenByEmp(next.recipient_emplid, next.recipient_deptid);
                        if (!string.IsNullOrEmpty(agentEmp))
                            SendHelper.SendMcpForApply(para.apply_type, sendType.Value, para.apply_number, agentEmp, next.recipient_deptid, para.step_id, 1, next.recipient_emplid);
                    }
                    catch
                    {
                        continue;
                    }
                }
            }

            #endregion

            #region 新增加簽者
            //新增加簽者，獲取當前單號的流程20250305
            FlowStepParaModel invateFlowData = new FlowStepParaModel()
            {
                apply_type = para.apply_type,
                apply_number = apply_number,
                action = _flowRepository.GetAction(para.flow_id, para.step_id, 4)
            };
            //郵件
            List<MailTypeUtils> mailTypes = new List<MailTypeUtils>();
            //如有新增的加簽者，則寄送郵件
            if (newInvitees.Count > 0)
            {
                if (flowData.apply_type == "C")
                {
                    mailTypes.Add(MailTypeUtils.C9);
                }
                else if (flowData.apply_type == "O")
                {
                    mailTypes.Add(MailTypeUtils.O8);
                }
                else if (flowData.apply_type == "A")
                {
                    mailTypes.Add(MailTypeUtils.F3);
                }
                //發送郵件調整為實時查詢Pike20250303
                SendMailService.SendMail(PublicHelper.GetApplicationType(flowData.apply_type), flowData.apply_number, "", -1, stepList, mailTypes, newInvitees);

                //加簽者需要取加簽流程是不是要發MCP20250305
                if (invateFlowData.action.is_send_mcp)
                {
                    foreach (MailRecipientResultModel next in newInvitees)
                    {
                        //如果簽核人為空
                        if (string.IsNullOrEmpty(next.recipient_emplid)) continue;
                        try
                        {
                            SendHelper.SendMcpForApply(para.apply_type, EnumContractSendType.Invite, para.apply_number, next.recipient_emplid, next.recipient_deptid, para.step_id, 0);

                            string agentEmp = _applyPermissionRepository.GetAgenByEmp(next.recipient_emplid, next.recipient_deptid);
                            if (!string.IsNullOrEmpty(agentEmp))
                                SendHelper.SendMcpForApply(para.apply_type, EnumContractSendType.Invite, para.apply_number, agentEmp, next.recipient_deptid, para.step_id, 1, next.recipient_emplid);
                        }
                        catch
                        {
                            continue;
                        }
                    }
                }
            }
            #endregion

            mailTypes = new List<MailTypeUtils>();
            //如有被刪除的加簽者，則寄送郵件
            List<string> RemoveSignEmpList = DelSingerEmpList.Concat(DelInviteeEmpList)
                                                             .Concat(DelSpecialInviteeEmpList).ToList();
            List<MailRecipientResultModel> removedInvitees = oldPageApplySignatoryEmp.inviteeList
                                        .ToList()
                                        .Where(s => RemoveSignEmpList.Contains(s.emplid))
                                        .Select(s => new MailRecipientResultModel()
                                        {
                                            recipient_emplid = s.emplid,
                                            recipient_deptid = s.deptid,
                                            recipient_type = s.invitee_type.ToString()
                                        })
                                        .ToList();
            //如有被刪除的加簽者，則寄送郵件
            if (removedInvitees.Count > 0)
            {
                if (flowData.apply_type == "C")
                {
                    mailTypes.Add(MailTypeUtils.C10);
                }
                else if (flowData.apply_type == "O")
                {
                    mailTypes.Add(MailTypeUtils.O9);
                }
                else if (flowData.apply_type == "A")
                {
                    mailTypes.Add(MailTypeUtils.F4);
                }
                SendMailService.SendMail(PublicHelper.GetApplicationType(flowData.apply_type), para.apply_number, MvcContext.UserInfo.current_emp, para.step_id, stepList, mailTypes, removedInvitees);
            }

            //獲取所有的被移除的簽核人&加簽人
            List<MailRecipientResultModel> removeMcpEmp = oldPageApplySignatoryEmp.inviteeList.Concat(oldPageApplySignatoryEmp.singerList)
                                        .ToList()
                                        .Where(s => RemoveSignEmpList.Contains(s.emplid))
                                        .Select(s => new MailRecipientResultModel()
                                        {
                                            recipient_emplid = s.emplid,
                                            recipient_deptid = s.deptid,
                                            recipient_type = s.invitee_type.ToString()
                                        })
                                        .ToList();
            //如有被移除的加簽者，則取消MCP
            foreach (MailRecipientResultModel removedItem in removeMcpEmp)
            {
                SendHelper.CancelForm(para.apply_number, removedItem.recipient_emplid, para.step_id);

                string agentEmp = _applyPermissionRepository.GetAgenByEmp(removedItem.recipient_emplid, removedItem.recipient_deptid);
                if (!string.IsNullOrEmpty(agentEmp))
                    SendHelper.CancelForm(para.apply_number, agentEmp, para.step_id);
            }

            #endregion

            ActionFilter.InitLogRecord(modifySignatoryEmpModel, log =>
            {
                StringBuilder stringBuilderFormer = new();
                if (!string.IsNullOrEmpty(apply_number))
                    stringBuilderFormer.AppendLine($"{ActionFilter.GetMultilingualValue("Signatory_applyNumber", true)}：{apply_number}");
                stringBuilderFormer.AppendLine($"{ActionFilter.GetMultilingualValue("Signatory_SingerList", true)}：");
                oldapplySignatoryEmp.singerList.ForEach(e =>
                {
                    stringBuilderFormer.AppendLine($"{e.name + "(" + e.name_a + ")"}");
                });
                if (oldapplySignatoryEmp.inviteeList.Count > 0)
                {
                    stringBuilderFormer.AppendLine($"{ActionFilter.GetMultilingualValue("Signatory_inviteeList", true)}：");
                    oldapplySignatoryEmp.inviteeList.ForEach(e =>
                    {
                        stringBuilderFormer.AppendLine($"{e.name + "(" + e.name_a + ")"}");
                    });
                }

                StringBuilder stringBuilder = new();
                if (!string.IsNullOrEmpty(apply_number))
                    stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("Signatory_applyNumber", true)}：{apply_number}");
                stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("Signatory_SingerList", true)}：");
                _repository.GetNameByEmplids(string.Join(",", singerEmpList)).Split(",").ToList().ForEach(e =>
                {
                    stringBuilder.AppendLine($"{e}");
                });
                if (applySignatoryEmp.inviteeList.Count > 0)
                {
                    stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("Signatory_inviteeList", true)}：");
                    _repository.GetNameByEmplids(string.Join(",", inviteeEmpList)).Split(",").ToList().ForEach(e =>
                    {
                        stringBuilder.AppendLine($"{e}");
                    });
                }
                log.Detail = stringBuilder.ToString();
                log.DetailFormer = stringBuilderFormer.ToString();
            });

            return new ApiResultModelByObject()
            {
                rtnSuccess = true,
                listData = true
            };
        }
    }
}
