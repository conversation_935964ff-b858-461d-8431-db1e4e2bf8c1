using Elegal.Interface.Api.Common.Control.Filter;
using Elegal.Interface.Api.Common.Model.ParaModel.FlowApi;
using Elegal.Interface.Api.Common.Model.ResultModel.FlowApi;
using Elegal.Interface.Api.Common.Model.ViewModel.FlowApi;
using Elegal.Interface.Api.Common.Repository;
using Elegal.Interface.ApiData.Service.Model.DbModel;
using Elegal.Orm;
using System.Text;

namespace Elegal.Flow.Api.Repository.DataQuery
{
    /// <summary>
    /// 经办人转单
    /// </summary>
    public class HandlerTransfersRepository : BaseRepository
    {
        /// <summary>
        /// 案件清单
        /// </summary>
        /// <param name="emplid">工号</param>
        /// <param name="deptid">經辦人部門</param>
        /// <param name="transfer_pic_number">申请单号</param>
        /// <returns></returns>
        public IEnumerable<ApplyDetail> CaseData(string emplid = "", string deptid = "", string transfer_pic_number = "")
        {
            StringBuilder sqlBuilder = new StringBuilder();
            sqlBuilder.Append(@$"SELECT
                                    appData.apply_form,--案件類型層級拼接
                                    appData.apply_type,--申請單類型(一級)
                                    appData.form_type,--申請單類型(二級)
                                    appData.apply_number,--申請單號
                                    appData.apply_time,--申請日期
                                    fe.entity,--我方(簡稱)
                                    appData.other_party,--他方
                                    appData.contract_name,--合約名稱
                                    appData.confiden_level,--機密等級，當數值=N'01'時，需要對[合約名稱]進行隱碼顯示
                                    (SELECT fun_name FROM dbo.sys_parameters WHERE para_code = N'confidentStatus' AND lang_type = N'{MvcContext.UserInfo.logging_locale}' AND func_code = CONVERT(NVARCHAR, appData.confiden_level)) AS confiden_level_name,--機密等級名稱
                                    appData.pic_emplid,--經辦人工號
                                    pee.name AS pic_cuser,--經辦人中文名稱
                                    pee.name_a AS pic_euser,--經辦人英文名稱
                                    iif(pee.termination is null,0,1)  AS pic_IsResign ,--經辦人人是否离职
                                    appData.incumbent_emplid,--現任聯絡人
                                    iee.name AS inc_cuser,--現任聯絡人中文名稱
                                    iee.name_a AS inc_euser,--現任聯絡人英文名稱
                                    iif(iee.termination is null,0,1)  AS inc_IsResign  ,--現任聯絡人是否离职
                                    appData.pic_deptid,--原經辦人部門
                                    appData.pic_manager_emplid,--原經辦人部門主管
                                    appData.fill_emplid as apply_fill_emplid,--原經辦人部門主管
                                    pmee.name AS pm_cuser,--原經辦人部門主管中文名稱
                                    pmee.name_a AS pm_euser,--原經辦人部門主管英文名稱
                                    iif(pmee.termination is null,0,1)  AS pm_IsResign   ,--原經辦人部門主是否離職
                                    appData.application_state,--案件狀態
                                    (SELECT fun_name FROM dbo.sys_parameters WHERE para_code = N'applicationState' AND lang_type = N'{MvcContext.UserInfo.logging_locale}' AND func_code = CONVERT(NVARCHAR, appData.application_state)) AS application_state_name,--案件狀態名稱
                                    appData.origin_archive_type,--正本歸檔狀態
                                    (SELECT fun_name FROM dbo.sys_parameters WHERE para_code = N'archiveStatus' AND lang_type = N'{MvcContext.UserInfo.logging_locale}' AND func_code = CONVERT(NVARCHAR, appData.origin_archive_type)) AS origin_archive_type_name,--正本歸檔狀態名稱
                                    (select case when ");
            if (!string.IsNullOrEmpty(transfer_pic_number))
            {
                sqlBuilder.Append(@$" ((tpm.trans_application_status in ('01','02') and tpm.transfer_pic_number = @transfer_pic_number) or (tpm.trans_application_status in ('01','02','03','00') and tpm.transfer_pic_number != @transfer_pic_number))");
            }
            else
            {
                sqlBuilder.Append(@$" tpm.trans_application_status in ('01','02','03','00')");
            }

            sqlBuilder.Append(@$" then 1 else 0 end) as status--申請單狀態
                                    FROM (
                                    SELECT apply_form,apply_type,form_type,apply_number,apply_time,entity_id,other_party,contract_name,pic_emplid,incumbent_emplid,confiden_level,pic_deptid,application_state,fill_emplid,
                                    (SELECT manager_id FROM dbo.ps_sub_og_lgl_vw_a AS og WHERE og.deptid = va.pic_deptid) AS pic_manager_emplid,--經辦人部門主管
                                    (SELECT origin_archive_type FROM dbo.original_archive_record AS ora WHERE ora.apply_number = va.apply_number) AS origin_archive_type
                                    FROM dbo.v_getallapplication AS va
                                    WHERE (application_state = N'A' OR application_state = N'E' OR application_state = N'F')
                                    AND (apply_type = N'C' OR form_type = N'R') ");
            if (!string.IsNullOrEmpty(emplid))
                sqlBuilder.Append(@$" AND (COALESCE(incumbent_emplid,pic_emplid) = CONCAT(N'', @emplid))");
            if (!string.IsNullOrEmpty(deptid))
                sqlBuilder.Append(@$" AND pic_deptid = CONCAT(N'', @deptid)");
            sqlBuilder.Append(@$" ) AS appData
                                    LEFT JOIN (SELECT entity,entity_id,status,entity_namec,entity_namee,group_entityid,1 AS entity_type FROM dbo.fnp_entity
                                    UNION
                                    SELECT aff_company_abb AS entity,aff_company_code AS entity_id,CASE WHEN aff_status = 1 THEN 0 ELSE 1 END AS status,aff_company_cname AS entity_namec,aff_company_ename AS entity_namee,aff_group_entity AS group_entityid,2 AS entity_type FROM dbo.affiliate_company) AS fe ON fe.entity_id = appData.entity_id
                                    LEFT JOIN (SELECT emplid,name,name_a,termination FROM dbo.ps_sub_ee_lgl_vw_a) AS pee ON pee.emplid = appData.pic_emplid
                                    LEFT JOIN (SELECT emplid,name,name_a,termination FROM dbo.ps_sub_ee_lgl_vw_a) AS iee ON iee.emplid = appData.incumbent_emplid
                                    LEFT JOIN (SELECT emplid,name,name_a,termination FROM dbo.ps_sub_ee_lgl_vw_a) AS pmee ON pmee.emplid = appData.pic_manager_emplid
                                    LEFT JOIN (SELECT tph.apply_number,tpm.trans_application_status,transfer_pic_number FROM dbo.transfer_pic_main tpm join transfer_pic_history tph on tpm.transfer_id=tph.transfer_id where tpm.trans_application_status not in  (N'04',N'05')) AS tpm ON tpm.apply_number = appData.apply_number
                                    ORDER BY appData.apply_number ASC;");

            return DbAccess.Database.SqlQuery<ApplyDetail>(sqlBuilder.ToString(), new { emplid = emplid, deptid = deptid, transfer_pic_number = transfer_pic_number });
        }
        /// <summary>
        /// 插入經辦人轉單
        /// </summary>
        /// <param name="model"></param>
        /// <param name="applySequence"></param>
        /// <param name="fillEmp"></param>
        /// <param name="dbContext"></param>
        /// <returns></returns>
        public int Submit(ModifyHandlerTransferModel model, int applySequence, PsSubEeLglVwA fillEmp, IDbContext? dbContext = null)
        {
            model.apply_number = string.Join(",", model.apply_number_list);
            string sql = @"INSERT INTO transfer_pic_main
                    (transfer_pic_number,approved_status,is_new, trans_application_status, approve_emplid, fill_emplid, fill_deptid, apply_emplid, handover_emplid, transfer_remarks, create_user, apply_time,sign_options,approve_deptid,transfer_deptid)
                    VALUES(@transfer_pic_number,1,0, N'01', (select manager_id from ps_sub_og_lgl_vw_a where deptid = @handover_deptid), @create_user,@fill_deptid, COALESCE(@incumbent_emplid,@pic_emplid),@handover_emplid, @transfer_remarks, @create_user,GETUTCDATE(),@sign_options,@handover_deptid,@handover_deptid);

                           INSERT INTO dbo.transfer_pic_history
                            (transfer_id, apply_number, apply_fill_emplid, apply_pic_emplid, apply_pic_deptid, apply_incumbent_emplid, create_user)
                           select SCOPE_IDENTITY(),
                            apply_number,
                            fill_emplid,
                            pic_emplid as apply_pic_emplid,
                            pic_deptid as apply_pic_deptid,
                            COALESCE(incumbent_emplid,pic_emplid) as apply_incumbent_emplid,
                            CONCAT(N'', @create_user) as creat_user 
                            from v_getallapplication as vga
                            where EXISTS(SELECT 1 FROM (SELECT value AS apply_number FROM STRING_SPLIT(@apply_number, ',')) AS aie 
                            WHERE aie.apply_number = vga.apply_number);

                        INSERT INTO flow_step_signer 
(flow_id, step_id, apply_number, signer_time, signer_emplid, signer_deptid, create_time, create_user, is_sign_mcp)
VALUES(13, 1302, @transfer_pic_number, getutcdate(), (select manager_id from ps_sub_og_lgl_vw_a where deptid = @handover_deptid), @handover_deptid, getutcdate(), @create_user, 0)

                        INSERT INTO flow_step_history (flow_id, apply_number, step_id, apply_sequence, step_action, step_opinion, actual_signer_emplid, actual_signer_name, actual_signer_name_a, actual_signer_deptid, should_signer_emplid, should_signer_name, should_signer_name_a, should_signer_deptid, create_time)
                        VALUES (13, @transfer_pic_number, 1301, @applySequence,0 , NULL, @actual_signer_emplid, @actual_signer_name, @actual_signer_name_a, @actual_signer_deptid, @should_signer_emplid, @should_signer_name, @should_signer_name_a, @should_signer_deptid, getutcdate());";
            return ExecuteCommandToTransaction(sql, dbContext,
                new
                {
                    model.transfer_pic_number,
                    model.pic_manager_emplid,
                    create_user = MvcContext.UserInfo.current_emp,
                    incumbent_emplid = model.apply_incumbent_emp?.Emplid,
                    pic_emplid = model.apply_pic_emp?.Emplid,
                    incumbent_deptid = model.apply_incumbent_emp?.Deptid,
                    pic_deptid = model.apply_pic_emp?.Deptid,
                    handover_emplid = model.handover_emp?.Emplid,
                    transfer_remarks = model.transfer_remarks,
                    fill_deptid = MvcContext.UserInfo.current_dept,
                    model.apply_number,
                    model.sign_options,
                    handover_deptid = model.handover_deptid,
                    applySequence = applySequence,
                    actual_signer_emplid = fillEmp.Emplid,
                    actual_signer_name = fillEmp.Name,
                    actual_signer_name_a = fillEmp.NameA,
                    actual_signer_deptid = fillEmp.Deptid,
                    should_signer_emplid = fillEmp.Emplid,
                    should_signer_name = fillEmp.Name,
                    should_signer_name_a = fillEmp.NameA,
                    should_signer_deptid = fillEmp.Deptid,
                });
        }

        /// <summary>
        /// 查詢子列表
        /// </summary>
        /// <param name="transfer_id">transfer_id</param>
        /// <returns></returns>
        internal IEnumerable<ApplyDetail> GetDetailData(string transfer_id)
        {
            StringBuilder sqlBuilder = new StringBuilder();
            sqlBuilder.Append(@$"SELECT
                                    appData.apply_form,--案件類型層級拼接
                                    appData.apply_type,--申請單類型(一級)
                                    appData.form_type,--申請單類型(二級)
                                    appData.apply_number,--申請單號
                                    appData.apply_time,--申請日期
                                    fe.entity,--我方(簡稱)
                                    appData.other_party,--他方
                                    appData.contract_name,--合約名稱
                                    appData.confiden_level,--機密等級，當數值=N'01'時，需要對[合約名稱]進行隱碼顯示
                                    (SELECT fun_name FROM dbo.sys_parameters WHERE para_code = N'confidentStatus' AND lang_type = N'{MvcContext.UserInfo.logging_locale}' AND func_code = CONVERT(NVARCHAR, appData.confiden_level)) AS confiden_level_name,--機密等級名稱
                                    appData.pic_emplid,--經辦人工號
                                    pee.name AS pic_cuser,--經辦人中文名稱
                                    pee.name_a AS pic_euser,--經辦人英文名稱
                                    iif(pee.termination is null,0,1)  AS pic_IsResign ,--經辦人人是否离职
                                    appData.incumbent_emplid,--現任聯絡人
                                    iee.name AS inc_cuser,--現任聯絡人中文名稱
                                    iee.name_a AS inc_euser,--現任聯絡人英文名稱
                                    iif(iee.termination is null,0,1)  AS inc_IsResign  ,--現任聯絡人是否离职
                                    appData.pic_deptid,--原經辦人部門
                                    appData.pic_manager_emplid,--原經辦人部門主管
                                    appData.fill_emplid as apply_fill_emplid,--填單人
                                    pmee.name AS pm_cuser,--原經辦人部門主管中文名稱
                                    pmee.name_a AS pm_euser,--原經辦人部門主管英文名稱
                                    iif(pmee.termination is null,0,1)  AS pm_IsResign   ,--原經辦人部門主是否離職
                                    appData.application_state,--案件狀態
                                    (SELECT fun_name FROM dbo.sys_parameters WHERE para_code = N'applicationState' AND lang_type = N'{MvcContext.UserInfo.logging_locale}' AND func_code = CONVERT(NVARCHAR, appData.application_state)) AS application_state_name,--案件狀態名稱
                                    appData.origin_archive_type,--正本歸檔狀態
                                    (SELECT fun_name FROM dbo.sys_parameters WHERE para_code = N'archiveStatus' AND lang_type = N'{MvcContext.UserInfo.logging_locale}' AND func_code = CONVERT(NVARCHAR, appData.origin_archive_type)) AS origin_archive_type_name--正本歸檔狀態名稱
                                    FROM (
                                    SELECT apply_form,apply_type,form_type,va.apply_number,apply_time,entity_id,other_party,contract_name,pic_emplid,incumbent_emplid,confiden_level,pic_deptid,application_state,fill_emplid,
                                    (SELECT manager_id FROM dbo.ps_sub_og_lgl_vw_a AS og WHERE og.deptid = va.pic_deptid) AS pic_manager_emplid,--經辦人部門主管
                                    (SELECT origin_archive_type FROM dbo.original_archive_record AS ora WHERE ora.apply_number = va.apply_number) AS origin_archive_type
                                    FROM dbo.v_getallapplication AS va
                                    INNER JOIN transfer_pic_history tph on tph.apply_number = va.apply_number 
                                    WHERE tph.transfer_id = @transfer_id) AS appData
                                    LEFT JOIN (SELECT entity,entity_id,status,entity_namec,entity_namee,group_entityid,1 AS entity_type FROM dbo.fnp_entity
                                    UNION
                                    SELECT aff_company_abb AS entity,aff_company_code AS entity_id,CASE WHEN aff_status = 1 THEN 0 ELSE 1 END AS status,aff_company_cname AS entity_namec,aff_company_ename AS entity_namee,aff_group_entity AS group_entityid,2 AS entity_type FROM dbo.affiliate_company) AS fe ON fe.entity_id = appData.entity_id
                                    LEFT JOIN (SELECT emplid,name,name_a,termination FROM dbo.ps_sub_ee_lgl_vw_a) AS pee ON pee.emplid = appData.pic_emplid
                                    LEFT JOIN (SELECT emplid,name,name_a,termination FROM dbo.ps_sub_ee_lgl_vw_a) AS iee ON iee.emplid = appData.incumbent_emplid
                                    LEFT JOIN (SELECT emplid,name,name_a,termination FROM dbo.ps_sub_ee_lgl_vw_a) AS pmee ON pmee.emplid = appData.pic_manager_emplid
                                    ORDER BY appData.apply_number ASC;");
            return DbAccess.Database.SqlQuery<ApplyDetail>(sqlBuilder.ToString(), new { transfer_id = transfer_id });
        }

        /// <summary>
        /// 獲取歷史經辦人部門
        /// </summary>
        /// <param name="emplid">工號</param>
        /// <returns></returns>
        internal IEnumerable<string> GetFormerDept(string emplid)
        {
            StringBuilder sql = new();
            //20250115經辦人部門從獲取經辦人主+副部門改為取該經辦人歷史申請單的所有部門
            sql.Append(@"select DISTINCT deptid from (
                            select pic_deptid as deptid,count(pic_deptid) count
                            from V_GetAllApplication
                            where incumbent_emplid = @emplid
                            and (application_state = N'A' OR application_state = N'E' OR application_state = N'F')
                            and (apply_type = N'C' OR form_type = N'R')
                            group by pic_deptid
                            union 
                            select pic_deptid as deptid,count(pic_deptid) count
                            from V_GetAllApplication where pic_emplid = @emplid
                            and (incumbent_emplid = '' or incumbent_emplid is NULL) 
                            and (application_state = N'A' OR application_state = N'E' OR application_state = N'F')
                            and (apply_type = N'C' OR form_type = N'R')
                            group by pic_deptid
                            )a where a.count > 0  ");
            return DbAccess.Database.SqlQuery<string>(sql.ToString(), new { emplid = emplid });
        }

        /// <summary>
        /// 作廢
        /// </summary>
        /// <param name="model">作廢</param>
        /// <returns></returns>
        internal int Cancellation(ModifyHandlerTransferModel model, IDbContext? dbContext = null)
        {
            string sql = $@"UPDATE dbo.transfer_pic_main
                                SET trans_application_status = N'05', approved_status = 2, approve_emplid = N'', complete_date = getutcdate(), modify_user = N'{MvcContext.UserInfo.current_emp}', modify_time = getutcdate(),
                                    void_reason = N'{model.sign_options}'
                                WHERE transfer_pic_number = N'{model.transfer_pic_number}';";
            return ExecuteCommandToTransaction(sql, dbContext, null);
        }

        /// <summary>
        /// 撤回
        /// </summary>
        /// <param name="model">撤回</param>
        /// <returns></returns>
        internal int WithDraw(ModifyHandlerTransferModel model, string approve_emplid, IDbContext? dbContext = null)
        {
            string sql = $@"UPDATE dbo.transfer_pic_main
                                SET trans_application_status = N'00', approved_status = 1, approve_emplid = NULL, modify_user = N'{MvcContext.UserInfo.current_emp}', modify_time = getutcdate(),
                                    void_reason = N'{model.sign_options}'
                                WHERE transfer_pic_number = N'{model.transfer_pic_number}';";
            return ExecuteCommandToTransaction(sql, dbContext, new { approve_emplid = approve_emplid });
        }

        /// <summary>
        /// Check Apply Transfering
        /// </summary>
        /// <param name="apply_number_list">apply_number_list</param>
        /// <param name="transfer_pic_number">transfer_pic_number</param>
        /// <returns></returns>
        internal IEnumerable<string> CheckApplyNumber(List<string> apply_number_list, string transfer_pic_number = "")
        {
            StringBuilder sql = new StringBuilder();
            sql.Append($@"SELECT tph.apply_number
                            FROM dbo.transfer_pic_main tpm join transfer_pic_history tph on tpm.transfer_id=tph.transfer_id
                            where tph.apply_number in (SELECT value AS apply_number FROM STRING_SPLIT(@apply_number, ',')) ");
            if (!string.IsNullOrEmpty(transfer_pic_number))
            {
                sql.Append(@" and ((tpm.trans_application_status in ('01','02') and tpm.transfer_pic_number = @transfer_pic_number) or (tpm.trans_application_status in ('01','02','03','00') and tpm.transfer_pic_number != @transfer_pic_number))");
            }
            else
            {
                sql.Append(@" and (tpm.trans_application_status in ('01','02','03','00'))");
            }

            return DbAccess.Database.SqlQuery<string>(sql.ToString(), new { apply_number = string.Join(",", apply_number_list), transfer_pic_number = transfer_pic_number });

        }

        /// <summary>
        /// 導出查詢
        /// </summary>
        /// <param name="list">list</param>
        /// <returns></returns>
        internal IEnumerable<HandlerTransferExportModel> QueryExportList(List<string> list)
        {
            StringBuilder sqlBuilder = new();
            sqlBuilder.Append(@$"SELECT DISTINCT
                                        tData.transfer_pic_number,--轉單單號
                                        tData.transfer_time,--轉單日期
                                        tData.apply_emplid as pic_emplid,--經辦/原聯絡人
                                        aee.name AS pic_cuser,--經辦/原聯絡人中文名稱
                                        aee.name_a AS pic_euser,--經辦/原聯絡人英文名稱
                                        tData.handover_emplid,--交接人
                                        hee.name AS han_cuser,--交接人中文名稱
                                        hee.name_a AS han_euser,--交接人英文名稱
                                        (SELECT fun_name FROM dbo.sys_parameters WHERE para_code = N'transApplyStatus' AND lang_type = N'{MvcContext.UserInfo.logging_locale}' AND func_code = CONVERT(NVARCHAR, tData.trans_application_status)) AS trans_application_status_pfn,--申請單狀態名稱
                                        tData.approve_emplid,--目前待審人員
                                        see.name AS approve_cuser,--目前待審人員中文名稱
                                        see.name_a AS approve_euser,--目前待審人員英文名稱
                                        tData.apply_fill_emplid,--填單人
										fee.name AS fill_cuser,--填單人中文名稱
										fee.name_a AS fill_euser,--填單人英文名稱
										aee.deptid as apply_pic_deptid,--原經辦人部門 tData.apply_pic_deptid,--原經辦部門
										pog.manager_id as pic_manager_emplid,--原經辦部門主管
										mee.name AS manager_cuser,--原經辦部門主管中文名稱
										mee.name_a AS manager_euser,--原經辦部門主管英文名稱
										tData.transfer_remarks,--備註
                                        tData.apply_number,
                                        tData.apply_time,
                                        tData.entity,
                                        tData.other_party,
                                        tData.contract_name,
                                        tData.confiden_level_name,
                                        tData.confiden_level,
                                        tData.application_state,
                                        tData.operate_user,--更新人
                                        oee.name AS operate_cuser,--更新人中文名稱
                                        oee.name_a AS operate_euser,--更新人英文名稱
                                        tData.operate_time--更新時間
                                        FROM (
                                        SELECT
                                        tpm.transfer_pic_number,--轉單單號
                                        tpm.apply_time as transfer_time,--轉單日期
                                        tpm.apply_emplid,--經辦/原聯絡人
                                        tpm.handover_emplid,--交接人
                                        tpm.fill_emplid as apply_fill_emplid,--填單人
                                        tpm.transfer_remarks,--備註
                                        tpm.trans_application_status,--申請單狀態
                                        tpm.approve_emplid,--目前待審人員
                                        tph.*,
                                        COALESCE(modify_user,create_user) AS operate_user,--更新者
                                        
                                        COALESCE(modify_time,create_time) AS operate_time--更新日期
                                        FROM dbo.transfer_pic_main AS tpm
                                        LEFT JOIN (SELECT
                                        	appData.transfer_id,
		                                    appData.apply_type,--申請單類型
		                                    appData.apply_number,--申請單號
		                                    appData.apply_time,--申請日期
		                                    fe.entity,--我方(簡稱)
		                                    appData.other_party,--他方
		                                    appData.contract_name,--合約名稱
		                                    appData.confiden_level,--機密等級，當數值=N'01'時，需要對[合約名稱]進行隱碼顯示
		                                    appData .application_state,
		                                    (SELECT fun_name FROM dbo.sys_parameters WHERE para_code = N'confidentStatus' AND lang_type = N'{MvcContext.UserInfo.logging_locale}' AND func_code = CONVERT(NVARCHAR, appData.confiden_level)) AS confiden_level_name--機密等級名稱
		                                    FROM (
		                                    SELECT transfer_id,apply_type,va.apply_number,apply_time,entity_id,other_party,contract_name,confiden_level,application_state 
		                                    FROM dbo.v_getallapplication AS va
		                                    INNER JOIN transfer_pic_history tph on tph.apply_number = va.apply_number 
		                                    --WHERE tph.transfer_id = @transfer_id
		                                    ) AS appData
		                                    LEFT JOIN (SELECT entity,entity_id,status,entity_namec,entity_namee,group_entityid,1 AS entity_type FROM dbo.fnp_entity
		                                    UNION
		                                    SELECT aff_company_abb AS entity,aff_company_code AS entity_id,CASE WHEN aff_status = 1 THEN 0 ELSE 1 END AS status,aff_company_cname AS entity_namec,aff_company_ename AS entity_namee,aff_group_entity AS group_entityid,2 AS entity_type FROM dbo.affiliate_company) AS fe
		                                    ON fe.entity_id = appData.entity_id) AS tph ON tpm.transfer_id = tph.transfer_id
                                        WHERE 1 = 1 ) AS tData
                                        --經辦人/原聯絡人
                                        LEFT JOIN (SELECT emplid,name,name_a,deptid FROM dbo.ps_sub_ee_lgl_vw_a) AS aee ON aee.emplid = tData.apply_emplid
                                        --交接人
                                        LEFT JOIN (SELECT emplid,name,name_a FROM dbo.ps_sub_ee_lgl_vw_a) AS hee ON hee.emplid = tData.handover_emplid
                                        --目前待審人員
                                        LEFT JOIN (SELECT emplid,name,name_a FROM dbo.ps_sub_ee_lgl_vw_a) AS see ON see.emplid = tData.approve_emplid
                                        --更新人
                                        LEFT JOIN (SELECT emplid,name,name_a FROM dbo.ps_sub_ee_lgl_vw_a) AS oee ON oee.emplid = tData.operate_user
                                        INNER JOIN (SELECT value AS transfer_pic_number FROM STRING_SPLIT(@transger_list, ',')) AS an ON an.transfer_pic_number = tData.transfer_pic_number
                                        --填單人
                                        LEFT JOIN (SELECT emplid,name,name_a FROM dbo.ps_sub_ee_lgl_vw_a) AS fee ON fee.emplid = tData.apply_fill_emplid
                                        --經辦人部門
                                        LEFT JOIN (SELECT manager_id,deptid FROM dbo.ps_sub_og_lgl_vw_a) AS pog ON pog.deptid = aee.deptid
                                        --經辦人部門主管
                                        LEFT JOIN (SELECT emplid,name,name_a FROM dbo.ps_sub_ee_lgl_vw_a) AS mee ON mee.emplid = pog.manager_id
                                        ORDER BY operate_time DESC");
            string transger_list = string.Join(",", list);
            return DbAccess.Database.SqlQuery<HandlerTransferExportModel>(sqlBuilder.ToString(), new { transger_list = transger_list });
        }

        /// <summary>
        /// 查詢轉單部分郵件相關人
        /// </summary>
        /// <param name="list">list</param>
        /// <returns></returns>
        internal IEnumerable<TransferEmpInfoModel> QuaryTransferMailInfo(string transfer_pic_number)
        {
            string sql = @"select tpm.transfer_pic_number ,fee.name as fill_name,fee.name_a as fill_name_a,fee.email_address_a  as fill_email_address_a,
                           he.name as handover_name,he.name_a as handover_name_a,he.email_address_a as handover_email_address_a,
                           ma.name as manager_name,ma.name_a as manager_name_a,ma.email_address_a as manager_email_address_a,
                           operator.name as operator_name,operator.name_a as operator_name_a,operator.email_address_a as operator_email_address_a
                    from transfer_pic_main tpm
                    left join (select emplid,name,name_a,email_address_a from ps_sub_ee_lgl_vw_a)fee on tpm.fill_emplid = fee.emplid
                    left join (select emplid,name,name_a,email_address_a from ps_sub_ee_lgl_vw_a)he on tpm.handover_emplid = he.emplid  
                    left join (select emplid,deptid from ps_sub_ee_lgl_vw_a)pic on pic.emplid = tpm.apply_emplid 
                    left join (select manager_id,deptid from ps_sub_og_lgl_vw_a)og on og.deptid = pic.deptid
                    left join (select emplid,name,name_a,email_address_a from ps_sub_ee_lgl_vw_a)ma on og.manager_id = ma.emplid
                    left join (select emplid,name,name_a,email_address_a from ps_sub_ee_lgl_vw_a)operator on operator.emplid = COALESCE (tpm.modify_user,tpm.create_user)
                    where transfer_pic_number = @transfer_pic_number";
            return DbAccess.Database.SqlQuery<TransferEmpInfoModel>(sql.ToString(), new { transfer_pic_number = transfer_pic_number });
        }

        /// <summary>
        /// 寫入轉單鏈接
        /// </summary>
        /// <param name="list">list</param>
        /// <returns></returns>
        internal void MergeApplicationUrl(string transfer_pic_number, string encodeUrl)
        {
            string sql = @"MERGE INTO dbo.application_url AS tgt
                        USING (select @apply_number as apply_number, @hyperlink_path as hyperlink_path, @create_user as create_user, @create_time as create_time) AS src
                        ON (tgt.apply_number=src.apply_number)
                        WHEN MATCHED
                        THEN UPDATE SET
                        tgt.hyperlink_path=src.hyperlink_path, tgt.create_user=src.create_user, tgt.create_time=src.create_time
                        WHEN NOT MATCHED
                        THEN INSERT (apply_number, hyperlink_path, create_user, create_time)
                        VALUES (src.apply_number, src.hyperlink_path, src.create_user, src.create_time);";
            this.ExecuteCommand(sql, new { apply_number = transfer_pic_number, hyperlink_path = encodeUrl, create_user = MvcContext.UserInfo.current_emp, create_time = DateTime.UtcNow, });
        }

        /// <summary>
        /// 重新提交
        /// </summary>
        /// <param name="model">model</param>
        /// <returns></returns>
        internal void ReSubmit(ModifyHandlerTransferModel model, int applySequence, PsSubEeLglVwA fillEmp, IDbContext? dbContext = null)
        {
            string sql = @"

                UPDATE transfer_pic_main SET 
                    trans_application_status = '01',
                    approved_status = 1,
                    approve_emplid = @pic_manager_emplid,
                    modify_user = '',
                    modify_time = @modify_time, 
                    sign_options = @sign_options
                    WHERE transfer_pic_number = @transfer_pic_number;

                 INSERT INTO flow_step_history (flow_id, apply_number, step_id, apply_sequence, step_action, step_opinion, actual_signer_emplid, actual_signer_name, actual_signer_name_a, actual_signer_deptid, should_signer_emplid, should_signer_name, should_signer_name_a, should_signer_deptid, create_time)
                        VALUES (13, @transfer_pic_number, 1301, @applySequence,0 , NULL, @actual_signer_emplid, @actual_signer_name, @actual_signer_name_a, @actual_signer_deptid, @should_signer_emplid, @should_signer_name, @should_signer_name_a, @should_signer_deptid, getutcdate());

                 UPDATE flow_step_signer SET step_id=1302, signer_time=@modify_time, signer_emplid=@pic_manager_emplid, signer_deptid= (select deptid from ps_sub_ee_lgl_vw_a where emplid = @pic_manager_emplid), is_reject=1 WHERE apply_number= @transfer_pic_number;

                 ";
            ExecuteCommandToTransaction(sql, dbContext,
            new
            {
                model.transfer_pic_number,
                model.pic_manager_emplid,
                model.sign_options,
                transfer_deptid = model.handover_deptid,
                applySequence = applySequence,
                actual_signer_emplid = fillEmp.Emplid,
                actual_signer_name = fillEmp.Name,
                actual_signer_name_a = fillEmp.NameA,
                actual_signer_deptid = fillEmp.Deptid,
                should_signer_emplid = fillEmp.Emplid,
                should_signer_name = fillEmp.Name,
                should_signer_name_a = fillEmp.NameA,
                should_signer_deptid = fillEmp.Deptid,
                modify_time = DateTime.UtcNow
            });

        }

        internal void UpdateFlowStepSigner(FlowStepSigner model, IDbContext? dbContext = null)
        {
            string sql = @"UPDATE flow_step_signer SET 
                                        step_id=@step_id, 
                                        signer_time=@signer_time, 
                                        signer_emplid=@signer_emplid,
                                        signer_deptid=@signer_deptid,
                                        is_reject=@is_reject WHERE apply_number=@apply_number;";
            ExecuteCommandToTransaction(sql, dbContext,
            new
            {
                step_id = model.StepId,
                signer_time = DateTime.UtcNow,
                signer_emplid = model.SignerEmplid,
                signer_deptid = model.SignerDeptid,
                is_return = model.IsReturn,
                is_reject = model.IsReject,
                apply_number = model.ApplyNumber
            });
        }

        internal TransferLogInfo QueryLogInfo(string transfer_pic_number)
        {
            string sql = @"select 
                            transfer_pic_number ,
                            string_agg(tph.apply_number,',') as apply_list,
                            concat(pee.name_a,'/',pee.name,'(',pee.emplid,')') as pic,
                            concat(hee.name_a,'/',hee.name,'(',hee.emplid,')') as handover,
                            transfer_deptid,
                            transfer_remarks 
                            from transfer_pic_main tpm
                            inner join transfer_pic_history tph on tpm.transfer_id = tph .transfer_id  
                            left join (select emplid,name,name_a from ps_sub_ee_lgl_vw_a) pee on pee.emplid = tpm.apply_emplid
                            left join (select emplid,name,name_a from ps_sub_ee_lgl_vw_a) hee on hee.emplid = tpm.handover_emplid where transfer_pic_number = @transfer_pic_number
                            group by    transfer_pic_number,concat(pee.name_a,'/',pee.name,'(',pee.emplid,')'),  concat(hee.name_a,'/',hee.name,'(',hee.emplid,')'),transfer_deptid,transfer_remarks";
            return DbAccess.Database.SqlQuery<TransferLogInfo>(sql.ToString(), new { transfer_pic_number = transfer_pic_number }).FirstOrDefault();
        }
    }
}
