﻿using Elegal.Flow.Api.Services.Application;
using Elegal.Interface.Api.Common.Control.Filter;
using Elegal.Interface.Api.Common.Control.Web;
using Elegal.Interface.Api.Common.Model.Enum;
using Elegal.Interface.Api.Common.Model.ResultModel;
using Elegal.Interface.Api.Common.Model.ViewModel.FlowApi;
using Elegal.Interface.Api.Common.Model.ViewModel.FlowApi.Application;
using Elegal.Interface.ApiData.Service.FuncService;
using Elegal.Interface.ApiData.Service.Model.DbModel;
using Microsoft.AspNetCore.Mvc;
using System.Text;

namespace Elegal.Flow.Api.Controllers.Application
{
    /// <summary>
    /// 合约管理作业(关企建档)
    /// </summary>
    [Route("[controller]")]
    [ApiController]
    public class EnterpriseOriginalArchiveController : BaseController
    {
        /// <summary>
        /// 基本资料查询
        /// </summary>
        /// <param name="applyNumber">申请单号</param>
        /// <returns></returns>
        [HttpGet("GetBaseInformation")]
        public async Task<ApiResultModelByObject> GetBaseInformation(string applyNumber)
        {
            ApiResultModelByObject apiResult = new ApiResultModelByObject()
            {
                listData = EnterpriseOriginalArchiveService.GetBaseInformation(applyNumber),
                rtnSuccess = true,
            };
            InitLogRecord(LogRecord => { 
                StringBuilder stringBuilder = new StringBuilder();
                stringBuilder.AppendLine("申請單號:" + applyNumber);
                LogRecord.Detail = stringBuilder.ToString();
            });
            return await Task.FromResult(apiResult);
        }
        /// <summary>
        /// 提交归档资讯
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost("SetOriginalArchive")]
        public async Task<ApiResultModelByObject> SetOriginalArchive(EnterpriseContractViewModel model)
        {
            #region 申請單已結案，攔截操作(場景：前端多開分頁點擊結案)
            var enterpriseApplication = EnterpriseApplicationDataService.FindByKey(model.ApplyNumber);
            if (enterpriseApplication == null || enterpriseApplication.ApplicationState.Equals("E"))
                throw new Exception(ActionFilter.GetMultilingualValue("custom:messageContent:dataChurn"));
            #endregion
            if (model.PaperBasicDatas.GroupBy(b => b.PaperCode).Count() < model.PaperBasicDatas.Count)
                return await Task.FromResult(new ApiResultModelByObject()
                {
                    rtnSuccess = false,
                    messageType = MessageTypeUtils.Warning.ToString(),
                    messageContent = [ActionFilter.GetMultilingualValue("custom:messageContent:repeatPaper")],
                });
            InitLogRecord(model, logRecord => { 
                StringBuilder stringBuilder = new StringBuilder();
                stringBuilder.AppendLine("申請單號:" + model.ApplyNumber);
                logRecord.Detail = stringBuilder.ToString();
            });
            return await Task.FromResult(EnterpriseOriginalArchiveService.SetOriginalArchive(model));
        }
    }
}
