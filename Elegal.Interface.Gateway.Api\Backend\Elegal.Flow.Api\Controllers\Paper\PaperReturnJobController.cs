﻿using Elegal.Flow.Api.Services.Paper;
using Elegal.Interface.Api.Common.Control.Filter;
using Elegal.Interface.Api.Common.Control.Web;
using Elegal.Interface.Api.Common.Model.Enum;
using Elegal.Interface.Api.Common.Model.ParaModel;
using Elegal.Interface.Api.Common.Model.ParaModel.FlowApi;
using Elegal.Interface.Api.Common.Model.ResultModel;
using Microsoft.AspNetCore.Mvc;

namespace Elegal.Flow.Api.Controllers.Paper
{
    /// <summary>
    /// 紙本歸還作業
    /// </summary>
    [Route("[controller]")]
    [ApiController]
    public class PaperReturnJobController : BaseController
    {
        #region 獲取歸還紙本信息
        /// <summary>
        /// 獲取歸還紙本信息
        /// </summary>
        /// <param name="prpm">查詢參數</param>
        /// <returns></returns>
        [HttpPost]
        [Route("GetReturnPaperData")]
        public async Task<ApiResultModelByObject> GetReturnPaperData([FromBody] PaperReturnParaModel prpm)
        {
            ApiResultModelByObject apiResult = new ApiResultModelByObject();

            if (prpm != null)
            {
                switch (prpm.returnType)
                {
                    case 1://掃碼歸還
                        apiResult.listData = PaperReturnJobService.QueryReturnDataByScan(prpm.paperCode, MvcContext.UserInfo.logging_locale);
                        InitLogRecord(prpm, log =>
                        {
                            log.Detail = string.Format(ActionFilter.GetMultilingualValue("PaperReturnJob_paperCode", true), prpm.paperCode);
                        });
                        break;
                    case 2://批次歸還
                        apiResult.listData = PaperReturnJobService.QueryReturnDataByPara(prpm, MvcContext.UserInfo.time_zone, MvcContext.UserInfo.logging_locale);
                        //記錄日誌
                        InsertBatchLog(prpm);
                        break;
                }
                apiResult.rtnSuccess = true;
            }
            return await Task.FromResult(apiResult);
        }

        #region 插入查詢log記錄
        /// <summary>
        /// 插入查詢log記錄
        /// </summary>
        /// <param name="prpm"></param>
        private void InsertBatchLog(PaperReturnParaModel prpm)
        {
            #region 增加日誌
            InitLogRecord(prpm, log =>
            {
                List<string> getLog = new List<string>();
                if (!string.IsNullOrEmpty(prpm.paperCode))
                { getLog.Add(string.Format(ActionFilter.GetMultilingualValue("PaperReturnJob_paperCode", true), prpm.paperCode)); }
                if (!string.IsNullOrEmpty(prpm.lendNumber))
                { getLog.Add(string.Format(ActionFilter.GetMultilingualValue("PaperReturnJob_lendNumber", true), prpm.lendNumber)); }
                if (prpm.actualPickupTimeStart.HasValue)
                { getLog.Add(string.Format(ActionFilter.GetMultilingualValue("PaperReturnJob_actualPickupTimeStart", true), prpm.actualPickupTimeStart.Value.ToString("yyyy/MM/dd"))); }
                if (prpm.actualPickupTimeEnd.HasValue)
                { getLog.Add(string.Format(ActionFilter.GetMultilingualValue("PaperReturnJob_actualPickupTimeEnd", true), prpm.actualPickupTimeEnd.Value.ToString("yyyy/MM/dd"))); }
                if (!string.IsNullOrEmpty(prpm.empValue))
                { getLog.Add(string.Format(ActionFilter.GetMultilingualValue("PaperReturnJob_empValue", true), prpm.empValue)); }
                if (!string.IsNullOrEmpty(prpm.paperName))
                { getLog.Add(string.Format(ActionFilter.GetMultilingualValue("PaperReturnJob_paperName", true), prpm.paperName)); }
                log.Detail = string.Join("\r\n", getLog.Select(s => s));
            });
            #endregion
        }
        #endregion

        #endregion

        #region 紙本歸還作業
        /// <summary>
        /// 紙本歸還作業
        /// </summary>
        /// <param name="prjpm">歸還參數</param>
        /// <returns></returns>
        [HttpPut]
        [Route("ReturnLendPaperJob")]
        public async Task<ApiResultModel> ReturnLendPaperJob([FromBody] List<PaperReturnJobParaModel> prjpm)
        {
            ApiResultModel apiResult = new ApiResultModel();

            if (prjpm.Count() > 0)
            {
                //檢查角色是否多頁面異動
                string checkData = PaperReturnJobService.CheckLendReturnData(prjpm.Select(s => s.paperLendID).Distinct().ToList());
                if (string.IsNullOrEmpty(checkData))
                {
                    apiResult.rtnSuccess = PaperReturnJobService.ReturnLendPaperJob(prjpm, MvcContext.UserInfo.current_emp);
                }
                else
                {
                    apiResult.messageType = MessageTypeUtils.Warning.ToString();
                    apiResult.messageTitle = ActionFilter.GetMultilingualValue("commonWord:information");
                    apiResult.messageContent = new List<string> { checkData };
                }
            }

            if (apiResult.rtnSuccess)
            {
                InsertReturnLog(prjpm);
            }

            return await Task.FromResult(apiResult);
        }

        #region 插入歸還log日誌
        /// <summary>
        /// 插入歸還log日誌
        /// </summary>
        /// <param name="prjpm"></param>
        private void InsertReturnLog(List<PaperReturnJobParaModel> prjpm)
        {
            InitLogRecord(prjpm, log =>
            {
                if (prjpm.Count() > 0)
                {
                    List<string> getLog = new List<string>();
                    //根據lendNumber排序
                    List<PaperReturnJobParaModel> listNewData = prjpm.OrderBy(s => s.lendNumber).OrderBy(p => p.paperCode).ToList();
                    List<string> listLendNumber = listNewData.Select(p => p.lendNumber).Distinct().Order().ToList();
                    //循環獲取到的 listLendNumber 進行遍歷
                    foreach (string ln in listLendNumber)
                    {
                        if (!string.IsNullOrEmpty(ln))
                        { getLog.Add(string.Format(ActionFilter.GetMultilingualValue("PaperReturnJob_lendNumber", true), ln)); }
                        List<PaperReturnJobParaModel> listPaperData = prjpm.Where(p => p.lendNumber.ToLower() == ln.ToLower()).ToList();
                        foreach (PaperReturnJobParaModel lpd in listPaperData)
                        {
                            getLog.Add(string.Format(ActionFilter.GetMultilingualValue("PaperReturnJob_paperCodeAndReturnName", true), lpd.paperCode, lpd.paperReturnName));
                        }
                        getLog.Add(@"-------------------------------------");
                    }

                    log.Detail = string.Join("\r\n", getLog.Select(s => s));
                }
            });
        }
        #endregion

        #endregion
    }
}
