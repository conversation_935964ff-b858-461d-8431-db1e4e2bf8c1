﻿using Elegal.Flow.Api.Services.ApplicationPrint;
using Elegal.Flow.Common.Services.GuanEnterpriseApply;
using Elegal.Interface.Api.Common.Control.Filter;
using Elegal.Interface.Api.Common.Control.Web;
using Elegal.Interface.Api.Common.Model.DBModel.GuanEnterpriseApply;
using Elegal.Interface.Api.Common.Model.ParaModel.FlowApi.FlowStep;
using Elegal.Interface.Api.Common.Model.ResultModel;
using Elegal.Orm;
using iText.StyledXmlParser.Css.Selector.Item;
using Microsoft.AspNetCore.Mvc;
using System.Text;

namespace Elegal.Flow.Api.Controllers.ApplyCase
{
    /// <summary>
    /// 關企建檔
    /// </summary>
    [Route("[controller]")]
    [ApiController]
    public class GuanEnterpriseApplyController : BaseController
    {
        #region 關企建檔資訊 -> 暫存單

        #region 當前操作人員是否存在暫存單
        /// <summary>
        /// 當前操作人員是否存在暫存單
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("GetTempGuanEnterpriseApplication")]
        public async Task<ApiResultModelByObject> GetTempGuanEnterpriseApplication()
        {
            ApiResultModelByObject apiResult = new ApiResultModelByObject();

            apiResult.listData = GuanEnterpriseApplyService.GetTempGuanEnterpriseApplication();
            apiResult.rtnSuccess = true;

            return await Task.FromResult(apiResult);
        }
        #endregion

        #region 重置暫存單(事務處理)
        /// <summary>
        /// 重置暫存單
        /// </summary>
        [HttpPut]
        [Route("ResetTempGuanEnterpriseApplication")]
        public async Task<ApiResultModelByObject> ResetTempGuanEnterpriseApplication()
        {
            ApiResultModelByObject apiResult = new ApiResultModelByObject();

            apiResult.rtnSuccess = GuanEnterpriseApplyService.ResetTempGuanEnterpriseApplicationToTransaction();
            //記錄日誌
            InitLogRecord(logRecord =>
            {
            });
            return await Task.FromResult(apiResult);
        }
        #endregion

        #region 將暫存單改為待歸檔(事務處理)
        /// <summary>
        /// 將暫存單改為待歸檔
        /// </summary>
        /// <param name="para"></param>
        /// <returns></returns>
        [HttpPut]
        [Route("SetGuanEnterpriseApplyInProgress")]
        public async Task<ApiResultModelByObject> SetGuanEnterpriseApplyInProgress([FromBody] FormProgressParaModel? para = null)
        {
            ApiResultModelByObject apiResult = new ApiResultModelByObject();
            var result = GuanEnterpriseApplyService.SetGuanEnterpriseApplyInProgressToTransaction(para);
            apiResult.listData = result;
            apiResult.rtnSuccess = true;
            //記錄日誌
            InitLogRecord(logRecord =>
            {
                StringBuilder stringBuilder = new();
                if (!string.IsNullOrEmpty(result.apply_number)) stringBuilder.AppendLine($"申請單號: {result.apply_number}");
                logRecord.Detail = stringBuilder.ToString();
            });
            return await Task.FromResult(apiResult);
        }
        #endregion

        #endregion

        #region 關企建檔資訊功能修改/新增(事務處理)
        /// <summary>
        /// 關企建檔資訊功能修改/新增
        /// </summary>
        /// <param name="heam"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("HandleGuanEnterpriseApplication")]
        public async Task<ApiResultModelByObject> HandleGuanEnterpriseApplication([FromBody] HandleEnterpriseApplicationModel heam)
        {
            ApiResultModelByObject apiResult = new ApiResultModelByObject();
            string applyNumber = GuanEnterpriseApplyService.HandleGuanEnterpriseApplicationToTransaction(heam);
            apiResult.listData = applyNumber;
            apiResult.rtnSuccess = !string.IsNullOrEmpty(applyNumber);
            //記錄日誌
            InitLogRecord(logRecord =>
            {
                StringBuilder stringBuilder = new();
                var istemp = DbAccess.Database.ExecuteScalar($"select emplid from ps_sub_ee_lgl_vw_a where emplid ='{applyNumber}'")?.ToString() ?? "";
                if (istemp != null && istemp != "")
                    if (!string.IsNullOrEmpty(applyNumber)) stringBuilder.AppendLine($"申請單號: -");
                else if (!string.IsNullOrEmpty(applyNumber)) stringBuilder.AppendLine($"申請單號: {applyNumber}");
                logRecord.Detail = stringBuilder.ToString();
            });
            return await Task.FromResult(apiResult);
        }
        #endregion

        #region 關企建檔資訊 -> 查詢+列印

        #region 獲取關企建檔資訊申請單資訊(全部)
        /// <summary>
        /// 獲取關企建檔資訊申請單資訊(全部)
        /// </summary>
        /// <param name="applyNumber"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetGuanEnterpriseApplication")]
        public async Task<ApiResultModelByObject> GetGuanEnterpriseApplication([FromQuery] string applyNumber)
        {
            ApiResultModelByObject apiResult = new ApiResultModelByObject();

            apiResult.listData = GuanEnterpriseApplyService.GetGuanEnterpriseApplication(applyNumber);
            apiResult.rtnSuccess = true;

            return await Task.FromResult(apiResult);
        }
        #endregion

        #region 獲取關企建檔申請單資訊(列印)
        /// <summary>
        /// 獲取關企建檔申請單資訊(列印)
        /// </summary>
        /// <param name="applyNumber"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetGuanEnterpriseApplicationByPrint")]
        public async Task<ApiResultModelByObject> GetGuanEnterpriseApplicationByPrint([FromQuery] string applyNumber)
        {
            ApiResultModelByObject apiResult = new ApiResultModelByObject();

            apiResult.listData = GuanEnterpriseApplyPrintService.GetGuanEnterpriseApplicationByPrint(applyNumber);
            apiResult.rtnSuccess = true;

            return await Task.FromResult(apiResult);
        }
        #endregion

        #endregion

        #region 自動編號
        /// <summary>
        /// 自動編號
        /// </summary>
        [HttpGet]
        [Route("AutoCompleteContractNumberByGuanEnterprise")]
        public async Task<ApiResultModelByObject> AutoCompleteContractNumberByGuanEnterprise([FromQuery] string apply_number, string entityID, string? year, string? main_contract_number)
        {
            ApiResultModelByObject apiResult = new ApiResultModelByObject();

            bool autoComplete;
            apiResult.listData = GuanEnterpriseApplyService.AutoCompleteContractNumberByGuanEnterprise(apply_number, entityID, year, main_contract_number, out autoComplete);
            apiResult.rtnSuccess = autoComplete;
            if (!autoComplete)
            {
                apiResult.messageContent.Add(ActionFilter.GetMultilingualValue("custom:messageContent:EntityDifferent"));
            }
            return await Task.FromResult(apiResult);
        }
        #endregion

        #region 檢查合約編號
        /// <summary>
        /// 檢查合約編號
        /// </summary>
        [HttpGet]
        [Route("CheckContractNumberByGuanEnterprise")]
        public async Task<ApiResultModelByObject> CheckContractNumberByGuanEnterprise([FromQuery] string contract_number, string apply_number)
        {
            ApiResultModelByObject apiResult = new ApiResultModelByObject();

            apiResult.listData = GuanEnterpriseApplyService.CheckContractNumberByGuanEnterprise(contract_number, apply_number);
            apiResult.rtnSuccess = true;

            return await Task.FromResult(apiResult);
        }
        #endregion
    }
}
