﻿using Elegal.Flow.Api.Repository.LendAppliction;
using Elegal.Flow.Api.Services.Paper;
using Elegal.Interface.Api.Common.Control.Filter;
using Elegal.Interface.Api.Common.FuncHelper;
using Elegal.Interface.Api.Common.FuncService;
using Elegal.Interface.Api.Common.Model.ParaModel.FlowApi.LendAppliction;
using Elegal.Interface.Api.Common.Model.ResultModel.FlowApi.LendAppliction;
using Elegal.Interface.Api.Common.Model.ViewModel;
using NPOI.SS.UserModel;
using NPOI.XSSF.UserModel;

namespace Elegal.Flow.Api.Services.LendAppliction;

public static class LendSearchHisForUserService
{
    private static LendSearchHisForUserRepository _LendSearchRepository = new LendSearchHisForUserRepository();

    /// <summary>
    /// 查詢用戶的歷史借出
    /// </summary>
    /// <param name="para"></param>
    /// <param name="user"></param>
    /// <returns></returns>
    public static List<LendUserHis> QueryLendHisForUser(LendHisForUserPara para, UserInfoModel user)
    {
        if (para.LendStatus == null || para.LendStatus.Count == 0)
        {
            para.LendStatus = new List<string>() { "04", "05" };
        }

        para.ReturnTime.SetUserTimeZone(user.time_zone);
        para.PickupTime.SetUserTimeZone(user.time_zone);
        return _LendSearchRepository.QueryLendHisForUser(para, user.current_emp, user.logging_locale, o =>
        {
            o.ApplicationtTime = o.ApplicationtTime.ConvertDateByTimeZoneByUtc(user.time_zone);
        });
    }

    /// <summary>
    /// 查詢歷史借出的紙本借閲詳情
    /// </summary>
    /// <returns></returns>
    public static List<PaperLendHisDetail> QueryPaperLendHisDetailListForUser(string lendNumber, UserInfoModel user)
    {
        return _LendSearchRepository.QueryPaperLendHisDetailList(lendNumber, o =>
        {
            if (!string.IsNullOrEmpty(o.ContractNumber)) o.ContractNumber = o.ContractNumber.Replace("\u001f", "");
            o.IsOverdueAndReturn = o.OverdueDay > 0 && o.IsReturn == 1;
            o.ShouldReturnTime = o.ShouldReturnTime.ConvertDateByTimeZoneByUtc(user.time_zone);
            o.ActualReturnTime = o.ActualReturnTime.ConvertDateByTimeZoneByUtc(user.time_zone);
            //未取件的没有归还状态
            if (o.IsPickupLend != 1) o.IsReturn = null;
        }, user.logging_locale);
    }



    /// <summary>
    /// 我的借出，历史借出，导出
    /// </summary>
    /// <param name="para"></param>
    /// <param name="user"></param>
    /// <returns></returns>
    public static byte[] QueryLendHisForUserExprt(LendHisForUserExportPara para, UserInfoModel user)
    {
        para.ReturnTime.SetUserTimeZone(user.time_zone);
        para.PickupTime.SetUserTimeZone(user.time_zone);
        List<LendUserHisExport> data = _LendSearchRepository.QueryLendHisForUserExprt(para, user.current_emp, user.logging_locale);

        //创建工作表
        IWorkbook workbook = new XSSFWorkbook();
        ISheet sheet = workbook.CreateSheet("Sheet1");

        //定义一个字体
        IFont font = workbook.CreateFont().configFont(f =>
        {
            f.FontHeightInPoints = 12;
            f.FontName = "Calibri";
        });

        //定义一个字体
        IFont fontRed = workbook.CreateFont().configFont(f =>
        {
            f.FontHeightInPoints = 12;
            f.FontName = "Calibri";
            f.Color = IndexedColors.Red.Index;
        });
        //定义一个样式
        ICellStyle redStyle = workbook.CreateCellStyle().SetDefaultBorder().AddFont(fontRed);
        ICellStyle defaultstyle = sheet.Workbook.CreateCellStyle().SetDefaultBorder().AddFont(font);
        ICellStyle styleCenter = sheet.Workbook.CreateCellStyle().SetDefaultBorder().HorizontallyCenter().AddFont(font);
        ICellStyle redCenter = workbook.CreateCellStyle().SetDefaultBorder().HorizontallyCenter().AddFont(fontRed);

        //设置表头
        List<string> heards = new List<string>()
        {
                ActionFilter.GetMultilingualValue("export:lendId"),
                ActionFilter.GetMultilingualValue("export:accessNumber"),
                ActionFilter.GetMultilingualValue("export:applyDate"),
                ActionFilter.GetMultilingualValue("export:applyStatus"),
                ActionFilter.GetMultilingualValue("export:contractNum"),
                ActionFilter.GetMultilingualValue("export:paperId"),
                ActionFilter.GetMultilingualValue("export:paperName"),
                ActionFilter.GetMultilingualValue("export:returnDate"),
                ActionFilter.GetMultilingualValue("export:overdueDays"),
                ActionFilter.GetMultilingualValue("export:pickUp"),
                ActionFilter.GetMultilingualValue("export:returnPaperStatus")

        };
        sheet.SetHeader(heards);

        //自动列宽
        sheet.SetColumnListWidth(new List<int>() { 4000, 4000, 4000, 4000, 4000, 6000, 10000, 4000, 2000, 3000, 4000 });

        //写入数据
        sheet.WriteData(data, (item, row) =>
        {
            item.ContractNumber = item.ContractNumber?.Replace("\u001f", "") ?? string.Empty;
            row.CreateElegalDefaultCell(0, defaultstyle).SetCellValue(item.LendNumber);
            row.CreateElegalDefaultCell(1, defaultstyle).SetCellValue(item.RetrieveNumber);
            row.CreateElegalDefaultCell(2, defaultstyle).SetCellValue(item.ApplicationtTime.ConvertDateByTimeZoneByUtc(user.time_zone).ToString("yyyy/MM/dd"));
            row.CreateElegalDefaultCell(3, defaultstyle).SetCellValue(item.LendStatusName);
            row.CreateElegalDefaultCell(4, defaultstyle).SetCellValue(item.ContractNumber);
            row.CreateElegalDefaultCell(5, defaultstyle).SetCellValue(item.PaperCode);
            row.CreateElegalDefaultCell(6, defaultstyle).SetCellValue(item.PaperName);
            row.CreateElegalDefaultCell(7, defaultstyle).SetCellValue(item.ActualReturnTime.ConvertDateByTimeZoneByUtc(user.time_zone).ToString("yyyy/MM/dd"));
            row.CreateElegalDefaultCell(8, styleCenter).SetCellValue(item.OverdueDay);
            row.CreateElegalDefaultCell(9, defaultstyle).SetCellValue(item.IsPickupLend == 1 ? ActionFilter.GetMultilingualValue("export:hasPickUp") : ActionFilter.GetMultilingualValue("export:noPickUp"));
            row.CreateElegalDefaultCell(10, defaultstyle).SetCellValue(item.ReturnStatusName);

            if (item.IsOverdueAndReturn)
            {
                row.Cells[7].CellStyle = redStyle;
                row.Cells[8].CellStyle = redCenter;
            }

        });
        return workbook.ToBytes();
    }

    /// <summary>
    /// 借出历史检视 详情数据
    /// </summary>
    /// <param name="historyId">出借历史表主键</param>
    /// <returns></returns>
    public static LoanDetailsView QueryLendHisDetailsViewForUser(string lendNumber, UserInfoModel user)
    {
        return new LoanDetailsView()
        {
            PersonnelInfo = _LendSearchRepository.QueryPersonnelInfoByHisLendNumber(lendNumber, user.logging_locale).Assign(o =>
            {
                o.ApplicationTime = o.ApplicationTime.ConvertDateByTimeZoneByUtc(user.time_zone);
            }),
            DemandInfo = new DemandInfoWebView().Assign(e =>
            {
                List<DemandInfo> list = _LendSearchRepository.QueryDemandInfoByHisLendNumber(lendNumber);
                if (list == null || list.Count == 0) return;
                var d1 = list[0];
                e.RetrieveNumber = d1.RetrieveNumber;
                e.BorrowDays = d1.BorrowDays;
                e.DemandReason = d1.DemandReason;
                e.Discloseperson = d1.Discloseperson;
                e.Other = d1.Other + " " + d1.RetrieveReason;
                e.ActualReturnTime = list.Select(e => e.ActualReturnTime).FirstOrDefault(e => e.HasValue);
                e.Otherperson = d1.Otherperson;
                e.AuthDateEnd = d1.AuthDateEnd.ConvertDateByTimeZoneByUtc(user.time_zone);
                e.AuthDateStart = d1.AuthDateStart.ConvertDateByTimeZoneByUtc(user.time_zone);
                e.Applynumber = string.Join(",", list.Select(e => e.Applynumber).Distinct());
                string dcstr = ActionFilter.GetMultilingualValue("commonWord:DepartmentCode");
                string dacstr = ActionFilter.GetMultilingualValue("commonWord:DepartmentAndCompany");
                e.InHouseStaff = string.Join("<br />", list.Select(item => $"{item.EName}({item.Empid}),{dcstr}:{item.Depid},{dacstr}{item.Company}").Distinct());
            }),
            PickUpInfo = _LendSearchRepository.QueryPickUpInfoByHisLendNumber(lendNumber, user.logging_locale).Assign(o =>
            {
                o.PickupTime = o.PickupTime.ConvertDateByTimeZoneByUtc(user.time_zone);
                o.ActualPickupTime = o.ActualPickupTime.ConvertDateByTimeZoneByUtc(user.time_zone);
            }),
            LendDetails = _LendSearchRepository.QueryLendHisDetailListForView(lendNumber, user.logging_locale, o =>
            {
                o.ActualReturnTime = o.ActualReturnTime.ConvertDateByTimeZoneByUtc(user.time_zone);
                if (!string.IsNullOrEmpty(o.ContractNumber)) o.ContractNumber = o.ContractNumber.Replace("\u001f", "");
                o.AuthDateEnd = o.AuthDateEnd.ConvertDateByTimeZoneByUtc(user.time_zone);
                o.ShouldReturnTime = o.ShouldReturnTime.ConvertDateByTimeZoneByUtc(user.time_zone);
            }),
            UnlendingDetails = _LendSearchRepository.QueryPaperUnlendingDetailList(lendNumber, user.logging_locale, o =>
            {
                o.ContractNumber = o.ContractNumber?.Replace("\u001f", "");
                o.AuthDateEnd = o.AuthDateEnd.ConvertDateByTimeZoneByUtc(user.time_zone);
                o.ShouldReturnTime = o.ShouldReturnTime.ConvertDateByTimeZoneByUtc(user.time_zone);
            })
        }.Assign(res =>
        { //2024/3/13依借出申请的检视接口规格添加同一字段
            if (!string.IsNullOrEmpty(res.DemandInfo.RetrieveNumber))
                res.retrieve_detail = PaperLendingApplicationService.GetOtherApplicationDetail(res.DemandInfo.RetrieveNumber).FirstOrDefault();
        });
    }


}
