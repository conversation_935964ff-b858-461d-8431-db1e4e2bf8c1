using Elegal.Interface.Api.Common.Control.Filter;
using Elegal.Interface.Api.Common.FuncHelper;
using Elegal.Interface.Api.Common.Model.ParaModel;
using Elegal.Interface.Api.Common.Model.ParaModel.FlowApi;
using Elegal.Interface.Api.Common.Repository;

namespace Elegal.Flow.Api.Repository
{
    /// <summary>
    /// 當日下載過多提醒
    /// </summary>
    public class DownloadLogRepository : BaseRepository
    {
        /// <summary>
        /// 實例化
        /// </summary>
        public DownloadLogRepository() : base() { }

        /// <summary>
        /// 插入下載記錄日誌
        /// </summary>
        /// <param name="downloadLog">數據模型</param>
        /// <returns></returns>
        public int Insert(DownloadLogModel downloadLog)
        {
            string sql = @"INSERT INTO sys_download_log
                        (apply_number, emplid_login, emplid_current, function_entrance)
                        VALUES(@apply_number, @emplid_login, @emplid_current, @function_entrance);";
            return this.ExecuteCommand(sql, new { apply_number = downloadLog.apply_number, emplid_login = MvcContext.UserInfo.logging_emp, emplid_current = MvcContext.UserInfo.current_emp, function_entrance = downloadLog.function_entrance});
        }
    }
}
