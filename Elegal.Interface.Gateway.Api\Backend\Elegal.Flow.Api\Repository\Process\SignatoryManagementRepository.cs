using Elegal.Interface.Api.Common.Control.Filter;
using Elegal.Interface.Api.Common.FuncHelper;
using Elegal.Interface.Api.Common.Model.ParaModel.FlowApi.ApplicationSearch;
using Elegal.Interface.Api.Common.Model.ResultModel.FlowApi.Process;
using Elegal.Interface.Api.Common.Repository;
using Elegal.Orm;
using System.Text;

namespace Elegal.Flow.Api.Repository.Process
{
    /// <summary>
    /// 簽核人員管理
    /// </summary>
    public class SignatoryManagementRepository : BaseRepository
    {
        #region 申請單簽核關卡匯總
        /// <summary>
        /// 申請單簽核關卡匯總
        /// </summary>
        private static readonly List<string> signOffStep = AppSettingHelper.GetValue("signOffStep").Split(",").ToList();
        #endregion

        private static PublicHelperRepository _publicHelperRepository = new PublicHelperRepository();

        /// <summary>
        /// 實例化
        /// </summary>
        public SignatoryManagementRepository() : base() { }

        /// <summary>
        /// 簽核人員查詢業務
        /// </summary>
        internal List<SignatoryManagerResultModel> GetDataList(SignatoryManagementSearchModel condition)
        {
            StringBuilder stringBuilder = new StringBuilder();
            TimeZoneInfo timeZoneInfo = TimeZoneInfo.FindSystemTimeZoneById(MvcContext.UserInfo.time_zone);
            string baseUtcOffset = timeZoneInfo.BaseUtcOffset.ToString();
            if (baseUtcOffset.IndexOf("-") > -1)
            {
                baseUtcOffset = baseUtcOffset.Substring(0, 6);
            }
            else
            {
                baseUtcOffset = "+" + baseUtcOffset.Substring(0, 5);
            }
            stringBuilder.Append(@$"SELECT
                            appData.apply_type,
                            appData.form_type,
                            appData.apply_form,
                            appData.apply_number,--申請單號
                            appData.apply_time,--申請日期
                            CASE WHEN appData.apply_type = N'O'
                            THEN (SELECT fun_name FROM dbo.sys_parameters WHERE para_code = N'formType_O' AND lang_type = N'{MvcContext.UserInfo.logging_locale}' AND func_code = CONVERT(NVARCHAR,appData.form_type))
                            ELSE appData.contract_name END AS contract_name,--合約/文件名稱
                            appData.pic_emplid,--經辦人
                            appData.pic_deptid,--經辦部門
                            (CASE WHEN appData.form_type = N'AR' THEN appData.pic_emplid ELSE ee.name END) AS pic_cname,--經辦人中文名稱
                            (CASE WHEN appData.form_type = N'AR' THEN appData.pic_emplid ELSE ee.name_a END) AS pic_ename,--經辦人英文名稱
                            appData.incumbent_emplid,--現任聯絡人
                            (CASE WHEN appData.form_type = N'AR' THEN appData.incumbent_emplid ELSE iee.name END) AS incumbent_cname,--現任聯絡人中文名稱
                            (CASE WHEN appData.form_type = N'AR' THEN appData.incumbent_emplid ELSE iee.name_a END) AS incumbent_ename,--現任聯絡人英文名稱
                            fsh.signer_time as signer_time,--簽核時間 20250221簽核時間顯示改為年月日時分
                            appData.is_new,--案件狀態
                            appData.confiden_level,--機密等級
                            appData.application_state,
                            (SELECT fun_name FROM dbo.sys_parameters WHERE lang_type = N'{MvcContext.UserInfo.logging_locale}' AND para_code = N'confidentStatus' AND func_code = CONVERT(NVARCHAR,appData.confiden_level)) AS confiden_level_pfn,--機密等級名稱
                            fss.step_id as current_levels,--關卡id
                            (SELECT fun_name FROM (SELECT func_code,fun_name FROM dbo.sys_parameters AS sp WHERE lang_type = N'{MvcContext.UserInfo.logging_locale}'
                            AND EXISTS(SELECT 1 FROM (SELECT value AS para_code FROM STRING_SPLIT(@signOffStep, ',')) AS fpc WHERE fpc.para_code = sp.para_code)) AS sp WHERE sp.func_code = CONVERT(NVARCHAR,fss.step_id)) AS current_levels_pfn,--關卡名稱
                            (SELECT STRING_AGG(signerName,N',') FROM dbo.F_GetApplicationSignerName(appData.apply_number) WHERE termination_flag = 0 and inviteSigner = 0) AS current_signer,--目前待審人員(在職)
                            (SELECT STRING_AGG(signerName,N',') FROM dbo.F_GetApplicationSignerName(appData.apply_number) WHERE termination_flag = 1 and inviteSigner = 0) AS current_signer_over,--目前待審人員(離職)
                            (SELECT STRING_AGG(signerName,N',') FROM dbo.F_GetApplicationSignerName(appData.apply_number) WHERE termination_flag = 0 and inviteSigner = 1) AS current_invite_signer,--目前待審人員(在職)
                            (SELECT STRING_AGG(signerName,N',') FROM dbo.F_GetApplicationSignerName(appData.apply_number) WHERE termination_flag = 1 and inviteSigner = 1) AS current_invite_signer_over--目前待審人員(離職)
                            FROM (
                            SELECT apply_number,apply_time,contract_name,incumbent_emplid,pic_emplid,pic_deptid,is_new,confiden_level,apply_type,form_type,apply_form,entity_id,application_state FROM (
                            --簽核中的單據
                            SELECT apply_type,apply_number,apply_time,contract_name,(SELECT contract_number FROM dbo.v_getallcontractnumber AS vgn WHERE vgn.apply_number = gaa.apply_number) AS contract_number,pic_emplid,incumbent_emplid,fill_emplid,pic_deptid,fill_deptid,entity_id,other_party,confiden_level,apply_form,form_type,is_new,application_state FROM v_getallapplication AS gaa WHERE application_state = N'I'
                            UNION
                            --已核准的單據
                            SELECT apply_type,apply_number,apply_time,contract_name,(SELECT contract_number FROM dbo.v_getallcontractnumber AS vgn WHERE vgn.apply_number = gaa.apply_number) AS contract_number,pic_emplid,incumbent_emplid,fill_emplid,pic_deptid,fill_deptid,entity_id,other_party,confiden_level,apply_form,form_type,is_new,application_state FROM v_getallapplication AS gaa WHERE application_state = N'A'
                            UNION
                            --待歸檔的單據
                            SELECT apply_type,apply_number,apply_time,contract_name,(SELECT contract_number FROM dbo.v_getallcontractnumber AS vgn WHERE vgn.apply_number = gaa.apply_number) AS contract_number,pic_emplid,incumbent_emplid,fill_emplid,pic_deptid,fill_deptid,entity_id,other_party,confiden_level,apply_form,form_type,is_new,application_state FROM v_getallapplication AS gaa WHERE application_state = N'F'
                            ) AS ad WHERE 1 = 1 
                            ");

            #region 申請單類型
            string applyType = string.Empty;
            if (condition.applyType.Any())
            {
                applyType = string.Join(",", condition.applyType);
                stringBuilder.AppendLine(@"--案件類型
                                AND EXISTS(SELECT 1 FROM (SELECT value AS applyType FROM STRING_SPLIT(@applyType, ',')) AS alt WHERE alt.applyType = ad.apply_type)");
            }
            #endregion

            #region 案件類型
            //修改簽核人員改為三大申請單類型查詢20250217 pike
            //string formType = string.Empty;
            //if (condition.formType.Any())
            //{
            //    formType = string.Join(",", condition.formType);
            //    stringBuilder.AppendLine(@"--案件類型
            //                    AND EXISTS(SELECT 1 FROM (SELECT value AS form_type FROM STRING_SPLIT(@formType, ',')) AS alt WHERE alt.form_type = ad.apply_form)");
            //}
            #endregion

            #region 申請單號
            if (!string.IsNullOrEmpty(condition.applyNumber))
                stringBuilder.AppendLine(@"--申請單號
                            AND ad.apply_number LIKE CONCAT(N'%',@applyNumber,N'%')");
            #endregion

            #region 申請日期

            if (condition.applyTimeStart != null)
                stringBuilder.AppendLine(@$"--申請日期起始
                            AND (CONVERT(VARCHAR(100), SWITCHOFFSET(ad.apply_time, '{baseUtcOffset}'), 23) >= CONVERT(VARCHAR(100), SWITCHOFFSET(@applyTimeStart, '{baseUtcOffset}'), 23)) ");

            if (condition.applyTimeEnd != null)
                stringBuilder.AppendLine(@$"--申請日期結束
                            AND (CONVERT(VARCHAR(100), SWITCHOFFSET(ad.apply_time, '{baseUtcOffset}'), 23) <= CONVERT(VARCHAR(100), SWITCHOFFSET(@applyTimeEnd, '{baseUtcOffset}'), 23)) ");
            #endregion

            #region 合約編號
            if (!string.IsNullOrEmpty(condition.contractNumber))
                stringBuilder.AppendLine(@"--合約編號
                            AND ad.contract_number LIKE CONCAT(N'%',@contractNumber,N'%')");
            #endregion

            #region 申請人/經辦人

            string empData = string.Empty;
            if (condition.empData.Any())
            {
                empData = string.Join(",", condition.empData);
                stringBuilder.AppendLine(@"--申請人/經辦人
                            AND (
                            --申請人
                            EXISTS(SELECT 1 FROM (SELECT value AS fill_emplid FROM STRING_SPLIT(@empData, ',')) AS alt WHERE alt.fill_emplid = ad.fill_emplid)
                            OR
                            --經辦人
                            EXISTS(SELECT 1 FROM (SELECT value AS pic_emplid FROM STRING_SPLIT(@empData, ',')) AS alt WHERE alt.pic_emplid = ad.pic_emplid)
                            OR
                            --現任聯絡人
                            EXISTS(SELECT 1 FROM (SELECT value AS incumbent_emplid FROM STRING_SPLIT(@empData, ',')) AS alt WHERE alt.incumbent_emplid = ad.incumbent_emplid)
                            )");
            }

            #endregion

            #region 經辦人部門
            string deptID = string.Empty;
            if (condition.deptID.Any())
            {
                deptID = string.Join(",", condition.deptID);
                stringBuilder.AppendLine(@"--經辦人部門
                            AND EXISTS(SELECT 1 FROM (SELECT value AS pic_deptid FROM STRING_SPLIT(@deptID, ',')) AS alt WHERE alt.pic_deptid = ad.pic_deptid)");
            }
            #endregion

            #region 我方主體
            string entityID = string.Empty;
            if (condition.entityID.Any())
            {
                entityID = string.Join(",", condition.entityID);
                stringBuilder.AppendLine(@"--我方主體
                            AND EXISTS(SELECT 1 FROM (SELECT value AS entity_id FROM STRING_SPLIT(@entityID, ',')) AS alt WHERE alt.entity_id = ad.entity_id)");
            }
            #endregion

            //#region 他方
            //if (condition.otherParty.Any())
            //{
            //    List<string> partySql = new List<string>();
            //    foreach (string op in condition.otherParty)
            //    {
            //        partySql.Add(string.Format(@"other_party LIKE CONCAT(N'%', N'{0}', N'%')", op));
            //    }
            //    stringBuilder.AppendLine(string.Format(@"AND ({0})", string.Join(" OR ", partySql.Select(s => s))));
            //}
            //#endregion

            #region 他方 -> 需要進行特殊處理，不能直接使用查詢寫法
            List<string> listOtherPrartyApplyNumber = new List<string>();
            if (condition.otherParty.Any())
            {
                listOtherPrartyApplyNumber = _publicHelperRepository.GetOtherPartApplyNumber(condition.otherParty);
                stringBuilder.AppendLine(@"AND EXISTS(SELECT 1 FROM (SELECT value AS apply_number FROM STRING_SPLIT(@otherPrartyApplyNumber, ',')) AS oan WHERE oan.apply_number = ad.apply_number) ");
            }
            #endregion

            //eidt by SpringJiang 20250620 -> 添加 flow_step_history_from_wilegal 查詢範圍
            stringBuilder.Append(@") AS appData
                            LEFT JOIN (SELECT emplid,name_a,name FROM dbo.ps_sub_ee_lgl_vw_a) AS ee ON ee.emplid = appData.pic_emplid
                            LEFT JOIN (SELECT emplid,name_a,name FROM dbo.ps_sub_ee_lgl_vw_a) AS iee ON iee.emplid = appData.incumbent_emplid
                            LEFT JOIN (SELECT apply_number,MAX(step_id) AS step_id FROM dbo.flow_step_signer GROUP BY apply_number) AS fss ON fss.apply_number = appData.apply_number
                            LEFT JOIN (select apply_number ,Max(signer_time) as signer_time from (
                                            SELECT apply_number,create_time AS signer_time FROM dbo.flow_step_history
                                            union
                                            SELECT apply_number,create_time AS signer_time FROM dbo.flow_step_history_from_wilegal
                                            union 
                                            select apply_number,signer_time FROM dbo.flow_step_signer)a group by a.apply_number ) AS fsh ON fsh.apply_number = appData.apply_number
                            LEFT JOIN (SELECT entity_id,status,stoptype,deliverydate,entity,is_search_data FROM dbo.fnp_entity 
										UNION
										SELECT aff_company_code AS entity_id,CASE WHEN aff_status = 1 THEN 0 ELSE 1 END AS status,N'' AS stoptype,NULL AS deliverydate,aff_company_abb as entity,1 as is_search_data FROM dbo.affiliate_company) entity on entity.entity_id = appData .entity_id 
                            --目前待審人員
                            INNER JOIN (
                            SELECT DISTINCT apply_number FROM (
                            SELECT apply_number,signer_emplid FROM dbo.flow_step_signer WHERE is_invitee = 1
                            UNION
                            --查詢當前關卡簽核者時，需要排除被加簽過的申請單號
                            SELECT apply_number,signer_emplid FROM dbo.flow_step_signer WHERE is_invitee = 0
                            UNION
                            --需要根據簽核層級獲取加簽者的目前待審人員
                            SELECT apply_number,invitee_emplid AS signer_emplid FROM dbo.flow_step_signer_invitee AS fssi
                            WHERE COALESCE(sp_invite_level,0) = (SELECT MIN(COALESCE(sp_invite_level,0)) FROM dbo.flow_step_signer_invitee AS mfssi WHERE mfssi.apply_number = fssi.apply_number)
                            ) AS sid");

            #region 簽核者
            string currentSigner = string.Empty;
            if (condition.currentSigner.Any())
            {
                currentSigner = string.Join(",", condition.currentSigner);
                stringBuilder.Append(@"--簽核者
                            WHERE EXISTS(SELECT 1 FROM (SELECT value AS signer_emplid FROM STRING_SPLIT(@currentSigner, ',')) AS cs WHERE cs.signer_emplid = sid.signer_emplid)");
            }
            #endregion

            stringBuilder.Append(@") AS cs ON cs.apply_number = appData.apply_number
                            WHERE (entity.is_search_data != 0 or entity.is_search_data is NULL) and ");

            //在職條件篩選
            if (condition.is_abnormal != null && condition.is_abnormal.Any())
            {
                stringBuilder.Append(@"(1 != 1 ");
            }
            else
            {
                stringBuilder.Append(@"(1 = 1 ");
            }
            #region 簽核人員是否在職
            if (condition.is_abnormal != null && condition.is_abnormal.Contains("Y"))
                //20250218異常單包含簽核人查不出的單號
                //20250220異常單修改為沒有在職的簽核人DONNA確認的邏輯
                stringBuilder.Append(@" or
                             ((SELECT count(1) FROM dbo.F_GetApplicationSignerName(appData.apply_number) WHERE termination_flag = 0)=0) ");
            if (condition.is_abnormal != null && condition.is_abnormal.Contains("N"))
                stringBuilder.Append(@"or
                             ((SELECT count(1) FROM dbo.F_GetApplicationSignerName(appData.apply_number) WHERE termination_flag = 0)>0)
                             ");
            stringBuilder.Append(") ");
            #endregion

            #region 目前關卡
            string currentLevels = string.Empty;
            if (condition.currentLevels.Any())
            {
                currentLevels = string.Join(",", condition.currentLevels);
                stringBuilder.Append(@"--目前關卡
                            AND EXISTS(SELECT 1 FROM (SELECT value AS step_id FROM STRING_SPLIT(@currentLevels, ',')) AS alt WHERE alt.step_id = fss.step_id)");
            }
            #endregion

            string asignOffStep = string.Join(',', signOffStep.Select(s => s));
            //根據申請日期排序問題提交20250221
            stringBuilder.Append($@" ORDER BY CONVERT(VARCHAR(100), SWITCHOFFSET(appData.apply_time, '{baseUtcOffset}'), 23) DESC,appData.apply_number DESC;");
            return this.NpgsqlSearchByList<SignatoryManagerResultModel>(stringBuilder.ToString(), new
            {
                applyType = applyType,
                //修改簽核人員改為三大申請單類型查詢20250217 pike
                //formType = formType,
                applyNumber = condition.applyNumber,
                applyTimeStart = condition.applyTimeStart,
                applyTimeEnd = condition.applyTimeEnd,
                contractNumber = condition.contractNumber,
                empData = empData,
                deptID = deptID,
                entityID = entityID,
                currentSigner = currentSigner,
                currentLevels = currentLevels,
                signOffStep = string.Join(',', signOffStep.Select(s => s)),
                otherPrartyApplyNumber = string.Join(',', listOtherPrartyApplyNumber.Select(s => s))
            });
        }

        /// <summary>
        /// 查詢申請單簽核詳情
        /// </summary>
        internal ApplySignatoryDetailResultModel GetApplySignatoryDetail(string apply_number)
        {
            string sql = @"select a.apply_number,apply_type,form_type,fill_emplid,fill_cname, fill_ename,pic_emplid,string_agg(legal_name,', ') as legal_name,
pic_cename,pic_ename,contract_name,confiden_level,confiden_level_name,current_levels,current_levels_name,entity,entity_namec,entity_namee,application_state from ( 
 
                                SELECT
                                fa.apply_number,--申請單號
                                fa.fill_emplid,--申請人工號
                                fee.name AS fill_cname,--申請人中文名稱
                                fee.name_a AS fill_ename,--申請人英文名稱
                                fa.pic_emplid,--經辦人工號
                                case when fa.apply_number like 'AR%' then fa.pic_emplid else pee.name end AS pic_cename,--經辦人英文名稱
                                case when fa.apply_number like 'AR%' then fa.pic_emplid else pee.name_a end AS pic_ename,--經辦人英文名稱
                                fa.contract_name,--合約/文件名稱
                                COALESCE (lee.name_a,lee.name ) AS legal_name,--承辦法務英文名稱
                                fa.confiden_level,--機密等級
                                (SELECT fun_name FROM dbo.sys_parameters WHERE lang_type = @langType AND para_code = N'confidentStatus' AND func_code = CONVERT(NVARCHAR,fa.confiden_level)) AS confiden_level_name,--機密等級名稱
                                fa.current_levels,--當前關卡
                                fa.current_levels_name,--當前關卡名稱
                                fe.entity,--主體
                                fe.entity_namec,
                                fe.entity_namee,
                                fa.application_state--案件狀態
                                FROM (
                                SELECT apply_number,application_state,fill_emplid,COALESCE(incumbent_emplid,pic_emplid) AS pic_emplid,contract_name,entity_id,legal_affairs_emplid,confiden_level,
                                current_levels,
                                case when current_levels = '112' then (select fun_name from sys_parameters 
                                where para_code = N'acknowledgeStep' 
                                and func_code = (select min(acknow_level) from flow_step_signer_acknowledge where apply_number = @apply_number and is_used = 0)
                                and lang_type = @langType) else (SELECT fun_name FROM (SELECT func_code,fun_name FROM dbo.sys_parameters AS sp WHERE lang_type = @langType
                                AND EXISTS(SELECT 1 FROM (SELECT value AS para_code FROM STRING_SPLIT(@signOffStep, ',')) AS fpc WHERE fpc.para_code = sp.para_code)) AS sp WHERE sp.func_code = CONVERT(NVARCHAR,current_levels)) end AS current_levels_name
                                FROM dbo.v_getapplicationsearch WHERE apply_number = @apply_number
                                ) AS fa
                                LEFT JOIN (SELECT emplid,name_a,name FROM dbo.ps_sub_ee_lgl_vw_a) AS fee ON fee.emplid = fa.fill_emplid
                                LEFT JOIN (SELECT emplid,name_a,name FROM dbo.ps_sub_ee_lgl_vw_a) AS pee ON pee.emplid = fa.pic_emplid
                                LEFT JOIN (SELECT emplid,name_a,name FROM dbo.ps_sub_ee_lgl_vw_a) AS lee ON lee.emplid = fa.legal_affairs_emplid
                                LEFT JOIN (
                                SELECT entity_id,entity,entity_namec,entity_namee FROM dbo.fnp_entity
                                UNION
                                SELECT aff_company_code AS entity_id,aff_company_abb AS entity,aff_company_cname AS entity_namec,aff_company_ename AS entity_namee FROM dbo.affiliate_company) AS fe ON fe.entity_id = fa.entity_id           

                                UNION

                                SELECT
                                fa.apply_number,--申請單號
                                fa.fill_emplid,--申請人工號
                                fee.name AS fill_cname,--申請人中文名稱
                                fee.name_a AS fill_ename,--申請人英文名稱
                                fa.pic_emplid,--經辦人工號
                                case when fa.apply_number like 'AR%' then fa.pic_emplid else pee.name end AS pic_cename,--經辦人英文名稱
                                case when fa.apply_number like 'AR%' then fa.pic_emplid else pee.name_a end AS pic_ename,--經辦人英文名稱
                                fa.contract_name,--合約/文件名稱
                                COALESCE (lee.name_a,lee.name ) AS legal_name,--承辦法務英文名稱
                                fa.confiden_level,--機密等級
                                (SELECT fun_name FROM dbo.sys_parameters WHERE lang_type = @langType AND para_code = N'confidentStatus' AND func_code = CONVERT(NVARCHAR,fa.confiden_level)) AS confiden_level_name,--機密等級名稱
                                fa.current_levels,--當前關卡
                                fa.current_levels_name,--當前關卡名稱
                                fe.entity,
                                fe.entity_namec,
                                fe.entity_namee,
                                fa.application_state--案件狀態
                                FROM (
                                SELECT DISTINCT apply_number,application_state,fill_emplid,pic_emplid,(SELECT fun_name FROM dbo.sys_parameters WHERE lang_type = @langType AND para_code = N'formType_O' AND func_code = CONVERT(NVARCHAR,form_type)) AS contract_name,
                                legal_emplid AS legal_affairs_emplid,--承辦法務
                                NULL AS confiden_level,--機密等級
                                oa.current_levels,--當前關卡
                                oa.entity_id,
                                (SELECT fun_name FROM (SELECT func_code,fun_name FROM dbo.sys_parameters AS sp WHERE lang_type = @langType
                                AND EXISTS(SELECT 1 FROM (SELECT value AS para_code FROM STRING_SPLIT(@signOffStep, ',')) AS fpc WHERE fpc.para_code = sp.para_code)) AS sp WHERE sp.func_code = CONVERT(NVARCHAR,current_levels)) AS current_levels_name
                                FROM dbo.v_getoapplicaion AS oa WHERE apply_number = @apply_number
                                ) AS fa
                                LEFT JOIN (SELECT emplid,name_a,name FROM dbo.ps_sub_ee_lgl_vw_a) AS fee ON fee.emplid = fa.fill_emplid
                                LEFT JOIN (SELECT emplid,name_a,name FROM dbo.ps_sub_ee_lgl_vw_a) AS pee ON pee.emplid = fa.pic_emplid
                                LEFT JOIN (SELECT emplid,name_a,name FROM dbo.ps_sub_ee_lgl_vw_a) AS lee ON lee.emplid = fa.legal_affairs_emplid
                                LEFT JOIN (
                                SELECT entity_id,entity,entity_namec,entity_namee FROM dbo.fnp_entity
                                UNION
                                SELECT aff_company_code AS entity_id,aff_company_abb AS entity,aff_company_cname AS entity_namec,aff_company_ename AS entity_namee FROM dbo.affiliate_company) AS fe ON fe.entity_id = fa.entity_id  
                                ) a
                                left join (select apply_number,apply_type,form_type from V_GetAllApplication) vga on vga.apply_number = a.apply_number
                                group by a.apply_number,apply_type,form_type,fill_emplid,fill_cname, fill_ename,pic_emplid,pic_cename,pic_ename,contract_name,confiden_level,confiden_level_name,current_levels,current_levels_name,entity,entity_namec,entity_namee,application_state

                                ";
            return this.NpgsqlSearchBySingle<ApplySignatoryDetailResultModel>(sql, new
            {
                apply_number = apply_number,
                langType = MvcContext.UserInfo.logging_locale,
                signOffStep = string.Join(',', signOffStep.Select(s => s))
            });
        }

        /// <summary>
        /// 查詢申請單簽核列表
        /// </summary>
        internal List<SignatoryCaseList> GetApplySignatoryCaseList(string apply_number)
        {
            string sql = @$"	select 
	apply.apply_number,
    va.apply_type,
    va.form_type,
	apply.apply_reason,
	apply.item_list,
	fe.entity_id,
	fe.entity ,
	fe.entity_namec ,
	fe.entity_namee,
	va.other_party ,
	COALESCE (va.incumbent_deptid,va.pic_deptid ) as pic_deptid,
	fcn.contract_number ,
	va.contract_name ,
	va.confiden_level,--機密等級，當數值=N'01'時，需要對[合約名稱]進行隱碼顯示
(SELECT fun_name FROM dbo.sys_parameters WHERE para_code = N'confidentStatus' AND lang_type = N'{MvcContext.UserInfo.logging_locale}' AND func_code = CONVERT(NVARCHAR, va.confiden_level)) AS confiden_level_name--機密等級名稱

	from (
				                                SELECT apply_number,apply_reason,retrieve_apply_number as item_list,null as entity_id FROM dbo.other_application_a WHERE apply_number = @apply_number
				                                union
				                                SELECT b.apply_number,apply_reason,b2.item_list as item_list,null as entity_id FROM dbo.other_application_b b
				                                inner join (SELECT @apply_number as apply_number,trim(value) as item_list FROM STRING_SPLIT((SELECT estimate_apply_number FROM other_application_b WHERE apply_number = @apply_number), ',')) b2
				                                on b.apply_number = b2.apply_number
				                                union
				                                SELECT apply_number,apply_reason,voided_apply_number as item_list,null as entity_id FROM dbo.other_application_c WHERE apply_number = @apply_number
				                                union
				                                SELECT apply_number,apply_reason,seal_apply_number as item_list,null as entity_id FROM dbo.other_application_d WHERE apply_number = @apply_number
				                                union
				                                SELECT apply_number,apply_reason,null as item_list,history_entity_id as entity_id FROM dbo.other_application_e WHERE apply_number = @apply_number
				                                union
				                                SELECT apply_number,apply_reason,null as item_list,special_entity_id as entity_id FROM dbo.other_application_f WHERE apply_number = @apply_number
				                                )apply 
				                                left join dbo.v_getallapplication AS va on apply.item_list = va.apply_number 
				                                 LEFT JOIN (SELECT entity,entity_id,status,entity_namec,entity_namee,group_entityid,1 AS entity_type FROM dbo.fnp_entity
				                                 UNION
				                                 SELECT aff_company_abb AS entity,aff_company_code AS entity_id,CASE WHEN aff_status = 1 THEN 0 ELSE 1 END AS status,aff_company_cname AS entity_namec,aff_company_ename AS entity_namee,aff_group_entity AS group_entityid,2 AS entity_type FROM dbo.affiliate_company) AS fe
				                                on fe.entity_id = COALESCE (apply.entity_id	,va.entity_id)		
				                                left join form_contract_number fcn on fcn.apply_number = va.apply_number";
            return this.NpgsqlSearchByList<SignatoryCaseList>(sql, new
            {
                apply_number = apply_number
            });
        }

        /// <summary>
        /// 根據申請單號查詢簽核人員
        /// </summary>
        internal List<SignatoryEmp> GetApplySingerList(string apply_number)
        {
            string sql = @"--根據申請單號查詢簽核人員
                            --待審人員
                            SELECT signer_emplid as emplid,ee.name,ee.name_a,ee.deptid,
                            case when ee.termination is null then 0 else 1 end as is_termination,
                            null as reason
                            FROM dbo.flow_step_signer
                            inner join ps_sub_ee_lgl_vw_a ee on ee.emplid = signer_emplid
                            WHERE apply_number = @apply_number";
            return this.NpgsqlSearchByList<SignatoryEmp>(sql, new
            {
                apply_number = apply_number
            });
        }

        /// <summary>
        /// 根據申請單號查詢加簽人員
        /// </summary>
        internal List<SignatoryEmp> GetApplyInviteeList(string apply_number)
        {
            string sql = @"--加簽人員
                            SELECT invitee_emplid  as emplid,ee.name,ee.name_a,ee.deptid,
                            case when ee.termination is null then 0 else 1 end as is_termination,
                            null as reason
                            FROM dbo.flow_step_signer_invitee
                            inner join ps_sub_ee_lgl_vw_a ee on ee.emplid = invitee_emplid
                            WHERE invitee_type = 1 AND apply_number = @apply_number;";
            return this.NpgsqlSearchByList<SignatoryEmp>(sql, new
            {
                apply_number = apply_number
            });
        }

        /// <summary>
        /// 根據申請單號查詢特殊加簽人員
        /// </summary>
        internal List<SignatoryEmp> GetApplySpecialInviteeList(string apply_number)
        {
            string sql = @"SELECT 
                            invitee_emplid as emplid,ee.name,ee.name_a,ee.deptid,
                            case when ee.termination is null then 0 else 1 end as is_termination,
                            case when fssi.sp_invite_level = 1 then (select string_agg(fun_name,',') from sys_parameters where para_code = 'spInviteByPic' and lang_type = 'ZH-TW' and func_code in (SELECT value AS para_code FROM STRING_SPLIT(fssi.pic_sp_reason,',')))
                                        when fssi.sp_invite_level = 2 then (select string_agg(fun_name,',') from sys_parameters where para_code = 'spInviteByLegal' and lang_type = 'ZH-TW' and func_code in (SELECT value AS para_code FROM STRING_SPLIT(fssi.legal_sp_reason,',')))
                            end as reason
                            FROM dbo.flow_step_signer_invitee AS fssi 
                            inner join ps_sub_ee_lgl_vw_a ee on ee.emplid = fssi.invitee_emplid
                            WHERE COALESCE(sp_invite_level,0) = (SELECT MIN(COALESCE(sp_invite_level,0)) FROM dbo.flow_step_signer_invitee AS mfssi WHERE mfssi.apply_number = fssi.apply_number)
                            AND invitee_type = 2 
                            AND apply_number = @apply_number;";
            return this.NpgsqlSearchByList<SignatoryEmp>(sql, new
            {
                apply_number = apply_number
            });
        }

        /// <summary>
        /// 當關簽核人員數量
        /// </summary>
        internal int GetEffectiveSigner(string empList)
        {
            string sql = @"select count(1) from ps_sub_ee_lgl_vw_a
                            where emplid in (select value from STRING_SPLIT(@empList,','))
                            and termination is NULL ";
            return (int)this.NpgsqlSearchBySingleValue(sql, new
            {
                empList = empList
            });
        }

        /// <summary>
        /// 檢查申請單狀態
        /// </summary>
        internal int CheckApplyState(string apply_number)
        {
            string sql = @"select DISTINCT fs.step_type from flow_step_signer fss
                            left join flow_step fs on fss.step_id = fs.step_id 
                            where fss.apply_number = @apply_number";
            return (int)this.NpgsqlSearchBySingleValue(sql, new
            {
                apply_number = apply_number
            });
        }

        /// <summary>
        /// 插入簽核人員
        /// </summary>
        internal void InsertSignEmp(string empList, string apply_number, int current_level, IDbContext? dbContext = null)
        {
            StringBuilder sql = new StringBuilder();
            sql.Append(@"insert into flow_step_signer (signer_emplid,signer_deptid,flow_id,step_id,apply_number,is_return,return_step_id,is_invitee,is_acknowledge,is_error,
                    is_reject,is_sign_mcp,signer_time,create_time,create_user)
                    select ee.emplid,ee.deptid,fss.*,GETUTCDATE() as signer_time,GETUTCDATE() as create_time,'' as create_user  from ps_sub_ee_lgl_vw_a ee
                    inner join (SELECT TOP 1 flow_id, step_id, apply_number, is_return, return_step_id, case when (select count(1) from flow_step_signer_invitee where apply_number = @apply_number) > 0 then 1 else 0 end as is_invitee, is_acknowledge, is_error, is_reject, is_sign_mcp FROM dbo.flow_step_signer where apply_number = @apply_number) fss
                    on 1 = 1 
                    where emplid in (select value from STRING_SPLIT(@empList,','));");

            //會簽關卡業務處理
            if (112 == current_level)
            {
                sql.Append(@"insert into flow_step_signer_acknowledge (apply_number, acknow_emplid, acknow_deptid, acknow_level, step_id, acknow_reason, is_used)
                              select apply.apply_number, ee.emplid as acknow_emplid,ee.deptid as acknow_deptid, apply.acknow_level, apply.step_id, apply.acknow_reason, apply.is_used from ps_sub_ee_lgl_vw_a ee
                              inner join (select Top 1 apply_number,  acknow_level, step_id, acknow_reason, is_used from dbo.flow_step_signer_acknowledge --20250221會簽關卡報錯提交
                               where apply_number = @apply_number
                               and acknow_level = (select max(acknow_level) from flow_step_signer_acknowledge e
   												                            where e.apply_number = @apply_number
   												                            and e.is_used = 1)) apply on 1 = 1 
                              where ee.emplid in (select value from STRING_SPLIT(@empList,','));");
            }
            //組織主管關卡業務處理
            if (107 == current_level)
            {
                sql.Append(@"  insert into flow_step_signer_org (apply_number, org_emplid, org_deptid, org_level, step_id, org_type, is_used)
                              select apply.apply_number, ee.emplid as org_emplid,ee.deptid as org_deptid, apply.org_level, apply.step_id, apply.org_type, apply.is_used from ps_sub_ee_lgl_vw_a ee
                              inner join (select Top 1 apply_number, org_level, step_id, org_type, is_used from dbo.flow_step_signer_org 
                               where apply_number = @apply_number --20250221會簽關卡報錯提交 
                                and org_level = (select max(org_level) from flow_step_signer_org e
   												                            where e.apply_number = @apply_number
   												                            and e.is_used = 1)) apply on 1 = 1 
                              where ee.emplid in (select value from STRING_SPLIT(@empList,','));");
            }

            //sql.Append(@"  INSERT INTO flow_step_process 
            //             (apply_number, step_id, signer_emplid, signer_deptid, dept_level, signer_c_name, signer_e_name, signer_quit_time, current_e_name, current_c_name, step_name, next_step_id, step_type, approval_type, approve_level, ac_type_level, ac_type, ac_type_e_name, ac_type_c_name, ac_step, ac_step_level, ac_step_e_name, ac_step_c_name, is_dynamic_step, is_acknowledge, create_user, create_time, apply_type, form_type)
            //              select apply.apply_number, apply.step_id,ee.emplid as signer_emplid,ee.deptid as signer_deptid, apply.dept_level, ee.name as signer_c_name,ee.name_a as signer_e_name, 
            //              apply.signer_quit_time, apply.current_e_name, apply.current_c_name, apply.step_name, apply.next_step_id, apply.step_type, apply.approval_type, apply.approve_level, apply.ac_type_level,
            //              apply.ac_type, apply.ac_type_e_name, apply.ac_type_c_name, apply.ac_step, apply.ac_step_level, apply.ac_step_e_name, apply.ac_step_c_name, apply.is_dynamic_step, apply.is_acknowledge,
            //              create_user, create_time, apply_type, form_type from ps_sub_ee_lgl_vw_a ee
            //              inner join (select Top 1  apply_number, step_id, signer_emplid, signer_deptid, dept_level, signer_c_name, signer_e_name, signer_quit_time, current_e_name, current_c_name, step_name, next_step_id, step_type, approval_type, approve_level, ac_type_level, ac_type, ac_type_e_name, ac_type_c_name, ac_step, ac_step_level, ac_step_e_name, ac_step_c_name, is_dynamic_step, is_acknowledge, create_user, create_time, apply_type, form_type
  			       //                  from flow_step_process where apply_number = @apply_number and step_id = @current_level) apply on 1 = 1 
            //              where ee.emplid in (select value from STRING_SPLIT(@empList,','))");

            ExecuteCommandToTransaction(sql.ToString(), dbContext, new
            {
                empList = empList,
                apply_number = apply_number,
                current_level = current_level
            });
        }

        /// <summary>
        /// 刪除簽核人員
        /// </summary>
        internal void DeleteSignEmp(string empList, string apply_number, int current_level, IDbContext? dbContext = null)
        {
            StringBuilder sql = new();
            sql.Append(@"DELETE FROM dbo.flow_step_signer WHERE apply_number = @apply_number AND signer_emplid in (select value from  STRING_SPLIT(@empList,','));");
            if (112 == current_level)
                sql.Append(@"delete from flow_step_signer_acknowledge where apply_number = @apply_number AND acknow_emplid in (select value from STRING_SPLIT(@empList,','));");
            if (107 == current_level)
                sql.Append(@"delete from flow_step_signer_org where apply_number = @apply_number AND org_emplid in (select value from STRING_SPLIT(@empList,','));");
            //sql.Append(@"delete from flow_step_process where apply_number = @apply_number AND signer_emplid in (select value from STRING_SPLIT(@empList,',')) and step_id = @current_level");
            ExecuteCommandToTransaction(sql.ToString(), dbContext, new
            {
                empList = empList,
                apply_number = apply_number,
                current_level = current_level
            });
        }

        /// <summary>
        /// 刪除加簽人員
        /// </summary>
        internal void DeleteInviteeSignEmp(string empList, string apply_number, bool update_sign_status, int current_level, IDbContext? dbContext = null)
        {
            StringBuilder sql = new();
            sql.Append(@"DELETE FROM dbo.flow_step_signer_invitee WHERE apply_number = @apply_number AND invitee_emplid in (select value from STRING_SPLIT(@empList,',')) AND invitee_type = 1;");

            //20250221會簽關卡沒有加簽人員
            //if (112 == current_level)
            //    sql.Append(@"delete from flow_step_signer_acknowledge where apply_number = @apply_number AND acknow_emplid in (select value from STRING_SPLIT(@empList,','));");
            if (107 == current_level)
                sql.Append(@"delete from flow_step_signer_org where apply_number = @apply_number AND org_emplid in (select value from STRING_SPLIT(@empList,','));");
            //sql.Append(@"delete from flow_step_process where apply_number = @apply_number AND signer_emplid in (select value from STRING_SPLIT(@empList,',')) and step_id = @current_level");

            if (update_sign_status)
                sql.Append(@" update flow_step_signer set is_invitee = 0 where apply_number = @apply_number;");
            ExecuteCommandToTransaction(sql.ToString(), dbContext, new
            {
                empList = empList,
                apply_number = apply_number,
                current_level = current_level
            });
        }

        /// <summary>
        /// 插入加簽人員
        /// </summary>
        internal void InsertInviteeEmp(string empList, string apply_number, bool update_sign_status, int current_level, IDbContext? dbContext = null)
        {
            StringBuilder sql = new();
            sql.Append(@"INSERT INTO dbo.flow_step_signer_invitee
                    (invitee_emplid, invitee_deptid, flow_id, step_id, invitee_type, apply_number,  is_error, create_user, create_time)
 
                    select ee.emplid as invitee_emplid, ee.deptid as invitee_deptid, fss.*,GETUTCDATE() as create_time,'' as create_user  from ps_sub_ee_lgl_vw_a ee
                    inner join (SELECT TOP 1 flow_id, step_id, 1 as invitee_type, apply_number,0 as is_error FROM dbo.flow_step_signer where apply_number = @apply_number) fss
                                        on 1 = 1 
                                        where ee.emplid in (select value from STRING_SPLIT(@empList,','));");

            //20250221會簽關卡沒有加簽人員
            //會簽關卡業務處理
            //if (112 == current_level)
            //{
            //    sql.Append(@"insert into flow_step_signer_acknowledge (apply_number, acknow_emplid, acknow_deptid, acknow_level, step_id, acknow_reason, is_used)
            //                  select apply.apply_number, ee.emplid as acknow_emplid,ee.deptid as acknow_deptid, apply.acknow_level, apply.step_id, apply.acknow_reason, apply.is_used from ps_sub_ee_lgl_vw_a ee
            //                  inner join (select apply_number,  acknow_level, step_id, acknow_reason, is_used from dbo.flow_step_signer_acknowledge 
            //                   where apply_number = @apply_number
            //                   and acknow_level = (select max(acknow_level) from flow_step_signer_acknowledge e
            //			                            where e.apply_number = @apply_number
            //			                            and e.is_used = 1)) apply on 1 = 1 
            //                  where ee.emplid in (select value from STRING_SPLIT(@empList,','));");
            //}

            //組織主管關卡業務處理
            if (107 == current_level)
            {
                sql.Append(@"  insert into flow_step_signer_org (apply_number, org_emplid, org_deptid, org_level, step_id, org_type, is_used)
                              select apply.apply_number, ee.emplid as org_emplid,ee.deptid as org_deptid, apply.org_level, apply.step_id, apply.org_type, apply.is_used from ps_sub_ee_lgl_vw_a ee
                              inner join (select Top 1 apply_number, org_level, step_id, org_type, is_used from dbo.flow_step_signer_org 
                               where apply_number = @apply_number --20250221會簽關卡報錯提交
                                and org_level = (select max(org_level) from flow_step_signer_org e
   												                            where e.apply_number = @apply_number
   												                            and e.is_used = 1)) apply on 1 = 1 
                              where ee.emplid in (select value from STRING_SPLIT(@empList,','));");
            }

            //sql.Append(@"  INSERT INTO flow_step_process 
            //             (apply_number, step_id, signer_emplid, signer_deptid, dept_level, signer_c_name, signer_e_name, signer_quit_time, current_e_name, current_c_name, step_name, next_step_id, step_type, approval_type, approve_level, ac_type_level, ac_type, ac_type_e_name, ac_type_c_name, ac_step, ac_step_level, ac_step_e_name, ac_step_c_name, is_dynamic_step, is_acknowledge, create_user, create_time, apply_type, form_type)
            //              select apply.apply_number, apply.step_id,ee.emplid as signer_emplid,ee.deptid as signer_deptid, apply.dept_level, ee.name as signer_c_name,ee.name_a as signer_e_name, 
            //              apply.signer_quit_time, apply.current_e_name, apply.current_c_name, apply.step_name, apply.next_step_id, apply.step_type, apply.approval_type, apply.approve_level, apply.ac_type_level,
            //              apply.ac_type, apply.ac_type_e_name, apply.ac_type_c_name, apply.ac_step, apply.ac_step_level, apply.ac_step_e_name, apply.ac_step_c_name, apply.is_dynamic_step, apply.is_acknowledge,
            //              create_user, create_time, apply_type, form_type from ps_sub_ee_lgl_vw_a ee
            //              inner join (select Top 1 apply_number, step_id, signer_emplid, signer_deptid, dept_level, signer_c_name, signer_e_name, signer_quit_time, current_e_name, current_c_name, step_name, next_step_id, step_type, approval_type, approve_level, ac_type_level, ac_type, ac_type_e_name, ac_type_c_name, ac_step, ac_step_level, ac_step_e_name, ac_step_c_name, is_dynamic_step, is_acknowledge, create_user, create_time, apply_type, form_type
  			       //                  from flow_step_process where apply_number = @apply_number and step_id = @current_level) apply on 1 = 1 
            //              where ee.emplid in (select value from STRING_SPLIT(@empList,','));");


            if (update_sign_status)
                sql.Append(@" update flow_step_signer set is_invitee = 1 where apply_number = @apply_number;");
            ExecuteCommandToTransaction(sql.ToString(), dbContext, new
            {
                empList = empList,
                apply_number = apply_number,
                current_level = current_level
            });
        }

        /// <summary>
        /// 插入特殊簽核人員
        /// </summary>
        internal void InsertSpecialInviteeEmp(string empList, string apply_number)
        {
            string sql = @"select ee.emplid as invitee_emplid, ee.deptid as invitee_deptid, NULL as sp_invite_level,fss.*,GETUTCDATE() as create_time,'' as create_user  from ps_sub_ee_lgl_vw_a ee
                    left join (SELECT TOP 1 flow_id, step_id, invitee_type, apply_number, is_error, pic_sp_reason, legal_sp_reason, pic_sp_reamrk, legal_sp_reamrk,  sp_invitee_setting, invitee_remark FROM dbo.flow_step_signer_invitee fssi
			                    WHERE invitee_type = 2 
			                    AND COALESCE(sp_invite_level,0) = (SELECT MIN(COALESCE(sp_invite_level,0)) FROM dbo.flow_step_signer_invitee AS mfssi WHERE mfssi.apply_number = fssi.apply_number) 
			                    and fssi.apply_number = @apply_number) fss
                                        on 1 = 1 
                                        where emplid in (select value from STRING_SPLIT(@empList,','))";
            this.ExecuteCommand(sql, new
            {
                empList = empList,
                apply_number = apply_number
            });
        }

        /// <summary>
        /// 獲取申請單流程簽核數據
        /// </summary>
        internal ApplyStepDataModel GetFlowStepData(string apply_number)
        {
            string sql = @"select fss.flow_id,fss.step_id,fss.apply_number,v.apply_type ,v.form_type  from flow_step_signer fss
                            left join V_GetAllApplication v on fss.apply_number = v.apply_number 
                            where fss.apply_number = @apply_number";
            return this.NpgsqlSearchBySingle<ApplyStepDataModel>(sql, new { apply_number = apply_number });
        }

        /// <summary>
        /// 查詢申請單當前關卡
        /// </summary>
        internal int FindCurrentLevelByApplyNumber(string apply_number)
        {
            string sql = @"select current_levels from V_GetApplicationSearch where apply_number = @apply_number
                            union
                           select current_levels from v_getoapplicaion where apply_number = @apply_number";
            return this.NpgsqlSearchBySingle<int>(sql, new { apply_number = apply_number });
        }

        /// <summary>
        /// 查詢申請單當前關卡
        /// </summary>
        internal string GetCurrentLevelsName(string currentLevel)
        {
            string sql = @"SELECT string_agg(fun_name,',') FROM dbo.sys_parameters AS sp WHERE lang_type = @langType
                            AND EXISTS(SELECT 1 FROM (SELECT value AS para_code FROM STRING_SPLIT(N'acknowledgeStep,generalContractStep,capitalContractStep,hrContractStep,rdContractStep,otherAContractStep,otherBContractStep,otherCContractStep,otherDContractStep,otherEContractStep,otherFContractStep,otherGContractStep,geContractStep', ',')) AS fpc WHERE fpc.para_code = sp.para_code)
	                            and sp.func_code in (select value from string_split(@currentLevel,','))";
            return this.NpgsqlSearchBySingle<string>(sql, new { currentLevel = currentLevel, langType = MvcContext.UserInfo.logging_locale });
        }

        /// <summary>
        /// 查詢申請單當前關卡
        /// </summary>
        internal string GetNameByEmplids(string emplids)
        {
            string sql = @"select string_agg(CONCAT(name,'(',name_a,')'),',') from ps_sub_ee_lgl_vw_a
                        where emplid in (select value from string_split(@emplids,','))";
            return this.NpgsqlSearchBySingle<string>(sql, new { emplids = emplids });
        }

        internal string getFormTypeName(List<string> applyType)
        {
            string applyTypes = string.Join(",", applyType);
            //修改簽核人員改為三大申請單類型查詢20250217 pike
            string sql = @"select string_agg(fun_name,',') from (
	                            SELECT fun_name FROM dbo.sys_parameters 
	                            WHERE lang_type = N'ZH-TW' 
	                            AND para_code = N'applicationType' 
	                            AND func_code in (SELECT value AS applyType FROM STRING_SPLIT(@applyTypes, ','))
                            )a";
            //            string sql = @"select string_agg(form_name,',') from (
            //SELECT
            //CONCAT((SELECT fun_name FROM dbo.sys_parameters WHERE lang_type = N'ZH-TW' AND para_code = N'applicationType' AND func_code = CONVERT(NVARCHAR,a.apply_type COLLATE Chinese_Taiwan_Stroke_CI_AS)),N' - ',
            //(SELECT fun_name FROM dbo.sys_parameters AS spl WHERE lang_type = N'ZH-TW' AND EXISTS(SELECT 1 FROM dbo.sys_parameters AS sps WHERE sps.para_code = N'applicationType' AND spl.para_code = CONCAT(N'formType_',sps.func_code)) AND func_code = CONVERT(NVARCHAR,a.form_type COLLATE Chinese_Taiwan_Stroke_CI_AS))) as form_name
            //from (SELECT left(value,1) apply_type,SUBSTRING(value, CHARINDEX('_', value) + 1, LEN(value)) form_type FROM STRING_SPLIT(@formTypes, ',')) a
            //)a";
            return this.NpgsqlSearchBySingle<string>(sql, new { applyTypes = applyTypes });
        }

        /// <summary>
        /// 根據工號列表查詢英文名
        /// </summary>
        internal string getEENameAByEmplids(string emplids)
        {
            string sql = @"select string_agg(name_a,',') from (
	                            SELECT name_a FROM dbo.ps_sub_ee_lgl_vw_a 
	                            WHERE emplid in (SELECT value FROM STRING_SPLIT(@emplids, ','))
                            )a";
            return this.NpgsqlSearchBySingle<string>(sql, new { emplids = emplids });
        }

        /// <summary>
        /// 判斷申請單有沒有在mcp簽核中
        /// </summary>
        internal bool CheckMcpSign(string apply_number)
        {
            string sql = @"select count(1) from (     
                    select apply_number from flow_step_signer_invitee where is_sign_mcp = 1 and apply_number = @apply_number
                    UNION 
                    select apply_number from flow_step_signer where is_sign_mcp = 1 and apply_number = @apply_number) sign";
            return this.NpgsqlSearchBySingle<int>(sql, new { apply_number = apply_number }) > 0;
        }

        /// <summary>
        /// 查詢人員姓名
        /// </summary>
        /// <returns></returns>
        internal string QueryEEInfoByEmplids(string entityID)
        {
            string sql = @"select string_agg(concat(name,'(',name_a,')'),',') from ps_sub_ee_lgl_vw_a ee
where EXISTS(SELECT 1 FROM (SELECT value AS emplid FROM STRING_SPLIT(@entityID, ',')) AS alt WHERE alt.emplid = ee.emplid)";
            return this.NpgsqlSearchBySingle<string>(sql, new { entityID = entityID });
        }

        /// <summary>
        /// 根据主体ID查询主体简称
        /// </summary>
        /// <returns></returns>
        public string GetEntityByEntityID(string entity_id)
        {
            string sql = "select string_agg(entity,',') from fnp_entity e where EXISTS (SELECT 1 FROM (SELECT value AS entity_id FROM STRING_SPLIT(@entity_id, ',')) AS alt WHERE alt.entity_id = e.entity_id);";
            return (string)this.NpgsqlSearchBySingleValue(sql, new { entity_id = entity_id });
        }
    }
}
