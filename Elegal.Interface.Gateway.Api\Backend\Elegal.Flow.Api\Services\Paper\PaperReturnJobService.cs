﻿using Elegal.Flow.Api.Repository.Paper;
using Elegal.Flow.Common.Services;
using Elegal.Interface.Api.Common.Control.Filter;
using Elegal.Interface.Api.Common.Model.Enum;
using Elegal.Interface.Api.Common.Model.ParaModel;
using Elegal.Interface.Api.Common.Model.ParaModel.FlowApi;
using Elegal.Interface.Api.Common.Model.ViewModel.FlowApi;
using Elegal.Orm;

namespace Elegal.Flow.Api.Services.Paper
{
    /// <summary>
    /// 紙本歸還作業
    /// </summary>
    public static class PaperReturnJobService
    {
        private static PaperReturnJobRepository _paperReturnJobRepository = new PaperReturnJobRepository();

        #region 掃碼查詢歸還紙本信息
        /// <summary>
        /// 掃碼查詢歸還紙本信息
        /// </summary>
        /// <param name="paperCode">紙本編號</param>
        /// <param name="langType">語係</param>
        /// <returns></returns>
        public static List<PaperReturnJobViewModel> QueryReturnDataByScan(string paperCode, string langType = "ZH-TW")
        {
            return _paperReturnJobRepository.QueryReturnDataByScan(paperCode);
        }
        #endregion

        #region 查詢歸還紙本信息
        /// <summary>
        /// 查詢歸還紙本信息
        /// </summary>
        /// <param name="prpm">查詢參數集合</param>
        /// <param name="timeZone">時區</param>
        /// <param name="langType">語係</param>
        /// <returns></returns>
        public static List<PaperReturnJobViewModel> QueryReturnDataByPara(PaperReturnParaModel prpm, string timeZone, string langType = "ZH-TW")
        {
            return _paperReturnJobRepository.QueryReturnDataByPara(prpm);
        }
        #endregion

        #region 查詢歸還的lendid是否存在非借出單號
        /// <summary>
        /// 查詢歸還的lendid是否存在非借出單號
        /// </summary>
        /// <param name="listLendID">借出的lendid集合</param>
        /// <returns></returns>
        public static string CheckLendReturnData(List<int> listLendID)
        {
            return _paperReturnJobRepository.CheckLendReturnData(listLendID) ? ActionFilter.GetMultilingualValue("custom:messageContent:lendReturnCheck") : "";
        }
        #endregion

        #region 歸還作業邏輯處理(事務處理)
        /// <summary>
        /// 歸還作業邏輯處理
        /// </summary>
        /// <param name="listPrjpm">操作對象</param>
        /// <param name="emplid">操作者工號</param>
        /// <returns></returns>
        public static bool ReturnLendPaperJob(List<PaperReturnJobParaModel> listPrjpm, string emplid)
        {
            bool updateResult = false;
            List<int> closeLendID = new List<int>();//獲取結案的借出申請單id

            //歸還作業改為使用事務的寫法
            DbAccess.PerformInTransaction(context =>
            {
                updateResult = _paperReturnJobRepository.ReturnLendPaperJob(listPrjpm, emplid, context);
                List<int> listLendID = listPrjpm.Select(s => s.paperLendID).Distinct().ToList();
                //修改數據成功後，修改主表狀態
                if (updateResult)
                {
                    foreach (int lendID in listLendID)
                    {
                        //驗證是否存在借出中數據
                        if (_paperReturnJobRepository.HasOnLendData(lendID, context))
                        {
                            //表示無借出中案件，對該申請單結案，並且插入歷史表
                            updateResult = _paperReturnJobRepository.UpdatePaperLendStatus(lendID, emplid, context);
                            //存入歷史表
                            updateResult = _paperReturnJobRepository.InsertLendHistory(lendID, emplid, context);
                            //將結案的lendid存入集合，後面統一發送郵件
                            closeLendID.Add(lendID);
                        }
                    }
                }
            });

            //新增結案郵件
            if (closeLendID.Any())
            {
                SendCloseMail(closeLendID);
            }

            return updateResult;
        }
        #endregion

        #region 結案郵件
        /// <summary>
        /// 結案郵件
        /// </summary>
        /// <param name="closeLendID">已被結案的申請單id</param>
        /// <exception cref="Exception"></exception>
        private static void SendCloseMail(List<int> closeLendID)
        {
            //改用新方法
            foreach (int cli in closeLendID.Distinct().ToList())
            {
                //根據被結案的id獲取對應的歸還案件單號
                string lend_number = _paperReturnJobRepository.GetLendNumber(cli);
                if (!string.IsNullOrEmpty(lend_number))
                {
                    SendMailService.SendLendingMail(lend_number, new List<MailTypeUtils> { MailTypeUtils.LC });
                }
            }

            #region 原始方法 後期刪除
            ////獲取郵件模板(借出申請歸還結案通知)
            //SysEmailContent sysEmailContent = DbAccess.FirstOrDefault<SysEmailContent>(f => f.EType.Equals(MailTypeUtils.LC.ToString()));
            ////不為空繼續作業
            //if (sysEmailContent != null)
            //{
            //    foreach (int cli in closeLendID.Distinct().ToList())
            //    {
            //        #region 總信箱+默認發送人員
            //        //總信箱
            //        string legalEMail = AppSettingHelper.Configuration["legalEMail"] ?? "";
            //        //默認發送人員(如有揭露人員/經辦人，則不需要使用默認發送人員)
            //        string eSend = legalEMail;
            //        #endregion

            //        #region 獲取發送body中必要數據
            //        //根據 lendid 獲取 借出單號，申請日期，其他申請單號，經辦人(單一數據)
            //        LendClosedMailModel lcmm = _paperReturnJobRepository.GetLendClosedData(cli);
            //        //根據 lendid 獲取 填單人、填單人代理人、經辦人郵箱、Legal總信箱(單一數據)
            //        string cc = _paperReturnJobRepository.GetLendClosedCc(cli);
            //        //根據 lendid 獲取 借出單號對應的已經取件的借出明細數據集合(需要拼接)
            //        List<LendClosedMailModel> listDetial = _paperReturnJobRepository.GetLendClosedDetailData(cli);
            //        #endregion

            //        #region 獲取郵件發送人員信息
            //        //根據 lendid 獲取需要發送揭露人員(多個人員，已拼接為一筆數據，只發送一封郵件)
            //        List<LendClosedMailModel> listReceiver = _paperReturnJobRepository.GetLendClosedExposeData(cli);
            //        //如果揭露人員無數據時，則將郵件發送給經辦人
            //        if (string.IsNullOrEmpty(listReceiver[0].oCEname) && string.IsNullOrEmpty(listReceiver[0].oEmail))
            //        { listReceiver = _paperReturnJobRepository.GetLendClosedHandleData(cli); }
            //        //當郵件不為空時，移除郵件中的NULL值
            //        if (!string.IsNullOrEmpty(listReceiver[0].oEmail))
            //        { eSend = listReceiver[0].oEmail.Replace("NULL;", "").Replace("NULL", ""); }
            //        #endregion

            //        #region 拼接郵件信息
            //        SysEmail se = new SysEmail();
            //        //郵件標題
            //        se.ESubject = string.Format(sysEmailContent.ESubject, lcmm.lend_number);

            //        #region 郵件發送人員
            //        //1、edit by springjiang 20240305
            //        //  1.1、沒有揭露人員的時候發給經辦人
            //        //  1.2、如果揭露人員和經辦人都沒有郵箱（正常情況下不會發生），就發給elegal總郵箱
            //        //  1.3、CC不變
            //        se.EReceiver = string.IsNullOrEmpty(eSend) ? legalEMail : eSend;
            //        #endregion

            //        //郵件抄送人員
            //        se.ECc = string.IsNullOrEmpty(cc) ? legalEMail : string.Format(@"{0};{1}", cc, legalEMail);

            //        #region 郵件內容

            //        #region 表格模板 紙本名稱需要使用text-align: left;樣式
            //        string tableTemp = @"<tr><td>{0}</td><td>{1}</td><td style=""text-align: left;"">{2}</td><td>{3}</td><td>{4}</td></tr>";
            //        //中文模板數據
            //        List<string> tableContentC = new List<string>();
            //        //英文模板數據
            //        List<string> tableContentE = new List<string>();
            //        //根據紙本code排序
            //        if (listDetial.Count() > 0) { listDetial = listDetial.OrderBy(p => p.paper_code).ToList(); }
            //        //拼接郵件表格模板數據
            //        foreach (LendClosedMailModel lcl in listDetial)
            //        {
            //            tableContentC.Add(string.Format(tableTemp, lcl.contract_number, lcl.paper_code, lcl.paper_name, lcl.returnStatusName, lcl.localActualDate));
            //            tableContentE.Add(string.Format(tableTemp, lcl.contract_number, lcl.paper_code, lcl.paper_name, lcl.returnStatusEName, lcl.localActualDate));
            //        }
            //        #endregion

            //        //根據查詢結果拼接郵件發送內容模板
            //        se.EContent = string.Format(sysEmailContent.EContent,
            //                           listReceiver[0].oCEname.Replace("()", ""),//揭露人員中(英文名)(多個人員，以N'、'分割)
            //                            lcmm.lend_number,//申請單號
            //                            lcmm.localApplicationDate,//申請日期
            //                            lcmm.retrieve_number,//其他申請調閱單號
            //                            lcmm.hCName,//經辦人中文名稱
            //                            lcmm.hEName,//經辦人英文名稱
            //                            string.Join("\r\n", tableContentC.Select(c => c)),//借出明細中文集合
            //                            string.Join("\r\n", tableContentE.Select(e => e))//借出明細英文集合
            //                            );
            //        #endregion

            //        #region 郵件默認參數
            //        se.ESendtime = DateTime.UtcNow;
            //        se.ESendnum = 0;
            //        se.EIssend = (int)YesOrNoUtils.No;
            //        se.EType = sysEmailContent.EType;
            //        #endregion

            //        #endregion

            //        //新增郵件數據
            //        SysEmailDataService.Create(se);
            //    }
            //}
            //else
            //{
            //    throw new Exception(ActionFilter.GetMultilingualValue("custom:messageContent:mailContentEmpty"));
            //}
            #endregion
        }
        #endregion
    }
}
