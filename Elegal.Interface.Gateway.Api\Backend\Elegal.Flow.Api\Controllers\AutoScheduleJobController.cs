﻿using Elegal.Interface.Api.Common.Control.Filter;
using Elegal.Interface.Api.Common.Control.Web;
using Elegal.Interface.Api.Common.Model.ParaModel;
using Elegal.Interface.Api.Common.Model.ResultModel;
using Elegal.Interface.Api.Common.Model.ViewModel;
using Elegal.Orm.Filters;
using Elegal.Flow.Api.Services;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System.Text;
using Microsoft.Extensions.Primitives;

#nullable disable
namespace Elegal.Flow.Api.Controllers
{
    /// <summary>
    /// ScheduleJobController
    /// </summary>
    [Route("[controller]")]
    [ApiController]
    public class AutoScheduleJobController : BaseController

    {
        /// <summary>
        /// 查询列表数据
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [Route("QueryScheduleJobInfo")]
        public ApiResultModelByObject QueryScheduleJobInfo()
        {
            ApiResultModelByObject model = new ApiResultModelByObject();
            UserInfoModel user = MvcContext.UserInfo;
            model.listData = AutoScheduleJobService.QueryScheduleJobInfo(user.logging_locale, user.time_zone);
            model.rtnSuccess = true;
            return model;
        }

        /// <summary>
        /// 根據Name查詢排程數據
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [Route("QueryScheduleJobInfoByName")]
        public ApiResultModelByObject QueryScheduleJobInfoByName([FromBody] SysAutoJobModel sysAutoJobModel)
        {
            ApiResultModelByObject model = new ApiResultModelByObject();
            UserInfoModel user = MvcContext.UserInfo;
            model.listData = AutoScheduleJobService.QueryScheduleJobInfoByName(sysAutoJobModel, user.logging_locale, user.time_zone);
            InitLogRecord(sysAutoJobModel, log =>
            {
                StringBuilder stringBuilder = new();
                if (!string.IsNullOrEmpty(sysAutoJobModel.job_run_program))
                    stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("AutoSchedule_log:JobName", true)}：{sysAutoJobModel.job_run_program}");
                log.Detail = stringBuilder.ToString();
            });
            model.rtnSuccess = true;
            return model;
        }

        /// <summary>
        /// 根據Name修改排程信息
        /// </summary>
        /// <returns></returns>
        [HttpPut]
        [Route("UpdateScheduleJobInfoByName")]
        public async Task<ApiResultModelByObject> UpdateScheduleJobInfoByName([FromBody] SysAutoJobModel sysAutoJobModel)
        {
            ApiResultModelByObject model = new ApiResultModelByObject();
            UserInfoModel user = MvcContext.UserInfo;
            sysAutoJobModel.opearte_emp = user.current_emp;
            if (AutoScheduleJobService.VerifyScheduleJobRepetition(sysAutoJobModel))
            {
                string job_run_interval = sysAutoJobModel.job_run_interval;
                SysAutoJobModel autoJobModel = AutoScheduleJobService.QueryScheduleJobInfoByName(sysAutoJobModel, user.logging_locale, user.time_zone);
                model.listData = AutoScheduleJobService.UpdateScheduleJobInfoByName(sysAutoJobModel);
                #region 日誌記錄 
                InitLogRecord(sysAutoJobModel, log =>
                {
                    StringBuilder stringBuilder = new();
                    StringBuilder stringBuilderFormer = new();

                    initUpdateLog(sysAutoJobModel, autoJobModel, stringBuilder, stringBuilderFormer);

                    if (!string.IsNullOrEmpty(sysAutoJobModel.job_run_program))
                    {
                        stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("AutoSchedule_log:JobProgram", true)}：{sysAutoJobModel.job_run_program}");
                        stringBuilderFormer.AppendLine($"{ActionFilter.GetMultilingualValue("AutoSchedule_log:JobProgram", true)}：{autoJobModel.job_run_program}");
                    }

                    InitScheduleTime(job_run_interval, sysAutoJobModel, autoJobModel, stringBuilder, stringBuilderFormer);

                    if (!string.IsNullOrEmpty(sysAutoJobModel.job_mail_address))
                    {
                        stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("AutoSchedule_log:JobEmail", true)}：{sysAutoJobModel.job_mail_address}");
                        stringBuilderFormer.AppendLine($"{ActionFilter.GetMultilingualValue("AutoSchedule_log:JobEmail", true)}：{autoJobModel.job_mail_address}");
                    }
                    log.Detail = stringBuilder.ToString();
                    log.DetailFormer = stringBuilderFormer.ToString();
                });
                #endregion
                model.rtnSuccess = true;
            }
            else {
                model.messageTitle = ActionFilter.GetMultilingualValue("custom:messageTitle:updateFail");
                model.messageContent = new List<string> { string.Format(ActionFilter.GetMultilingualValue("custom:messageContent:scheduleJobNameExist")) };
            }
            return await Task.FromResult<ApiResultModelByObject>(model);
        }

        private static void initUpdateLog(SysAutoJobModel sysAutoJobModel, SysAutoJobModel autoJobModel, StringBuilder stringBuilder, StringBuilder stringBuilderFormer)
        {
            if (!string.IsNullOrEmpty(sysAutoJobModel.job_name))
            {
                stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("AutoSchedule_log:JobName", true)}：{sysAutoJobModel.job_name}");
                stringBuilderFormer.AppendLine($"{ActionFilter.GetMultilingualValue("AutoSchedule_log:JobName", true)}：{autoJobModel.job_name}");
            }

            if (!string.IsNullOrEmpty(sysAutoJobModel.job_descr))
            {
                stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("AutoSchedule_log:JobDescr", true)}：{sysAutoJobModel.job_descr}");
                stringBuilderFormer.AppendLine($"{ActionFilter.GetMultilingualValue("AutoSchedule_log:JobDescr", true)}：{autoJobModel.job_descr}");
            }

            if (!string.IsNullOrEmpty(sysAutoJobModel.job_status) && sysAutoJobModel.job_run_program != "ImportHRDataJob")
            {
                string status = sysAutoJobModel.job_status.Equals("0") ? $"{ActionFilter.GetMultilingualValue("AutoSchedule_log:Disable", true)}" : $"{ActionFilter.GetMultilingualValue("AutoSchedule_log:Enable", true)}";
                stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("AutoSchedule_log:JobStatus", true)}：{status}");
                string statusFormer = autoJobModel.job_status.Equals("False") ? $"{ActionFilter.GetMultilingualValue("AutoSchedule_log:Disable", true)}" : $"{ActionFilter.GetMultilingualValue("AutoSchedule_log:Enable", true)}";
                stringBuilderFormer.AppendLine($"{ActionFilter.GetMultilingualValue("AutoSchedule_log:JobStatus", true)}：{statusFormer}");
            }
        }

        private static void InitScheduleTime(string job_run_interval ,SysAutoJobModel sysAutoJobModel, SysAutoJobModel autoJobModel, StringBuilder stringBuilder, StringBuilder stringBuilderFormer)
        {
            if (sysAutoJobModel.job_run_type != null && sysAutoJobModel.job_run_program != "ImportHRDataJob")
            {
                StringBuilder time = new StringBuilder();
                switch (sysAutoJobModel.job_run_type) {
                    case 1:
                        time.Append($"{ActionFilter.GetMultilingualValue("AutoSchedule_log:DailyScheduleLog", true)}" + sysAutoJobModel.job_run_time);
                        break;
                    case 2:
                        time.Append(string.Format($"{ActionFilter.GetMultilingualValue("AutoSchedule_log:WeekScheduleLog", true)}", sysAutoJobModel.job_run_interval, sysAutoJobModel.job_run_time));
                        break;
                    case 3:
                        time.Append(string.Format($"{ActionFilter.GetMultilingualValue("AutoSchedule_log:MonthScheduleLog", true)}", sysAutoJobModel.job_run_interval, sysAutoJobModel.job_run_time));
                        break;
                }
                StringBuilder timeFormer = new StringBuilder();
                switch (autoJobModel.job_run_type)
                {
                    case 1:
                        timeFormer.Append($"{ActionFilter.GetMultilingualValue("AutoSchedule_log:DailyScheduleLog", true)}" + autoJobModel.job_run_time);
                        break;
                    case 2:
                        timeFormer.Append(string.Format($"{ActionFilter.GetMultilingualValue("AutoSchedule_log:WeekScheduleLog", true)}", autoJobModel.job_run_interval, autoJobModel.job_run_time));
                        break;
                    case 3:
                        timeFormer.Append(string.Format($"{ActionFilter.GetMultilingualValue("AutoSchedule_log:MonthScheduleLog", true)}", autoJobModel.job_run_interval, autoJobModel.job_run_time));
                        break;
                }
                stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("AutoSchedule_log:JobSchedule", true)}：{time.ToString()}");
                stringBuilderFormer.AppendLine($"{ActionFilter.GetMultilingualValue("AutoSchedule_log:JobSchedule", true)}：{timeFormer.ToString()}");
            }
        }
    }
}
