﻿using Elegal.Flow.Api.Services.Process;
using Elegal.Interface.Api.Common.Control.Filter;
using Elegal.Interface.Api.Common.Control.Web;
using Elegal.Interface.Api.Common.Model.ParaModel.FlowApi;
using Elegal.Interface.Api.Common.Model.ResultModel;
using Elegal.Interface.Api.Common.Model.ResultModel.FlowApi;
using Elegal.Interface.Api.Common.Model.ViewModel;
using Elegal.Interface.ApiData.Service.FuncService;
using Elegal.Interface.ApiData.Service.Model.QueryCondition;
using Elegal.Orm.Utils;
using Microsoft.AspNetCore.Mvc;
using System.Text;

namespace Elegal.Flow.Api.Controllers.Process
{
    /// <summary>
    /// 董監事管理服務
    /// </summary>
    [Route("[controller]")]
    [ApiController]
    public class DirectorsSupervisorsManagementController : BaseController
    {
        /// <summary>
        /// 董監事管理頁面查詢
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [Route("GetDataList")]
        public async Task<ApiResultModelByObject> GetDataList([FromBody] DirectorsSupervisorsSearchModel DirectorsSupervisors)
        {
            ApiResultModelByObject apiResult = new();
            UserInfoModel user = MvcContext.UserInfo;

            apiResult.listData = DirectorsSupervisorsManagementService.QueryDirectorsSupervisors(DirectorsSupervisors, user.logging_locale, user.time_zone);
            apiResult.rtnSuccess = true;
            return await Task.FromResult(apiResult);
        }

        /// <summary>
        /// 董監事管理頁面新增数据
        /// </summary>
        /// <param name="model">新增参数</param>
        /// <returns></returns>
        [HttpPost]
        [Route("AddData")]
        public async Task<ApiResultModelByObject> AddData([FromBody] DirectorsSupervisorsSearchModel model)
        {
            model.CreateUser = MvcContext.UserInfo.current_emp;
            return await Task.FromResult(DirectorsSupervisorsManagementService.AddData(model));
        }

        /// <summary>
        /// 董監事管理頁面刪除数据
        /// </summary>
        /// <param name="model"> 刪除数据</param>
        /// <returns></returns>
        [HttpPut]
        [Route("DeleteData")]
        public async Task<ApiResultModelByObject> DeleteData([FromBody] DirectorsSupervisorsResult model)
        {
            return await Task.FromResult(DirectorsSupervisorsManagementService.DeleteData(model));
        }

        /// <summary>
        /// 获取董監事職稱下拉数据源
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("GetDirectorsSupervisorsSelectData")]
        public async Task<ApiResultModelByObject> GetDirectorsSupervisorsSelectData()
        {
            ApiResultModelByObject apiResult = new();
            SysParametersQueryCondition sysParametersQueryCondition = new SysParametersQueryCondition()
            {
                LangType = MvcContext.UserInfo.logging_locale,
                ParaCode = "dsTitle",
                OrderBys = new List<Orm.Dtos.OrderByParam>()
                {
                    new Orm.Dtos.OrderByParam(){Field="SortOrder",Order=Orm.Dtos.OrderBy.ASC}
                }
            };
            apiResult.listData = SysParametersDataService.Query(sysParametersQueryCondition);
            apiResult.rtnSuccess = true;

            return await Task.FromResult(apiResult);
        }

        /// <summary>
        /// 列表数据导出日志记录
        /// </summary>
        /// <param name="DirectorsSupervisors"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("ExportLog")]
        public async Task<ApiResultModelByObject> ExportLog(DirectorsSupervisorsSearchModel DirectorsSupervisors)
        {
            ApiResultModelByObject apiResult = new ApiResultModelByObject()
            {
                listData = true,
                rtnSuccess = true,
            };
            InitLogRecord(log =>
            {
                StringBuilder stringBuilder = new();
                if (DirectorsSupervisors.title_types != null && DirectorsSupervisors.title_types.Count > 0)
                    stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("DirectorsSupervisorsManagement_titletype", true)}：{string.Join(",", DirectorsSupervisors.title_types.Select(kvp => kvp.Value))}");
                if (!string.IsNullOrEmpty(DirectorsSupervisors.area.Value))
                    stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("DirectorsSupervisorsManagement_area", true)}：{DirectorsSupervisors.area.Value}");
                if (!string.IsNullOrEmpty(DirectorsSupervisors.entity.Value))
                    stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("DirectorsSupervisorsManagement_fnpEntity", true)}：{DirectorsSupervisors.entity.Value}");
                if (!string.IsNullOrEmpty(DirectorsSupervisors.emp_id))
                    stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("DirectorsSupervisorsManagement_name", true)}：{DirectorsSupervisors.emp_id}");
                if (DirectorsSupervisors.filterList.Any())
                {
                    stringBuilder.AppendLine(CommonUtil.GetDivisionLine(ActionFilter.GetMultilingualValue("DirectorsSupervisorsManagement_filter", true)));
                    stringBuilder.AppendLine(string.Join("\r\n", DirectorsSupervisors.filterList.Select(s => $"{ActionFilter.GetMultilingualValue($"DirectorsSupervisorsManagement_export:{Nomenclature.ToCamel(s.Key)}", true)}：{s.Value}")));
                }
                log.Detail = stringBuilder.ToString();
            });
            return await Task.FromResult(apiResult);
        }
    }
}
