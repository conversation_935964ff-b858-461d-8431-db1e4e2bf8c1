﻿using Elegal.Interface.Api.Common.Model.ViewModel.FlowApi;
using Elegal.Interface.Api.Common.Repository;
using Elegal.Interface.ApiData.Service.Model.DbModel;

namespace Elegal.Flow.Api.Repository
{
    /// <summary>
    /// 資料查詢 -> 申請單查詢
    /// </summary>
    public class ApplicationSearchRepository : BaseRepository
    {
        #region 獲取關聯合約編號
        /// <summary>
        /// 獲取關聯合約編號
        /// </summary>
        /// <returns></returns>
        public List<string> GetGroupContractNumber()
        {
            return this.NpgsqlSearchByList<string>(@"SELECT DISTINCT group_contract_number AS contract_number FROM dbo.v_getallcontractnumber WHERE ISNULL(group_contract_number,N'') <> N'' ORDER BY group_contract_number ASC;");
        }
        #endregion

        #region 獲取關企建檔掛帳部門資訊
        /// <summary>
        /// 獲取關企建檔掛帳部門資訊
        /// </summary>
        /// <param name="deptid"></param>
        /// <returns></returns>
        public List<PsSubOgLglVwA> GetAccountDeptID(string deptid)
        {
            string getAccountDeptIDSql = @"
              SELECT TOP 200 Deptid,Descr,DescrA FROM (
              SELECT deptid AS Deptid,descr AS Descr,descr_a AS DescrA FROM dbo.ps_sub_og_lgl_vw_a
              UNION
              SELECT account_deptid AS Deptid,N'' AS Descr,N'' AS DescrA FROM dbo.enterprise_application AS ea WHERE NOT EXISTS(SELECT 1 FROM dbo.ps_sub_og_lgl_vw_a AS og WHERE og.deptid = ea.account_deptid)
              ) AS deData WHERE ISNULL(Deptid,N'') <> N''";
            if (!string.IsNullOrEmpty(deptid))
            {
                getAccountDeptIDSql += @" AND (Deptid LIKE CONCAT(N'%',@deptid,N'%') OR Descr LIKE CONCAT(N'%',@deptid,N'%') OR DescrA LIKE CONCAT(N'%',@deptid,N'%'))";
            }
            return this.NpgsqlSearchByList<PsSubOgLglVwA>(getAccountDeptIDSql, new { deptid = deptid });
        }
        #endregion
    }
}