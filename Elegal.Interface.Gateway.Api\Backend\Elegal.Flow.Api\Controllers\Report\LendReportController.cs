﻿using Elegal.Flow.Api.Services.Report;
using Elegal.Interface.Api.Common.Control.Filter;
using Elegal.Interface.Api.Common.Control.Web;
using Elegal.Interface.Api.Common.Model.Enum;
using Elegal.Interface.Api.Common.Model.ParaModel.FlowApi.Report;
using Elegal.Interface.Api.Common.Model.ResultModel;
using Microsoft.AspNetCore.Mvc;

namespace Elegal.Flow.Api.Controllers.Report
{
    /// <summary>
    /// 纸本借出统计表
    /// </summary>
    [Route("[controller]")]
    [ApiController]
    public class LendReportController : BaseController
    {
        /// <summary>
        /// 查询列表数据
        /// </summary>
        /// <param name="condition">查询参数</param>
        /// <returns></returns>
        [HttpPost]
        [Route("GetListData")]
        public async Task<ApiResultModelByObject> GetListData(LendReportQueryCondition condition)
        {
            //申请单日期查询条件必填
            if (!condition.ApplicationTimeStart.HasValue && !condition.ApplicationTimeEnd.HasValue)
                return await Task.FromResult(new ApiResultModelByObject()
                {
                    messageContent = new List<string>() {
                        string.Format(ActionFilter.GetMultilingualValue("custom:messageContent:searchreQuired"),ActionFilter.GetMultilingualValue("LendReport_ApplicationTime",true)),
                    },
                    messageType = MessageTypeUtils.Warning.ToString(),
                });
            //申请单状态查询条件必填
            if (!condition.LendStatus.Any())
                return await Task.FromResult(new ApiResultModelByObject()
                {
                    messageContent = new List<string>() {
                        string.Format(ActionFilter.GetMultilingualValue("custom:messageContent:searchreQuired"),ActionFilter.GetMultilingualValue("LendReport_LendStatus",true)),
                    },
                    messageType = MessageTypeUtils.Warning.ToString(),
                });
            //我方主体、经办人部门、经办人必填一项
            if (!condition.Entity.Any() && !condition.LendHandlerDeptid.Any() && !condition.LendHandler.Any())
                return await Task.FromResult(new ApiResultModelByObject()
                {
                    messageContent = new List<string>() {
                        string.Format(ActionFilter.GetMultilingualValue("custom:messageContent:searchreQuired"),ActionFilter.GetMultilingualValue("InventoryReport_EntityOrHandler",true)),
                    },
                    messageType = MessageTypeUtils.Warning.ToString(),
                });
            ApiResultModelByObject apiResult = new ApiResultModelByObject()
            {
                listData = LendReportService.GetListData(condition),
                rtnSuccess = true,
            };
            InitLogRecord(log =>
            {
                log.Detail = LendReportService.GetLogDetail(condition);
            });
            return await Task.FromResult(apiResult);
        }

        /// <summary>
        /// 查询列表详情数据
        /// </summary>
        /// <param name="condition"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("GetDetailListData")]
        public async Task<ApiResultModelByObject> GetDetailListData(LendDetailListCondition condition)
        {
            ApiResultModelByObject apiResult = new ApiResultModelByObject()
            {
                listData = LendReportService.GetDetailListData(condition),
                rtnSuccess = true,
            };
            return await Task.FromResult(apiResult);
        }

        /// <summary>
        /// 列表数据导出日志记录
        /// </summary>
        /// <param name="condition"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("ExportLog")]
        public async Task<ApiResultModelByObject> ExportLog(LendReportQueryCondition condition)
        {
            ApiResultModelByObject apiResult = new ApiResultModelByObject()
            {
                listData = true,
                rtnSuccess = true,
            };
            InitLogRecord(log =>
            {
                log.Detail = LendReportService.GetLogDetail(condition);
            });
            return await Task.FromResult(apiResult);
        }
    }
}
