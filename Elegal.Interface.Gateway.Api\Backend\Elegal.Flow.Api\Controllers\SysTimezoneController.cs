﻿using Elegal.Flow.Api.Services;
using Elegal.Interface.Api.Common.Control.Web;
using Elegal.Interface.Api.Common.Model.ResultModel;
using Elegal.Interface.ApiData.Service.FuncService;
using Elegal.Interface.ApiData.Service.Model.DbModel;
using Elegal.Interface.ApiData.Service.Model.ViewModel;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Globalization;
using System.Security.Policy;
using System.Text;

namespace Elegal.Flow.Api.Controllers
{
    /// <summary>
    /// 區域
    /// </summary>
    [Route("[controller]")]
    [ApiController]
    public class SysTimezoneController : BaseController
    {
        #region 下拉數據源
        /// <summary>
        /// 下拉數據源
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("GetSelectData")]
        public async Task<ApiResultModelByObject> GetSelectData()
        {
            ApiResultModelByObject apiResult = new();

            var orgCulture = Thread.CurrentThread.CurrentCulture;
            Thread.CurrentThread.CurrentCulture
                = Thread.CurrentThread.CurrentUICulture
                    = new CultureInfo("en-us");

            var zones = TimeZoneInfo.GetSystemTimeZones();

            Thread.CurrentThread.CurrentCulture
                = Thread.CurrentThread.CurrentUICulture
                    = orgCulture;

            apiResult.listData = zones;

            apiResult.rtnSuccess = true;

            return await Task.FromResult(apiResult);
        }
        #endregion

        #region 透過emplid取得時區
        /// <summary>
        /// 透過emplid取得時區
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("GetTimezone")]
        public async Task<ApiResultModelByObject> GetTimezone(string emplid)
        {
            ApiResultModelByObject apiResult = new();

            var res = SysTimezoneDataService.FindByKey(emplid);
            Dictionary<string, object> dt = new Dictionary<string, object>();
            if (res != null)
            {
                //根據用戶id查詢到的timezoneid獲取是否為夏令時以及與utc時間比對的偏移量
                dt = !string.IsNullOrEmpty(res.Timezone) ? SysTimezoneService.GetTimeZoneDaylight(res.Timezone) : dt;
                dt["Emplid"] = emplid;
                dt["Timezone"] = res.Timezone;
                dt["Lang"] = res.Lang;
            }
            apiResult.listData = res != null ? dt : res;
            apiResult.rtnSuccess = true;
            InitLogRecord<object>(new(), LogRecord =>
            {
                StringBuilder stringBuilder = new();
                stringBuilder.AppendLine($"時區查詢：{emplid}");
                stringBuilder.AppendLine($"時區：{(res != null ? res.Timezone : "")}");
                stringBuilder.AppendLine($"語系：{(res != null ? res.Lang : "")}");
                LogRecord.Detail = stringBuilder.ToString();
            });

            return await Task.FromResult(apiResult);
        }
        #endregion

        #region 更新時區
        /// <summary>
        /// 更新時區
        /// </summary>
        /// <returns></returns>
        [HttpPut]
        [Route("UpdateTimezone")]
        public async Task<ApiResultModelByObject> UpdateTimezone(string emplid, string? timezone, string? lang)
        {
            ApiResultModelByObject apiResult = new();

            if (string.IsNullOrEmpty(timezone) && string.IsNullOrEmpty(lang))
            {
                apiResult.rtnSuccess = false;
                apiResult.messageTitle = "時區與語系不可同時為空";
                return await Task.FromResult(apiResult);
            }

            if (!string.IsNullOrEmpty(timezone))
            {
                TimeZoneInfo timezoneinfo = TimeZoneInfo.FindSystemTimeZoneById(timezone);
            }

            SysTimezone sys_timezone = new SysTimezone()
            {
                Emplid = emplid,
                Timezone = timezone,
                Lang = lang
            };
            var sysTimeZone = SysTimezoneDataService.FindByKey(emplid);
            if (sysTimeZone == null)
            {
                apiResult.listData = SysTimezoneDataService.Create(sys_timezone);
                apiResult.rtnSuccess = true;
            }
            else
            {
                apiResult.listData = SysTimezoneDataService.Update(sys_timezone);
                apiResult.rtnSuccess = true;
            }

            InitLogRecord<object>(new(), LogRecord =>
            {
                LogRecord.Detail = GetNewSysTime(timezone, lang);
                LogRecord.DetailFormer = GetOldSysTime(timezone, lang, sysTimeZone);
            });

            return await Task.FromResult(apiResult);
        }

        #region 插入log日誌

        #region 拼接用戶傳遞時區
        /// <summary>
        /// 拼接用戶傳遞時區
        /// </summary>
        /// <param name="timezone"></param>
        /// <param name="lang"></param>
        /// <returns></returns>
        private string GetNewSysTime(string? timezone, string? lang)
        {
            var orgCulture = Thread.CurrentThread.CurrentCulture;
            Thread.CurrentThread.CurrentCulture
                = Thread.CurrentThread.CurrentUICulture
                    = new CultureInfo("en-us");

            var zones = TimeZoneInfo.GetSystemTimeZones();

            Thread.CurrentThread.CurrentCulture
                = Thread.CurrentThread.CurrentUICulture
                    = orgCulture;

            StringBuilder stringBuilder = new();
            if (!string.IsNullOrEmpty(timezone))
            {
                TimeZoneInfo zone = zones.Where(x => x.Id == timezone).FirstOrDefault();
                stringBuilder.AppendLine($"時區：{(zone != null ? zone.DisplayName : "")}");
            }
            if (!string.IsNullOrEmpty(lang)) stringBuilder.AppendLine($"語系：{lang}");

            return stringBuilder.ToString();
        }
        #endregion

        #region 拼接根據用戶id查詢的時區
        /// <summary>
        /// 拼接根據用戶id查詢的時區
        /// </summary>
        /// <param name="timezone"></param>
        /// <param name="lang"></param>
        /// <param name="sysTimeZone"></param>
        /// <returns></returns>
        private string GetOldSysTime(string? timezone, string? lang, SysTimezone? sysTimeZone)
        {
            var orgCulture = Thread.CurrentThread.CurrentCulture;
            Thread.CurrentThread.CurrentCulture
                = Thread.CurrentThread.CurrentUICulture
                    = new CultureInfo("en-us");

            var zones = TimeZoneInfo.GetSystemTimeZones();

            Thread.CurrentThread.CurrentCulture
                = Thread.CurrentThread.CurrentUICulture
                    = orgCulture;

            StringBuilder stringBuilder = new StringBuilder();
            if (sysTimeZone != null)
            {
                stringBuilder = new();
                if (!string.IsNullOrEmpty(timezone))
                {
                    TimeZoneInfo old_zone = zones.Where(x => x.Id == sysTimeZone.Timezone).FirstOrDefault();
                    stringBuilder.AppendLine($"時區：{(old_zone != null ? old_zone.DisplayName : "")}");
                }
                if (!string.IsNullOrEmpty(lang)) stringBuilder.AppendLine($"語系：{sysTimeZone.Lang}");
            }
            return stringBuilder.ToString();
        }
        #endregion

        #endregion

        #endregion

        #region 檢查是否執行夏令時
        /// <summary>
        /// 檢查是否執行夏令時
        /// </summary>
        /// <param name="stz"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("CheckIsDaylightSavingTime")]
        public async Task<ApiResultModelByObject> CheckIsDaylightSavingTime([FromBody] SysTimezoneViewModel stz)
        {
            ApiResultModelByObject apiResult = new ApiResultModelByObject();
            apiResult.rtnSuccess = true;
            apiResult.listData = !string.IsNullOrEmpty(stz.Timezone) ? SysTimezoneService.GetTimeZoneDaylight(stz.Timezone) : new Dictionary<string, object>();
            //apiResult.rtnSuccess = tzi.IsDaylightSavingTime(DateTime.Now);//服務器時間而非utc時間，參考1.0[無效]
            return await Task.FromResult(apiResult);
        }
        #endregion
    }
}
