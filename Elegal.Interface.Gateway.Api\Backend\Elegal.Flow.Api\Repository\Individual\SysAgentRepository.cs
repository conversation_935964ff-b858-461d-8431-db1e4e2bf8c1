﻿using Elegal.Interface.Api.Common.Control.Filter;
using Elegal.Interface.Api.Common.FuncHelper;
using Elegal.Interface.Api.Common.Model.ParaModel.FlowApi;
using Elegal.Interface.Api.Common.Model.ViewModel.FlowApi;
using Elegal.Interface.ApiData.Service.FuncService;
using Elegal.Interface.ApiData.Service.Model.QueryCondition;
using Elegal.Interface.ApiData.Service.Model.ViewModel;
using Elegal.Orm;

namespace Elegal.Flow.Api.Repository.Individual
{
    /// <summary>
    /// 代理人設定
    /// </summary>
    public class SysAgentRepository
    {
        private readonly string Prefix = DbAccess.Database.GetParameterPrefix();
        private readonly string Schema = DbAccess.Database.GetDbSchema();

        #region 檢查時間段內是否該部門已被代理
        /// <summary>
        /// 檢查時間段內是否該部門已被代理
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public bool IsAgent(SysAgentAddModel model)
        {
            var startTime = model.StartTime.GetValueOrDefault().ConvertDateByTimeZoneByUtc(MvcContext.UserInfo.time_zone).Date;
            var endTime = model.EndTime.GetValueOrDefault().ConvertDateByTimeZoneByUtc(MvcContext.UserInfo.time_zone).Date.AddDays(1);
            var list = SysAgentDataService.Query(new SysAgentQueryCondition()
            {
                AuthEmpid = model.AuthEmp.Key,
                AuthDeptid = model.AuthDeptid,
                State = "1",
                Type = "A",
            }).Select(agent =>
            {
                if (agent.StartTime.HasValue)
                    agent.StartTime = agent.StartTime.Value.ConvertDateByTimeZoneByUtc(MvcContext.UserInfo.time_zone).Date;
                if (agent.EndTime.HasValue)
                    agent.EndTime = agent.EndTime.Value.ConvertDateByTimeZoneByUtc(MvcContext.UserInfo.time_zone).Date.AddDays(1);
                return agent;
            });
            if (list.Any())
            {
                return list.Where(agent =>
                    startTime >= agent.StartTime && startTime < agent.EndTime ||
                    endTime <= agent.EndTime && endTime > agent.StartTime ||
                    startTime <= agent.StartTime && endTime >= agent.EndTime
                )
                    .Count() > 0;
            }
            return false;
        }
        #endregion

        /// <summary>
        /// 获取我的代理人设定列表数据
        /// </summary>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public IEnumerable<SysAgentModel> GetAgentList()
        {
            string sql = @$"select
                                sa.e_id EId,
                                comp.descrshort Company,
                                agent.deptid Deptid,
                                agent.name Name,
                                agent.name_a EName,
                                auth.name Authorized,
                                auth.name_a AuthorizedEName,
                                case when isnull(agent.termination,'') = '' then 1 else 0 end Status,
                                sa.start_time StartTime,
                                sa.end_time EndTime,
                                sa.auth_deptid AuthDeptid,
                                sa.create_time operate_time,
                                operate.name operate_cuser,
                                operate.name_a operate_euser,
                                auth.termination Termination
                            from sys_agent sa
                            left join (select emplid,name,name_a,company,deptid,termination from ps_sub_ee_lgl_vw_a) agent on agent.emplid = sa.agent_empid
                            left join (select descrshort,company from ps_sub_comp_vw_a) comp on comp.company = agent.company
                            left join (select emplid,name,name_a,termination from ps_sub_ee_lgl_vw_a) auth on auth.emplid = sa.auth_empid
                            left join (select emplid,name,name_a from ps_sub_ee_lgl_vw_a) operate on operate.emplid = sa.create_user
	                        where sa.create_user ={Prefix}auth_empid or sa.auth_empid ={Prefix}auth_empid order by sa.create_time desc";
            return DbAccess.Database.SqlQuery<SysAgentModel>(sql, new { auth_empid = MvcContext.UserInfo.current_emp });
        }

        /// <summary>
        /// 获取担任他人代理列表数据
        /// </summary>
        /// <returns></returns>
        public IEnumerable<SysAgentModel> GetBeAgentList()
        {
            string sql = @$"select
                                comp.descrshort Company,
                                auth.deptid Deptid,
                                auth.name Name,
                                auth.name_a EName,
                                case when isnull(auth.termination,'') = '' then 1 else 0 end Status,
                                sa.start_time StartTime,
                                sa.end_time EndTime,
                                sa.auth_deptid AuthDeptid,
                                sa.create_time operate_time,
                                operate.name operate_cuser,
                                operate.name_a operate_euser
                            from sys_agent sa
                            left join (select emplid,name,name_a,company,deptid,termination from ps_sub_ee_lgl_vw_a) auth on auth.emplid = sa.auth_empid
                            left join (select descrshort,company from ps_sub_comp_vw_a) comp on comp.company = auth.company
                            left join (select emplid,name,name_a from ps_sub_ee_lgl_vw_a) operate on operate.emplid = sa.create_user
	                        where sa.agent_empid ={Prefix}agent_empid order by sa.create_time desc";
            return DbAccess.Database.SqlQuery<SysAgentModel>(sql, new { agent_empid = MvcContext.UserInfo.current_emp });
        }

        /// <summary>
        /// 依照工號获取担任他人代理列表数据
        /// </summary>
        /// <returns></returns>
        public IEnumerable<SysAgentModel> GetBeAgentListByEmplid(string emplid)
        {
            string sql = @$"select
                                auth_empid as AgentEmpid,
                                comp.descrshort Company,
                                auth.deptid Deptid,
                                auth.name Name,
                                auth.name_a EName,
                                case when isnull(auth.termination,'') = '' then 1 else 0 end Status,
                                sa.start_time StartTime,
                                sa.end_time EndTime,
                                sa.auth_deptid AuthDeptid,
                                sa.create_time operate_time,
                                operate.name operate_cuser,
                                operate.name_a operate_euser
                            from sys_agent sa
                            left join (select emplid,name,name_a,company,deptid,termination from ps_sub_ee_lgl_vw_a) auth on auth.emplid = sa.auth_empid
                            left join (select descrshort,company from ps_sub_comp_vw_a) comp on comp.company = auth.company
                            left join (select emplid,name,name_a from ps_sub_ee_lgl_vw_a) operate on operate.emplid = sa.create_user
	                        where sa.agent_empid ={Prefix}agent_empid order by sa.create_time desc";
            return DbAccess.Database.SqlQuery<SysAgentModel>(sql, new { agent_empid = emplid });
        }

        #region 獲取開放欄位顯示值，用於替換郵件模板裏的内容
        /// <summary>
        /// 獲取開放欄位顯示值，用於替換郵件模板裏的内容
        /// </summary>
        /// <param name="model"></param>
        public object? GetOpenFiledValue(SysAgentMailModel model)
        {
            string time_zone = "Taipei Standard Time";
            string sql = @"select * from F_ProxyMailContent(@departments,@proxy,@auth,@startTime,@endTime)";
            return DbAccess.Database.SqlQuery<object>(sql, new
            {
                departments = string.Join(",", model.Departments.Select(s => s.Key)),
                proxy = model.Empid,
                auth = model.AuthEmp,
                startTime = model.StartTime.HasValue ? model.StartTime.Value.ConvertDateByTimeZoneByUtc(time_zone).Date.ToString("yyyy/MM/dd HH:mm:ss") : "",
                endTime = model.EndTime.HasValue ? model.EndTime.Value.ConvertDateByTimeZoneByUtc(time_zone).Date.AddDays(1).AddSeconds(-1).ToString("yyyy/MM/dd HH:mm:ss") : ""
            }).FirstOrDefault();
        }
        #endregion
    }
}
