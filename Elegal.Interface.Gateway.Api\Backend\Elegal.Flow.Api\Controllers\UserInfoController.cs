﻿using Elegal.Interface.Api.Common.Control.Filter;
using Elegal.Interface.Api.Common.Control.Web;
using Elegal.Interface.Api.Common.FuncService;
using Elegal.Interface.Api.Common.Model.Enum;
using Elegal.Interface.Api.Common.Model.ParaModel.FlowApi;
using Elegal.Interface.Api.Common.Model.ResultModel;
using Elegal.Interface.ApiData.Service.Model.DbModel;
using Elegal.Interface.ApiData.Service.Model.QueryCondition;
using Microsoft.AspNetCore.Mvc;
#nullable disable
namespace Elegal.Flow.Api.Controllers
{
    /// <summary>
    /// 人员信息接口
    /// </summary>
    [Route("[controller]")]
    [ApiController]
    public class UserInfoController : BaseController
    {

        /// <summary>
        /// 查询用户信息(多条件复杂查询)
        /// </summary>
        /// <param name="condition">查询参数</param>
        /// <returns></returns>
        [HttpPost]
        [Route("Query")]
        public async Task<ApiResultModelByObject> Query(UserQueryCondition condition)
        {
            ApiResultModelByObject apiResult = new ApiResultModelByObject();
            apiResult.listData = UserInfoService.Query(condition);
            apiResult.rtnSuccess = true;
            return await Task.FromResult(apiResult);
        }
        /// <summary>
        /// 獲取用戶信息
        /// </summary>
        [HttpPost]
        [Route("GetUserList")]
        public async Task<ApiResultModelByObject> GetUserList([FromBody] PsSubEeLglVwAQueryCondition condition)
        {
            ApiResultModelByObject apiResult = new ApiResultModelByObject();

            apiResult.listData = UserInfoService.GetUserList(condition);
            apiResult.rtnSuccess = true;

            return await Task.FromResult(apiResult);
        }

        /// <summary>
        /// 獲取用戶信息
        /// </summary>
        /// <param name="key">关键词(工号、姓名)</param>
        /// <param name="empid">工号</param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetUserByKey")]
        public async Task<ApiResultModelByObject> GetUserByKey(string key, string empid = "")
        {
            ApiResultModelByObject apiResult = new ApiResultModelByObject();

            if (string.IsNullOrEmpty(empid)) empid = MvcContext.UserInfo.current_emp;
            apiResult.listData = UserInfoService.GetUserList(key, empid);
            apiResult.rtnSuccess = true;

            return await Task.FromResult(apiResult);
        }

        /// <summary>
        /// 查询用户信息
        /// </summary>
        /// <param name="condition"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("QueryUserInfo")]
        public async Task<ApiResultModelByObject> QueryUserInfo(PsSubEeLglVwAQueryCondition condition)
        {
            ApiResultModelByObject apiResult = new ApiResultModelByObject();

            apiResult.listData = UserInfoService.QueryUserInfo(condition);
            apiResult.rtnSuccess = true;

            return await Task.FromResult(apiResult);
        }

        /// <summary>
        /// 獲取用戶信息(紙本追蹤批次作業-法务校对中-除行政程序外承办窗口人员)
        /// </summary>
        /// <param name="condition"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("GetUserPaperTracking")]
        public async Task<ApiResultModelByObject> GetUserPaperTracking([FromBody] PsSubEeLglVwAQueryCondition condition)
        {
            ApiResultModelByObject apiResult = new ApiResultModelByObject();

            apiResult.listData = UserInfoService.GetUserPaperTracking(condition);
            apiResult.rtnSuccess = true;

            return await Task.FromResult(apiResult);
        }

        #region 嗶咔讀取用戶信息(或手動輸入完整工號) -> 20250326PRD更版
        /// <summary>
        /// 嗶咔讀取用戶信息(或手動輸入完整工號)
        /// </summary>
        /// <param name="badgeId"></param>
        /// <param name="paperTracking">紙本進度追蹤狀態</param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetUserByBadgeId")]
        public async Task<ApiResultModelByObject> GetUserByBadgeId(string badgeId, string paperTracking)
        {
            PsSubEeLglVwA user = UserInfoService.GetUserByBadgeId(badgeId);
            //未找到或離職，提示“取得人事資料失敗”
            if (user == null || user.Termination.HasValue) throw new Exception(ActionFilter.GetMultilingualValue("custom:messageContent:authLoginError_EmplidMissed"));
            //紙本進度追蹤狀態為法务校对中時，人員限制：除行政程序外承办窗口人员
            if (!string.IsNullOrEmpty(paperTracking) && paperTracking.Equals("03"))
            {
                var users = UserInfoService.GetUserPaperTracking(new PsSubEeLglVwAQueryCondition() { Emplid = user.Emplid });
                //非法務人員提示“非有權收件者”
                if (users.Count == 0 || users.FirstOrDefault(f => f.Emplid.Equals(user.Emplid)) == null)
                    return await Task.FromResult(new ApiResultModelByObject()
                    {
                        messageTitle = ActionFilter.GetMultilingualValue("custom:messageTitle:noAuthorizedRecipient"),
                        messageType = MessageTypeUtils.Warning.ToString(),
                        rtnSuccess = false,
                        messageContent = [ActionFilter.GetMultilingualValue("custom:messageContent:noAuthorizedRecipient")]
                    });
            }
            ApiResultModelByObject apiResult = new ApiResultModelByObject()
            {
                listData = user,
                rtnSuccess = true,
            };
            return await Task.FromResult(apiResult);
        }
        #endregion
    }
}
