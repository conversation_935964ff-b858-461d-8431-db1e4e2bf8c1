﻿using Elegal.Flow.Api.Services.ApplicationPrint;
using Elegal.Flow.Common.Services.FlowStep;
using Elegal.Flow.Common.Services.FormApply;
using Elegal.Interface.Api.Common.Control.Filter;
using Elegal.Interface.Api.Common.Control.Web;
using Elegal.Interface.Api.Common.Model.DBModel.formApply;
using Elegal.Interface.Api.Common.Model.ParaModel.FlowApi;
using Elegal.Interface.Api.Common.Model.ParaModel.FlowApi.FlowStep;
using Elegal.Interface.Api.Common.Model.ResultModel;
using Elegal.Orm;
using Microsoft.AspNetCore.Mvc;
using System.Text;

namespace Elegal.Flow.Api.Controllers.ApplyCase
{
    /// <summary>
    /// 合約申請
    /// </summary>
    [Route("[controller]")]
    [ApiController]
    public class FormApplyController(FormApplyRefactorService formApplyRefactorService) : BaseController
    {
        #region 獲取合約暫存單數據
        /// <summary>
        /// 獲取合約暫存單數據
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("GetTempFormApply")]
        public async Task<ApiResultModelByObject> GetTempFormApply()
        {
            ApiResultModelByObject apiResult = new();

            apiResult.listData = FormApplyService.GetTempFormApply();
            apiResult.rtnSuccess = true;

            return await Task.FromResult(apiResult);
        }
        #endregion

        #region 根據工號獲取經辦人/填單人資訊
        /// <summary>
        /// 根據工號獲取經辦人/填單人資訊
        /// </summary>
        [HttpGet]
        [Route("GetFormEmpInfo")]
        public async Task<ApiResultModelByObject> GetFormEmpInfo([FromQuery] string emplid)
        {
            ApiResultModelByObject apiResult = new();

            apiResult.listData = FormApplyService.GetFormEmpInfo(emplid);
            apiResult.rtnSuccess = true;

            return await Task.FromResult(apiResult);
        }
        #endregion

        #region 獲取承辦法務資訊
        /// <summary>
        /// 獲取承辦法務資訊
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("GetLegalAffairsEmplidList")]
        public async Task<ApiResultModelByObject> GetLegalAffairsEmplidList()
        {
            ApiResultModelByObject apiResult = new();

            var res = FormApplyService.GetLegalAffairsEmplidList();
            apiResult.listData = res;
            apiResult.totalCount = res.Count();
            apiResult.rtnSuccess = true;

            return await Task.FromResult(apiResult);
        }
        #endregion

        #region 獲取客戶暱稱列表
        /// <summary>
        /// 獲取客戶暱稱列表
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("GetHypocorismNameList")]
        public async Task<ApiResultModelByObject> GetHypocorismNameList()
        {
            ApiResultModelByObject apiResult = new();

            var res = await formApplyRefactorService.GetHypocorismNameList();
            apiResult.listData = res;
            apiResult.totalCount = res.Count();
            apiResult.rtnSuccess = true;

            return apiResult;
        }
        #endregion

        #region 根據工號+部門獲取主體列表
        /// <summary>
        /// 根據工號+部門獲取主體列表
        /// </summary>
        /// <param name="emplid"></param>
        /// <param name="pic_deptid"></param>
        /// <param name="entity_str"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetFormApplyEntityList")]
        public async Task<ApiResultModelByObject> GetFormApplyEntityList([FromQuery] string emplid, string pic_deptid, string? entity_str)
        {
            ApiResultModelByObject apiResult = new();

            var res = FormApplyService.GetFormApplyEntityList(emplid, pic_deptid, entity_str);
            apiResult.listData = res;
            apiResult.totalCount = res.Count();
            apiResult.rtnSuccess = true;

            return await Task.FromResult(apiResult);
        }
        #endregion

        #region 根據部門+主體 判斷是否顯示[他方為學校、研究或學術機構]
        /// <summary>
        /// 根據部門+主體 判斷是否顯示[他方為學校、研究或學術機構]
        /// </summary>
        /// <param name="deptid"></param>
        /// <param name="entity_id"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("IsShowInterSchool")]
        public async Task<ApiResultModelByObject> IsShowInterSchool([FromQuery] string deptid, string? entity_id)
        {
            ApiResultModelByObject apiResult = new();

            var res = FormApplyService.IsShowInterSchool(deptid, entity_id);
            apiResult.listData = res;
            apiResult.rtnSuccess = true;

            return await Task.FromResult(apiResult);
        }
        #endregion

        #region 根據PR單號獲取PR單資訊
        /// <summary>
        /// 根據PR單號獲取PR單資訊
        /// </summary>
        /// <param name="prInfoPara"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("GetPrInfoData")]
        public async Task<ApiResultModelByObject> GetPrInfoData([FromBody] prInfoPara prInfoPara)
        {
            ApiResultModelByObject apiResult = new();

            var res = FormApplyService.GetPrInfoData(prInfoPara);
            apiResult.listData = res;
            apiResult.rtnSuccess = true;

            return await Task.FromResult(apiResult);
        }
        #endregion

        #region 根據幣別獲取匯率
        /// <summary>
        /// 根據幣別獲取匯率
        /// </summary>
        /// <param name="currency"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetExchangeRate")]
        public async Task<ApiResultModelByObject> GetExchangeRate([FromQuery] string currency)
        {
            ApiResultModelByObject apiResult = new();

            var res = FormApplyService.GetExchangeRate(currency);
            apiResult.listData = res;
            apiResult.rtnSuccess = true;

            return await Task.FromResult(apiResult);
        }
        #endregion

        #region 根據幣別+匯率轉換為台幣
        /// <summary>
        /// 根據幣別+匯率轉換為台幣
        /// </summary>
        /// <param name="amount"></param>
        /// <param name="exchange_rate"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("ConvertAmount")]
        public async Task<ApiResultModelByObject> ConvertAmount([FromQuery] decimal amount, decimal exchange_rate)
        {
            ApiResultModelByObject apiResult = new();

            var res = FormApplyService.ConvertAmount(amount, exchange_rate);
            apiResult.listData = res;
            apiResult.rtnSuccess = true;

            return await Task.FromResult(apiResult);
        }
        #endregion

        #region 獲取掛帳部門列表
        /// <summary>
        /// 獲取掛帳部門列表
        /// </summary>
        /// <param name="deptid"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetAccountDeptidList")]
        public async Task<ApiResultModelByObject> GetAccountDeptidList([FromQuery] string deptid)
        {
            ApiResultModelByObject apiResult = new();

            var res = FormApplyService.GetAccountDeptidList(deptid);
            apiResult.listData = res;
            apiResult.totalCount = res.Count();
            apiResult.rtnSuccess = true;

            return await Task.FromResult(apiResult);
        }
        #endregion

        #region 根據申請單號獲取到合約明細
        /// <summary>
        /// 取得合約申請單
        /// </summary>
        /// <param name="apply_number"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetFormApply_New")]
        public async Task<ApiResultModelByObject> GetFormApply_New([FromQuery] string apply_number)
        {
            ApiResultModelByObject apiResult = new();

            apiResult.listData = FormApplyService.GetFormApply_New(apply_number);
            apiResult.rtnSuccess = true;

            return await Task.FromResult(apiResult);
        }
        #endregion

        #region 合約申請匯總動作
        /// <summary>
        /// 合約申請匯總動作
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("HandleFormApplication")]
        public async Task<ApiResultModel> HandleFormApplication([FromBody] HandleFormApplicationModel data)
        {
            ApiResultModel apiResult = new();
            HandleFormApplicationModel oldData = new HandleFormApplicationModel();
            (apiResult.rtnSuccess, oldData) = FormApplyService.HandleFormApplicationToTransaction(data);
            //日志
            InitLogRecord(data, logRecord =>
            {
                StringBuilder stringBuilder = new();
                StringBuilder oldstringBuilder = new();
                var istemp = DbAccess.Database.ExecuteScalar($"select emplid from ps_sub_ee_lgl_vw_a where emplid ='{data.apply_number}'")?.ToString() ?? "";
                if (istemp != null && istemp != "")
                    stringBuilder.AppendLine($"申請單號: -");
                else
                    if (!string.IsNullOrEmpty(data.apply_number)) stringBuilder.AppendLine($"申請單號: {data.apply_number}");
                //if (!string.IsNullOrEmpty(para.step_opinion)) stringBuilder.AppendLine($"簽核意見: {data.step_opinion}");
                string newstr = string.Empty;
                string oldstr = string.Empty;
                (newstr, oldstr) = FlowStepService.GetApproveLogStrForApply(data, oldData);
                if (!string.IsNullOrEmpty(newstr)) stringBuilder.AppendLine(newstr);
                if (!string.IsNullOrEmpty(oldstr))
                {
                    if (istemp != null && istemp != "")
                        oldstringBuilder.AppendLine($"申請單號: -");
                    else
                    if (!string.IsNullOrEmpty(data.apply_number)) oldstringBuilder.AppendLine($"申請單號: {data.apply_number}");
                    oldstringBuilder.AppendLine(oldstr);
                }
                logRecord.Detail = stringBuilder.ToString();
                logRecord.DetailFormer = oldstringBuilder.ToString();
            });
            return await Task.FromResult(apiResult);
        }
        #endregion

        #region 重置暫存單(事務)
        /// <summary>
        /// 重置暫存單
        /// </summary>
        /// <returns></returns>
        [HttpPut]
        [Route("ResetTempForm")]
        public async Task<ApiResultModelByObject> ResetTempForm()
        {
            ApiResultModelByObject apiResult = new();

            apiResult.listData = FormApplyService.ResetTempFormApplicationToTransaction();
            apiResult.rtnSuccess = true;

            HttpContext httpContext = MvcContext.GetContext();

            InitLogRecord<object>(new(), logRecord =>
            {
                StringBuilder stringBuilder = new();
                stringBuilder.AppendLine($"");

                logRecord.Detail = stringBuilder.ToString();
            });


            return await Task.FromResult(apiResult);
        }
        #endregion

        #region 將暫存單改為進行中(事務)
        /// <summary>
        /// 將暫存單改為進行中
        /// </summary>
        /// <param name="para"></param>
        /// <returns></returns>
        [HttpPut]
        [Route("SetFormInProgress")]
        public async Task<ApiResultModelByObject> SetFormInProgress([FromBody] FormProgressParaModel? para = null)
        {
            ApiResultModelByObject apiResult = new();

            var res = FormApplyService.SetFormApplyInProgressToTransaction(para);

            apiResult.listData = res;
            apiResult.rtnSuccess = true;

            InitLogRecord<object>(new(), LogRecord =>
            {
                LogRecord.Detail = @$"申請單號: {res.apply_number}";
            });

            return await Task.FromResult(apiResult);
        }
        #endregion

        #region 取得合約申請單(列印)
        /// <summary>
        /// 取得合約申請單(列印)
        /// </summary>
        /// <param name="apply_number"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetFormApply_Print")]
        public async Task<ApiResultModelByObject> GetFormApply_Print([FromQuery] string apply_number)
        {
            ApiResultModelByObject apiResult = new();

            apiResult.listData = FormApplyPrintService.GetFormApply_Print(apply_number);
            apiResult.rtnSuccess = true;

            InitLogRecord<object>(new(), LogRecord =>
            {
                LogRecord.Detail = @$"申請單號: {apply_number}";
            });

            return await Task.FromResult(apiResult);
        }
        #endregion

        #region 取得BG部門清單
        /// <summary>
        /// 取得BG部門清單
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("GetBGDept")]
        public async Task<ApiResultModelByObject> GetBGDept()
        {
            ApiResultModelByObject apiResult = new();

            var res = FormApplyService.GetBGDept();
            apiResult.listData = res;
            apiResult.rtnSuccess = true;

            return await Task.FromResult(apiResult);
        }
        #endregion

        #region 判斷最終簽核關卡
        /// <summary>
        /// 判斷最終簽核關卡  Issue：282
        /// </summary>
        /// <param name="apply_number"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("EstimateFinalCheck")]
        public async Task<ApiResultModelByObject> EstimateFinalCheck([FromQuery] string apply_number)
        {
            ApiResultModelByObject apiResult = new();
            apiResult.listData = FormApplyAcknowledgeService.EstimateFinalCheck(apply_number);
            apiResult.rtnSuccess = true;

            return await Task.FromResult(apiResult);
        }
        #endregion

        #region 正本簽屬主管
        /// <summary>
        /// 正本簽屬主管
        /// </summary>
        /// <param name="apply_number"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetSupervisorList")]
        public async Task<ApiResultModelByObject> GetSupervisorList([FromQuery] string apply_number)
        {
            ApiResultModelByObject apiResult = new();

            apiResult.listData = FormApplyService.GetSupervisorList(apply_number);
            apiResult.rtnSuccess = true;

            return await Task.FromResult(apiResult);
        }
        #endregion

        #region 賦予合約編號自動編號
        /// <summary>
        /// 賦予合約編號自動編號
        /// </summary>
        /// <param name="apply_number"></param>
        /// <param name="year"></param>
        /// <param name="main_contract_number"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("AutoCompleteContractNumber")]
        public async Task<ApiResultModelByObject> AutoCompleteContractNumber([FromQuery] string apply_number, string? year, string? main_contract_number)
        {
            ApiResultModelByObject apiResult = new();

            bool autoComplete;
            apiResult.listData = FormApplyService.AutoCompleteContractNumber(apply_number, year, main_contract_number, out autoComplete);
            apiResult.rtnSuccess = autoComplete;
            if (!autoComplete)
            {
                apiResult.messageContent.Add(ActionFilter.GetMultilingualValue("custom:messageContent:EntityDifferent"));
            }
            return await Task.FromResult(apiResult);
        }
        #endregion

        #region 檢查合約編號
        /// <summary>
        /// 檢查合約編號
        /// </summary>
        /// <param name="contract_number"></param>
        /// <param name="apply_number"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("CheckContractNumber")]
        public async Task<ApiResultModelByObject> CheckContractNumber([FromQuery] string contract_number, string apply_number)
        {
            ApiResultModelByObject apiResult = new();

            apiResult.listData = FormApplyService.CheckContractNumber(contract_number, apply_number);
            apiResult.rtnSuccess = true;

            return await Task.FromResult(apiResult);
        }
        #endregion

        #region 經辦人/填單人資訊

        /// <summary>
        /// 經辦人/填單人資訊
        /// </summary>
        [HttpGet]
        [Route("GetFormEmpInfoByDept")]
        public async Task<ApiResultModelByObject> GetFormEmpInfoByDept([FromQuery] string deptid)
        {
            ApiResultModelByObject apiResult = new();

            apiResult.listData = FormApplyService.GetFormEmpInfoByDept(deptid);
            apiResult.rtnSuccess = true;

            return await Task.FromResult(apiResult);
        }

        #endregion

        #region 系統參數

        /// <summary>
        /// 系統參數
        /// </summary>
        [HttpGet]
        [Route("GetParameter")]
        public async Task<ApiResultModelByObject> GetParameter()
        {
            ApiResultModelByObject apiResult = new();

            apiResult.listData = FormApplyService.GetParameter();
            apiResult.rtnSuccess = true;

            return await Task.FromResult(apiResult);
        }

        #region 填單人資訊系統參數
        /// <summary>
        /// 填單人資訊系統參數
        /// </summary>
        [HttpGet]
        [Route("GetFormParameter")]
        public async Task<ApiResultModelByObject> GetFormParameter()
        {
            ApiResultModelByObject apiResult = new();

            apiResult.listData = FormApplyService.GetFormParameter();
            apiResult.rtnSuccess = true;

            return await Task.FromResult(apiResult);
        }

        #endregion

        #region 財務資訊系統參數

        /// <summary>
        /// 財務資訊系統參數
        /// </summary>
        [HttpGet]
        [Route("GetFinanceParameter")]
        public async Task<ApiResultModelByObject> GetFinanceParameter()
        {
            ApiResultModelByObject apiResult = new();

            apiResult.listData = FormApplyService.GetFinanceParameter();
            apiResult.rtnSuccess = true;

            return await Task.FromResult(apiResult);
        }

        #endregion

        #region 總部承辦法務資訊系統參數

        /// <summary>
        /// 總部承辦法務資訊系統參數
        /// </summary>
        [HttpGet]
        [Route("GetUntertakeParameter")]
        public async Task<ApiResultModelByObject> GetUntertakeParameter()
        {
            ApiResultModelByObject apiResult = new();

            apiResult.listData = FormApplyService.GetUntertakeParameter();
            apiResult.rtnSuccess = true;

            return await Task.FromResult(apiResult);
        }

        #endregion

        #region 總部法務行政資訊/正本簽屬人系統參數

        /// <summary>
        /// 總部法務行政資訊/正本簽屬人系統參數
        /// </summary>
        [HttpGet]
        [Route("GetAdminParameter")]
        public async Task<ApiResultModelByObject> GetAdminParameter()
        {
            ApiResultModelByObject apiResult = new();

            apiResult.listData = FormApplyService.GetAdminParameter();
            apiResult.rtnSuccess = true;

            return await Task.FromResult(apiResult);
        }

        #endregion

        #endregion

        #region 取得主體列表

        /// <summary>
        /// 取得主體列表 總部法務行政資訊>合約編號主體
        /// </summary>
        [HttpGet]
        [Route("GetFnpEntityList")]
        public async Task<ApiResultModelByObject> GetFnpEntityList()
        {
            ApiResultModelByObject apiResult = new();

            var res = FormApplyService.GetFnpEntityList();
            apiResult.listData = res;
            apiResult.totalCount = res.Count();
            apiResult.rtnSuccess = true;

            return await Task.FromResult(apiResult);
        }



        #endregion

        #region 取得人員列表

        /// <summary>
        /// 取得建議指定正本簽屬主管列表
        /// </summary>
        [HttpGet]
        [Route("GetOriginalEmpList")]
        public async Task<ApiResultModelByObject> GetOriginalEmpList([FromQuery] string emplid, string pic_deptid)
        {
            ApiResultModelByObject apiResult = new();

            var res = FormApplyService.GetOriginalEmpList(emplid, pic_deptid);
            apiResult.listData = res;
            apiResult.totalCount = res.Count();
            apiResult.rtnSuccess = true;

            return await Task.FromResult(apiResult);
        }

        #endregion

        #region 取得他方清單
        /// <summary>
        /// 取得他方清單
        /// </summary>
        /// <param name="vendor_name"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetVendors")]
        public async Task<ApiResultModelByObject> GetVendors([FromQuery] string vendor_name)
        {
            ApiResultModelByObject apiResult = new();

            var res = FormApplyService.GetVendors(vendor_name);
            apiResult.listData = res;
            apiResult.totalCount = res.Count();
            apiResult.rtnSuccess = true;

            return await Task.FromResult(apiResult);
        }
        #endregion

        #region 取得會簽類型
        /// <summary>
        /// 取得會簽類型
        /// </summary>
        /// <param name="apply_number"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetAcknowledgeType")]
        public async Task<ApiResultModelByObject> GetAcknowledgeType([FromQuery] string apply_number)
        {
            ApiResultModelByObject apiResult = new();

            apiResult.listData = FormApplyAcknowledgeService.GetAcknowledgeType(apply_number);
            apiResult.rtnSuccess = true;

            return await Task.FromResult(apiResult);
        }
        #endregion

        #region 取得主約編號、關聯合約群組代號列表
        /// <summary>
        /// 取得主約編號、關聯合約群組代號列表
        /// </summary>
        /// <param name="contract_number"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetGroupContractNumberList")]
        public async Task<ApiResultModelByObject> GetGroupContractNumberList([FromQuery] string contract_number)
        {
            ApiResultModelByObject apiResult = new();

            apiResult.listData = FormApplyService.GetGroupContractNumberList(contract_number);
            apiResult.rtnSuccess = true;

            return await Task.FromResult(apiResult);
        }
        #endregion

        #region 檢查總經理/董事長關卡是否有設定人員
        /// <summary>
        /// 檢查總經理/董事長關卡是否有設定人員
        /// </summary>
        /// <param name="apply_number"></param>
        /// <param name="final_sign_level"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("CheckCEOAndPresidentStep")]
        public async Task<ApiResultModelByObject> CheckCEOAndPresidentStep([FromQuery] string apply_number, string final_sign_level)
        {
            ApiResultModelByObject apiResult = new();

            //Issue：92 更改為新的驗證方法
            apiResult.listData = FormApplyService.CheckCEOAndPresidentStepNew(apply_number, final_sign_level);
            apiResult.rtnSuccess = true;

            return await Task.FromResult(apiResult);
        }
        #endregion

        #region 更新總經理/董事長關卡簽核人員
        /// <summary>
        /// 更新總經理/董事長關卡簽核人員  Issue：282
        /// </summary>
        /// <param name="apply_number"></param>
        /// <param name="update_type"></param>
        /// <returns></returns>
        [HttpPut]
        [Route("UpdateCEOAndPresidentStep")]
        public async Task<ApiResultModelByObject> UpdateCEOAndPresidentStep([FromQuery] string apply_number, int update_type)
        {
            ApiResultModelByObject apiResult = new();

            apiResult.listData = FormApplyService.UpdateCEOAndPresidentStep(apply_number, update_type);
            apiResult.rtnSuccess = true;

            return await Task.FromResult(apiResult);
        }
        #endregion
    }
}
