﻿using Elegal.Interface.Api.Common.Control.Filter;
using Elegal.Interface.Api.Common.FuncHelper;
using Elegal.Interface.Api.Common.Model.ParaModel.FlowApi.Report;
using Elegal.Interface.Api.Common.Model.ResultModel.FlowApi.Report;
using Elegal.Interface.ApiData.Service.Model.QueryCondition;
using Elegal.Interface.ApiData.Service.Repository;
using Elegal.Orm;
using Elegal.Orm.Dtos;

namespace Elegal.Flow.Api.Repository.Report
{
    /// <summary>
    /// 纸本借出统计表
    /// </summary>
    public class LendReportRepository
    {
        private readonly string unionSql = @"(select * from(select
                                    entity_id,entity,entity_namec,entity_namee,status,area_id
                                from
                                    fnp_entity
                                union 
                                select
                                    aff_company_code entity_id,
                                    aff_company_abb entity,
                                    aff_company_cname entity_namec,
                                    aff_company_ename entity_namee,
                                    case when aff_status = '1' then 0 else 1 end status,
                                    null area_id
                                from
                                    affiliate_company)tt)";
        private readonly string baseSql = @"--待取件/出借中數據查詢
                                            SELECT pldd.detail_id,pla.lend_number,pla.create_time AS application_time,CASE WHEN ISNULL(pld.retrieve_number,'') = '' THEN 0 ELSE 1 END AS hasRetrieveNumber,CASE WHEN pldd.overdue_day > 0 THEN 1 ELSE 0 END AS isOverdue,pbd.paper_confiden_level,pla.lend_status,pbd.paper_type,pads.my_entity_id,pla.lend_handler_emplid,pla.lend_handler_deptid,pla .lend_handler_bu,pla.lend_handler_bg FROM (SELECT lend_id,lend_number,lend_handler_emplid,lend_handler_deptid,lend_status,create_time,lend_handler_bu,lend_handler_bg FROM paper_lending_application WHERE lend_status = N'01' OR lend_status = N'02' OR lend_status = N'03') AS pla
                                            INNER JOIN paper_lending_demand AS pld ON pld.paper_lend_id = pla.lend_id
                                            INNER JOIN paper_lending_detail AS pldd ON pldd.paper_lend_id = pla.lend_id
                                            INNER JOIN paper_basic_data AS pbd ON pbd.basic_id = pldd.paper_basic_id
                                            INNER JOIN paper_application_data AS pads ON pads.application_id = pbd.paper_applica_id
                                            UNION
                                            --已結案/已作廢數據查詢
                                            SELECT plh.history_id AS detail_id,plh.lend_number,plh.application_time,CASE WHEN ISNULL(plh.retrieve_number,'') = '' THEN 0 ELSE 1 END AS hasRetrieveNumber,CASE WHEN plh.overdue_day > 0 THEN 1 ELSE 0 END AS isOverdue,pbd.paper_confiden_level,plh.lend_status,pbd.paper_type,pads.my_entity_id,plh.lend_handler_emplid,plh.lend_handler_deptid,plh .lend_handler_bu,plh.lend_handler_bg FROM (SELECT history_id,lend_number,paper_basic_id,application_time,retrieve_number,overdue_day,lend_status,lend_handler_emplid,lend_handler_deptid,lend_handler_bu,lend_handler_bg FROM paper_lending_history WHERE  lend_status = N'04' OR lend_status = N'05') AS plh
                                            INNER JOIN paper_basic_data AS pbd ON pbd.basic_id = plh.paper_basic_id
                                            INNER JOIN paper_application_data AS pads ON pads.application_id = pbd.paper_applica_id";
        /// <summary>
        /// 借出统计数据
        /// </summary>
        /// <param name="condition">查询条件</param>
        /// <returns></returns>
        public IEnumerable<LendReportData> GetLendReportData(LendReportQueryCondition condition)
        {
            string where = string.Empty;
            if (condition.Entity.Any() && !(condition.Entity.Count.Equals(1) && condition.Entity[0].Key.Equals("-1"))) where = $@" AND my_entity_id in(N'{string.Join("',N'", condition.Entity.Select(s => s.Key))}')";
            else if (condition.LendHandlerDeptid.Any()) where = $@" AND lend_handler_deptid in(N'{string.Join("',N'", condition.LendHandlerDeptid.Select(s => s.Key))}')";
            else if (condition.LendHandler.Any()) where = $@" AND lend_handler_emplid in(N'{string.Join("',N'", condition.LendHandler.Select(s => s.Key))}')";
            string sql = $@"select * from ({baseSql}) lendData {(condition.LendHandler.Any() ? "INNER JOIN(SELECT emplid,name,name_a FROM ps_sub_ee_lgl_vw_a) AS ee ON ee.emplid = lendData.lend_handler_emplid" : "")} where 1=1 ";
            if (condition.ApplicationTimeStart.HasValue)
                sql += $" AND application_time >= N'{TimeZoneInfoConvertHelper.ConvertDateByTimeZoneByUtc(condition.ApplicationTimeStart, MvcContext.UserInfo.time_zone)}'";
            if (condition.ApplicationTimeEnd.HasValue)
                sql += $" AND application_time <= N'{TimeZoneInfoConvertHelper.ConvertDateByTimeZoneByUtc(condition.ApplicationTimeEnd, MvcContext.UserInfo.time_zone)}'";
            if (condition.HasRetrieveNumber.Any())
                sql += $@" AND hasRetrieveNumber in({string.Join(',', condition.HasRetrieveNumber.Select(s => s.Key))})";
            if (condition.IsOverdue.Any())
                sql += $@" AND isOverdue in({string.Join(',', condition.IsOverdue.Select(s => s.Key))})";
            if (condition.PaperConfidenLevel.Any())
                sql += $@" AND paper_confiden_level in(N'{string.Join("',N'", condition.PaperConfidenLevel.Select(s => s.Key))}')";
            if (condition.LendStatus.Any())
                sql += $@" AND lend_status in(N'{string.Join("',N'", condition.LendStatus.Select(s => s.Key))}')";
            if (condition.PaperType.Any())
                sql += $@" AND paper_type in(N'{string.Join("',N'", condition.PaperType.Select(s => s.Key))}')";
            sql += where;
            return DbAccess.Database.SqlQuery<LendReportData>(sql);
        }

        #region 借出统计数据
        /// <summary>
        /// 主体维度纸本类型
        /// </summary>
        /// <param name="condition"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        internal IEnumerable<object> GetLendReportDataEntityType(LendReportQueryCondition condition)
        {
            //纸本数据
            string sql = $@"select EntityId,PaperType,LendStatus 
                            from (select * from V_PaperLendDetail union all select * from V_PaperLendDetailHistory)dt 
                            where 1=1";
            SearchItemGroup searchItemGroup = GetBaseSearchQuery(condition);
            SearchItem searchItem = new();
            //自选主体
            if (condition.Entity.Any() && !(condition.Entity.Count.Equals(1) && condition.Entity[0].Key.Equals("-1")))
            {
                searchItemGroup.Items.Add(new SearchItem() { Field = "dt.EntityId", Compare = CompareOperator.IN, Value = string.Join(",", condition.Entity.Select(s => s.Key)), Logic = LogicOperator.And });
                searchItem = new SearchItem() { Field = "fe.entity_id", Compare = CompareOperator.IN, Value = string.Join(",", condition.Entity.Select(s => s.Key)), Logic = LogicOperator.And };
            }
            sql += Util.GetSearchItemGroup(searchItemGroup);
            //表视图数据
            string leftSql = $@"select fe.area_id AreaId,fe.entity_id EntityId,fe.entity Entity,sp.PaperType,sp.PaperTypeName,sp.PaperTypeOrder from {unionSql} fe
                                cross join(select func_code PaperType,fun_name PaperTypeName,sort_order PaperTypeOrder from sys_parameters where para_code = N'lib_paperType' and lang_type = N'{MvcContext.UserInfo.logging_locale}') sp
                                where 1=1";
            searchItemGroup = new() { Logic = LogicOperator.And, Items = new List<SearchItem>() };
            searchItemGroup.Items.Add(searchItem);
            searchItemGroup.Items.Add(new SearchItem() { Field = "sp.PaperType", Compare = CompareOperator.IN, Value = string.Join(",", condition.PaperType.Select(s => s.Key)), Logic = LogicOperator.And });
            leftSql += Util.GetSearchItemGroup(searchItemGroup);
            //纸本(申请单状态)透视数据
            string pivotSql = $@"select EntityId,PaperType,{GetLendStatusListString()} from
                                ({sql}) sourceTable
                                pivot (count(LendStatus) for LendStatus in({GetLendStatusListString()})) as unpivotedTable";
            sql = $@"select a.EntityId,a.Entity ShowName,a.PaperType,a.PaperTypeName,
                    {GetLendStatusNameListString()} 
                    from ({leftSql}) a
                    left join({pivotSql}) b
                    on a.EntityId = b.EntityId and a.PaperType = b.PaperType
                    order by Entity,PaperTypeOrder";
            return DbAccess.Database.SqlQuery<object>(sql);
        }
        /// <summary>
        /// 主体维度无纸本类型
        /// </summary>
        /// <param name="condition"></param>
        /// <returns></returns>
        internal IEnumerable<object> GetLendReportDataEntityNoType(LendReportQueryCondition condition)
        {
            //纸本数据
            string sql = $@"select EntityId,LendStatus 
                            from (select * from V_PaperLendDetail union all select * from V_PaperLendDetailHistory)dt 
                            where 1=1";
            SearchItemGroup searchItemGroup = GetBaseSearchQuery(condition);
            //自选主体
            SearchItem searchItem = new();
            if (condition.Entity.Any() && !(condition.Entity.Count.Equals(1) && condition.Entity[0].Key.Equals("-1")))
            {
                searchItemGroup.Items.Add(new SearchItem() { Field = "dt.EntityId", Compare = CompareOperator.IN, Value = string.Join(",", condition.Entity.Select(s => s.Key)), Logic = LogicOperator.And });
                searchItem = new SearchItem() { Field = "entity_id", Compare = CompareOperator.IN, Value = string.Join(",", condition.Entity.Select(s => s.Key)) };
            }
            sql += Util.GetSearchItemGroup(searchItemGroup);
            //表视图数据
            searchItemGroup = new();
            searchItemGroup.Items.Add(searchItem);
            string leftSql = $@"select dt2.entity_id EntityId,dt2.entity Entity from {unionSql} dt2 where 1=1 {Util.GetSearchItemGroup(searchItemGroup)}";
            //纸本(申请单状态)透视数据
            string pivotSql = $@"select EntityId,{GetLendStatusListString()} from
                                ({sql}) sourceTable
                                pivot (count(LendStatus) for LendStatus in({GetLendStatusListString()})) as unpivotedTable";
            sql = $@"select a.EntityId,a.entity ShowName,{GetLendStatusNameListString()} 
                    from ({leftSql}) a
                    left join({pivotSql}) b
                    on a.EntityId = b.EntityId
                    order by Entity";
            return DbAccess.Database.SqlQuery<object>(sql);
        }
        /// <summary>
        /// 经办人部门维度纸本类型
        /// </summary>
        /// <param name="condition"></param>
        /// <returns></returns>
        internal IEnumerable<object> GetLendReportDataHandlerDeptType(LendReportQueryCondition condition)
        {
            //纸本数据
            string sql = $@"select HandlerBg,HandlerDeptid,PaperType,LendStatus 
                            from (select * from V_PaperLendDetail union all select * from V_PaperLendDetailHistory)dt 
                            where 1=1";
            SearchItemGroup searchItemGroup = GetBaseSearchQuery(condition);
            searchItemGroup.Items.Add(new SearchItem() { Field = "dt.HandlerDeptid", Compare = CompareOperator.IN, Value = string.Join(",", condition.LendHandlerDeptid.Select(s => s.Key)), Logic = LogicOperator.And });
            sql += Util.GetSearchItemGroup(searchItemGroup);
            //表视图数据
            string leftSql = $@"select og.bg HandlerBg,og.deptid HandlerDeptid,og.descr HandlerDeptName,sp.PaperType,sp.PaperTypeName,sp.PaperTypeOrder from ps_sub_og_lgl_vw_a og cross join
                                (select func_code PaperType,fun_name PaperTypeName,sort_order PaperTypeOrder from sys_parameters where para_code = 'lib_paperType' and lang_type = '{MvcContext.UserInfo.logging_locale}') sp
                                where 1=1";
            searchItemGroup = new();
            searchItemGroup.Items.Add(new SearchItem() { Field = "sp.PaperType", Compare = CompareOperator.IN, Value = string.Join(",", condition.PaperType.Select(s => s.Key)), Logic = LogicOperator.And });
            searchItemGroup.Items.Add(new SearchItem() { Field = "og.deptid", Compare = CompareOperator.IN, Value = string.Join(",", condition.LendHandlerDeptid.Select(s => s.Key)), Logic = LogicOperator.And });
            leftSql += Util.GetSearchItemGroup(searchItemGroup);
            //纸本(申请单状态)透视数据
            string pivotSql = $@"select HandlerBg,HandlerDeptid,PaperType,{GetLendStatusListString()} from
                                ({sql}) sourceTable
                                pivot (count(LendStatus) for LendStatus in({GetLendStatusListString()})) as unpivotedTable";
            sql = $@"select a.HandlerBg,a.HandlerDeptid,a.HandlerDeptName ShowName,a.PaperType,a.PaperTypeName,
                    {GetLendStatusNameListString()} 
                    from ({leftSql}) a
                    left join({pivotSql}) b
                    on a.HandlerDeptid = b.HandlerDeptid and a.PaperType = b.PaperType
                    order by HandlerDeptid,PaperTypeOrder";
            return DbAccess.Database.SqlQuery<object>(sql);
        }
        /// <summary>
        /// 经办人部门维度无纸本类型
        /// </summary>
        /// <param name="condition"></param>
        /// <returns></returns>
        internal IEnumerable<object> GetLendReportDataHandlerDeptNoType(LendReportQueryCondition condition)
        {
            //纸本数据
            string sql = $@"select HandlerBg,HandlerDeptid,LendStatus 
                            from (select * from V_PaperLendDetail union all select * from V_PaperLendDetailHistory)dt 
                            where 1=1";
            SearchItemGroup searchItemGroup = GetBaseSearchQuery(condition);
            searchItemGroup.Items.Add(new SearchItem() { Field = "dt.HandlerDeptid", Compare = CompareOperator.IN, Value = string.Join(",", condition.LendHandlerDeptid.Select(s => s.Key)), Logic = LogicOperator.And });
            sql += Util.GetSearchItemGroup(searchItemGroup);
            //表视图数据
            string leftSql = $@"select bg HandlerBg,deptid HandlerDeptid,descr HandlerDeptName from ps_sub_og_lgl_vw_a 
                                where 1=1";
            searchItemGroup = new() { Logic = LogicOperator.And, Items = new List<SearchItem>() };
            searchItemGroup.Items.Add(new SearchItem() { Field = "deptid", Compare = CompareOperator.IN, Value = string.Join(",", condition.LendHandlerDeptid.Select(s => s.Key)), Logic = LogicOperator.And });
            leftSql += Util.GetSearchItemGroup(searchItemGroup);
            //纸本(申请单状态)透视数据
            string pivotSql = $@"select HandlerBg,HandlerDeptid,{GetLendStatusListString()} from
                                ({sql}) sourceTable
                                pivot (count(LendStatus) for LendStatus in({GetLendStatusListString()})) as unpivotedTable";
            sql = $@"select a.HandlerBg,a.HandlerDeptid,a.HandlerDeptName ShowName,
                    {GetLendStatusNameListString()} 
                    from ({leftSql}) a
                    left join({pivotSql}) b
                    on a.HandlerDeptid = b.HandlerDeptid
                    order by HandlerDeptid";
            return DbAccess.Database.SqlQuery<object>(sql);
        }
        /// <summary>
        /// 经办人维度纸本类型
        /// </summary>
        /// <param name="condition"></param>
        /// <returns></returns>
        internal IEnumerable<object> GetLendReportDataHandlerType(LendReportQueryCondition condition)
        {
            //纸本数据
            string sql = $@"select HandlerEmplid,PaperType,LendStatus 
                            from (select * from V_PaperLendDetail union all select * from V_PaperLendDetailHistory)dt 
                            where 1=1";
            SearchItemGroup searchItemGroup = GetBaseSearchQuery(condition);
            searchItemGroup.Items.Add(new SearchItem() { Field = "dt.HandlerEmplid", Compare = CompareOperator.IN, Value = string.Join(",", condition.LendHandler.Select(s => s.Key)), Logic = LogicOperator.And });
            sql += Util.GetSearchItemGroup(searchItemGroup);
            //表视图数据
            string leftSql = $@"select ee.emplid HandlerEmplid,ee.name Name,ee.name_a NameA,sp.PaperType,sp.PaperTypeName,sp.PaperTypeOrder from ps_sub_ee_lgl_vw_a ee cross join
                                (select func_code PaperType,fun_name PaperTypeName,sort_order PaperTypeOrder from sys_parameters where para_code = 'lib_paperType' and lang_type = '{MvcContext.UserInfo.logging_locale}') sp
                                where 1=1";
            searchItemGroup = new();
            searchItemGroup.Items.Add(new SearchItem() { Field = "sp.PaperType", Compare = CompareOperator.IN, Value = string.Join(",", condition.PaperType.Select(s => s.Key)), Logic = LogicOperator.And });
            searchItemGroup.Items.Add(new SearchItem() { Field = "ee.emplid", Compare = CompareOperator.IN, Value = string.Join(",", condition.LendHandler.Select(s => s.Key)), Logic = LogicOperator.And });
            leftSql += Util.GetSearchItemGroup(searchItemGroup);
            //纸本(申请单状态)透视数据
            string pivotSql = $@"select HandlerEmplid,PaperType,{GetLendStatusListString()} from
                                ({sql}) sourceTable
                                pivot (count(LendStatus) for LendStatus in({GetLendStatusListString()})) as unpivotedTable";
            sql = $@"select a.HandlerEmplid,a.Name,a.NameA,a.NameA+'('+a.Name+')' ShowName,a.PaperType,a.PaperTypeName,
                    {GetLendStatusNameListString()} 
                    from ({leftSql}) a
                    left join({pivotSql}) b
                    on a.HandlerEmplid = b.HandlerEmplid and a.PaperType = b.PaperType
                    order by stuff(a.HandlerEmplid,1,patindex('%[A-z]%',substring(a.HandlerEmplid,1,1))-1,'') asc, len(a.HandlerEmplid) desc,a.HandlerEmplid desc,PaperTypeOrder";
            return DbAccess.Database.SqlQuery<object>(sql);
        }
        /// <summary>
        /// 经办人维度无纸本类型
        /// </summary>
        /// <param name="condition"></param>
        /// <returns></returns>
        internal IEnumerable<object> GetLendReportDataHandlerNoType(LendReportQueryCondition condition)
        {
            //纸本数据
            string sql = $@"select HandlerEmplid,LendStatus 
                            from (select * from V_PaperLendDetail union all select * from V_PaperLendDetailHistory)dt 
                            where 1=1";
            SearchItemGroup searchItemGroup = GetBaseSearchQuery(condition);
            searchItemGroup.Items.Add(new SearchItem() { Field = "dt.HandlerEmplid", Compare = CompareOperator.IN, Value = string.Join(",", condition.LendHandler.Select(s => s.Key)), Logic = LogicOperator.And });
            sql += Util.GetSearchItemGroup(searchItemGroup);
            //表视图数据
            string leftSql = $@"select emplid HandlerEmplid,name Name,name_a NameA from ps_sub_ee_lgl_vw_a where 1=1";
            searchItemGroup = new();
            searchItemGroup.Items.Add(new SearchItem() { Field = "emplid", Compare = CompareOperator.IN, Value = string.Join(",", condition.LendHandler.Select(s => s.Key)), Logic = LogicOperator.And });
            leftSql += Util.GetSearchItemGroup(searchItemGroup);
            //纸本(申请单状态)透视数据
            string pivotSql = $@"select HandlerEmplid,{GetLendStatusListString()} from
                                ({sql}) sourceTable
                                pivot (count(LendStatus) for LendStatus in({GetLendStatusListString()})) as unpivotedTable";
            sql = $@"select a.HandlerEmplid,a.Name,a.NameA,a.NameA+'('+a.Name+')' ShowName,{GetLendStatusNameListString()} 
                    from ({leftSql}) a
                    left join({pivotSql}) b
                    on a.HandlerEmplid = b.HandlerEmplid
                    order by stuff(a.HandlerEmplid,1,patindex('%[A-z]%',substring(a.HandlerEmplid,1,1))-1,'') asc, len(a.HandlerEmplid) desc,a.HandlerEmplid desc";
            return DbAccess.Database.SqlQuery<object>(sql);
        }
        /// <summary>
        /// 基础查询条件
        /// </summary>
        /// <param name="condition"></param>
        /// <returns></returns>
        private static SearchItemGroup GetBaseSearchQuery(LendReportQueryCondition condition)
        {
            SearchItemGroup searchItemGroup = new() { Logic = LogicOperator.And, Items = new List<SearchItem>() };
            if (condition.ApplicationTimeStart.HasValue)
                searchItemGroup.Items.Add(new SearchItem() { Field = "dt.ApplicationTime", Compare = CompareOperator.GE, Value = TimeZoneInfoConvertHelper.ConvertDateByTimeZoneByUtc(condition.ApplicationTimeStart, MvcContext.UserInfo.time_zone).ToString("yyyy/MM/dd HH:mm:ss"), Logic = LogicOperator.And });
            if (condition.ApplicationTimeEnd.HasValue)
                searchItemGroup.Items.Add(new SearchItem() { Field = "dt.ApplicationTime", Compare = CompareOperator.LE, Value = TimeZoneInfoConvertHelper.ConvertDateByTimeZoneByUtc(condition.ApplicationTimeEnd, MvcContext.UserInfo.time_zone).ToString("yyyy/MM/dd HH:mm:ss"), Logic = LogicOperator.And });
            if (condition.HasRetrieveNumber.Any())
                searchItemGroup.Items.Add(new SearchItem() { Field = "dt.HasRetrieveNumber", Compare = CompareOperator.IN, Value = string.Join(',', condition.HasRetrieveNumber.Select(s => s.Key)), Logic = LogicOperator.And });
            if (condition.IsOverdue.Any())
                searchItemGroup.Items.Add(new SearchItem() { Field = "dt.IsOverdue", Compare = CompareOperator.IN, Value = string.Join(',', condition.IsOverdue.Select(s => s.Key)), Logic = LogicOperator.And });
            if (condition.PaperConfidenLevel.Any())
                searchItemGroup.Items.Add(new SearchItem() { Field = "dt.PaperConfidenLevel", Compare = CompareOperator.IN, Value = string.Join(',', condition.PaperConfidenLevel.Select(s => s.Key)), Logic = LogicOperator.And });
            if (condition.PaperType.Any())
                searchItemGroup.Items.Add(new SearchItem() { Field = "dt.PaperType", Compare = CompareOperator.IN, Value = string.Join(',', condition.PaperType.Select(s => s.Key)), Logic = LogicOperator.And });
            return searchItemGroup;
        }
        /// <summary>
        /// 申请单状态
        /// </summary>
        /// <returns></returns>
        private static string GetLendStatusListString()
        {
            SysParametersDataRepository repository = new SysParametersDataRepository();
            var status = repository.Query(new SysParametersQueryCondition()
            {
                ParaCode = "lib_lendStatus",
                LangType = MvcContext.UserInfo.logging_locale,
                OrderBys = new List<OrderByParam>() { new OrderByParam() { Field = "sort_order", Order = OrderBy.ASC } }
            }).Select(s => $"[{s.FuncCode}]");
            return string.Join(',', status);
        }
        /// <summary>
        /// 申请单状态名称
        /// </summary>
        /// <returns></returns>
        private static string GetLendStatusNameListString()
        {
            SysParametersDataRepository repository = new SysParametersDataRepository();
            var status = repository.Query(new SysParametersQueryCondition()
            {
                ParaCode = "lib_lendStatus",
                LangType = MvcContext.UserInfo.logging_locale,
                OrderBys = new List<OrderByParam>() { new OrderByParam() { Field = "sort_order", Order = OrderBy.ASC } }
            }).Select(s => $"isnull([{s.FuncCode}],0) as 'lib_lendStatus_{s.FuncCode}'");
            return string.Join(',', status);
        }
        #endregion

        /// <summary>
        /// 查询列表详情数据
        /// </summary>
        /// <param name="condition"></param>
        /// <returns></returns>
        public IEnumerable<object> GetDetailListData(LendDetailListCondition condition)
        {
            string sql = $@"select 
	                            paper.PaperCode,paper.ContractNumber,paper.LendNumber,paper.RetrieveNumber,
	                            paper.FillEmplid,paper.FillCname,paper.FillEname,paper.HandlerEmplid,
	                            paper.HandlerDeptid,paper.HandleCname,paper.HandleEname,
	                            paper.ApplicationTime,paper.EntityId,paper.Entity,paper.PaperName,
	                            paper.ActualReturnTime,paper.ShouldReturnTime,paper.IsOverdue,paper.OverdueDay,
                                (select fun_name from sys_parameters where lang_type = N'{MvcContext.UserInfo.logging_locale}' and para_code = 'lib_lendStatus' and func_code = paper.LendStatus)LendStatusName,
                                (select fun_name from sys_parameters where lang_type = N'{MvcContext.UserInfo.logging_locale}' and para_code = 'lib_paperType' and func_code = paper.PaperType)PaperTypeName
                            from (select * from V_PaperLendDetail union all select * from V_PaperLendDetailHistory) paper
                            where 1=1 ";
            SearchItemGroup searchItemGroup = new() { Logic = LogicOperator.And, Items = new List<SearchItem>() };
            if (condition.ApplicationTimeStart.HasValue)
                searchItemGroup.Items.Add(new SearchItem() { Field = "paper.ApplicationTime", Compare = CompareOperator.GE, Value = condition.ApplicationTimeStart.ToString("yyyy/MM/dd HH:mm:ss"), Logic = LogicOperator.And });
            if (condition.ApplicationTimeEnd.HasValue)
                searchItemGroup.Items.Add(new SearchItem() { Field = "paper.ApplicationTime", Compare = CompareOperator.LE, Value = condition.ApplicationTimeEnd.ToString("yyyy/MM/dd HH:mm:ss"), Logic = LogicOperator.And });
            if (condition.HasRetrieveNumber.Any()) searchItemGroup.Items.Add(new SearchItem() { Field = "paper.HasRetrieveNumber", Compare = CompareOperator.IN, Value = string.Join(",", condition.HasRetrieveNumber), Logic = LogicOperator.And });
            if (condition.IsOverdue.Any()) searchItemGroup.Items.Add(new SearchItem() { Field = "paper.IsOverdue", Compare = CompareOperator.IN, Value = string.Join(",", condition.IsOverdue), Logic = LogicOperator.And });
            if (condition.PaperConfidenLevel.Any()) searchItemGroup.Items.Add(new SearchItem() { Field = "paper.PaperConfidenLevel", Compare = CompareOperator.IN, Value = string.Join(",", condition.PaperConfidenLevel), Logic = LogicOperator.And });
            if (condition.PaperType.Any()) searchItemGroup.Items.Add(new SearchItem() { Field = "paper.PaperType", Compare = CompareOperator.IN, Value = string.Join(",", condition.PaperType), Logic = LogicOperator.And });
            searchItemGroup.Items.Add(new SearchItem() { Field = "paper.LendStatus", Compare = CompareOperator.IN, Value = string.Join(",", condition.LendStatus), Logic = LogicOperator.And });
            switch (condition.Dimension)
            {
                case "entity":
                    searchItemGroup.Items.Add(new SearchItem() { Field = "paper.EntityId", Compare = CompareOperator.IN, Value = string.Join(",", condition.Entity), Logic = LogicOperator.And });
                    break;
                case "dept":
                    searchItemGroup.Items.Add(new SearchItem() { Field = "paper.HandlerDeptid", Compare = CompareOperator.IN, Value = string.Join(",", condition.LendHandlerDeptid), Logic = LogicOperator.And });
                    break;
                case "handler":
                    searchItemGroup.Items.Add(new SearchItem() { Field = "paper.HandlerEmplid", Compare = CompareOperator.IN, Value = string.Join(",", condition.LendHandler), Logic = LogicOperator.And });
                    break;
            }
            sql += Util.GetSearchItemGroup(searchItemGroup);
            sql += "order by paper.LendNumber,paper.PaperCode";
            return DbAccess.Database.SqlQuery<object>(sql);
        }
    }
}
