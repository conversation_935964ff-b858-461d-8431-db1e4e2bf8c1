﻿using Elegal.Flow.Api.Services;
using Elegal.Interface.Api.Common.Control.Filter;
using Elegal.Interface.Api.Common.Control.Web;
using Elegal.Interface.Api.Common.FuncHelper;
using Elegal.Interface.Api.Common.Model.ResultModel;
using Elegal.Interface.Api.Common.Model.ResultModel.FlowApi;
using MathNet.Numerics;
using Microsoft.AspNetCore.Mvc;
using System.Text;
#nullable disable
namespace Elegal.Flow.Api.Controllers
{
    /// <summary>
    /// 合約效期提醒
    /// </summary>
    [Route("[controller]")]
    [ApiController]
    public class ContractExpiryController(ContractExpiryRefactorService _ContractExpiryService) : BaseController
    {
        #region 查询列表数据
        /// <summary>
        /// 查询列表数据
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("ListData")]
        public async Task<ApiResultModelByObject> ListData()
        {
            ApiResultModelByObject model = new ApiResultModelByObject();
            model.listData = await _ContractExpiryService.ListData_hiddenmodel();
            model.rtnSuccess = true;
            ActionFilter.InitLogRecord(model, log =>
            {});
            return model;
        }
        #endregion

        #region 標記已讀
        /// <summary>
        /// 標記已讀
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("SignRead")]
        public ApiResultModelByObject SignRead(string applyNumber)
        {
            ApiResultModelByObject model = new ApiResultModelByObject();
            ContractExpiryService.SignRead(applyNumber);
            model.rtnSuccess = true;
            ActionFilter.InitLogRecord(applyNumber, log =>
            {
                StringBuilder stringBuilder = new();
                if (!string.IsNullOrEmpty(applyNumber))
                    stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("Common_applyNumber", true)}：{applyNumber}");
                log.Detail = stringBuilder.ToString();
            });
            return model;
        }
        #endregion
    }
}
