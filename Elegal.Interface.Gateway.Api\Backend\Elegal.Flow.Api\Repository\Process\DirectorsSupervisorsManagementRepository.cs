﻿using Elegal.Interface.Api.Common.Model.ParaModel.FlowApi;
using Elegal.Interface.Api.Common.Model.ResultModel.FlowApi;
using Elegal.Interface.Api.Common.Repository;
using System.Text;

namespace Elegal.Flow.Api.Repository.Process
{
    public class DirectorsSupervisorsManagementRepository : BaseRepository
    {
        public DirectorsSupervisorsManagementRepository() : base() { }

        public List<DirectorsSupervisorsResult> QueryDirectorsSupervisors(DirectorsSupervisorsSearchModel directorsSupervisors, string logging_locale, string time_zone)
        {
            StringBuilder sqlBuilder = new();

            sqlBuilder.Append(@$"SELECT
                                scm.rowid,
                                fe.entity,--主體公司簡稱
                                sa.area_name,--區域
                                sa.area_id,
                                (SELECT fun_name FROM dbo.sys_parameters WHERE para_code = N'dsTitle' AND lang_type = @logging_locale AND func_code = scm.title_type) AS title_name,--職稱
                                scm.emp_id,--員工工號
                                ee.name,--員工中文名稱
                                ee.name_a,--員工英文名稱
                                ee.termination, --员工离职时间
                                CASE WHEN RTRIM(LTRIM(ISNULL(ee.prefix_dial_code_a,''))) = N'' THEN '0000' ELSE RTRIM(LTRIM(ISNULL(ee.prefix_dial_code_a,''))) END AS prefix_dial_code_a,--區碼
                                RTRIM(LTRIM(ISNULL(ee.phone_a, ''))) AS phone_a,--分機
                                ee.email_address_a,--郵箱地址
                                CASE WHEN ISNULL(ee.termination,'') = '' THEN 1 ELSE 0 END AS emp_status,--任職狀態 在職(1)/離職(0)
                                scm.title_remark,--備註
                                mee.name AS operate_cuser,--更新人中文名
                                mee.name_a AS operate_euser,--更新人英文名
                                ISNULL(scm.modify_time,scm.create_time) AS operate_time--更新時間
                                FROM (select rowid,emp_id,title_type,title_remark,modify_time,create_time,modify_user,create_user,entity_id from directors_supervisors where 1=1 ");
            if (!string.IsNullOrEmpty(directorsSupervisors.entity.Key))
                sqlBuilder.Append(@$" and entity_id = @entity_id");
            string keysAsString = string.Empty;
            if (directorsSupervisors.title_types != null && directorsSupervisors.title_types.Count > 0)
            {
                keysAsString = string.Join(",", directorsSupervisors.title_types.Select(kvp => kvp.Key));
                sqlBuilder.Append(@$" AND title_type in (SELECT value FROM STRING_SPLIT(@title_type, ',')) ");
            }
            sqlBuilder.Append(@$") AS scm
                                LEFT JOIN (SELECT entity_id,entity,area_id FROM dbo.fnp_entity) AS fe ON fe.entity_id = scm.entity_id
                                LEFT JOIN (SELECT area_id,area_name FROM dbo.sys_area ");
            sqlBuilder.Append(@$") AS sa ON sa.area_id = fe.area_id
                                LEFT JOIN (SELECT emplid,name,name_a,email_address_a,termination,prefix_dial_code_a,phone_a FROM dbo.ps_sub_ee_lgl_vw_a) AS ee ON ee.emplid = scm.emp_id
                                LEFT JOIN (SELECT emplid,name,name_a FROM dbo.ps_sub_ee_lgl_vw_a) AS mee ON mee.emplid = ISNULL(scm.modify_user,scm.create_user)
                                WHERE 1 = 1");
            if (directorsSupervisors.area.Key != 0 && directorsSupervisors.area.Key != null)
                sqlBuilder.Append(@$" and fe.area_id = @area_id");
            if (!string.IsNullOrEmpty(directorsSupervisors.emp_id))
                sqlBuilder.Append(@$" AND ((ee.emplid = CONCAT(N'', @emplid) OR ee.emplid LIKE CONCAT(N'%', @emplid, N'%')) OR (ee.name = CONCAT(N'', @emplid) OR ee.name LIKE CONCAT(N'%', @emplid, N'%')) OR (ee.name_a = CONCAT(N'', @emplid) OR ee.name_a LIKE CONCAT(N'%',@emplid, N'%')))");
            sqlBuilder.Append(@$" order by sa.area_name, fe.entity, title_name, stuff(scm.emp_id,1,patindex('%[A-z]%',substring(scm.emp_id,1,1))-1,'') asc, len(scm.emp_id) desc,scm.emp_id desc");
            return NpgsqlSearchByList<DirectorsSupervisorsResult>(sqlBuilder.ToString(), new { area_id = directorsSupervisors.area.Key, entity_id = directorsSupervisors.entity.Key, title_type = keysAsString, emplid = directorsSupervisors.emp_id, logging_locale },
                "time_zone", time_zone);
        }

        internal object DeleteByKey(int rowid)
        {
            string sql = @"delete from directors_supervisors where rowid = @rowid";
            return ExecuteCommand(sql, new { rowid });
        }
    }
}
