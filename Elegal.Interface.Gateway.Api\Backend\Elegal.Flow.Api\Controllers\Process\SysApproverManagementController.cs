﻿using Elegal.Flow.Api.Services.Process;
using Elegal.Interface.Api.Common.Control.Filter;
using Elegal.Interface.Api.Common.Control.Web;
using Elegal.Interface.Api.Common.FuncService;
using Elegal.Interface.Api.Common.Model.ParaModel.FlowApi;
using Elegal.Interface.Api.Common.Model.ResultModel;
using Elegal.Interface.Api.Common.Model.ResultModel.FlowApi;
using Elegal.Interface.Api.Common.Model.ViewModel.FlowApi;
using Elegal.Interface.ApiData.Service.FuncService;
using Elegal.Interface.ApiData.Service.Model.DbModel;
using Elegal.Interface.ApiData.Service.Model.QueryCondition;
using Elegal.Orm.Utils;
using Microsoft.AspNetCore.Mvc;
using System.Text;

namespace Elegal.Flow.Api.Controllers.Process
{
    /// <summary>
    /// 關卡人員管理
    /// </summary>
    [Route("[controller]")]
    [ApiController]
    public class SysApproverManagementController : BaseController
    {
        #region 新增數據
        /// <summary>
        /// 新增數據
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPut]
        [Route("AddData")]
        public async Task<ApiResultModelByObject> AddData([FromBody] SysApproverManagementAddModel model)
        {
            return await Task.FromResult(SysApproverManagementService.AddData(model));
        }
        #endregion

        #region 刪除數據
        /// <summary>
        /// 刪除數據
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("DeleteData")]
        public async Task<ApiResultModelByObject> DeleteData([FromBody] SysApproverManagementModel model)
        {
            ApiResultModelByObject apiResult = new();

            apiResult = SysApproverManagementService.DeleteData(model);
            if (apiResult.rtnSuccess)
                InitLogRecord(model, logRecord =>
                {
                    StringBuilder stringBuilder = new();
                    stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("SysApproverManagement_fnpEntity", true)}：{model.FnpEntity}");
                    stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("SysApproverManagement_approverManagement", true)}：{model.ApproverManagement}");
                    stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("SysApproverManagement_deptid", true)}：{model.Deptid}");
                    stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("SysApproverManagement_empId", true)}：{model.EmpId}");
                    stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("SysApproverManagement_cName", true)}：{model.Name}");
                    stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("SysApproverManagement_eName", true)}：{model.NameA}");
                    stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("SysApproverManagement_prefixPhone", true)}：{(string.IsNullOrEmpty(model.PrefixDialCodeA) ? "0000" : model.PrefixDialCodeA)}+{model.PhoneA}");
                    stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("SysApproverManagement_statusName", true)}：{model.StatusName}");
                    //CR：269+270 當地承辦財務+總部承辦財務添加查詢欄位
                    if (!string.IsNullOrEmpty(model.Site))
                        stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("SysApproverManagement_site", true)}：{model.Site}");
                    //CR：271 總經理關卡添加查詢欄位
                    if (!string.IsNullOrEmpty(model.ExcludeDeptid))
                        stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("SysApproverManagement_excludeDeptid", true)}：{model.ExcludeDeptid}");
                    logRecord.Detail = stringBuilder.ToString();
                });

            return await Task.FromResult(apiResult);
        }
        #endregion

        #region 根據主體id+關卡+工號驗證是否存在待簽核單據
        /// <summary>
        /// 根據主體id+關卡+工號驗證是否存在待簽核單據
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("CheckPendingDocumentsForLevelRemover")]
        public async Task<ApiResultModelByObject> CheckPendingDocumentsForLevelRemover([FromBody] SysApproverManagementModel model)
        {
            ApiResultModelByObject apiResult = new();
            apiResult = SysApproverManagementService.CheckPendingDocumentsForLevelRemover(model);
            return await Task.FromResult(apiResult);
        } 
        #endregion

        #region 查詢列表數據
        /// <summary>
        /// 查詢列表數據
        /// </summary>
        /// <param name="condition"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("GetDataList")]
        public async Task<ApiResultModelByObject> GetDataList([FromBody] SysApproverManagementSeachModel condition)
        {
            ApiResultModelByObject apiResult = new();

            apiResult.listData = SysApproverManagementService.GetDataList(condition);
            apiResult.rtnSuccess = true;

            InitLogRecord(condition, logRecord =>
            {
                StringBuilder stringBuilder = new();
                if (!string.IsNullOrEmpty(condition.ApproverManagement.Value))
                    stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("SysApproverManagement_approverManagement", true)}：{condition.ApproverManagement.Value}");
                if (!string.IsNullOrEmpty(condition.Contract.Value))
                    stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("SysApproverManagement_contract", true)}：{condition.Contract.Value}");
                if (!string.IsNullOrEmpty(condition.Category.Value))
                    stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("SysApproverManagement_category", true)}：{condition.Category.Value}");
                if (!string.IsNullOrEmpty(condition.Area.Value))
                    stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("SysApproverManagement_area", true)}：{condition.Area.Value}");
                if (!string.IsNullOrEmpty(condition.FnpEntity.Value))
                    stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("SysApproverManagement_fnpEntity", true)}：{condition.FnpEntity.Value}");
                if (!string.IsNullOrEmpty(condition.Name))
                    stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("SysApproverManagement_name", true)}：{condition.Name}");
                //CR：269+270 當地承辦財務+總部承辦財務添加查詢欄位
                if (!string.IsNullOrEmpty(condition.Site.Value))
                    stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("SysApproverManagement_site", true)}：{condition.Site.Value}");
                //CR：271 總經理關卡添加查詢欄位
                if (!string.IsNullOrEmpty(condition.ExcludeDeptid.Value))
                    stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("SysApproverManagement_excludeDeptid", true)}：{condition.ExcludeDeptid.Value}");
                logRecord.Detail = stringBuilder.ToString();
            });

            return await Task.FromResult(apiResult);
        }
        #endregion

        #region 獲取關卡下拉數據源
        /// <summary>
        /// 獲取關卡下拉數據源
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("GetApproverSelectData")]
        public async Task<ApiResultModelByObject> GetApproverSelectData()
        {
            ApiResultModelByObject apiResult = new();
            SysParametersQueryCondition sysParametersQueryCondition = new SysParametersQueryCondition()
            {
                LangType = MvcContext.UserInfo.logging_locale,
                ParaCode = "ApproverManagement",
                OrderBys = new List<Orm.Dtos.OrderByParam>()
                {
                    new Orm.Dtos.OrderByParam(){Field="SortOrder",Order=Orm.Dtos.OrderBy.ASC}
                }
            };
            apiResult.listData = SysParametersDataService.Query(sysParametersQueryCondition).Select(s =>
            {
                ApproveManagementSelect result = CommonUtil.Map<SysParameters, ApproveManagementSelect>(s);
                result.CheckPointId = SysApproverService.GetCheckpointId(s.FuncCode);
                return result;
            });
            apiResult.rtnSuccess = true;

            return await Task.FromResult(apiResult);
        }
        #endregion
    }
}
