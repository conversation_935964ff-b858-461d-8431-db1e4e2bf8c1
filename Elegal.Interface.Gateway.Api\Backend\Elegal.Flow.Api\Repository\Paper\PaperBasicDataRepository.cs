﻿using Elegal.Interface.Api.Common.Control.Filter;
using Elegal.Interface.Api.Common.FuncHelper;
using Elegal.Interface.Api.Common.Model.DBModel;
using Elegal.Interface.Api.Common.Model.ParaModel.FlowApi;
using Elegal.Interface.Api.Common.Model.ResultModel.FlowApi;
using Elegal.Interface.Api.Common.Model.ViewModel.FlowApi;
using Elegal.Interface.Api.Common.Repository;
using Elegal.Orm;
using Elegal.Orm.Dtos;
using Newtonsoft.Json;
using System.Text;

namespace Elegal.Flow.Api.Repository.Paper
{
    /// <summary>
    /// 紙本基本資料資料存取層
    /// 處理紙本文件相關的資料庫操作
    /// </summary>
    public class PaperBasicDataRepository : BaseRepository
    {
        private readonly string Schema = DbAccess.Database.GetDbSchema();

        #region 查詢存放位置清單
        /// <summary>
        /// 查詢存放位置清單
        /// </summary>
        /// <returns>所有不重複的存放位置列表</returns>
        public List<string> GetPaperPositionList()
        {
            string sql = $@"SELECT 
	                            DISTINCT paper_position 
                            FROM
	                            {Schema}.paper_basic_data
                            ORDER BY
                                paper_position ASC";
            return NpgsqlSearchByList<string>(sql);
        }
        #endregion

        #region 查詢紙本基本資料(新版)
        /// <summary>
        /// 查詢紙本基本資料(新版)
        /// </summary>
        /// <param name="qry">查詢條件</param>
        /// <returns>分頁的查詢結果</returns>
        /// <remarks>
        /// 效能優化說明：
        /// 1. 使用臨時表儲存中間結果
        /// 2. 優化查詢條件處理
        /// 3. 改善關聯查詢效率
        /// 
        /// 支援的查詢條件：
        /// - 紙本/合約編號
        /// - 紙本/合約名稱
        /// - 紙本類型
        /// - 申請單號
        /// - 我方主體
        /// - 入庫狀態
        /// - 機密等級
        /// - 存放位置
        /// </remarks>
        public PageResult<PaperBasicDataViewModel> QueryPaperBasicData_New(qryPaperBasicData qry)
        {
            PageResult<PaperBasicDataViewModel> result = new PageResult<PaperBasicDataViewModel>();
            StringBuilder countSql = new StringBuilder();
            StringBuilder sql = new StringBuilder();
            StringBuilder condition = new StringBuilder();

            // 紙本/合約編號
            if (!string.IsNullOrEmpty(qry.paper_code))
            {
                condition.Append(@$" AND ( pbd.paper_code LIKE CONCAT(N'%', @paper_code, N'%') OR va.contract_number LIKE CONCAT(N'%', @paper_code, N'%'))");
            }
            // 紙本/合約名稱
            if (!string.IsNullOrEmpty(qry.paper_name))
            {
                condition.Append(@$" AND ( pbd.paper_name LIKE CONCAT(N'%', @paper_name, N'%') OR va.contract_name LIKE CONCAT(N'%', @paper_name, N'%'))");
            }
            // 紙本類型
            if (!string.IsNullOrEmpty(qry.paper_type))
            {
                string[] types = qry.paper_type.Split(',');
                condition.Append(@$" AND pbd.paper_type IN ({string.Join(",", types.Select(s => $"'{s}'"))})");
            }
            // 申請單號
            if (!string.IsNullOrEmpty(qry.apply_number))
            {
                condition.Append(@$" AND pads.apply_number LIKE CONCAT(N'%', @apply_number, N'%')");
            }
            // 我方主體
            if (!string.IsNullOrEmpty(qry.my_entity_id))
            {
                string[] entitys = qry.my_entity_id.Split(',');
                condition.Append(@$" AND pads.my_entity_id IN ({string.Join(",", entitys.Select(s => $"'{s}'"))})");
            }
            // 入庫狀態
            if (!string.IsNullOrEmpty(qry.paper_entry_status))
            {
                string[] status = qry.paper_entry_status.Split(',');
                condition.Append(@$" AND pbd.paper_entry_status IN ({string.Join(",", status.Select(s => $"'{s}'"))})");
            }
            // 機密等級
            if (!string.IsNullOrEmpty(qry.paper_confiden_level))
            {
                string[] levels = qry.paper_confiden_level.Split(',');
                condition.Append(@$" AND pbd.paper_confiden_level IN ({string.Join(",", levels.Select(s => $"'{s}'"))})");
            }
            // 存放位置
            if (qry.paper_position != null && qry.paper_position.Count > 0)
            {
                string[] paper_position = qry.paper_position.ToArray();
                condition.Append(@$" AND pbd.paper_position IN ({string.Join(",", paper_position.Select(s => $"N'{s}'"))})");
            }

            // 將中間結果存儲到臨時表中
            sql.Append(@"SELECT 
                            pbd.basic_id, 
                            pbd.paper_code, 
                            va.contract_number, 
                            pbd.paper_name, 
                            pbd.paper_entry_status, 
                            pbd.paper_position, 
                            pads.my_entity_id, 
                            pads.party_a, 
                            pbd.paper_type, 
                            pbd.paper_confiden_level, 
                            paper_return_status, 
                            parer_borrow_days 
                        INTO #TempPaperData
                        FROM paper_basic_data AS pbd
                        LEFT JOIN paper_application_data AS pads ON pbd.paper_applica_id = pads.application_id
                        LEFT JOIN V_GetApplicationSearch AS va ON va.apply_number = pads.apply_number
                        WHERE 1 = 1 " + condition.ToString() + @";");

            // 查詢臨時表並應用分頁和排序
            sql.Append(@"SELECT
                            (SELECT COUNT(1) FROM #TempPaperData) AS total_count,
                            basic_id,--主鍵   
                            paper_code,--紙本編號
                            contract_number,--合約編號
                            paper_name,--紙本名稱
                            (SELECT fun_name FROM sys_parameters WHERE lang_type = @lang_type AND para_code = N'lib_paperEntryStatus' AND func_code = paper_entry_status) AS paper_entry_status_name,--入庫狀態
                            paper_position,--存放位置
                            my_entity_id,--我方主體
                            COALESCE(aff.aff_company_abb, ent.entity) AS my_entity,--我方主體
                            paper_return_status, --紙本現狀
                            (SELECT STRING_AGG(fun_name, ',') WITHIN GROUP (ORDER BY func_code) FROM sys_parameters WHERE func_code IN (SELECT value FROM STRING_SPLIT(paper_return_status, ',')) AND lang_type = @lang_type AND para_code = 'lib_returnStatus') AS paper_return_status_name, --紙本現狀說明
                            party_a,--他方
                            parer_borrow_days,
                            (SELECT fun_name FROM sys_parameters WHERE para_code = N'lib_paperType' AND lang_type = @lang_type AND func_code = paper_type) AS paper_type_name,--紙本類型
                            (SELECT fun_name FROM sys_parameters WHERE para_code = N'confidentStatus' AND lang_type = @lang_type AND func_code = paper_confiden_level) AS confiden_level_name--機密等級
                        FROM #TempPaperData AS paperData
                        LEFT JOIN affiliate_company AS aff ON aff.aff_company_code = paperData.my_entity_id
                        LEFT JOIN fnp_entity AS ent ON ent.entity_id = paperData.my_entity_id");

            if (qry.page == null || qry.page.PageSize == 0 && qry.page.PageIndex == 0)
                qry.page = new PageParam() { PageIndex = 1, PageSize = 25 };

            if (qry.order == null || string.IsNullOrEmpty(qry.order.Field))
                qry.order = new OrderByParam() { Field = "paper_code", Order = OrderBy.ASC };

            int offset = (qry.page.PageIndex - 1) * qry.page.PageSize;
            string orderStr = qry.order.Field + " " + qry.order.Order.ToString();
            sql.Append(@$" ORDER BY {orderStr} OFFSET {offset} ROWS FETCH NEXT {qry.page.PageSize} ROWS ONLY");

            result.Data = DbAccess.Database.SqlQuery<PaperBasicDataViewModel>(sql.ToString(),
            new
            {
                qry.paper_code,
                qry.paper_name,
                qry.apply_number,
                qry.paper_position,
                lang_type = MvcContext.UserInfo.logging_locale
            }).ToList();

            foreach (PaperBasicDataViewModel item in result.Data)
            {
                if (!string.IsNullOrEmpty(item.party_a) && item.party_a.Contains('\u001f'))
                {
                    List<string> partyAList = item.party_a.Split('\u001f').ToList();
                    item.party_a = JsonConvert.SerializeObject(partyAList);
                }
                //item.party_a = item.party_a.Replace("\u001f", "\n");
                if (!string.IsNullOrEmpty(item.contract_number)) item.contract_number = item.contract_number.Replace("\u001f", "");
            }

            if (result.Data != null && result.Data.Count > 0)
                result.TotalRows = result.Data[0].total_count;
            else
                result.TotalRows = 0;

            return result;
        }
        #endregion

        #region 獲取紙本明細
        /// <summary>
        /// 獲取紙本明細
        /// </summary>
        /// <param name="applyNumber"></param>
        /// <returns></returns>
        public PaperApplicationDataViewModel GetPaperApplicationData(string applyNumber)
        {
            string sql = $@"SELECT
                                application_id, --申請單號
                                vg.contract_number,--合約編號
                                (SELECT entity FROM fnp_entity WHERE entity_id = my_entity_id) AS entityName,--我方主體
                                vg.contract_name,--合約名稱
                                application_status 
                            FROM paper_application_data pad
							LEFT JOIN V_GetApplicationSearch vg on vg.apply_number = pad.apply_number
                            WHERE pad.apply_number = @applyNumber";
            return NpgsqlSearchBySingle<PaperApplicationDataViewModel>(sql, new { applyNumber });
        }
        #endregion

        #region 根據 application_id 獲取紙本資料
        /// <summary>
        /// 根據 application_id 獲取紙本資料
        /// </summary>
        /// <param name="application_id"></param>
        /// <returns></returns>
        public paper_application_data GetPaperApplicationDataByApplicationId(int application_id)
        {
            string sql = $@"SELECT * FROM paper_application_data WHERE application_id = @application_id";
            return NpgsqlSearchBySingle<paper_application_data>(sql, new { application_id });
        }
        #endregion

        #region 根據申請單號獲取紙本資料
        /// <summary>
        /// 根據申請單號獲取紙本資料
        /// </summary>
        /// <param name="applyNumber"></param>
        /// <returns></returns>
        public List<PaperApplicationDataViewModel> GetPaperApplicationDataList(string applyNumber)
        {
            string sql = $@"SELECT
                                pad.apply_number,
                                application_id, --申請單號
                                vg.contract_number,--合約編號
                                (SELECT fe.entity FROM (SELECT entity_id, entity FROM fnp_entity UNION ALL SELECT aff_company_code AS entity_id, aff_company_abb AS entity FROM affiliate_company) fe WHERE fe.entity_id = my_entity_id) AS entityName,--我方主體
                                vg.contract_name,--合約名稱
                                vg.confiden_level
                            FROM paper_application_data pad
							LEFT JOIN V_GetApplicationSearch vg on vg.apply_number = pad.apply_number
							WHERE pad.apply_number LIKE CONCAT(N'%', @applyNumber, N'%')
                            UNION 
							SELECT 
                                apply_number,
                                null as application_id, --申請單號
                                contract_number,--合約編號
                                (SELECT fe.entity FROM (SELECT entity_id, entity FROM fnp_entity UNION ALL SELECT aff_company_code AS entity_id, aff_company_abb AS entity FROM affiliate_company) fe WHERE fe.entity_id = v.entity_id) AS entityName,--我方主體
                                contract_name,--合約名稱
                                v.confiden_level
                            FROM V_GetUnConfirmedApplication v WHERE apply_number LIKE CONCAT(N'%', @applyNumber, N'%')";
            var res = NpgsqlSearchByList<PaperApplicationDataViewModel>(sql, new { applyNumber });
            foreach (PaperApplicationDataViewModel item in res)
            {
                if (!string.IsNullOrEmpty(item.contract_number)) item.contract_number = item.contract_number.Replace("\u001f", "");
                if (!string.IsNullOrEmpty(item.contract_name)) item.contract_name = item.contract_name.Replace("\u001f", "");
                //1.0紙本無機密等級，先放"機密"
                if (string.IsNullOrEmpty(item.confiden_level)) item.confiden_level = "02";
            }
            return res;
        }
        #endregion

        #region 獲取舊資料
        /// <summary>
        /// 獲取舊資料
        /// </summary>
        /// <param name="applyNumber"></param>
        /// <returns></returns>
        public V_GetUnConfirmedApplication GetOldApplication(string applyNumber)
        {
            string sql = $@"SELECT 
                                apply_number,
                                entity_id,
                                party_a,
                                emplid,
                                deptid,
                                contract_number,
                                contract_name,
                                filed_type,
                                filed_date,
                                confiden_level,
                                is_old_system
                            FROM V_GetUnConfirmedApplication
                            WHERE apply_number = @applyNumber";
            return NpgsqlSearchBySingle<V_GetUnConfirmedApplication>(sql, new { applyNumber });
        }
        #endregion

        #region 獲取紙本詳細
        /// <summary>
        /// 獲取紙本詳細
        /// </summary>
        /// <param name="paperCode"></param>
        /// <returns></returns>
        public paper_basic_data GetPaperBasicData(string paperCode)
        {
            string sql = $@"select basic_id, paper_applica_id, paper_code, paper_name, paper_type, paper_confiden_level, parer_borrow_days, paper_position, paper_entry_status, destroy_time, lost_time, paper_remarks, create_user, create_time, modify_user, modify_time from paper_basic_data where paper_code = @paperCode ";
            return NpgsqlSearchBySingle<paper_basic_data>(sql, new { paperCode });
        }
        #endregion

        #region 根據紙本id獲取明細
        /// <summary>
        /// 根據紙本id獲取明細
        /// </summary>
        /// <param name="basic_id"></param>
        /// <returns></returns>
        public paper_basic_data GetPaperBasicDataByBasicID(int basic_id)
        {
            string sql = $@"select basic_id, paper_applica_id, paper_code, paper_name, paper_type, paper_confiden_level, parer_borrow_days, paper_position, paper_entry_status, destroy_time, lost_time, paper_remarks, create_user, create_time, modify_user, modify_time from paper_basic_data where basic_id = @basic_id ";
            return NpgsqlSearchBySingle<paper_basic_data>(sql, new { basic_id });
        }
        #endregion

        #region 根據紙本主資料獲取紙本列表
        /// <summary>
        /// 根據紙本主資料獲取紙本列表
        /// </summary>
        /// <param name="paper_applica_id"></param>
        /// <returns></returns>
        public List<paper_basic_data> GetPaperBasicDataList(int paper_applica_id)
        {
            string sql = $@"select * from paper_basic_data where paper_applica_id = @paper_applica_id ";
            return NpgsqlSearchByList<paper_basic_data>(sql, new { paper_applica_id });
        }
        #endregion

        #region 獲取紙本基本資料
        /// <summary>
        /// 獲取紙本基本資料
        /// </summary>
        /// <param name="basic_id"></param>
        /// <returns></returns>
        public PaperBasicApplicationViewModel GetPaperBasicDataByBasicID(string basic_id)
        {
            string sql = $@"select 
                                pbd.basic_id, 
                                pbd.paper_applica_id, 
                                pbd.paper_code, 
                                pbd.paper_name, 
                                pbd.paper_type, 
                                pbd.paper_confiden_level, 
                                pbd.parer_borrow_days, 
                                pbd.paper_position, 
                                pbd.paper_entry_status, 
                                pbd.destroy_time, 
                                pbd.destroy_reason, 
                                pbd.lost_time, 
                                pbd.lost_reason,
                                pbd.paper_remarks, 
                                pbd.create_user, 
                                pbd.create_time, 
                                pbd.modify_user, 
                                pbd.modify_time,
                                vg.contract_number,
                                vg.contract_name,
                                pad.apply_number,
                                pad.my_entity_id, 
                                pbd.paper_return_status,
                                pad.application_remarks,    
                                COALESCE(aff.aff_company_abb, ent.entity) AS my_entity ,
                                COALESCE(pbd.modify_time, pbd.create_time) AS operate_time,--更新時間
                                COALESCE(ee_modi.name, ee_create.name) AS operate_cuser,--更新人中文名
                                COALESCE(ee_modi.name_a, ee_create.name_a) AS operate_euser--更新人英文名
                            from paper_basic_data pbd   
                            LEFT JOIN paper_application_data pad on pad.application_id = paper_applica_id 
							LEFT JOIN affiliate_company AS aff ON aff.aff_company_code = pad.my_entity_id 
							LEFT JOIN fnp_entity AS ent ON ent.entity_id = pad.my_entity_id 
                            LEFT JOIN (SELECT emplid,name,name_a FROM ps_sub_ee_lgl_vw_a) AS ee_create ON ee_create.emplid = pbd.create_user
							LEFT JOIN (SELECT emplid,name,name_a FROM ps_sub_ee_lgl_vw_a) AS ee_modi ON ee_modi.emplid = pbd.modify_user 
	                        LEFT JOIN V_GetApplicationSearch vg ON pad.apply_number = vg.apply_number 
                            where pbd.basic_id = @basic_id ";
            PaperBasicApplicationViewModel res = NpgsqlSearchBySingle<PaperBasicApplicationViewModel>(sql, new { basic_id });
            if (res != null && res.operate_time != null)
            {
                res.local_operate_time = res.operate_time.ConvertDateByTimeZoneByUtc(MvcContext.UserInfo.time_zone);
            }
            return res;
        }
        #endregion

        #region 獲取歷史資料
        /// <summary>
        /// 獲取歷史資料
        /// </summary>
        /// <param name="paper_basic_id"></param>
        /// <returns></returns>
        public List<PaperHistoryDataViewModel> GetPaperHistoryData(string paper_basic_id)
        {
            string sql = $@"SELECT
                                (SELECT fun_name FROM sys_parameters WHERE para_code = N'lib_paperEntryStatus' AND lang_type = @lang_type AND func_code = phd.paper_entry_status) AS entry_status_name,--入庫狀態
                                cee.name,--收件者中文名
                                cee.name_a,--收件者英文名
                                paper_remarks,--備註
                                borrow_applynumber,--借出單號
                                create_time AS operate_time,--更新時間
                                ee.name AS operate_cuser,--更新人中文名
                                ee.name_a AS operate_euser--更新人英文名
                            FROM (
                                SELECT paper_entry_status,course_emplid,paper_remarks,borrow_applynumber,create_user,create_time FROM paper_history_data WHERE 1 = 1
                                AND paper_basic_id = @paper_basic_id 
                                ) AS phd
                            LEFT JOIN (SELECT emplid,name_a,name FROM ps_sub_ee_lgl_vw_a) AS cee ON cee.emplid = phd.course_emplid
                            LEFT JOIN (SELECT emplid,name_a,name FROM ps_sub_ee_lgl_vw_a) AS ee ON ee.emplid = phd.create_user
                            order by operate_time asc";
            return NpgsqlSearchByList<PaperHistoryDataViewModel>(sql, new { paper_basic_id, lang_type = MvcContext.UserInfo.logging_locale });
        }
        #endregion

        #region 新增借出申請(事務處理)

        #region 獲取紙本明細
        /// <summary>
        /// 獲取紙本明細
        /// </summary>
        /// <param name="applyNumber"></param>
        /// <param name="context"></param>
        /// <returns></returns>
        public PaperApplicationDataViewModel GetPaperApplicationDataToTransaction(string applyNumber, IDbContext context)
        {
            string sql = $@"SELECT
                                application_id, --申請單號
                                vg.contract_number,--合約編號
                                (SELECT entity FROM fnp_entity WHERE entity_id = my_entity_id) AS entityName,--我方主體
                                vg.contract_name,--合約名稱
                                application_status 
                            FROM paper_application_data pad
							LEFT JOIN V_GetApplicationSearch vg on vg.apply_number = pad.apply_number
                            WHERE pad.apply_number = @applyNumber";
            return NpgsqlSearchBySingleToTransaction<PaperApplicationDataViewModel>(sql, context, new { applyNumber });
        }
        #endregion

        #region 查詢資料
        /// <summary>
        /// 查詢資料
        /// </summary>
        /// <param name="paper_applica_id"></param>
        /// <param name="context"></param>
        /// <returns></returns>
        public List<paper_basic_data> GetPaperBasicDataListToTransaction(int paper_applica_id, IDbContext context)
        {
            string sql = $@"select * from paper_basic_data where paper_applica_id = @paper_applica_id ";
            return NpgsqlSearchByListToTransaction<paper_basic_data>(sql, context, new { paper_applica_id });
        }
        #endregion

        #region 根據application_id獲取明細
        /// <summary>
        /// 根據application_id獲取明細
        /// </summary>
        /// <param name="application_id"></param>
        /// <param name="context"></param>
        /// <returns></returns>
        public paper_application_data GetPaperApplicationDataByApplicationIdToTransaction(int application_id, IDbContext context)
        {
            string sql = $@"SELECT * FROM paper_application_data WHERE application_id = @application_id";
            return NpgsqlSearchBySingleToTransaction<paper_application_data>(sql, context, new { application_id });
        }
        #endregion

        #region 獲取未借出明細
        /// <summary>
        /// 獲取未借出明細
        /// </summary>
        /// <param name="applyNumber"></param>
        /// <param name="context"></param>
        /// <returns></returns>
        public V_GetUnConfirmedApplication GetAllOldApplicationToTransaction(string applyNumber, IDbContext context)
        {
            string sql = $@"SELECT 
                                apply_number,
                                entity_id,
								other_party AS party_a,
								pic_emplid AS emplid,
                                pic_deptid As deptid,
                                contract_number,
                                contract_name,
								origin_archive_type AS filed_type,
								origin_archive_date AS filed_date,
                                confiden_level,
                                is_old_system
                            FROM V_GetApplicationSearch
                            WHERE apply_number = @applyNumber";
            return NpgsqlSearchBySingleToTransaction<V_GetUnConfirmedApplication>(sql, context, new { applyNumber });
        }
        #endregion

        #region 查詢基本資料表
        /// <summary>
        /// 查詢基本資料表
        /// </summary>
        /// <param name="basic_id"></param>
        /// <param name="context"></param>
        /// <returns></returns>
        public PaperBasicApplicationViewModel GetPaperBasicDataByBasicIDToTransaction(string basic_id, IDbContext context)
        {
            string sql = $@"select 
                                pbd.basic_id, 
                                pbd.paper_applica_id, 
                                pbd.paper_code, 
                                pbd.paper_name, 
                                pbd.paper_type, 
                                pbd.paper_confiden_level, 
                                pbd.parer_borrow_days, 
                                pbd.paper_position, 
                                pbd.paper_entry_status, 
                                pbd.destroy_time, 
                                pbd.destroy_reason, 
                                pbd.lost_time, 
                                pbd.lost_reason,
                                pbd.paper_remarks, 
                                pbd.create_user, 
                                pbd.create_time, 
                                pbd.modify_user, 
                                pbd.modify_time,
                                vg.contract_number,
                                vg.contract_name,
                                pad.apply_number,
                                pad.my_entity_id, 
                                pbd.paper_return_status,
                                pad.application_remarks,    
                                COALESCE(aff.aff_company_abb, ent.entity) AS my_entity ,
                                COALESCE(pbd.modify_time, pbd.create_time) AS operate_time,--更新時間
                                COALESCE(ee_modi.name, ee_create.name) AS operate_cuser,--更新人中文名
                                COALESCE(ee_modi.name_a, ee_create.name_a) AS operate_euser--更新人英文名
                            from paper_basic_data pbd   
                            LEFT JOIN paper_application_data pad on pad.application_id = paper_applica_id 
							LEFT JOIN affiliate_company AS aff ON aff.aff_company_code = pad.my_entity_id 
							LEFT JOIN fnp_entity AS ent ON ent.entity_id = pad.my_entity_id 
                            LEFT JOIN (SELECT emplid,name,name_a FROM ps_sub_ee_lgl_vw_a) AS ee_create ON ee_create.emplid = pbd.create_user
							LEFT JOIN (SELECT emplid,name,name_a FROM ps_sub_ee_lgl_vw_a) AS ee_modi ON ee_modi.emplid = pbd.modify_user 
	                        LEFT JOIN V_GetApplicationSearch vg ON pad.apply_number = vg.apply_number 
                            where pbd.basic_id = @basic_id ";
            PaperBasicApplicationViewModel res = this.NpgsqlSearchBySingleToTransaction<PaperBasicApplicationViewModel>(sql, context, new { basic_id });
            if (res != null && res.operate_time != null)
            {
                res.local_operate_time = res.operate_time.ConvertDateByTimeZoneByUtc(MvcContext.UserInfo.time_zone);
            }
            return res;
        }
        #endregion

        #region 修改基本資料表狀態
        /// <summary>
        /// 修改基本資料表狀態
        /// </summary>
        /// <param name="basic_id"></param>
        /// <param name="paper_entry_status"></param>
        /// <param name="context"></param>
        /// <returns></returns>
        public bool UpdatePaperBasicDataStatusToTransaction(int basic_id, string paper_entry_status, IDbContext context)
        {
            string sql = $@"UPDATE paper_basic_data SET 
                                paper_entry_status = @paper_entry_status,
                                modify_user = @modify_user, 
                                modify_time = @modify_time 
                            WHERE basic_id = @basic_id";
            return ExecuteCommandToTransaction(sql, context, new { basic_id, paper_entry_status, modify_user = MvcContext.UserInfo.current_emp, modify_time = DateTime.UtcNow }) > 0;
        }
        #endregion

        #region 插入紙本歷程
        /// <summary>
        /// 插入紙本歷程
        /// </summary>
        /// <param name="data"></param>
        /// <param name="context"></param>
        /// <returns></returns>
        public bool InsertPaperHistoryDataToTransaction(paper_history_data data, IDbContext context)
        {
            string sql = $@"INSERT INTO paper_history_data (
                                paper_basic_id,
                                paper_entry_status,
                                course_emplid,
                                paper_remarks,
                                borrow_applynumber,
                                create_user,
                                create_time ) 
                            VALUES ( 
                                @paper_basic_id,
                                @paper_entry_status,
                                @course_emplid,
                                @paper_remarks,
                                @borrow_applynumber,
                                @create_user,
                                @create_time )";
            return ExecuteCommandToTransaction(sql, context, data) > 0;
        }
        #endregion

        #region 更新紙本基本資料
        /// <summary>
        /// 更新紙本基本資料
        /// </summary>
        /// <param name="data"></param>
        /// <param name="context"></param>
        /// <returns></returns>
        public bool UpdatePaperBasicDataToTransaction(paper_basic_data data, IDbContext context)
        {
            string sql = $@"UPDATE paper_basic_data SET 
                                paper_code = @paper_code, 
                                paper_name = @paper_name, 
                                paper_type = @paper_type, 
                                paper_confiden_level = @paper_confiden_level, 
                                parer_borrow_days = @parer_borrow_days, 
                                paper_position = @paper_position, 
                                paper_entry_status = @paper_entry_status, 
                                paper_return_status = @paper_return_status,
                                destroy_time = @destroy_time, 
                                lost_time = @lost_time, 
                                paper_remarks = @paper_remarks,
                                modify_user = @modify_user, 
                                modify_time = @modify_time,
                                destroy_reason = @destroy_reason,
                                lost_reason = @lost_reason 
                            WHERE basic_id = @basic_id";
            return ExecuteCommandToTransaction(sql, context, data) > 0;
        }
        #endregion

        #region 插入紙本數據
        /// <summary>
        /// 插入紙本數據
        /// </summary>
        /// <param name="data"></param>
        /// <param name="context"></param>
        /// <returns></returns>
        public bool InsertPaperApplicationDataToTransaction(paper_application_data data, IDbContext context)
        {
            string sql = $@"INSERT INTO paper_application_data (
                                apply_number, 
                                my_entity_id, 
                                party_a, 
                                pic_emplid, 
                                pic_deptid,
                                contract_number, 
                                contract_name, 
                                having_paper, 
                                application_status, 
                                application_remarks, 
                                is_old_application, 
                                create_user, 
                                create_time ) 
                            VALUES (@apply_number,@my_entity_id, @party_a, @pic_emplid, @pic_deptid,
                                @contract_number, @contract_name, @having_paper, @application_status, @application_remarks, @is_old_application, @create_user, @create_time)";
            return ExecuteCommandToTransaction(sql, context, data) > 0;
        }
        #endregion

        #region 修改紙本信息
        /// <summary>
        /// 修改紙本信息
        /// </summary>
        /// <param name="appli"></param>
        /// <param name="context"></param>
        /// <returns></returns>
        public bool UpdatePaperApplicationDataToTransaction(paper_application_data appli, IDbContext context)
        {
            string sql = $@"UPDATE paper_application_data
            SET  my_entity_id = @my_entity_id
                ,party_a = @party_a
                ,pic_emplid = @pic_emplid
                ,pic_deptid = @pic_deptid
                ,contract_number = @contract_number
                ,contract_name = @contract_name
                ,modify_user = @modify_user
                ,modify_time = GETUTCDATE()
            WHERE apply_number = @apply_number";
            return ExecuteCommandToTransaction(sql, context, appli) > 0;
        }
        #endregion

        #region 新增紙本基本資料
        /// <summary>
        /// 新增紙本基本資料
        /// </summary>
        /// <param name="data"></param>
        /// <param name="context"></param>
        /// <returns></returns>
        public bool InsertPaperBasicDataToTransaction(paper_basic_data data, IDbContext context)
        {
            string sql = $@"INSERT INTO paper_basic_data (
                                paper_applica_id,
                                paper_code,
                                paper_name,
                                paper_type,
                                paper_confiden_level,
                                parer_borrow_days,
                                paper_position,
                                paper_entry_status,
                                paper_return_status,
                                destroy_time,
                                lost_time,
                                paper_remarks,
                                create_user,
                                create_time,
                                destroy_reason, 
                                lost_reason) 
                            VALUES ( @paper_applica_id, @paper_code, @paper_name, @paper_type, @paper_confiden_level, @parer_borrow_days, 
                                    @paper_position, @paper_entry_status, @paper_return_status, @destroy_time, @lost_time, @paper_remarks, @create_user, @create_time, @destroy_reason, @lost_reason)";
            return ExecuteCommandToTransaction(sql, context, data) > 0;
        }
        #endregion

        #region 獲取紙本基本資料
        /// <summary>
        /// 獲取紙本基本資料
        /// </summary>
        /// <param name="paperCode"></param>
        /// <param name="context"></param>
        /// <returns></returns>
        public paper_basic_data GetPaperBasicDataToTransaction(string paperCode, IDbContext context)
        {
            string sql = $@"select basic_id, paper_applica_id, paper_code, paper_name, paper_type, paper_confiden_level, parer_borrow_days, paper_position, paper_entry_status, destroy_time, lost_time, paper_remarks, create_user, create_time, modify_user, modify_time from paper_basic_data where paper_code = @paperCode ";
            return NpgsqlSearchBySingleToTransaction<paper_basic_data>(sql, context, new { paperCode });
        }
        #endregion

        #region 修改備註信息
        /// <summary>
        /// 修改備註信息
        /// </summary>
        /// <param name="applyNumber"></param>
        /// <param name="application_remark"></param>
        /// <param name="context"></param>
        /// <returns></returns>
        public bool UpdatePaperApplicationDataRemarkToTransaction(string applyNumber, string application_remark, IDbContext context)
        {
            string sql = $@"UPDATE paper_application_data SET application_remarks = @application_remark WHERE apply_number = @applyNumber";
            return ExecuteCommandToTransaction(sql, context, new { applyNumber, application_remark }) > 0;
        }
        #endregion

        #region 刪除基本資料
        /// <summary>
        /// 刪除基本資料
        /// </summary>
        /// <param name="paper_applica_id"></param>
        /// <param name="context"></param>
        /// <returns></returns>
        public bool BatchDeletePaperBasicDataToTransaction(int paper_applica_id, IDbContext context)
        {
            string sql = $@"DELETE FROM paper_basic_data WHERE paper_applica_id = @paper_applica_id";
            return ExecuteCommandToTransaction(sql, context, new { paper_applica_id }) > 0;
        }
        #endregion

        #region 刪除紙本基本資料
        /// <summary>
        /// 刪除紙本基本資料
        /// </summary>
        /// <param name="basic_id"></param>
        /// <param name="context"></param>
        /// <returns></returns>
        public bool DeletePaperBasicDataToTransaction(string basic_id, IDbContext context)
        {
            string sql = $@"DELETE FROM paper_basic_data WHERE basic_id = @basic_id";
            return ExecuteCommandToTransaction(sql, context, new { basic_id }) > 0;
        }
        #endregion

        #endregion
    }
}
