﻿using Elegal.Flow.Api.Services.Paper;
using Elegal.Interface.Api.Common.Control.Filter;
using Elegal.Interface.Api.Common.Control.Web;
using Elegal.Interface.Api.Common.Model.ParaModel.FlowApi;
using Elegal.Interface.Api.Common.Model.ParaModel.FlowApi.ChangedRecord;
using Elegal.Interface.Api.Common.Model.ResultModel;
using Elegal.Orm.Dtos;
using Microsoft.AspNetCore.Mvc;
using System.Text;
using Elegal.Interface.Api.Common.FuncHelper;
using Minio.DataModel;
using Elegal.Interface.Api.Common.Model.ResultModel.FlowApi.ChangedRecord;
using Elegal.Flow.Api.Services.ChangedRecord;
using Elegal.Flow.Api.Services.LendAppliction;
using Elegal.Interface.Api.Common.Model.ParaModel.FlowApi.LendAppliction;
using MathNet.Numerics.LinearAlgebra.Factorization;
using Elegal.Interface.Api.Common.GetLogCommon;

namespace Elegal.Flow.Api.Controllers.ChangedRecord;

/// <summary>
/// 異動記錄
/// </summary>
[Route("[controller]")]
[ApiController]
public class ChangedRecordController : BaseController
{
    /// <summary>
    /// 查詢異動記錄
    /// </summary>
    /// <returns></returns>
    [HttpPost]
    [Route("Query")]
    public async Task<ApiResultModelByObject> QueryChangeRecordsData(QryChangeRecordPara qry)
    {
        ApiResultModelByObject apiResult = new ApiResultModelByObject();

        ChangePageResult<QryChangeRecordResultModel> result = ChangedRecordService.QueryPageViewResult(qry??new QryChangeRecordPara());
        apiResult.listData = result;
        apiResult.totalCount = result.TotalRows;
        apiResult.rtnSuccess = true;

        //記錄日誌
        InitLogRecord(qry, logRecord =>
        {
            StringBuilder stringBuilder = new();
            if (!string.IsNullOrWhiteSpace(qry.empid)) {
                stringBuilder.AppendLine(ActionFilter.GetMultilingualValue("ChangedRecord_empid", true) + ":" + string.Join(",", GetLogCommon.GetUserName([qry.empid])));
            }
            if (!string.IsNullOrWhiteSpace(qry.work_key))
            {
                stringBuilder.AppendLine(ActionFilter.GetMultilingualValue("ChangedRecord_workkey", true) + ":" + qry.work_key);
            }
            if (!string.IsNullOrWhiteSpace(qry.work_key))
            {
                var d1 = qry.startTime.ConvertDateByTimeZoneByUtc(MvcContext.UserInfo.time_zone)?.ToString("yyyy/MM/dd")??string.Empty;
                var d2 = qry.endTime.ConvertDateByTimeZoneByUtc(MvcContext.UserInfo.time_zone)?.ToString("yyyy/MM/dd") ?? string.Empty;
                stringBuilder.AppendLine(ActionFilter.GetMultilingualValue("ChangedRecord_ChangeTime", true) + ":" + d1+"-"+d2);
            }
            if (qry.change_types.Count > 0) {
                stringBuilder.AppendLine(ActionFilter.GetMultilingualValue("ChangedRecord_changetypes", true)+":"+string.Join(",", qry.change_types.Select(e => e.Value)));
            }
            if (qry.isModifyAnnexs.Count > 0)
            {
                stringBuilder.AppendLine(ActionFilter.GetMultilingualValue("ChangedRecord_isModifyAnnexs", true) + ":" + string.Join(",", qry.isModifyAnnexs.Select(e => e)));
            }
            logRecord.Detail = stringBuilder.ToString();

        });

        return await Task.FromResult(apiResult);
    }



    /// <summary>
    /// 资料查询，借出中查询 导出
    /// </summary>
    /// <param name="para"></param>
    /// <returns></returns>
    [HttpPost]
    [Route("ChangeRecordsExport")]
    public FileContentResult ChangeRecordsExport([FromBody] QryChangeRecordExportPara para)
    {
        ApiResultModelByObject apiResult = new ApiResultModelByObject();
        var excelBytes = ChangedRecordService.ChangeRecordsExport(para, MvcContext.UserInfo);
        #region 記錄日誌
        //記錄日誌
        InitLogRecord(para, logRecord =>
        {
            StringBuilder stringBuilder = new();
            if (!string.IsNullOrWhiteSpace(para.empid))
            {
                stringBuilder.AppendLine(ActionFilter.GetMultilingualValue("ChangedRecord_empid", true) + ":" + string.Join(",", GetLogCommon.GetUserName([para.empid])));
            }
            if (!string.IsNullOrWhiteSpace(para.work_key))
            {
                stringBuilder.AppendLine(ActionFilter.GetMultilingualValue("ChangedRecord_workkey", true) + ":" + para.work_key);
            }
            if (!string.IsNullOrWhiteSpace(para.work_key))
            {
                var d1 = para.startTime.ConvertDateByTimeZoneByUtc(MvcContext.UserInfo.time_zone)?.ToString("yyyy/MM/dd") ?? string.Empty;
                var d2 = para.endTime.ConvertDateByTimeZoneByUtc(MvcContext.UserInfo.time_zone)?.ToString("yyyy/MM/dd") ?? string.Empty;
                stringBuilder.AppendLine(ActionFilter.GetMultilingualValue("ChangedRecord_ChangeTime", true) + ":" + d1 + "-" + d2);
            }
            if (para.change_types.Count > 0)
            {
                stringBuilder.AppendLine(ActionFilter.GetMultilingualValue("ChangedRecord_changetypes", true) + ":" + string.Join(",", para.change_types.Select(e => e.Value)));
            }
            if (para.isModifyAnnexs.Count > 0)
            {
                stringBuilder.AppendLine(ActionFilter.GetMultilingualValue("ChangedRecord_isModifyAnnexs", true) + ":" + string.Join(",", para.isModifyAnnexs.Select(e => e)));
            }
            logRecord.Detail = stringBuilder.ToString();

        });
        #endregion
        return File(excelBytes, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "資料查詢_異動案件查詢_" + DateTime.UtcNow.ConvertDateByTimeZoneByUtc(MvcContext.UserInfo.time_zone).ToString("yyyyMMdd") + ".xlsx");
    }
}
