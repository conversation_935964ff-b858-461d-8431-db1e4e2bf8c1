﻿using Elegal.Interface.Api.Common.Control.Filter;
using Elegal.Interface.Api.Common.FuncHelper;
using Elegal.Interface.Api.Common.Model.ParaModel.FlowApi;
using Elegal.Interface.Api.Common.Model.ResultModel.FlowApi;
using Elegal.Interface.Api.Common.Model.ResultModel.Mino;
using Elegal.Interface.Api.Common.Model.ViewModel.FlowApi;
using Elegal.Interface.Api.Common.Repository;
using Microsoft.IdentityModel.Tokens;
using System.Text;

namespace Elegal.Flow.Api.Repository
{
    /// <summary>
    /// 公共頁簽和公告管理
    /// </summary>
    public class BulletinsRepository : BaseRepository
    {
        /// <summary>
        /// 實例化
        /// </summary>
        public BulletinsRepository() : base() { }

        /// <summary>
        /// 查詢所有公共
        /// </summary>
        /// <returns></returns>
        public List<BulletinsResult> QueryBulletins(Bulletins bulletins, string locale, string timeZone = "Taipei Standard Time")
        {
            StringBuilder sqlBuilder = new();
            string StartTimeString = string.Empty;
            string EndTimeString = string.Empty;
            sqlBuilder.Append(@$"SELECT
                                newid,
                                news_subject,--公告標題
                                entity_id,--我方主體(需要轉換為主體簡稱)
                                case entity_id when '-1' then N'全部主體'
                                		else (select STRING_AGG(entity, ', ') from fnp_entity where entity_id in (SELECT Value FROM STRING_SPLIT(newData.entity_id, ','))) END AS entity_name,
                                (SELECT fun_name FROM sys_parameters WHERE para_code = 'infoManage' AND lang_type = '{locale}' AND func_code = leaf_id) AS leaf_name,--顯示區塊
                                leaf_id,
                                (CASE WHEN (leaf_id = N'02' AND is_builtin = 1) then 
                                (select IIF(COALESCE(max(pur.create_time),max(pr.create_time)) > new_time,COALESCE(max(pur.create_time),max(pr.create_time)),new_time) as create_time
									from p_user_role pur
									right join p_role pr 
									on pur.r_id = pr.r_id 
									where pr.r_name in (      
									select CONCAT(fe.entity,N'合約管理人Contract Admin') as r_name from fnp_entity fe
									join fnp_news_entity fne 
									on fe.entity_id = fne.entity_id
									where fne.newid = newData.newid)) else new_time end) as new_time,--公告日期
                                news_start_time,--公告開始時間
                                news_end_time,--公告結束時間
                                news_status,--公告狀態
                                builtin_entity_id,
                                is_builtin,
                                operate_time,--更新時間
                                ee.name AS operate_cuser,--更新人中文名
                                ee.name_a AS operate_euser--更新人英文名
                                FROM (
                                SELECT fn.newid,leaf_id,new_time,is_builtin,
                                fne.entity_id,news_subject,news_start_time,news_end_time,news_status,ISNULL(modify_user,create_user) AS operate_user,ISNULL(modify_time,create_time) AS operate_time,fne.builtin_entity_id
                                FROM fnp_news fn join (select DISTINCT fne.newid ,fe.entity_id,builtin_entity_id from  fnp_news_entity fne 
                                join (select news.newid,STRING_AGG(news.entity_id, ',') entity_id from fnp_news_entity news where
                                news.newid in (select DISTINCT newid from fnp_news_entity where 1=1 ");
            if (!string.IsNullOrEmpty(bulletins.entity_id.Key))
            {
                if (bulletins.quert_flag.Equals(1))
                {
                    string[] ids = bulletins.entity_id.Key.Split(',');
                    string result = string.Join(",", ids.Select(id => $"'{id}'"));
                    sqlBuilder.Append(@$" AND entity_id in ({bulletins.entity_id.Key},-1)");
                }
                else
                {
                    string[] ids = bulletins.entity_id.Key.Split(',');
                    string result = string.Join(",", ids.Select(id => $"'{id}'"));
                    sqlBuilder.Append(@$" AND entity_id in ({bulletins.entity_id.Key})");
                }
            }
            sqlBuilder.Append($@") group by news.newid) fe
                                    on fne.newid = fe.newid
                                    where 1 = 1 and 
((fne.newid in (select DISTINCT newid from fnp_news_entity where builtin_entity_id is not null)
and fne.builtin_entity_id is not null) or
(fne.newid in (select DISTINCT newid from fnp_news_entity t1
where not EXISTS (select 1 from fnp_news_entity t2 where builtin_entity_id is not null
and t1.newid = t2.newid)
and fne.builtin_entity_id is null))) ");
            sqlBuilder.Append(@$" ) fne on fne.newid = fn.newid WHERE 1 = 1");

            if (!string.IsNullOrEmpty(bulletins.leaf_ids.Key))
            {
                var leafList = bulletins.leaf_ids.Key.Split(',');
                sqlBuilder.Append(@$" AND leaf_id IN({string.Join(",", leafList.Select(s => $"'{s}'"))})");
            }
            if (!string.IsNullOrEmpty(bulletins.news_subject))
                sqlBuilder.Append($@" AND news_subject LIKE N'%{bulletins.news_subject}%'");
            if (bulletins.news_status != null)
                sqlBuilder.Append($@" AND news_status = N''+@news_status");
            if (bulletins.news_start_time != null)
            {
                StartTimeString = TimeZoneInfo.ConvertTimeFromUtc(bulletins.news_start_time.Value, TimeZoneInfo.FindSystemTimeZoneById(timeZone)).ToString("yyyy-MM-dd");
                TimeZoneInfo timeZoneInfo = TimeZoneInfo.FindSystemTimeZoneById(timeZone);
                string baseUtcOffset = timeZoneInfo.BaseUtcOffset.ToString();
                if (baseUtcOffset.IndexOf("-") > -1)
                {
                    baseUtcOffset = baseUtcOffset.Substring(0, 6);
                }
                else
                {
                    baseUtcOffset = "+" + baseUtcOffset.Substring(0, 5);
                }
                sqlBuilder.Append($@" AND (CONVERT(VARCHAR(100), SWITCHOFFSET(news_end_time, '{baseUtcOffset}'), 23) >= @news_start_time or news_end_time is null)");
            }

            if (bulletins.news_end_time != null)
            {
                EndTimeString = TimeZoneInfo.ConvertTimeFromUtc(bulletins.news_end_time.Value, TimeZoneInfo.FindSystemTimeZoneById(timeZone)).ToString("yyyy-MM-dd");
                TimeZoneInfo timeZoneInfo = TimeZoneInfo.FindSystemTimeZoneById(timeZone);
                string baseUtcOffset = timeZoneInfo.BaseUtcOffset.ToString();
                if (baseUtcOffset.IndexOf("-") > -1)
                {
                    baseUtcOffset = baseUtcOffset.Substring(0, 6);
                }
                else
                {
                    baseUtcOffset = "+" + baseUtcOffset.Substring(0, 5);
                }
                sqlBuilder.Append($@" AND (CONVERT(VARCHAR(100), SWITCHOFFSET(news_start_time, '{baseUtcOffset}'), 23) <= @news_end_time or news_start_time is null)");
            }
            sqlBuilder.Append(@$"
                                ) AS newData
                                LEFT JOIN (SELECT emplid,name,name_a FROM ps_sub_ee_lgl_vw_a) AS ee ON ee.emplid = newData.operate_user
                                order by operate_time desc");
            return this.NpgsqlSearchByList<BulletinsResult>(sqlBuilder.ToString(), new { news_status = bulletins.news_status, news_start_time = StartTimeString, news_end_time = EndTimeString },
                "time_zone", timeZone);
        }

        /// <summary>
        /// 刪除公告
        /// </summary>
        /// <param name="newid"></param>
        /// <returns></returns>
        public int DeleteBulletinsByNewid(int newid)
        {
            StringBuilder sqlBuilder = new();
            sqlBuilder.Append(@$"DELETE FROM fnp_news WHERE newid = @newid;");
            return this.ExecuteCommand(sqlBuilder.ToString(), new { newid = newid });
        }

        /// <summary>
        /// 刪除公告對應的主體
        /// </summary>
        /// <param name="newid"></param>
        /// <returns></returns>
        public int DeleteBulletinsEntityByNewid(int newid)
        {
            StringBuilder sqlBuilder = new();
            sqlBuilder.Append(@$"DELETE FROM fnp_news_entity where newid = @newid;");
            return this.ExecuteCommand(sqlBuilder.ToString(), new { newid = newid });
        }

        /// <summary>
        /// 根據ID查詢公告時其他主體的內置公告
        /// </summary>
        /// <param name="newid"></param>
        /// <returns></returns>
        public string QueryBuiltinEntityByNewid(int newid)
        {
            StringBuilder sqlBuilder = new();
            sqlBuilder.Append(@$"select DISTINCT  (case when (select count(1) from fnp_news_entity where newid = @newid and builtin_entity_id is not null)>0
                                then (select builtin_entity_id from fnp_news_entity where newid = @newid and builtin_entity_id is not null)
                                else '' end) as builtin_entity_id from fnp_news_entity where newid = @newid");
            return (string)this.NpgsqlSearchBySingleValue(sqlBuilder.ToString(), new { newid = newid });
        }

        /// <summary>
        /// 根據newid查詢單筆信息
        /// </summary>
        /// <param name="newid"></param>
        /// <param name="timeZone"></param>
        /// <param name="local"></param>
        /// <returns></returns>
        public BulletinsResult QueryBulletinsByNewid(int newid, string timeZone = "Taipei Standard Time", string local = "ZH-TW")
        {
            StringBuilder sqlBuilder = new();
            sqlBuilder.Append(@$"
                        DECLARE @temp TABLE (  
    id INT IDENTITY(1,1),
    emplid NVARCHAR(255) COLLATE Chinese_Taiwan_Stroke_CI_AS,
    name_a NVARCHAR(255) COLLATE Chinese_Taiwan_Stroke_CI_AS,
    name NVARCHAR(255) COLLATE Chinese_Taiwan_Stroke_CI_AS,
    phone_a NVARCHAR(255) COLLATE Chinese_Taiwan_Stroke_CI_AS,
    prefix_dial_code_a NVARCHAR(255) COLLATE Chinese_Taiwan_Stroke_CI_AS,
    email_address_a NVARCHAR(255) COLLATE Chinese_Taiwan_Stroke_CI_AS
    
)  
  
INSERT INTO @temp (emplid,name_a,name,phone_a,prefix_dial_code_a,email_address_a   )  
SELECT emplid,name_a,name,phone_a,prefix_dial_code_a,email_address_a   
FROM ps_sub_ee_lgl_vw_a   
WHERE emplid in (select u_id 
			from p_user_role pur
			join p_role pr 
			on pur.r_id = pr.r_id 
			where pr.r_name in (      
			select CONCAT(fe.entity,N'合約管理人Contract Admin') as r_name from fnp_entity fe
			join fnp_news_entity fne 
			on fe.entity_id = fne.builtin_entity_id
			where fne.newid = @newid))  
ORDER BY   
    stuff(emplid, 1, patindex('%[A-z]%', substring(emplid, 1, 1)) - 1, '') ASC,   
    len(emplid) DESC,   
    emplid DESC  

SELECT
                                newid,
                                news_subject,--公告標題
                                (CASE WHEN (leaf_id = N'02' AND is_builtin = 1)
                                THEN (SELECT CONCAT(STRING_AGG(CONCAT(
                                N'姓名Name：',ee.name_a,emplid ,N'(',ee.name,N')','
',N'區碼+分機ext：',(CASE WHEN RTRIM(LTRIM(ISNULL(phone_a, ''))) = N'' THEN '' ELSE (
                        CONCAT(CASE RTRIM(LTRIM(ISNULL(prefix_dial_code_a, ''))) WHEN '' THEN '0000' ELSE RTRIM(LTRIM(ISNULL(prefix_dial_code_a, '0000'))) END,N'+',RTRIM(LTRIM(ISNULL(phone_a, ''))))
                        ) END
                        ),'
',N'e-mail：',CASE WHEN ee.email_address_a = N'NULL' THEN '' ELSE ee.email_address_a END,'
'),N'------------------------------------------------
'),'
') from @temp ee)
			else news_content end)news_content_builtin,--公告内容
                                news_content,
                                is_global,--是否全局顯示
                                entity_id,--我方主體(需要轉換為主體簡稱)
                                case entity_id when '-1' then N'全部主體'
                                		else (select STRING_AGG(entity, ', ') from fnp_entity where entity_id in (SELECT Value FROM STRING_SPLIT(newData.entity_id, ','))) END AS entity_name,
                                leaf_id,--顯示區塊
                                (SELECT fun_name FROM sys_parameters WHERE para_code = 'infoManage' AND lang_type = N'' + @local AND func_code = leaf_id) AS leaf_name,
                                (CASE WHEN (leaf_id = N'02' AND is_builtin = 1) then 
                                (select IIF(COALESCE(max(pur.create_time),max(pr.create_time)) > new_time,COALESCE(max(pur.create_time),max(pr.create_time)),new_time) as create_time
									from p_user_role pur
									right join p_role pr 
									on pur.r_id = pr.r_id 
									where pr.r_name in (      
									select CONCAT(fe.entity,N'合約管理人Contract Admin') as r_name from fnp_entity fe
									join fnp_news_entity fne 
									on fe.entity_id = fne.entity_id
									where fne.newid = newData.newid)) else new_time end) as new_time,--公告日期
                                news_start_time,--公告開始時間
                                news_end_time,--公告結束時間
                                news_status,--公告狀態
                                is_builtin,
                                suf.file_name,--關聯文件名
                                suf.fileid as file_id,--關聯文件ID
                                builtin_entity_id,
                                operate_time,--更新時間
                                ee.name AS operate_cuser,--更新人中文名
                                ee.name_a AS operate_euser--更新人英文名
                                FROM (
                                SELECT fn.newid,leaf_id,new_time,news_content,is_global,fne.entity_id,news_subject,news_start_time,is_builtin,fne.builtin_entity_id,
                                news_end_time,news_status,ISNULL(modify_user,create_user) AS operate_user,ISNULL(modify_time,create_time) AS operate_time 
                                FROM fnp_news fn join (select DISTINCT fne.newid ,fe.entity_id,(case when (select count(1) from fnp_news_entity where newid = @newid and builtin_entity_id is not null)>0
                                then (select builtin_entity_id from fnp_news_entity where newid = @newid and builtin_entity_id is not null)
                                else '' end) as builtin_entity_id from  fnp_news_entity fne 
                                join (select news.newid,STRING_AGG(news.entity_id, ',') entity_id from fnp_news_entity news where
                                news.newid in (select DISTINCT newid from fnp_news_entity where 1=1 
                                ) group by news.newid) fe
                                on fne.newid = fe.newid
                                where 1 = 1) fne on fne.newid = fn.newid WHERE 1 = 1");
            if (newid > 0)
                sqlBuilder.Append(@$"AND fn.newid = @newid");
            sqlBuilder.Append(@$") AS newData
                                LEFT JOIN (SELECT emplid,name,name_a FROM ps_sub_ee_lgl_vw_a) AS ee ON ee.emplid = newData.operate_user
                                LEFT JOIN (select suf.fileid ,suf.upload_key ,suf.file_name from sys_upload_file suf join (
                                select Max(fileid) as fileid  ,upload_key  from sys_upload_file where upload_type = 1
                                GROUP by upload_key) msuf
                                on suf.fileid = msuf.fileid and suf.upload_key = msuf.upload_key) AS suf on CONVERT(NVARCHAR(max), newData.newid) = CONVERT(NVARCHAR(max), suf.upload_key)");
            return this.NpgsqlSearchBySingle<BulletinsResult>(sqlBuilder.ToString(), new { newid = newid, local = local },
                "time_zone", timeZone);
        }

        /// <summary>
        /// 插入公告
        /// </summary>
        /// <param name="bulletins"></param>
        /// <returns></returns>
        public int InsertBulletins(Bulletins bulletins)
        {
            string sql;
            switch (bulletins.leaf_id)
            {
                case "03"://全局提示特殊處理
                    sql = @$"INSERT INTO fnp_news (leaf_id, news_subject, new_time, news_start_time, news_end_time, news_status, is_global, create_user)
                        VALUES(N''+@leaf_id, N''+@news_subject , @new_time, @news_start_time, @news_end_time, @news_status, @is_global , N''+@operate_cuser);
                        SELECT SCOPE_IDENTITY();";
                    break;
                default:
                    sql = @$"INSERT INTO fnp_news (leaf_id, news_subject, news_content, news_filename, news_path, new_time, news_start_time, news_end_time, news_status, create_user)
                        VALUES(N''+@leaf_id, N''+@news_subject, N''+@news_content, N''+@news_filename, N''+@news_path, @new_time, @news_start_time, @news_end_time,@news_status , N''+@operate_cuser);
                    SELECT SCOPE_IDENTITY();";
                    break;
            }
            return Convert.ToInt32(this.NpgsqlSearchBySingleValue(sql, new { leaf_id = bulletins.leaf_id, news_subject = bulletins.news_subject, new_time = bulletins.new_time, news_start_time = bulletins.news_start_time, news_end_time = bulletins.news_end_time, news_status = bulletins.news_status, operate_cuser = bulletins.operate_user, news_content = bulletins.news_content, news_filename = bulletins.news_filename, news_path = bulletins.news_path, is_global = bulletins.is_global }));
        }

        /// <summary>
        /// 插入公告主體
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        public int InsertBulletinsEntity(FnpNewsEntity entity)
        {
            string sql = $@"INSERT INTO fnp_news_entity
                            (newid, entity_id, create_user, create_time, builtin_entity_id)
                            VALUES(@newid, @entity_id, @create_user, getutcdate(),@builtin_entity_id);";
            return this.ExecuteCommand(sql, new { newid = entity.newid, entity_id = entity.entity_id, create_user = entity.create_user, builtin_entity_id = entity.builtin_entity_id });
        }

        /// <summary>
        /// 檢驗公告是否有重復
        /// </summary>
        /// <param name="news_subject"></param>
        /// <param name="newid"></param>
        /// <returns></returns>
        public Boolean DuplicateVerificationBulletins(string news_subject, int newid)
        {
            StringBuilder sql = new();
            sql.Append(@$"SELECT count(1)
                FROM fnp_news fn join (select DISTINCT fne.newid ,fe.entity_id from  fnp_news_entity fne 
                join (select news.newid,STRING_AGG(news.entity_id, ',') entity_id from fnp_news_entity news where
                news.newid in (select DISTINCT newid from fnp_news_entity where 1=1 
                ) group by news.newid) fe
                on fne.newid = fe.newid
                where 1 = 1) fne on fne.newid = fn.newid WHERE 1 = 1
                AND fn.news_subject = N''+@news_subject ");
            if (newid > 0)
                sql.Append(@$"AND fn.newid != @newid");
            return Convert.ToInt32(this.NpgsqlSearchBySingleValue(sql.ToString(), new { news_subject = news_subject, newid = newid })) > 0;
        }

        /// <summary>
        /// 修改公告
        /// </summary>
        /// <param name="bulletins"></param>
        /// <returns></returns>
        public int UpdateBulletins(Bulletins bulletins)
        {
            string? timeZone = AppSettingHelper.Configuration["TimeZone"];
            TimeZoneInfo timeZoneInfo = null;
            if (!string.IsNullOrEmpty(timeZone))
            {
                timeZoneInfo = TimeZoneInfo.FindSystemTimeZoneById(MvcContext.UserInfo.time_zone != null ? MvcContext.UserInfo.time_zone : timeZone);
            }
            string baseUtcOffset = timeZoneInfo.BaseUtcOffset.ToString();
            if (baseUtcOffset.IndexOf("-") > -1)
            {
                baseUtcOffset = baseUtcOffset.Substring(0, 6);
            }
            else
            {
                baseUtcOffset = "+" + baseUtcOffset.Substring(0, 5);
            }
            string sql;
            switch (bulletins.leaf_id)
            {
                case "03"://全局提示特殊處理
                    sql = @$"delete from fnp_news_click
                        where EXISTS (select 1 from fnp_news fe
                               where fe.newid = newid
                               and CONVERT(date,SWITCHOFFSET(fe.new_time, '{baseUtcOffset}')) != CONVERT(date,SWITCHOFFSET(@new_time, '{baseUtcOffset}'))
                                 and fe.newid = @newid)
                               and newid = @newid;

                        UPDATE fnp_news SET leaf_id = N''+@leaf_id,new_time = @new_time,news_subject = N''+@news_subject,news_content = '',news_start_time = @news_start_time,news_end_time = @news_end_time,news_status = @news_status,is_global = {(bulletins.is_global ? 1 : 0)},news_filename = '',news_path = '',modify_user = N''+@modify_user,modify_time = getutcdate() WHERE newid = @newid";
                    break;
                default:
                    sql = @$"delete from fnp_news_click
                        where EXISTS (select 1 from fnp_news fe
                               where fe.newid = newid
                               and CONVERT(date,SWITCHOFFSET(fe.new_time, '{baseUtcOffset}')) != CONVERT(date,SWITCHOFFSET('{bulletins.new_time}', '{baseUtcOffset}'))
                                 and fe.newid = @newid)
                               and newid = @newid;

                    UPDATE fnp_news SET leaf_id = N''+@leaf_id,new_time = @new_time,news_subject = N''+@news_subject,news_content = N''+@news_content,news_start_time = @news_start_time,news_end_time = @news_end_time,news_status = @news_status,is_global = 0,news_filename = N''+@news_filename,news_path = N''+@news_path,modify_user = N''+@modify_user,modify_time = getutcdate() WHERE newid = @newid";
                    break;
            }
            return this.ExecuteCommand(sql, new { leaf_id = bulletins.leaf_id, news_subject = bulletins.news_subject, new_time = bulletins.new_time, news_start_time = bulletins.news_start_time, news_end_time = bulletins.news_end_time, news_status = bulletins.news_status, operate_cuser = bulletins.operate_cuser, news_content = bulletins.news_content, news_filename = bulletins.news_filename, news_path = bulletins.news_path, modify_user = bulletins.operate_user, newid = bulletins.newid });
        }

        /// <summary>
        /// 绑定公告与文件服务
        /// </summary>
        /// <returns></returns>
        public int BindServicesToFile(int? file_id, string key)
        {
            string sql = @"UPDATE sys_upload_file
                        SET upload_key=@key, is_temporary = 0 ,modify_time = getutcdate()
                        WHERE fileid=@file_id;";
            return this.ExecuteCommand(sql, new { key = key, file_id = file_id });
        }

        /// <summary>
        /// 一处公告与文件绑定关系
        /// </summary>
        /// <returns></returns>
        public int RemoveBindingFile(string upload_key)
        {
            string sql = @"DELETE FROM sys_upload_file
                        WHERE upload_key=@upload_key and upload_type = 1";
            return this.ExecuteCommand(sql, new { upload_key = upload_key });
        }

        /// <summary>
        /// 校验主体步骤
        /// </summary>
        /// <returns></returns>
        public Boolean VerifyEntityEnable(string entity_id)
        {
            string sql = @" select count(1) from sys_entity_manage_step where entity_id = @entity_id";
            return Convert.ToInt32(this.NpgsqlSearchBySingleValue(sql, new { entity_id = entity_id })) > 0;
        }

        /// <summary>
        /// 删除公告，并删除相关关系
        /// </summary>
        /// <returns></returns>
        public int RelieveEntityAndBulletinsBindings(int newid, string entity_id)
        {
            string upload_key = newid.ToString();
            string sql = @"DELETE FROM fnp_news_entity where newid = @newid and entity_id = @entity_id;
                            IF (SELECT COUNT(1) FROM fnp_news_entity where newid = @newid) = 0
                            BEGIN
                                DELETE FROM fnp_news WHERE newid = @newid;
                                DELETE FROM sys_upload_file where upload_type = 1 and upload_key = @upload_key;
                            END";
            return this.ExecuteCommand(sql, new { newid = newid, entity_id = entity_id, upload_key = upload_key });
        }

        /// <summary>
        /// 根据主体ID查询主体简称
        /// </summary>
        /// <returns></returns>
        public string GetEntityByEntityID(string entity_id)
        {
            string sql = "select string_agg(entity,',') from fnp_entity where entity_id in (@entity_id);";
            return (string)this.NpgsqlSearchBySingleValue(sql, new { entity_id = entity_id });
        }

        /// <summary>
        /// 根据权限ID查询权限名
        /// </summary>
        /// <returns></returns>
        public string GetRoleNameByRID(int? r_id)
        {
            string sql = "select r_name from p_role where r_id = @r_id";
            return (string)this.NpgsqlSearchBySingleValue(sql, new { r_id = r_id });
        }

        /// <summary>
        /// 根据文件ID查询文件名
        /// </summary>
        /// <returns></returns>
        public string getFileName(int? file_id)
        {
            string sql = @"select file_name from sys_upload_file where fileid = @file_id";
            return (string)this.NpgsqlSearchBySingleValue(sql, new { file_id = file_id });
        }

        /// <summary>
        /// 根据公告ID查詢關聯文件
        /// </summary>
        /// <returns></returns>
        internal SysUploadFile FindFileByFileId(int newid)
        {
            string sql = @"select suf.* from sys_upload_file suf join (
                                select Max(fileid) as fileid  ,upload_key  from sys_upload_file where upload_type = 1
                                GROUP by upload_key) msuf
                                on suf.fileid = msuf.fileid and suf.upload_key = msuf.upload_key
                                where suf.upload_key = @newid";
            return this.NpgsqlSearchBySingle<SysUploadFile>(sql, new { newid = newid.ToString() });
        }
    }
}
