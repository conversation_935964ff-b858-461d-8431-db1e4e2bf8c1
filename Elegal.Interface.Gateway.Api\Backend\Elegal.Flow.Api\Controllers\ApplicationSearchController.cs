﻿using Elegal.Flow.Api.Services.ApplicationSearch;
using Elegal.Interface.Api.Common.Control.Filter;
using Elegal.Interface.Api.Common.Control.Web;
using Elegal.Interface.Api.Common.FuncHelper;
using Elegal.Interface.Api.Common.FuncService;
using Elegal.Interface.Api.Common.GetLogCommon;
using Elegal.Interface.Api.Common.Model;
using Elegal.Interface.Api.Common.Model.ParaModel.FlowApi.ApplicationSearch;
using Elegal.Interface.Api.Common.Model.ResultModel;
using Elegal.Interface.ApiData.Service.FuncService;
using Elegal.Interface.ApiData.Service.Model.QueryCondition;
using Elegal.Orm.Dtos;
using Microsoft.AspNetCore.Mvc;
using System.Text;
using Elegal.Interface.Api.Common.Control;
using Elegal.Interface.Api.Common.Model.SqlSugarModels;
using Elegal.Interface.Api.Common.Services;

namespace Elegal.Flow.Api.Controllers
{
    /// <summary>
    /// 資料查詢 -> 申請單查詢
    /// </summary>
    [Route("[controller]")]
    [ApiController]
    public class ApplicationSearchController(ApplicationSearchRefactorService applicationSearchRefactorService, ILogService logService) : BaseController
    {
        
        protected readonly ILogService _logService = logService;
        
        #region 根據案件狀態查詢不同的主體
        /// <summary>
        /// 根據案件狀態查詢不同的主體
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("GetFnpEntityByCaseStatus")]
        public async Task<ApiResultModelByObject> GetFnpEntityByCaseStatus(int caseStatus, int menuCode)
        {
            ApiResultModelByObject apiResult = new ApiResultModelByObject();
            apiResult.listData = (await applicationSearchRefactorService.GetFnpEntityByCaseStatus(caseStatus, menuCode, MvcContext.UserInfo.current_emp, MvcContext.UserInfo.current_roles)).DistinctBy(s => s.EntityId);
            apiResult.rtnSuccess = true;
            return apiResult;
        }
        #endregion

        #region 獲取我方主體(ACD)
        /// <summary>
        /// 獲取我方主體(ACD)
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("GetFnpEntityByOtherACD")]
        public async Task<ApiResultModelByObject> GetFnpEntityByOtherACD()
        {
            ApiResultModelByObject apiResult = new ApiResultModelByObject();
            apiResult.listData = ApplicationSearchService.GetFnpEntityByOtherACD().DistinctBy(s => s.EntityId);
            apiResult.rtnSuccess = true;
            return await Task.FromResult<ApiResultModelByObject>(apiResult);
        }
        #endregion

        #region 獲取檢視/特殊主體權限(E/F)
        /// <summary>
        /// 獲取檢視/特殊主體權限(E/F)
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("GetFnpEntityByOtherEF")]
        public async Task<ApiResultModelByObject> GetFnpEntityByOtherEF()
        {
            ApiResultModelByObject apiResult = new ApiResultModelByObject();
            apiResult.listData = ApplicationSearchService.GetFnpEntityByOtherEF().DistinctBy(s => s.EntityId);
            apiResult.rtnSuccess = true;
            return await Task.FromResult<ApiResultModelByObject>(apiResult);
        }
        #endregion

        #region 獲取關企建檔掛帳部門資訊
        /// <summary>
        /// 獲取關企建檔掛帳部門資訊
        /// </summary>
        /// <param name="deptid"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetAccountDeptID")]
        public async Task<ApiResultModelByObject> GetAccountDeptID(string deptid = "")
        {
            ApiResultModelByObject apiResult = new ApiResultModelByObject();

            apiResult.listData = ApplicationSearchService.GetAccountDeptID(deptid);
            apiResult.rtnSuccess = true;

            return await Task.FromResult(apiResult);
        }
        #endregion

        #region 獲取關聯合約編號
        /// <summary>
        /// 獲取關聯合約編號
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("GetGroupContractNumber")]
        public async Task<ApiResultModelByObject> GetGroupContractNumber()
        {
            ApiResultModelByObject apiResult = new ApiResultModelByObject();
            apiResult.listData = await applicationSearchRefactorService.GetGroupContractNumber();
            apiResult.rtnSuccess = true;
            return apiResult;
        }
        #endregion

        #region 目前關卡

        #region 關卡並集(合約申請，資料建檔，關企建檔)
        /// <summary>
        /// 關卡並集(合約申請，資料建檔，關企建檔)
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("GetFlowStepByParaCode")]
        public async Task<ApiResultModelByObject> GetFlowStepByParaCode()
        {
            ApiResultModelByObject apiResult = new ApiResultModelByObject();
            apiResult.listData = await applicationSearchRefactorService.GetFlowStepByParaCode(MvcContext.UserInfo.logging_locale);
            apiResult.rtnSuccess = true;
            return apiResult;
        }
        #endregion

        #region 關卡並集(其他申請)
        /// <summary>
        /// 關卡並集(其他申請)
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("GetOtherFlowStepByParaCode")]
        public async Task<ApiResultModelByObject> GetOtherFlowStepByParaCode()
        {
            ApiResultModelByObject apiResult = new ApiResultModelByObject();
            apiResult.listData = ApplicationSearchService.GetOtherFlowStepByParaCode(MvcContext.UserInfo.logging_locale);
            apiResult.rtnSuccess = true;
            return await Task.FromResult<ApiResultModelByObject>(apiResult);
        }
        #endregion

        #region 關卡並集(所有申請)
        /// <summary>
        /// 關卡並集(所有申請)
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("GetAllFlowStepByParaCode")]
        public async Task<ApiResultModelByObject> GetAllFlowStepByParaCode()
        {
            ApiResultModelByObject apiResult = new ApiResultModelByObject();
            apiResult.listData = ApplicationSearchService.GetAllFlowStepByParaCode(MvcContext.UserInfo.logging_locale);
            apiResult.rtnSuccess = true;
            return await Task.FromResult<ApiResultModelByObject>(apiResult);
        }
        #endregion

        #endregion

        #region 案件申請查詢
        /// <summary>
        /// 案件申請查詢
        /// </summary>
        /// <param name="asp">查詢條件</param>
        /// <returns></returns>
        [HttpPost]
        [Route("ApplicationSearchData")]
        public async Task<ApiResultModelByObject> ApplicationSearchData([FromBody] ApplicationSearchParaModel asp)
        {
            ApiResultModelByObject apiResult = new ApiResultModelByObject();
            //预先存储簽約日、生效日、到期日数据，在后续的查询过程中到期日数据会被修改
            var aspExpType = asp.expType.ToList();
            var aspSignType = asp.signType.ToList();
            var aspEffType = asp.effType.ToList();

            //int totalCount = 0;
            //apiResult.listData = ApplicationSearchService.GetApplicationData(asp, MvcContext.UserInfo.current_emp, MvcContext.UserInfo.current_roles, out totalCount, MvcContext.UserInfo.logging_locale);
            //apiResult.totalCount = totalCount;
            var userInfoModel = AppStaticServices.GetUserInfoFromHttpContext(this.HttpContext);
            //小綜合查詢改為使用存儲過程讀取數據  ISSUE：468 by SpringJiang 20250708
            var (totalCount, listData) = await applicationSearchRefactorService.GetApplicationFormInquiry(asp, userInfoModel.current_emp, userInfoModel.current_roles, userInfoModel.logging_locale);
            apiResult.listData = listData;
            apiResult.totalCount = totalCount;
            apiResult.rtnSuccess = true;

            #region 記錄日誌

            _ = _logService.InitLogRecordAsync(asp, (log, serviceProvider) =>
            {
                StringBuilder stringBuilder = new StringBuilder();
                var paracodes = new string[]
                {
                    "applicationState", "contractType", "targetType", "egType", "archiveStatus", "amountStatus",
                    "currency", "taxType", "accountType", "confidentStatus", "acknowledgeType", "dateOptions",
                    "effDateOptions"
                };
                var listData = SqlSugarHelper.Db.Queryable<sys_parameters>()
                    .Where(x => x.lang_type == userInfoModel.logging_locale && paracodes.Contains(x.para_code))
                    .OrderBy(x => x.sort_order).ToList();

                var flowStepList =
                    applicationSearchRefactorService.GetFlowStepByParaCode(userInfoModel.logging_locale).GetAwaiter()
                        .GetResult();

                List<DropDownListModel> flowStepListData = new List<DropDownListModel>();
                foreach (var item in flowStepList)
                {
                    var keys = item.paraKey.Split(',');
                    foreach (var key in keys)
                    {
                        DropDownListModel dropDownListModel = new DropDownListModel()
                        {
                            paraKey = key,
                            paraValue = item.paraValue
                        };
                        flowStepListData.Add(dropDownListModel);
                    }
                }

                var entityData = applicationSearchRefactorService
                    .GetFnpEntityByCaseStatus(asp.caseStatus, asp.menuCode, userInfoModel.current_emp,
                        MvcContext.UserInfo.current_roles).GetAwaiter().GetResult().DistinctBy(s => s.EntityId)
                    .Select(x => new { x.EntityId, x.Entity });


                //签约日、生效日、到期日类型拆分
                var signType1 = listData
                    .Where(x => x.para_code == "dateOptions" &&
                                (new List<string>() { "02", "05" }).Contains(x.func_code))
                    .Select(x => new { x.func_code, x.fun_name }).ToList(); //签约日有日期
                var signType2 = listData
                    .Where(x => x.para_code == "dateOptions" &&
                                !(new List<string>() { "02", "04", "05" }).Contains(x.func_code))
                    .Select(x => new { x.func_code, x.fun_name }).ToList(); //签约日无日期
                var effType1 = listData
                    .Where(x => x.para_code == "effDateOptions" &&
                                (new List<string>() { "02", "04" }).Contains(x.func_code))
                    .Select(x => new { x.func_code, x.fun_name }).ToList(); //生效日有日期
                var effType2 = listData
                    .Where(x => x.para_code == "effDateOptions" &&
                                !(new List<string>() { "02", "04" }).Contains(x.func_code))
                    .Select(x => new { x.func_code, x.fun_name }).ToList(); //生效日无日期
                var expType1 = listData
                    .Where(x => x.para_code == "dateOptions" &&
                                (new List<string>() { "02", "05" }).Contains(x.func_code))
                    .Select(x => new { x.func_code, x.fun_name }).ToList(); //到期日有日期
                var expType2 = listData
                    .Where(x => x.para_code == "dateOptions" &&
                                !(new List<string>() { "02", "05" }).Contains(x.func_code))
                    .Select(x => new { x.func_code, x.fun_name }).ToList(); //到期日无日期
                stringBuilder.GetLogString(asp.caseStatus, "ApplicationSearch_caseStatus")
                    .GetLogString("ApplicationSearch_applyType",
                        SysParametersService.GetApplicationType(MvcContext.UserInfo.logging_locale)
                            .Where(x => asp.applyType.Contains(x.paraKey)).Select(x => x.paraValue).ToList())
                    .GetLogString("ApplicationSearch_applicationState",
                        listData.Where(x =>
                                x.para_code == "applicationState" && asp.applicationState.Contains(x.func_code))
                            .Select(x => x.fun_name).ToList())
                    .GetLogString(asp.applyNumber, "ApplicationSearch_applyNumber")
                    .GetLogString(
                        asp.applyTimeStart.ConvertDateByTimeZoneByUtc(MvcContext.UserInfo.time_zone)
                            ?.ToString("yyyy/MM/dd") ?? string.Empty, "ApplicationSearch_applyTimeStart")
                    .GetLogString(
                        asp.applyTimeEnd.ConvertDateByTimeZoneByUtc(MvcContext.UserInfo.time_zone)
                            ?.ToString("yyyy/MM/dd") ?? string.Empty, "ApplicationSearch_applyTimeEnd")
                    .GetLogString("ApplicationSearch_entityID",
                        entityData.Where(x => asp.entityID.Contains(x.EntityId)).Select(x => x.Entity).ToList())
                    .GetLogString(asp.otherParty, "ApplicationSearch_otherParty")
                    .GetLogString(asp.contractName, "ApplicationSearch_contractName")
                    .GetLogString(GetLogCommon.GetUserName(asp.empData), "ApplicationSearch_empData")
                    .GetLogString(asp.deptID, "ApplicationSearch_deptID")
                    .GetLogString("ApplicationSearch_contractType",
                        listData.Where(x => x.para_code == "contractType" && asp.contractType.Contains(x.func_code))
                            .Select(x => x.fun_name).ToList())
                    .GetLogString("ApplicationSearch_contractObj",
                        listData.Where(x => x.para_code == "targetType" && asp.contractObj.Contains(x.func_code))
                            .Select(x => x.fun_name).ToList())
                    .GetLogString("ApplicationSearch_signType1",
                        signType1.Where(x => aspSignType.Contains(x.func_code)).Select(x => x.fun_name).ToList())
                    .GetLogString(asp.signDateStart, "ApplicationSearch_signDateStart")
                    .GetLogString(asp.signDateEnd, "ApplicationSearch_signDateEnd")
                    .GetLogString("ApplicationSearch_signType2",
                        signType2.Where(x => aspSignType.Contains(x.func_code)).Select(x => x.fun_name).ToList())
                    .GetLogString(asp.confirmSignDateStart, "ApplicationSearch_comfirmSignDateStart")
                    .GetLogString(asp.confirmSignDateEnd, "ApplicationSearch_comfirmSignDateEnd")
                    .GetLogString("ApplicationSearch_effType1",
                        effType1.Where(x => aspEffType.Contains(x.func_code)).Select(x => x.fun_name).ToList())
                    .GetLogString(asp.effDateStart, "ApplicationSearch_effDateStart")
                    .GetLogString(asp.effDateEnd, "ApplicationSearch_effDateEnd")
                    .GetLogString("ApplicationSearch_effType2",
                        effType2.Where(x => aspEffType.Contains(x.func_code)).Select(x => x.fun_name).ToList())
                    .GetLogString(asp.confirmEffDateStart, "ApplicationSearch_confirmEffDataStart")
                    .GetLogString(asp.confirmEffDateEnd, "ApplicationSearch_confirmEffDataEnd");
                // 有到期日分為延展和不延展
                if (aspExpType.Contains("02"))
                {
                    List<string> strings = new List<string>();
                    //對2做特殊處理
                    //判斷是否有延展
                    if (asp.hasExpExtend == 1) strings.Add("有延展");
                    if (asp.hasExpExtend == 0) strings.Add("無延展");
                    if (asp.hasExpExtend == -1)
                    {
                        strings.Add("有延展");
                        strings.Add("無延展");
                    }

                    strings.AddRange(expType1.Where(x => aspExpType.Contains(x.func_code) && x.func_code != "02")
                        .Select(x => x.fun_name).ToList());
                    stringBuilder.GetLogString("ApplicationSearch_expType1", strings)
                        .GetLogString(asp.expDateStart, "ApplicationSearch_expDateStart")
                        .GetLogString(asp.expDateEnd, "ApplicationSearch_expDateEnd")
                        .GetLogString("ApplicationSearch_expType2",
                            expType2.Where(x => aspExpType.Contains(x.func_code)).Select(x => x.fun_name).ToList());
                }
                else
                {
                    stringBuilder.GetLogString("ApplicationSearch_expType1",
                            expType1.Where(x => aspExpType.Contains(x.func_code)).Select(x => x.fun_name).ToList())
                        .GetLogString(asp.expDateStart, "ApplicationSearch_expDateStart")
                        .GetLogString(asp.expDateEnd, "ApplicationSearch_expDateEnd")
                        .GetLogString("ApplicationSearch_expType2",
                            expType2.Where(x => aspExpType.Contains(x.func_code)).Select(x => x.fun_name).ToList());
                }

                stringBuilder.GetLogString(asp.expExtDateStart, "ApplicationSearch_expExtDateStart")
                    .GetLogString(asp.expExtDateEnd, "ApplicationSearch_expExtDateEnd");
                //處理確認到期日
                if (!string.IsNullOrEmpty(asp.confirmExpDateEnd) || !string.IsNullOrEmpty(asp.confirmExpDateStart))
                {
                    List<string> strings = new List<string>();
                    if (asp.confirmHasExpExtend == -1)
                    {
                        strings.Add("有延展");
                        strings.Add("無延展");
                    }

                    if (asp.confirmHasExpExtend == 0) strings.Add("無延展");
                    if (asp.confirmHasExpExtend == 1) strings.Add("有延展");
                    stringBuilder.GetLogString("ApplicationSearch_confirmHasExpExtend", strings)
                        .GetLogString(asp.confirmExpDateStart, "ApplicationSearch_confirmExpDateStart")
                        .GetLogString(asp.confirmExpDateEnd, "ApplicationSearch_confirmExpDateEnd");
                }

                stringBuilder
                    .GetLogString(
                        asp.originArchiveDateStart.ConvertDateByTimeZoneByUtc(MvcContext.UserInfo.time_zone)
                            ?.ToString("yyyy/MM/dd") ?? string.Empty, "ApplicationSearch_originArchiveDateStart")
                    .GetLogString(
                        asp.originArchiveDateEnd.ConvertDateByTimeZoneByUtc(MvcContext.UserInfo.time_zone)
                            ?.ToString("yyyy/MM/dd") ?? string.Empty, "ApplicationSearch_originArchiveDateEnd")
                    .GetLogString(
                        asp.approvedDateStart.ConvertDateByTimeZoneByUtc(MvcContext.UserInfo.time_zone)
                            ?.ToString("yyyy/MM/dd") ?? string.Empty, "ApplicationSearch_approvedDateStart")
                    .GetLogString(
                        asp.approvedDateEnd.ConvertDateByTimeZoneByUtc(MvcContext.UserInfo.time_zone)
                            ?.ToString("yyyy/MM/dd") ?? string.Empty, "ApplicationSearch_approvedDateEnd")
                    .GetLogString(
                        asp.closedDateStart.ConvertDateByTimeZoneByUtc(MvcContext.UserInfo.time_zone)
                            ?.ToString("yyyy/MM/dd") ?? string.Empty, "ApplicationSearch_closedDateStart")
                    .GetLogString(
                        asp.closedDateEnd.ConvertDateByTimeZoneByUtc(MvcContext.UserInfo.time_zone)
                            ?.ToString("yyyy/MM/dd") ?? string.Empty, "ApplicationSearch_closedDateEnd")
                    .GetLogString(asp.contractNumber, "ApplicationSearch_contractNumber")
                    .GetLogString(asp.mainContractNumber, "ApplicationSearch_mainContractNumber")
                    .GetLogString(asp.localContractNumber, "ApplicationSearch_localContractNumber")
                    .GetLogString(asp.othePrartyNumber, "ApplicationSearch_othePrartyNumber")
                    .GetLogString(asp.earlyCeaseDateStrat, "ApplicationSearch_earlyCeaseDateStrat")
                    .GetLogString(asp.earlyCeaseDateEnd, "ApplicationSearch_earlyCeaseDateEnd")
                    .GetLogString("ApplicationSearch_endorsement",
                        listData.Where(x => x.para_code == "egType" && asp.endorsement.Contains(x.func_code))
                            .Select(x => x.fun_name).ToList())
                    .GetLogString("ApplicationSearch_originArchiveType",
                        listData.Where(x =>
                                x.para_code == "archiveStatus" && asp.originArchiveType.Contains(x.func_code))
                            .Select(x => x.fun_name).ToList())
                    .GetLogString("ApplicationSearch_isHavingMoney",
                        listData.Where(x => x.para_code == "amountStatus" && asp.isHavingMoney.Contains(x.func_code))
                            .Select(x => x.fun_name).ToList())
                    .GetLogString("ApplicationSearch_currency",
                        listData.Where(x => x.para_code == "currency" && asp.currency.Contains(x.func_code))
                            .Select(x => x.fun_name).ToList())
                    .GetLogString("ApplicationSearch_taxesType",
                        listData.Where(x => x.para_code == "taxType" && asp.taxesType.Contains(x.func_code))
                            .Select(x => x.fun_name).ToList())
                    .GetLogString("ApplicationSearch_accountType",
                        listData.Where(x => x.para_code == "accountType" && asp.accountType.Contains(x.func_code))
                            .Select(x => x.fun_name).ToList())
                    .GetLogString(asp.accountDeptid, "ApplicationSearch_accountDeptid")
                    .GetLogString(asp.codeName, "ApplicationSearch_codeName")
                    .GetLogString("ApplicationSearch_confidenLevel",
                        listData.Where(x => x.para_code == "confidentStatus" && asp.confidenLevel.Contains(x.func_code))
                            .Select(x => x.fun_name).ToList())
                    .GetLogString("ApplicationSearch_acknowledgeType",
                        listData.Where(x =>
                                x.para_code == "acknowledgeType" && asp.acknowledgeType.Contains(x.func_code))
                            .Select(x => x.fun_name).ToList())
                    .GetLogString("ApplicationSearch_currentLevels",
                        flowStepListData.Where(x => asp.currentLevels.Contains(Convert.ToInt32(x.paraKey)))
                            .Select(x => x.paraValue).Distinct().ToList())
                    .GetLogString(GetLogCommon.GetUserName(asp.currentSigner), "ApplicationSearch_currentSigner")
                    .GetLogString(GetLogCommon.GetUserName(asp.signerData), "ApplicationSearch_signerData")
                    .GetLogString(asp.signerDept, "ApplicationSearch_signerDept")
                    ;
                log.detail = stringBuilder.ToString();
            });
            
            #endregion

            return apiResult;
        }
        #endregion

        #region 案件申請查詢匯出
        /// <summary>
        /// 案件申請查詢匯出
        /// </summary>
        /// <param name="asp">查詢條件</param>
        /// <returns></returns>
        [HttpPost]
        [Route("ApplicationSearchDataExport")]
        public async Task<FileContentResult> ApplicationSearchDataExport([FromBody] ApplicationExportParaModel asp)
        {
            //预先存储簽約日、生效日、到期日数据，在后续的查询过程中到期日数据会被修改
            var aspExpType = asp.expType.ToList();
            var aspSignType = asp.signType.ToList();
            var aspEffType = asp.effType.ToList();
            var userInfoModel = AppStaticServices.GetUserInfoFromHttpContext(this.HttpContext);
            var excelBytes = await applicationSearchRefactorService.GetApplicationDataExport(asp,
                userInfoModel.current_emp, userInfoModel.current_roles, userInfoModel.logging_locale);

            #region 記錄日誌

            InitLogRecord(asp, log =>
            {
                StringBuilder stringBuilder = new StringBuilder();
                //獲取下拉框選項
                SysParametersQueryCondition sysParametersQueryCondition = new SysParametersQueryCondition()
                {
                    LangType = MvcContext.UserInfo.logging_locale,
                    //ParaCode = new string[] { "applicationState" },
                    SearchItemGroup = new SearchItemGroup()
                    {
                        Items = new List<SearchItem>()
                        {
                            new SearchItem()
                            {
                                Field = "ParaCode",
                                Values = new string[]
                                {
                                    "applicationState", "contractType", "targetType", "egType", "archiveStatus",
                                    "amountStatus", "currency", "taxType", "accountType", "confidentStatus",
                                    "acknowledgeType", "dateOptions", "effDateOptions"
                                },
                                Compare = CompareOperator.ARRAYIN
                            }
                        }
                    },
                    OrderBys = new List<Orm.Dtos.OrderByParam>()
                    {
                        new Orm.Dtos.OrderByParam() { Field = "SortOrder", Order = Orm.Dtos.OrderBy.ASC }
                    }
                };
                var listData = SysParametersDataService.Query(sysParametersQueryCondition);
                List<DropDownListModel> flowStepListData = new List<DropDownListModel>();
                foreach (var item in ApplicationSearchService.GetFlowStepByParaCode(MvcContext.UserInfo.logging_locale))
                {
                    var keys = item.paraKey.Split(',');
                    foreach (var key in keys)
                    {
                        DropDownListModel dropDownListModel = new DropDownListModel()
                        {
                            paraKey = key,
                            paraValue = item.paraValue
                        };
                        flowStepListData.Add(dropDownListModel);
                    }
                }

                var entityData = ApplicationSearchService
                    .GetFnpEntityByCaseStatus(asp.caseStatus, asp.menuCode, MvcContext.UserInfo.current_emp,
                        MvcContext.UserInfo.current_roles).DistinctBy(s => s.EntityId)
                    .Select(x => new { x.EntityId, x.Entity });
                //签约日、生效日、到期日类型拆分
                var signType1 = listData
                    .Where(x => x.ParaCode == "dateOptions" && (new List<string>() { "02", "05" }).Contains(x.FuncCode))
                    .Select(x => new { x.FuncCode, x.FunName }).ToList(); //签约日有日期
                var signType2 = listData
                    .Where(x => x.ParaCode == "dateOptions" &&
                                !(new List<string>() { "02", "04", "05" }).Contains(x.FuncCode))
                    .Select(x => new { x.FuncCode, x.FunName }).ToList(); //签约日无日期
                var effType1 = listData
                    .Where(x => x.ParaCode == "effDateOptions" &&
                                (new List<string>() { "02", "04" }).Contains(x.FuncCode))
                    .Select(x => new { x.FuncCode, x.FunName }).ToList(); //生效日有日期
                var effType2 = listData
                    .Where(x => x.ParaCode == "effDateOptions" &&
                                !(new List<string>() { "02", "04" }).Contains(x.FuncCode))
                    .Select(x => new { x.FuncCode, x.FunName }).ToList(); //生效日无日期
                var expType1 = listData
                    .Where(x => x.ParaCode == "dateOptions" && (new List<string>() { "02", "05" }).Contains(x.FuncCode))
                    .Select(x => new { x.FuncCode, x.FunName }).ToList(); //到期日有日期
                var expType2 = listData
                    .Where(x => x.ParaCode == "dateOptions" &&
                                !(new List<string>() { "02", "05" }).Contains(x.FuncCode))
                    .Select(x => new { x.FuncCode, x.FunName }).ToList(); //到期日无日期
                stringBuilder.GetLogString(asp.caseStatus, "ApplicationSearch_caseStatus")
                    .GetLogString("ApplicationSearch_applyType",
                        SysParametersService.GetApplicationType(MvcContext.UserInfo.logging_locale)
                            .Where(x => asp.applyType.Contains(x.paraKey)).Select(x => x.paraValue).ToList())
                    .GetLogString("ApplicationSearch_applicationState",
                        listData.Where(x =>
                                x.ParaCode == "applicationState" && asp.applicationState.Contains(x.FuncCode))
                            .Select(x => x.FunName).ToList())
                    .GetLogString(asp.applyNumber, "ApplicationSearch_applyNumber")
                    .GetLogString(
                        asp.applyTimeStart.ConvertDateByTimeZoneByUtc(MvcContext.UserInfo.time_zone)
                            ?.ToString("yyyy/MM/dd") ?? string.Empty, "ApplicationSearch_applyTimeStart")
                    .GetLogString(
                        asp.applyTimeEnd.ConvertDateByTimeZoneByUtc(MvcContext.UserInfo.time_zone)
                            ?.ToString("yyyy/MM/dd") ?? string.Empty, "ApplicationSearch_applyTimeEnd")
                    .GetLogString("ApplicationSearch_entityID",
                        entityData.Where(x => asp.entityID.Contains(x.EntityId)).Select(x => x.Entity).ToList())
                    .GetLogString(asp.otherParty, "ApplicationSearch_otherParty")
                    .GetLogString(asp.contractName, "ApplicationSearch_contractName")
                    .GetLogString(GetLogCommon.GetUserName(asp.empData), "ApplicationSearch_empData")
                    .GetLogString(asp.deptID, "ApplicationSearch_deptID")
                    .GetLogString("ApplicationSearch_contractType",
                        listData.Where(x => x.ParaCode == "contractType" && asp.contractType.Contains(x.FuncCode))
                            .Select(x => x.FunName).ToList())
                    .GetLogString("ApplicationSearch_contractObj",
                        listData.Where(x => x.ParaCode == "targetType" && asp.contractObj.Contains(x.FuncCode))
                            .Select(x => x.FunName).ToList())
                    .GetLogString("ApplicationSearch_signType1",
                        signType1.Where(x => aspSignType.Contains(x.FuncCode)).Select(x => x.FunName).ToList())
                    .GetLogString(asp.signDateStart, "ApplicationSearch_signDateStart")
                    .GetLogString(asp.signDateEnd, "ApplicationSearch_signDateEnd")
                    .GetLogString("ApplicationSearch_signType2",
                        signType2.Where(x => aspSignType.Contains(x.FuncCode)).Select(x => x.FunName).ToList())
                    .GetLogString(asp.confirmSignDateStart, "ApplicationSearch_comfirmSignDateStart")
                    .GetLogString(asp.confirmSignDateEnd, "ApplicationSearch_comfirmSignDateEnd")
                    .GetLogString("ApplicationSearch_effType1",
                        effType1.Where(x => aspEffType.Contains(x.FuncCode)).Select(x => x.FunName).ToList())
                    .GetLogString(asp.effDateStart, "ApplicationSearch_effDateStart")
                    .GetLogString(asp.effDateEnd, "ApplicationSearch_effDateEnd")
                    .GetLogString("ApplicationSearch_effType2",
                        effType2.Where(x => aspEffType.Contains(x.FuncCode)).Select(x => x.FunName).ToList())
                    .GetLogString(asp.confirmEffDateStart, "ApplicationSearch_confirmEffDataStart")
                    .GetLogString(asp.confirmEffDateEnd, "ApplicationSearch_confirmEffDataEnd");
                // 有到期日分為延展和不延展
                if (aspExpType.Contains("02"))
                {
                    List<string> strings = new List<string>();
                    //對2做特殊處理
                    //判斷是否有延展
                    if (asp.hasExpExtend == 1) strings.Add("有延展");
                    if (asp.hasExpExtend == 0) strings.Add("無延展");
                    if (asp.hasExpExtend == -1)
                    {
                        strings.Add("有延展");
                        strings.Add("無延展");
                    }

                    strings.AddRange(expType1.Where(x => aspExpType.Contains(x.FuncCode) && x.FuncCode != "02")
                        .Select(x => x.FunName).ToList());
                    stringBuilder.GetLogString("ApplicationSearch_expType1", strings)
                        .GetLogString(asp.expDateStart, "ApplicationSearch_expDateStart")
                        .GetLogString(asp.expDateEnd, "ApplicationSearch_expDateEnd")
                        .GetLogString("ApplicationSearch_expType2",
                            expType2.Where(x => aspExpType.Contains(x.FuncCode)).Select(x => x.FunName).ToList());
                }
                else
                {
                    stringBuilder.GetLogString("ApplicationSearch_expType1",
                            expType1.Where(x => aspExpType.Contains(x.FuncCode)).Select(x => x.FunName).ToList())
                        .GetLogString(asp.expDateStart, "ApplicationSearch_expDateStart")
                        .GetLogString(asp.expDateEnd, "ApplicationSearch_expDateEnd")
                        .GetLogString("ApplicationSearch_expType2",
                            expType2.Where(x => aspExpType.Contains(x.FuncCode)).Select(x => x.FunName).ToList());
                }

                stringBuilder.GetLogString(asp.expExtDateStart, "ApplicationSearch_expExtDateStart")
                    .GetLogString(asp.expExtDateEnd, "ApplicationSearch_expExtDateEnd");
                //處理確認到期日
                if (!string.IsNullOrEmpty(asp.confirmExpDateEnd) || !string.IsNullOrEmpty(asp.confirmExpDateStart))
                {
                    List<string> strings = new List<string>();
                    if (asp.confirmHasExpExtend == -1)
                    {
                        strings.Add("有延展");
                        strings.Add("無延展");
                    }

                    if (asp.confirmHasExpExtend == 0) strings.Add("無延展");
                    if (asp.confirmHasExpExtend == 1) strings.Add("有延展");
                    stringBuilder.GetLogString("ApplicationSearch_confirmHasExpExtend", strings)
                        .GetLogString(asp.confirmExpDateStart, "ApplicationSearch_confirmExpDateStart")
                        .GetLogString(asp.confirmExpDateEnd, "ApplicationSearch_confirmExpDateEnd");
                }

                stringBuilder
                    .GetLogString(
                        asp.originArchiveDateStart.ConvertDateByTimeZoneByUtc(MvcContext.UserInfo.time_zone)
                            ?.ToString("yyyy/MM/dd") ?? string.Empty, "ApplicationSearch_originArchiveDateStart")
                    .GetLogString(
                        asp.originArchiveDateEnd.ConvertDateByTimeZoneByUtc(MvcContext.UserInfo.time_zone)
                            ?.ToString("yyyy/MM/dd") ?? string.Empty, "ApplicationSearch_originArchiveDateEnd")
                    .GetLogString(
                        asp.approvedDateStart.ConvertDateByTimeZoneByUtc(MvcContext.UserInfo.time_zone)
                            ?.ToString("yyyy/MM/dd") ?? string.Empty, "ApplicationSearch_approvedDateStart")
                    .GetLogString(
                        asp.approvedDateEnd.ConvertDateByTimeZoneByUtc(MvcContext.UserInfo.time_zone)
                            ?.ToString("yyyy/MM/dd") ?? string.Empty, "ApplicationSearch_approvedDateEnd")
                    .GetLogString(
                        asp.closedDateStart.ConvertDateByTimeZoneByUtc(MvcContext.UserInfo.time_zone)
                            ?.ToString("yyyy/MM/dd") ?? string.Empty, "ApplicationSearch_closedDateStart")
                    .GetLogString(
                        asp.closedDateEnd.ConvertDateByTimeZoneByUtc(MvcContext.UserInfo.time_zone)
                            ?.ToString("yyyy/MM/dd") ?? string.Empty, "ApplicationSearch_closedDateEnd")
                    .GetLogString(asp.contractNumber, "ApplicationSearch_contractNumber")
                    .GetLogString(asp.mainContractNumber, "ApplicationSearch_mainContractNumber")
                    .GetLogString(asp.localContractNumber, "ApplicationSearch_localContractNumber")
                    .GetLogString(asp.othePrartyNumber, "ApplicationSearch_othePrartyNumber")
                    .GetLogString(asp.earlyCeaseDateStrat, "ApplicationSearch_earlyCeaseDateStrat")
                    .GetLogString(asp.earlyCeaseDateEnd, "ApplicationSearch_earlyCeaseDateEnd")
                    .GetLogString("ApplicationSearch_endorsement",
                        listData.Where(x => x.ParaCode == "egType" && asp.endorsement.Contains(x.FuncCode))
                            .Select(x => x.FunName).ToList())
                    .GetLogString("ApplicationSearch_originArchiveType",
                        listData.Where(x => x.ParaCode == "archiveStatus" && asp.originArchiveType.Contains(x.FuncCode))
                            .Select(x => x.FunName).ToList())
                    .GetLogString("ApplicationSearch_isHavingMoney",
                        listData.Where(x => x.ParaCode == "amountStatus" && asp.isHavingMoney.Contains(x.FuncCode))
                            .Select(x => x.FunName).ToList())
                    .GetLogString("ApplicationSearch_currency",
                        listData.Where(x => x.ParaCode == "currency" && asp.currency.Contains(x.FuncCode))
                            .Select(x => x.FunName).ToList())
                    .GetLogString("ApplicationSearch_taxesType",
                        listData.Where(x => x.ParaCode == "taxType" && asp.taxesType.Contains(x.FuncCode))
                            .Select(x => x.FunName).ToList())
                    .GetLogString("ApplicationSearch_accountType",
                        listData.Where(x => x.ParaCode == "accountType" && asp.accountType.Contains(x.FuncCode))
                            .Select(x => x.FunName).ToList())
                    .GetLogString(asp.accountDeptid, "ApplicationSearch_accountDeptid")
                    .GetLogString(asp.codeName, "ApplicationSearch_codeName")
                    .GetLogString("ApplicationSearch_confidenLevel",
                        listData.Where(x => x.ParaCode == "confidentStatus" && asp.confidenLevel.Contains(x.FuncCode))
                            .Select(x => x.FunName).ToList())
                    .GetLogString("ApplicationSearch_acknowledgeType",
                        listData.Where(x => x.ParaCode == "acknowledgeType" && asp.acknowledgeType.Contains(x.FuncCode))
                            .Select(x => x.FunName).ToList())
                    .GetLogString("ApplicationSearch_currentLevels",
                        flowStepListData.Where(x => asp.currentLevels.Contains(Convert.ToInt32(x.paraKey)))
                            .Select(x => x.paraValue).Distinct().ToList())
                    .GetLogString(GetLogCommon.GetUserName(asp.currentSigner), "ApplicationSearch_currentSigner")
                    .GetLogString(GetLogCommon.GetUserName(asp.signerData), "ApplicationSearch_signerData")
                    .GetLogString(asp.signerDept, "ApplicationSearch_signerDept")
                    ;
                log.Detail = stringBuilder.ToString();
            });

            #endregion

            return File(excelBytes, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                "application_inquiry" + DateTime.UtcNow.ConvertDateByTimeZoneByUtc(userInfoModel.time_zone)
                    .ToString("yyyyMMdd") + ".xlsx");
        }
        #endregion

        #region 其他申請查詢
        /// <summary>
        /// 其他申請查詢
        /// </summary>
        /// <param name="oasp">查詢條件</param>
        /// <returns></returns>
        [HttpPost]
        [Route("OtherApplicationSearchData")]
        public async Task<ApiResultModelByObject> OtherApplicationSearchData([FromBody] OtherApplicationSearchParaModel oasp)
        {
            ApiResultModelByObject apiResult = new ApiResultModelByObject();
            var userInfoModel = AppStaticServices.GetUserInfoFromHttpContext(this.HttpContext);
            // apiResult.listData = ApplicationSearchService.GetOtherApplicationData(oasp, MvcContext.UserInfo.current_emp, MvcContext.UserInfo.current_roles, out totalCount, MvcContext.UserInfo.logging_locale);
            var (totalCount, listData) = await applicationSearchRefactorService.GetOtherApplicationData(oasp, userInfoModel.current_emp, userInfoModel.current_roles, userInfoModel.logging_locale);
            apiResult.listData = listData;
            apiResult.totalCount = totalCount;
            apiResult.rtnSuccess = true;
            #region 記錄日誌
            InitLogRecord(oasp, log =>
            {
                //獲取下拉框選項
                SysParametersQueryCondition sysParametersQueryCondition = new SysParametersQueryCondition()
                {
                    LangType = MvcContext.UserInfo.logging_locale,
                    //ParaCode = new string[] { "applicationState" },
                    SearchItemGroup = new SearchItemGroup()
                    {
                        Items = new List<SearchItem>() { new SearchItem() { Field = "ParaCode", Values = new string[] { "applicationState", "formType_O", "otherDocType", "oSearchOtherFields" }, Compare = CompareOperator.ARRAYIN } }
                    },
                    OrderBys = new List<Orm.Dtos.OrderByParam>()
                    {
                        new Orm.Dtos.OrderByParam(){Field="SortOrder",Order=Orm.Dtos.OrderBy.ASC}
                    }
                };
                var listData = SysParametersDataService.Query(sysParametersQueryCondition);
                List<DropDownListModel> flowStepListData = new List<DropDownListModel>();
                foreach (var item in ApplicationSearchService.GetOtherFlowStepByParaCode(MvcContext.UserInfo.logging_locale))
                {
                    var keys = item.paraKey.Split(',');
                    foreach (var key in keys)
                    {
                        DropDownListModel dropDownListModel = new DropDownListModel()
                        {
                            paraKey = key,
                            paraValue = item.paraValue
                        };
                        flowStepListData.Add(dropDownListModel);
                    }
                }
                var entityData = ApplicationSearchService.GetFnpEntityByCaseStatus(3, oasp.menuCode, MvcContext.UserInfo.current_emp, MvcContext.UserInfo.current_roles).DistinctBy(s => s.EntityId).Select(x => new { x.EntityId, x.Entity }).Concat(ApplicationSearchService.GetFnpEntityByCaseStatus(4, oasp.menuCode, MvcContext.UserInfo.current_emp, MvcContext.UserInfo.current_roles).DistinctBy(s => s.EntityId).Select(x => new { x.EntityId, x.Entity })).DistinctBy(x => x.EntityId);
                StringBuilder stringBuilder = new StringBuilder();
                //if (!string.IsNullOrEmpty(oasp.applyNumber)) stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("OtherApplicationSearch_applyNumber", true)}：{oasp.applyNumber}");
                //if (!string.IsNullOrEmpty(oasp.applyTimeStart.ToString())) stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("OtherApplicationSearch_applyTimeStart", true)}：{oasp.applyTimeStart}");
                //if (!string.IsNullOrEmpty(oasp.applyTimeEnd.ToString())) stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("OtherApplicationSearch_applyTimeEnd", true)}：{oasp.applyTimeEnd}");
                //if (oasp.applicationState.Count>0) stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("OtherApplicationSearch_applicationState", true)}：[{string.Join(",", oasp.applicationState)}]");
                //if (oasp.formType.Count > 0) stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("OtherApplicationSearch_formType", true)}：[{string.Join(",", oasp.formType)}]");
                //if (oasp.empData.Count>0) stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("OtherApplicationSearch_empData", true)}：[{string.Join(",", oasp.empData)}]");
                //if (oasp.deptID.Count>0) stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("OtherApplicationSearch_deptID", true)}：[{string.Join(",", oasp.deptID)}]");
                //if (oasp.legalAdmin.Count>0) stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("OtherApplicationSearch_legalAdmin", true)}：[{string.Join(",", oasp.legalAdmin)}]");
                //if (oasp.exposeEmplid.Count > 0) stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("OtherApplicationSearch_exposeEmplid", true)}：[{string.Join(",", oasp.exposeEmplid)}]");
                //if (!string.IsNullOrEmpty(oasp.exposeOtherEmplid.ToString())) stringBuilder.AppendLine($"{ActionFilter.GetMultilingualValue("OtherApplicationSearch_exposeOtherEmplid", true)}：{oasp.exposeOtherEmplid}");
                stringBuilder.GetLogString(oasp.applyNumber, "OtherApplicationSearch_applyNumber")
                             .GetLogString(oasp.applyTimeStart.ConvertDateByTimeZoneByUtc(MvcContext.UserInfo.time_zone)?.ToString("yyyy/MM/dd") ?? string.Empty, "OtherApplicationSearch_applyTimeStart")
                             .GetLogString(oasp.applyTimeEnd.ConvertDateByTimeZoneByUtc(MvcContext.UserInfo.time_zone)?.ToString("yyyy/MM/dd") ?? string.Empty, "OtherApplicationSearch_applyTimeEnd")
                             .GetLogString("OtherApplicationSearch_applicationState", listData.Where(x => x.ParaCode == "applicationState" && oasp.applicationState.Contains(x.FuncCode)).Select(x => x.FunName).ToList())
                             .GetLogString("OtherApplicationSearch_formType", listData.Where(x => x.ParaCode == "formType_O" && oasp.formType.Contains(x.FuncCode)).Select(x => x.FunName).ToList())
                             .GetLogString(GetLogCommon.GetUserName(oasp.empData), "OtherApplicationSearch_empData")
                             .GetLogString(oasp.deptID, "OtherApplicationSearch_deptID")
                             .GetLogString(oasp.legalAdmin, "OtherApplicationSearch_legalAdmin")
                             .GetLogString(oasp.exposeEmplid, "OtherApplicationSearch_exposeEmplid")
                             .GetLogString(oasp.exposeOtherEmplid, "OtherApplicationSearch_exposeOtherEmplid")
                             .GetLogString(oasp.isValidityPeriod == 1 ? oasp.isValidityPeriod.ToString() : "", "OtherApplicationSearch_isValidityPeriod")
                             .GetLogString(oasp.actualDateStartBegin.ConvertDateByTimeZoneByUtc(MvcContext.UserInfo.time_zone)?.ToString("yyyy/MM/dd") ?? string.Empty, "OtherApplicationSearch_actualDateStartBegin")
                             .GetLogString(oasp.actualDateStartDone.ConvertDateByTimeZoneByUtc(MvcContext.UserInfo.time_zone)?.ToString("yyyy/MM/dd") ?? string.Empty, "OtherApplicationSearch_actualDateStartDone")
                             .GetLogString(oasp.actualDateEndBegin.ConvertDateByTimeZoneByUtc(MvcContext.UserInfo.time_zone)?.ToString("yyyy/MM/dd") ?? string.Empty, "OtherApplicationSearch_actualDateEndBegin")
                             .GetLogString(oasp.actualDateEndDone.ConvertDateByTimeZoneByUtc(MvcContext.UserInfo.time_zone)?.ToString("yyyy/MM/dd") ?? string.Empty, "OtherApplicationSearch_actualDateEndDone")
                             .GetLogString(oasp.contractNumber, "OtherApplicationSearch_contractNumber")
                             .GetLogString("OtherApplicationSearch_groupEntity", entityData.Where(x => oasp.entityID.Contains(x.EntityId)).Select(x => x.Entity).ToList())
                             .GetLogString(oasp.retrieveApplyNumber, "OtherApplicationSearch_retrieveApplyNumber")
                             .GetLogString("OtherApplicationSearch_viewEntityID", entityData.Where(x => oasp.viewEntityID.Contains(x.EntityId)).Select(x => x.Entity).ToList())
                             .GetLogString(oasp.otherParty, "OtherApplicationSearch_otherParty")
                             .GetLogString("OtherApplicationSearch_docType", listData.Where(x => x.ParaCode == "otherDocType" && oasp.docType.Contains(x.FuncCode)).Select(x => x.FunName).ToList())
                             .GetLogString(oasp.closedDateStart.ConvertDateByTimeZoneByUtc(MvcContext.UserInfo.time_zone)?.ToString("yyyy/MM/dd") ?? string.Empty, "OtherApplicationSearch_closedDateStart")
                             .GetLogString(oasp.closedDateEnd.ConvertDateByTimeZoneByUtc(MvcContext.UserInfo.time_zone)?.ToString("yyyy/MM/dd") ?? string.Empty, "OtherApplicationSearch_closedDateEnd")
                             .GetLogString("OtherApplicationSearch_currentLevels", flowStepListData.Where(x => oasp.currentLevels.Contains(Convert.ToInt32(x.paraKey))).Select(x => x.paraValue).Distinct().ToList())
                             .GetLogString(GetLogCommon.GetUserName(oasp.currentSigner), "OtherApplicationSearch_currentSigner")
                             .GetLogString(GetLogCommon.GetUserName(oasp.signerData), "OtherApplicationSearch_signerData")
                             .GetLogString(oasp.signerDept, "OtherApplicationSearch_signerDept")
                             ;
                log.Detail = stringBuilder.ToString();
            });
            #endregion
            
            return apiResult;
        }
        #endregion
    }
}
