﻿using Elegal.Flow.Api.Services;
using Elegal.Interface.Api.Common.Model.ParaModel.FlowApi;
using Elegal.Interface.Api.Common.Model.ResultModel;
using Microsoft.AspNetCore.Mvc;

namespace Elegal.Flow.Api.Controllers
{
    /// <summary>
    /// 下載記錄日誌服務
    /// </summary>
    [Route("[controller]")]
    [ApiController]
    public class DownloadLogController : ControllerBase
    {
        /// <summary>
        /// 插入下載記錄日誌
        /// </summary>
        /// <param name="downloadLog">數據模型</param>
        /// <returns></returns>
        [HttpPost]
        [Route("Insert")]
        public async Task<ApiResultModelByObject> Insert(DownloadLogModel downloadLog)
        {
            return await Task.FromResult(DownloadLogService.Insert(downloadLog));
        }
    }
}
