﻿using Elegal.Interface.Api.Common.Control.Filter;
using Elegal.Interface.Api.Common.Model.DBModel;
using Elegal.Interface.Api.Common.Model.ParaModel.FlowApi;
using Elegal.Interface.Api.Common.Model.ViewModel.FlowApi;
using Elegal.Interface.Api.Common.Repository;
using Elegal.Orm;
using Elegal.Orm.Dtos;
using System.Text;

namespace Elegal.Flow.Api.Repository.Paper
{
    /// <summary>
    /// 紙本確認Repository
    /// </summary>
    public class PaperOldDataRepository : BaseRepository
    {
        #region 獲取舊資料
        /// <summary>
        /// 獲取舊資料
        /// </summary>
        /// <param name="qry"></param>
        /// <returns></returns>
        public PageResult<PaperOldDataViewModel> GetOldData(qryPaperOldData qry)
        {
            PageResult<PaperOldDataViewModel> result = new PageResult<PaperOldDataViewModel>();
            string sql_uncomfirm = $@"SELECT
                                paper_applica_id as application_id, 
                                un_basic_id,--由已確認改為未確認時，存在paperid
                                pads.apply_number,--申請單號
                                vg.contract_number,--合約編號
                                my_entity_id,
                                vg.contract_name,--合約名稱
                                pud.paper_code,--紙本編號
                                vg.filed_type,--正本歸檔狀態
                                vg.confiden_level,--機密等級
                                COALESCE(pud.modify_time, pud.create_time) AS operate_time,--更新時間
                                COALESCE(ee_modi.name, ee_create.name) AS operate_cuser,--更新人中文名
                                COALESCE(ee_modi.name_a, ee_create.name_a) AS operate_euser--更新人英文名
                            FROM paper_unconfirmed_data AS pud
                            INNER JOIN (SELECT * FROM paper_application_data) AS pads ON pud.paper_applica_id = pads.application_id
                            LEFT JOIN (SELECT * FROM V_GetUnConfirmedApplication) AS vg ON vg.apply_number = pads.apply_number
                            LEFT JOIN (SELECT emplid,name,name_a FROM ps_sub_ee_lgl_vw_a) AS ee_create ON ee_create.emplid = pud.create_user
							LEFT JOIN (SELECT emplid,name,name_a FROM ps_sub_ee_lgl_vw_a) AS ee_modi ON ee_modi.emplid = pud.modify_user
                         UNION
                            SELECT
                                '' AS application_id,
                                0 AS un_basic_id,--完全由歷史單據進入，無任何紙本作業，默認為0
                                ua.apply_number,--申請單號
                                ua.contract_number,--合約編號
                                entity_id as my_entity_id,
                                ua.contract_name,--合約名稱
                                N'' AS paper_code,--紙本編號
                                filed_type,--正本歸檔狀態
                                ua.confiden_level,--機密等級
                                COALESCE(pad.modify_time, pad.create_time) AS operate_time,--更新時間
                                COALESCE(ee_modi.name, ee_create.name) AS operate_cuser,--更新人中文名
                                COALESCE(ee_modi.name_a, ee_create.name_a) AS operate_euser--更新人英文名
                            FROM V_GetUnConfirmedApplication AS ua 
                            LEFT JOIN paper_application_data pad on ua.apply_number = pad.apply_number
							LEFT JOIN (SELECT emplid,name,name_a FROM ps_sub_ee_lgl_vw_a) AS ee_create ON ee_create.emplid = pad.create_user
							LEFT JOIN (SELECT emplid,name,name_a FROM ps_sub_ee_lgl_vw_a) AS ee_modi ON ee_modi.emplid = pad.modify_user
                            WHERE NOT EXISTS(SELECT 1 FROM (SELECT DISTINCT apply_number FROM paper_application_data WHERE application_status <> N'01') AS oa WHERE oa.apply_number = ua.apply_number)";
            string sql_comfirm = $@"SELECT
                                        application_id,
                                        '0' as un_basic_id,  
                                        ua.apply_number,--申請單號
                                        vg.contract_number,--合約編號
                                        my_entity_id,   
                                        vg.contract_name,--合約名稱
                                        pbd.paper_code,--紙本編號
                                        vg.origin_archive_type as filed_type,--正本歸檔狀態 
                                        vg.confiden_level,--機密等級
                                        COALESCE(ua.modify_time, ua.create_time) AS operate_time,--更新時間
                                        COALESCE(ee_modi.name, ee_create.name) AS operate_cuser,--更新人中文名
                                        COALESCE(ee_modi.name_a, ee_create.name_a) AS operate_euser--更新人英文名
                                    FROM (SELECT * FROM paper_application_data WHERE (application_status = N'02' OR application_status = N'03')) AS ua
                                    LEFT JOIN (SELECT * FROM paper_basic_data) AS pbd ON ua.application_id = pbd.paper_applica_id
                                    LEFT JOIN (SELECT * FROM V_GetApplicationSearch) AS vg ON vg.apply_number = ua.apply_number
                                    LEFT JOIN (SELECT emplid,name,name_a FROM ps_sub_ee_lgl_vw_a) AS ee_create ON ee_create.emplid = ua.create_user
							        LEFT JOIN (SELECT emplid,name,name_a FROM ps_sub_ee_lgl_vw_a) AS ee_modi ON ee_modi.emplid = ua.modify_user";
            string sql_nopaper = $@"SELECT
                                        application_id,
                                        '0' as un_basic_id,
                                        ua.apply_number,--申請單號
                                        vg.contract_number,--合約編號
                                        my_entity_id,
                                        vg.contract_name,--合約名稱
                                        N'' AS paper_code,--紙本編號
                                        vg.origin_archive_type as filed_type,--正本歸檔狀態 
                                        vg.confiden_level,--機密等級
                                        COALESCE(ua.modify_time, ua.create_time) AS operate_time,--更新時間
                                        COALESCE(ee_modi.name, ee_create.name) AS operate_cuser,--更新人中文名
                                        COALESCE(ee_modi.name_a, ee_create.name_a) AS operate_euser--更新人英文名
                                        FROM (SELECT * FROM paper_application_data WHERE having_paper = 0 AND application_status = N'03') AS ua 
                                        LEFT JOIN (SELECT * FROM V_GetApplicationSearch) AS vg ON vg.apply_number = ua.apply_number
                                        LEFT JOIN (SELECT emplid,name,name_a FROM ps_sub_ee_lgl_vw_a) AS ee_create ON ee_create.emplid = ua.create_user
							            LEFT JOIN (SELECT emplid,name,name_a FROM ps_sub_ee_lgl_vw_a) AS ee_modi ON ee_modi.emplid = ua.modify_user";

            //(SELECT entity FROM fnp_entity WHERE entity_id = ua.my_entity_id) AS entity_name,--主體簡稱
            StringBuilder countSql = new StringBuilder();
            StringBuilder sql = new StringBuilder();
            StringBuilder condition = new StringBuilder();

            sql.Append(@$"SELECT 
                            Old.un_basic_id, 
                            Old.application_id, 
                            Old.apply_number, 
                            Old.contract_number, 
                            Old.my_entity_id, 
                            Old.contract_name, 
                            Old.paper_code, 
                            Old.filed_type,
                            Old.confiden_level,
                            COALESCE(aff.aff_company_abb, ent.entity) AS entity_name,
                            Old.operate_time,
                            Old.operate_cuser,
                            Old.operate_euser
                          FROM ( ");
            countSql.Append(@$"SELECT COUNT(*) FROM ( ");

            if (qry.paperComfirmType == "01")
            {
                sql.Append(sql_uncomfirm);
                countSql.Append(sql_uncomfirm);
            }
            else if (qry.paperComfirmType == "02")
            {
                sql.Append(sql_comfirm);
                countSql.Append(sql_comfirm);
            }
            else
            {
                sql.Append(sql_nopaper);
                countSql.Append(sql_nopaper);
            }

            sql.Append($@" ) AS Old");
            sql.Append(@$" LEFT JOIN affiliate_company AS aff ON aff.aff_company_code = Old.my_entity_id");
            sql.Append(@$" LEFT JOIN fnp_entity AS ent ON ent.entity_id = Old.my_entity_id");
            sql.Append(@$" WHERE 1=1");

            countSql.Append($@" ) AS Old");
            countSql.Append(@$" WHERE 1=1");

            //申請單號
            if (!string.IsNullOrEmpty(qry.apply_number_start))
            {
                if (!string.IsNullOrEmpty(qry.apply_number_end))
                {
                    condition.Append(@$" AND Old.apply_number >= @apply_number_start");
                    condition.Append(@$" AND Old.apply_number <= @apply_number_end");
                }
                else
                {
                    condition.Append(@$" AND Old.apply_number LIKE CONCAT(@apply_number_start, N'%') ");
                }
            }

            //合約編號
            if (!string.IsNullOrEmpty(qry.contract_number_start))
            {
                if (!string.IsNullOrEmpty(qry.contract_number_end))
                {
                    condition.Append(@$" AND Old.contract_number >= @contract_number_start");
                    condition.Append(@$" AND Old.contract_number <= @contract_number_end");
                }
                else
                {
                    condition.Append(@$" AND Old.contract_number LIKE CONCAT(@contract_number_start, N'%') ");
                }
            }
            //我方主體
            if (!string.IsNullOrEmpty(qry.my_entity_id))
            {
                string[] entitys = qry.my_entity_id.Split(',');
                condition.Append(@$" AND Old.my_entity_id IN ({string.Join(",", entitys.Select(s => $"'{s}'"))})");
            }
            //正本歸檔狀態
            if (!string.IsNullOrEmpty(qry.filed_type))
            {
                string[] filed_type = qry.filed_type.Split(',');
                condition.Append(@$" AND Old.filed_type IN ({string.Join(",", filed_type.Select(s => $"'{s}'"))})");
            }

            countSql.Append(condition.ToString());
            int totalCount = DbAccess.Database.SqlQuery<int>(countSql.ToString(), qry).FirstOrDefault();

            sql.Append(condition.ToString());
            if (qry.page == null || qry.page.PageSize == 0 && qry.page.PageIndex == 0)
                qry.page = new PageParam() { PageIndex = 1, PageSize = 25 };

            if (qry.order == null || string.IsNullOrEmpty(qry.order.Field))
                qry.order = new OrderByParam() { Field = "apply_number", Order = OrderBy.ASC };

            int offset = (qry.page.PageIndex - 1) * qry.page.PageSize;
            string orderStr = qry.order.Field + " " + qry.order.Order.ToString();
            if (qry.order.Field == "apply_number")
            {
                // 若 Field 為 apply_number，按照 C/D/R/AR 的順序進行排序
                orderStr = @"CASE 
                     WHEN SUBSTRING(apply_number, 1, 1) = 'C' THEN 1 
                     WHEN SUBSTRING(apply_number, 1, 1) = 'D' THEN 2 
                     WHEN SUBSTRING(apply_number, 1, 1) = 'R' THEN 3 
                     WHEN SUBSTRING(apply_number, 1, 2) = 'AR' THEN 4 
                     ELSE 5 
                 END, apply_number, paper_code " + qry.order.Order.ToString();
            }
            else
            {
                // 若 Field 不是 apply_number，按照指定的 Field 和 Order 進行排序
                orderStr = qry.order.Field + " " + qry.order.Order.ToString();
            }
            sql.Append($@" ORDER BY {orderStr} OFFSET {offset} ROWS FETCH NEXT {qry.page.PageSize} ROWS ONLY");

            result.Data = DbAccess.Database.SqlQuery<PaperOldDataViewModel>(sql.ToString(), qry).ToList();
            foreach (var item in result.Data)
            {
                if (!string.IsNullOrEmpty(item.contract_number)) item.contract_number = item.contract_number.Replace("\u001f", "");
                if (!string.IsNullOrEmpty(item.contract_name)) item.contract_name = item.contract_name.Replace("\u001f", "");
            }
            result.TotalRows = totalCount;
            return result;
        }
        #endregion

        #region 獲取舊申請單
        /// <summary>
        /// 獲取舊申請單
        /// </summary>
        /// <param name="apply_number"></param>
        /// <returns></returns>
        public PaperOldDataDetailViewModel GetOldApplicationData(string apply_number)
        {
            string sql = $@"SELECT
                                paper_applica_id as application_id, 
                                un_basic_id,--由已確認改為未確認時，存在paperid
                                pads.apply_number,--申請單號
                                vg.contract_number,--合約編號
                                my_entity_id,
                                vg.contract_name,--合約名稱
                                pud.paper_code,--紙本編號
                                vg.origin_archive_type as filed_type,--正本歸檔狀態
                                application_remarks,
								paper_entry_status,
                                (SELECT fun_name FROM sys_parameters WHERE lang_type = @lang AND para_code = N'lib_paperEntryStatus' AND func_code = paper_entry_status) AS paper_entry_status_name,--入庫狀態
                                vg.confiden_level COLLATE Chinese_Taiwan_Stroke_CI_AS AS confiden_level,--機密等級
                                (SELECT fun_name FROM sys_parameters WHERE lang_type = @lang AND para_code = N'confidentStatus' AND func_code = confiden_level COLLATE Chinese_Taiwan_Stroke_CI_AS) AS confiden_level_name,--機密等級								
                                COALESCE(aff.aff_company_abb, ent.entity) AS entity_name ,
                                COALESCE(pud.modify_time, pud.create_time) AS operate_time,--更新時間
                                COALESCE(ee_modi.name, ee_create.name) AS operate_cuser,--更新人中文名
                                COALESCE(ee_modi.name_a, ee_create.name_a) AS operate_euser--更新人英文名
                            FROM paper_unconfirmed_data AS pud
                            INNER JOIN (SELECT * FROM paper_application_data WHERE apply_number = @apply_number) AS pads ON pud.paper_applica_id = pads.application_id
						    LEFT JOIN affiliate_company AS aff ON aff.aff_company_code = my_entity_id 
							LEFT JOIN fnp_entity AS ent ON ent.entity_id = my_entity_id 
                            LEFT JOIN (SELECT emplid,name,name_a FROM ps_sub_ee_lgl_vw_a) AS ee_create ON ee_create.emplid = pud.create_user
							LEFT JOIN (SELECT emplid,name,name_a FROM ps_sub_ee_lgl_vw_a) AS ee_modi ON ee_modi.emplid = pud.modify_user
	                        LEFT JOIN V_GetApplicationSearch vg ON vg.apply_number = pads.apply_number                            
                            UNION
                            SELECT
                                '' AS application_id,
                                0 AS un_basic_id,--完全由歷史單據進入，無任何紙本作業，默認為0
                                ua.apply_number,--申請單號
                                ua.contract_number,--合約編號
                                ua.entity_id as my_entity_id,
                                ua.contract_name,--合約名稱
                                N'' AS paper_code,--紙本編號
                                ua.filed_type,--正本歸檔狀態
                                pad.application_remarks,
								N'' AS paper_entry_status,
                                N'' AS paper_entry_status_name,
                                ua.confiden_level COLLATE Chinese_Taiwan_Stroke_CI_AS AS confiden_level,--機密等級
								(SELECT fun_name FROM sys_parameters WHERE lang_type = @lang AND para_code = N'confidentStatus' AND func_code = confiden_level COLLATE Chinese_Taiwan_Stroke_CI_AS) AS confiden_level_name,--機密等級
								COALESCE(aff.aff_company_abb, ent.entity) AS entity_name ,
                                COALESCE(pad.modify_time, pad.create_time) AS operate_time,--更新時間
                                COALESCE(ee_modi.name, ee_create.name) AS operate_cuser,--更新人中文名
                                COALESCE(ee_modi.name_a, ee_create.name_a) AS operate_euser--更新人英文名
                            FROM V_GetUnConfirmedApplication AS ua 
							LEFT JOIN affiliate_company AS aff ON aff.aff_company_code = ua.entity_id 
							LEFT JOIN fnp_entity AS ent ON ent.entity_id = ua.entity_id 
                            LEFT JOIN paper_application_data pad ON pad.apply_number = ua.apply_number
                            LEFT JOIN (SELECT emplid,name,name_a FROM ps_sub_ee_lgl_vw_a) AS ee_create ON ee_create.emplid = pad.create_user
							LEFT JOIN (SELECT emplid,name,name_a FROM ps_sub_ee_lgl_vw_a) AS ee_modi ON ee_modi.emplid = pad.modify_user
							WHERE NOT EXISTS(SELECT 1 FROM (SELECT DISTINCT apply_number FROM paper_application_data WHERE application_status <> N'01') AS oa WHERE oa.apply_number = ua.apply_number)
                            AND ua.apply_number = @apply_number";
            PaperOldDataDetailViewModel res = NpgsqlSearchBySingle<PaperOldDataDetailViewModel>(sql, new { apply_number, lang = MvcContext.UserInfo.logging_locale });
            if (res != null)
            {
                if (!string.IsNullOrEmpty(res.contract_number)) res.contract_number = res.contract_number.Replace("\u001f", "");
                if (!string.IsNullOrEmpty(res.contract_name)) res.contract_name = res.contract_name.Replace("\u001f", "");
            }
            return res;
        }
        #endregion

        #region 獲取舊的未確認申請單
        /// <summary>
        /// 獲取舊的未確認申請單
        /// </summary>
        /// <param name="apply_number"></param>
        /// <returns></returns>
        public PaperComfirmedOldDataDetailViewModel GetComfirmedOldApplicationData(string apply_number)
        {
            string sql = $@"SELECT
                                application_id, 
                                pad.apply_number,--申請單號
                                vg.contract_number,--合約編號
                                my_entity_id,
                                vg.contract_name,--合約名稱
                                application_remarks,
								COALESCE(aff.aff_company_abb, ent.entity) AS entity_name ,
                                COALESCE(pad.modify_time, pad.create_time) AS operate_time,--更新時間
                                COALESCE(ee_modi.name, ee_create.name) AS operate_cuser,--更新人中文名
                                COALESCE(ee_modi.name_a, ee_create.name_a) AS operate_euser--更新人英文名
                            FROM paper_application_data AS pad
						    LEFT JOIN affiliate_company AS aff ON aff.aff_company_code = my_entity_id 
							LEFT JOIN fnp_entity AS ent ON ent.entity_id = my_entity_id 
                            LEFT JOIN (SELECT emplid,name,name_a FROM ps_sub_ee_lgl_vw_a) AS ee_create ON ee_create.emplid = pad.create_user
							LEFT JOIN (SELECT emplid,name,name_a FROM ps_sub_ee_lgl_vw_a) AS ee_modi ON ee_modi.emplid = pad.modify_user
		                    LEFT JOIN V_GetApplicationSearch vg ON vg.apply_number = pad.apply_number
							WHERE pad.application_status != '01' and pad.apply_number = @apply_number";
            PaperComfirmedOldDataDetailViewModel res = NpgsqlSearchBySingle<PaperComfirmedOldDataDetailViewModel>(sql, new { apply_number, lang = MvcContext.UserInfo.logging_locale });
            if (res != null)
            {
                if (!string.IsNullOrEmpty(res.contract_number)) res.contract_number = res.contract_number.Replace("\u001f", "");
                if (!string.IsNullOrEmpty(res.contract_name)) res.contract_name = res.contract_name.Replace("\u001f", "");
            }
            return res;
        }
        #endregion

        #region 獲取無紙本明細
        /// <summary>
        /// 獲取無紙本明細
        /// </summary>
        /// <param name="apply_number"></param>
        /// <returns></returns>
        public PaperOldDataDetailViewModel GetNonOldApplicationData(string apply_number)
        {
            string sql = $@"SELECT
                                application_id, 
                                0 as un_basic_id,--由已確認改為未確認時，存在paperid
                                pad.apply_number,--申請單號
                                vg.contract_number,--合約編號
                                my_entity_id,
                                vg.contract_name,--合約名稱
                                '' as paper_code,--紙本編號
                                '' as filed_type,--正本歸檔狀態
								'' as paper_entry_status,
                                '' AS paper_entry_status_name,--入庫狀態
	                            application_remarks,
                                application_status,
								COALESCE(aff.aff_company_abb, ent.entity) AS entity_name ,
                                COALESCE(pad.modify_time, pad.create_time) AS operate_time,--更新時間
                                COALESCE(ee_modi.name, ee_create.name) AS operate_cuser,--更新人中文名
                                COALESCE(ee_modi.name_a, ee_create.name_a) AS operate_euser--更新人英文名
                            FROM paper_application_data AS pad
						    LEFT JOIN affiliate_company AS aff ON aff.aff_company_code = my_entity_id 
							LEFT JOIN fnp_entity AS ent ON ent.entity_id = my_entity_id 
                            LEFT JOIN (SELECT emplid,name,name_a FROM ps_sub_ee_lgl_vw_a) AS ee_create ON ee_create.emplid = pad.create_user
							LEFT JOIN (SELECT emplid,name,name_a FROM ps_sub_ee_lgl_vw_a) AS ee_modi ON ee_modi.emplid = pad.modify_user
		                    LEFT JOIN V_GetApplicationSearch vg ON vg.apply_number = pad.apply_number
							WHERE pad.apply_number = @apply_number";

            PaperOldDataDetailViewModel res = NpgsqlSearchBySingle<PaperOldDataDetailViewModel>(sql, new { apply_number });
            if (res != null)
            {
                if (!string.IsNullOrEmpty(res.contract_number)) res.contract_number = res.contract_number.Replace("\u001f", "");
                if (!string.IsNullOrEmpty(res.contract_name)) res.contract_name = res.contract_name.Replace("\u001f", "");
            }
            return res;
        }
        #endregion

        #region 更新紙本狀態，無紙本時作業
        /// <summary>
        /// 更新紙本狀態，無紙本時作業
        /// </summary>
        /// <param name="application_id"></param>
        /// <param name="application_status"></param>
        /// <param name="application_remark"></param>
        /// <param name="having_paper"></param>
        /// <returns></returns>
        public bool UpdatePaperApplicationStatus(int application_id, string application_status, string? application_remark, int having_paper)
        {
            string sql = $@"UPDATE paper_application_data SET 
                                application_status = @application_status, 
                                application_remarks = @application_remark,
                                modify_user = @modify_user, 
                                modify_time = getutcdate() 
                            WHERE application_id = @application_id AND having_paper = @having_paper";
            return ExecuteCommand(sql, new
            {
                application_status,
                application_remark,
                modify_user = MvcContext.UserInfo.current_emp,
                application_id,
                having_paper
            }) > 0;
        }
        #endregion

        #region 獲取未確認明細
        /// <summary>
        /// 獲取未確認明細
        /// </summary>
        /// <param name="paper_applica_id"></param>
        /// <returns></returns>
        public List<paper_unconfirmed_data> GetPaperUnconfirmedData(string paper_applica_id)
        {
            string sql = $@"SELECT * FROM paper_unconfirmed_data WHERE paper_applica_id = @paper_applica_id";
            return NpgsqlSearchByList<paper_unconfirmed_data>(sql, new { paper_applica_id });
        }
        #endregion

        #region 事務處理

        #region 修改紙本狀態
        /// <summary>
        /// 修改紙本狀態
        /// </summary>
        /// <param name="application_id"></param>
        /// <param name="application_status"></param>
        /// <param name="having_paper"></param>
        /// <param name="context"></param>
        /// <returns></returns>
        public bool UpdateApplicationStatusToTransaction(int application_id, string application_status, int having_paper, IDbContext context)
        {
            string sql = $@"UPDATE paper_application_data SET 
                                application_status = @application_status, 
                                having_paper = @having_paper,
                                modify_user = @modify_user, 
                                modify_time = getutcdate() 
                            WHERE application_id = @application_id ";
            return ExecuteCommandToTransaction(sql, context, new
            {
                application_status,
                having_paper,
                modify_user = MvcContext.UserInfo.current_emp,
                application_id
            }) > 0;
        }
        #endregion

        #region 移除舊的未確認資料
        /// <summary>
        /// 移除舊的未確認資料
        /// </summary>
        /// <param name="paper_applica_id"></param>
        /// <param name="context"></param>
        /// <returns></returns>
        public bool DeletePaperUnconfirmDataToTransaction(int paper_applica_id, IDbContext context)
        {
            string sql = $@"DELETE FROM paper_unconfirmed_data WHERE paper_applica_id = @paper_applica_id";
            return ExecuteCommandToTransaction(sql, context, new { paper_applica_id }) > 0;
        }
        #endregion

        #region 修改紙本狀態
        /// <summary>
        /// 修改紙本狀態
        /// </summary>
        /// <param name="application_id"></param>
        /// <param name="application_status"></param>
        /// <param name="application_remark"></param>
        /// <param name="having_paper"></param>
        /// <param name="context"></param>
        /// <returns></returns>
        public bool UpdatePaperApplicationStatusToTransaction(int application_id, string application_status, string? application_remark, int having_paper, IDbContext context)
        {
            string sql = $@"UPDATE paper_application_data SET 
                                application_status = @application_status, 
                                application_remarks = @application_remark,
                                modify_user = @modify_user, 
                                modify_time = getutcdate() 
                            WHERE application_id = @application_id AND having_paper = @having_paper";
            return ExecuteCommandToTransaction(sql, context, new
            {
                application_status,
                application_remark,
                modify_user = MvcContext.UserInfo.current_emp,
                application_id,
                having_paper
            }) > 0;
        }
        #endregion

        #region 插入未確認資料
        /// <summary>
        /// 插入未確認資料
        /// </summary>
        /// <param name="paper_applica_id"></param>
        /// <param name="paper_remark"></param>
        /// <param name="context"></param>
        /// <returns></returns>
        public bool InsertPaperUnconfirmDataToTransaction(int paper_applica_id, string? paper_remark, IDbContext context)
        {
            string sql = $@"INSERT INTO paper_unconfirmed_data
                                (paper_applica_id, paper_code, paper_name, paper_type, paper_confiden_level, parer_borrow_days, paper_position, paper_entry_status, destroy_time, lost_time, paper_return_status, is_excel, paper_remarks, create_user)
                            SELECT paper_applica_id, paper_code, paper_name, paper_type, paper_confiden_level, parer_borrow_days, paper_position, '04', destroy_time, lost_time, paper_return_status, is_excel, CONCAT(paper_remarks, @paper_remarks) , @create_user AS create_user FROM paper_basic_data WHERE paper_applica_id = @paper_applica_id";
            return ExecuteCommandToTransaction(sql, context, new { paper_applica_id, paper_remarks = paper_remark, create_user = MvcContext.UserInfo.current_emp }) > 0;
        }
        #endregion

        #endregion
    }
}
